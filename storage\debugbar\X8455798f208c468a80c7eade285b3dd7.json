{"__meta": {"id": "X8455798f208c468a80c7eade285b3dd7", "datetime": "2025-06-08 15:40:15", "utime": **********.902232, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.144323, "end": **********.902259, "duration": 0.7579360008239746, "duration_str": "758ms", "measures": [{"label": "Booting", "start": **********.144323, "relative_start": 0, "end": **********.793306, "relative_end": **********.793306, "duration": 0.6489830017089844, "duration_str": "649ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.793319, "relative_start": 0.6489958763122559, "end": **********.902261, "relative_end": 1.9073486328125e-06, "duration": 0.10894203186035156, "duration_str": "109ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139472, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028789999999999996, "accumulated_duration_str": "28.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.836293, "duration": 0.027149999999999997, "duration_str": "27.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.304}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.878653, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 94.304, "width_percent": 2.605}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.889354, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.909, "width_percent": 3.091}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1913218307 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397211842%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InI2U3VhUk5LMlF3Vmkvd3dZTVA5L0E9PSIsInZhbHVlIjoiK1pGVi9rVzlYUEsyRjMxYzQxZjFpd2tNODhBeldkUGlUREszT2NyMnozNTRFUzYzbjZMdFptT1dCMEJTV01oMlZ6TEtnZzBKRmxqNTJnY3Jud2d4aVRSU0ZUaDZWak0vbzBJcEZCMnlQbGtvS2lQeiszUDdxQ2ZHYWRBTXBpUG5HbGhVL0F0TzM5RzB4UmtrbGlUZ1lmNjkzSTF1NU56R3g2QVAxNWo2Z1NHSDM4eGVwNW9KOFFlUWpNNkFkZlRoVUZGdldRbCtLcmF3NlJOZjFoaDQwNDhlQW9USTdGSC9hMk1WY3NnVCtuMXFXclphbmRzMWVlU2Vvc3dTbFhiQ3R6bVRYZFJ2VXo2QzlNMWI0UzN0aHhOZC9nQTNXYmJ1Q2tJaW9iTVZYcHFiWk1FNzBKNEhPU3ZDM2dHclhDcG8wTVJHVWlPU2hUZTFYdFRzaWJWbFpoRWpzcWtrb2U2U0psdDRMcUU5ejZRWG9xNnpPdkkrR3dSeFZ6UC91MmV5a0V3OXpZbVdCZlNRRlpwajVZaXR0YXk0RzVhbHZUQVVxQUI1eWwxbEZNZjl4ZFJGSXl4R2t1SFVNcllEWGtMbStMTzB3ZloxL1ZXRXlDb2R6U3ZKdXdnL1VBQ2FuVU93L3BaVm11aDZISDEzajY3SjhQbGZrOVpPUStzTkFoeEQiLCJtYWMiOiJhOGE5ZWJlMzg4MDU1NjNiYjJhNTg1MDMyNjUzNmRkYmIyOTZiOWMyMDExYWIxMmMyNGU3MjNkOWUyZTkyNmNlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkNTdElIbUF2Y0pEWm5vYTRQTTBwd0E9PSIsInZhbHVlIjoiMU5Pd3doRmxQd1F5Z2QxWUNaNjE5NFNLT0JIdXovdGxTT2hMMUVkcFVMaU9VVmZxTTB1YWtzNzhHYjdqd1BrelZicTA5eTZaNGRHUm0rQmdpVXM2NXRRNEVJQkFnNW9pNUtPUlVBaFAzeUdkcnFGYlNsenh3bDh4TXVWT0htSXZWMS80WVBJb1RtUXJqa3pSSjlRc29VakZiSEw2Q3cwbzI5WkhWclNuOGNMVXlOc1FoTXJuQmhIeHNlQnYvUENMUU5CUTZmeG9CdW5TMWV1elZpaUhoQUdBb0RjQ1BDU3c5OTlFU210Ym04N0taN3ZpSEdXSlRveHFMZ3dVNnQ2a08yRnNRQThkSTc3dkZyczZsdFkrYk5ZYlZ2bHN4dVM4d2tOLzZxQ2lWMVQ3emN4a3FmU2U3SGJiQ1MvMXNlT1lETW50cExCeTBCMEJidnVvVHA4cjhrOHl4MzFhdEZNZERPVGJpL1ZCZnVDa3hOdTRUZFpPN0lLek45R2NVaWNFZlpPeVpJQitiN2ZCaGc1aHVtQ3FVOFA5aDlMS2VlNk45M3UzSlVuNVpXV3BodWhnU3h0NHZuUFBHbFhqNmdVU3paL1ZxOUlPZDlvOVNwWmV0NW1YaGUrZEhJa0xwTkxNbUcvWnp0MzFpc25pM28rZ3QvRmxyMFVzK2ZaQnNGd0ciLCJtYWMiOiJmNTFmMzc4YjAxYWFmMTQwODc5ZDg0Mjk1ZTA2ZGQ4YzVhYzVmMjRlYWY2YjkzMDJmNjJiY2I4NjU3MmQ1N2ZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1913218307\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-90787229 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-90787229\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1330160191 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:40:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJjNHloZGY4cFh5Y3BraUVzd2d1cmc9PSIsInZhbHVlIjoiMXhYakRyeThuQUNYdzFTdnNWMS9SSUp5VGt0RE1iNmR2bkRkdytwN3diWmIyb2VtRUFLTnpNUWlOWFVGVm5xTDVDOTF2YUZSdEhqVE9MVENncUliZnRvVTVGMG41azVuWmVDRDN2ajhjWm5oeHVZMWdIb1ppdW8zdUVFM3BrZUNsQkcrOTRVTlBXcDIrRUo1UjlKeEIydm9SVjlmTWlHR2RiUHlmdU1HNDdwYml3WHBWaVcrVWtTRm8vdGNuODdzT1dlTTdmL3RReFVsRjlQWVNXR3dNaDVVVXNSMGdmWElhTWNkV2I3TkNyRkl3cnFPWnk1VHFJOHpyYjBkUVByK3g4NDErbmlndmZxNjRheEZrc1BWUDNJTXhHYXlxMUNrUUdTaXZ1OWZ2NXJ1ZXhvd2RKNWI4aG9ZZjA0cDRQMnhBcEpqS3lZcE5WYzhaR1cyMjk5ZXoxTnZ3L25UVlJDWXBXdHVRejMranVnNTdkWHhGV0tYb3RFbVVtNk4ySGhhRkFqK3dYS2pFMlZRU0NzT1hkQ0hQSWZ0RHduRUFGSXhOcUFiVnl3Uy9FR2tUdmliaW56QUlsbm55eGVQU2VwWnppK1F6ckJSeGgwekJxenJ6cytwUS85YVVVc1pCMjVSYWpDeW16NWNDOEZhMFFMYXlrM3hFUjlkdFZ0c2hyQWkiLCJtYWMiOiI1Nzc0NjVhNGIxYzVjNGVhYmFjZjRkMzQxNjhiZThhYjc5M2I0OWVlZDY1ODkzNzI1NjA1ZmQ5NzgwZGVjYjY4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:40:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkJ6VWYrNmJhN3dwYmFtT0dSa2k3cWc9PSIsInZhbHVlIjoidGtSR2dDMUdNTy9PZkUyeFZuR3p3SWt3UkdvTzJJa0NEM21hbVMyZ25oV0JaS1pSTTRyWW1GdTVwaGdVM1l0cTdRRE9MdUFocFlUNkVMdDBzSm05ME9sNVNDeVRlRis1YlhMQmZ1U0EwZUx3TTdQMVZDZGtvRkd0Zzk3eFBkdzRlSzRaaEYyOHh1ek5OaUVxVTdFWGRrOVFmdFZXZHIvWkRoQlRsK0xRcU5QVzU2a1phays0Umx3UFB4REZub2VWYlI5YmduK1BRT0p3ak93d2hRNXNlZHgzMTJZRVdrWnp3ak0wMEdwQW01WXNpQmxSZmtHcHJlZmtZRWZNakdMNTk0NlhWZDFJTVlhTzJaREZCOGVZcUpiN0ZXby8rcHFlVHZyaHVBQTVkK2VOOHRxdmRscXVIQ1EraTRnZGJCcWJ2VnNGQTM0MFMyUXZyd0EybW5nMi95TUplMEVYV3Jjb3lMeGsvNklWU2diR081QlRlcUUvTnc5TFhUcXJmd2R6YlJNd3VoNGNhUSt4bDRya0JtVTJuU25YSWRidDM2TDJjaXBpVzBiVEhycjhmTVg3M1NiZis4amNwc3dVU3V5cHk4TlRJdXNJcnNQZllWY2wzamtPdWdkRGRnMlF0ZVZwVnRQWWY1LzBpZkI4MFlJMFVmNlE0MUZCcFMveVdmU3YiLCJtYWMiOiJjODNkYWY3MzMyODAwZjI5NTczYjJhNDU1MTM4YzNmYTliZDQwOTQ0MjM4NDdkNzEwNzE1ODI1YjZkNmUxMjRlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:40:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJjNHloZGY4cFh5Y3BraUVzd2d1cmc9PSIsInZhbHVlIjoiMXhYakRyeThuQUNYdzFTdnNWMS9SSUp5VGt0RE1iNmR2bkRkdytwN3diWmIyb2VtRUFLTnpNUWlOWFVGVm5xTDVDOTF2YUZSdEhqVE9MVENncUliZnRvVTVGMG41azVuWmVDRDN2ajhjWm5oeHVZMWdIb1ppdW8zdUVFM3BrZUNsQkcrOTRVTlBXcDIrRUo1UjlKeEIydm9SVjlmTWlHR2RiUHlmdU1HNDdwYml3WHBWaVcrVWtTRm8vdGNuODdzT1dlTTdmL3RReFVsRjlQWVNXR3dNaDVVVXNSMGdmWElhTWNkV2I3TkNyRkl3cnFPWnk1VHFJOHpyYjBkUVByK3g4NDErbmlndmZxNjRheEZrc1BWUDNJTXhHYXlxMUNrUUdTaXZ1OWZ2NXJ1ZXhvd2RKNWI4aG9ZZjA0cDRQMnhBcEpqS3lZcE5WYzhaR1cyMjk5ZXoxTnZ3L25UVlJDWXBXdHVRejMranVnNTdkWHhGV0tYb3RFbVVtNk4ySGhhRkFqK3dYS2pFMlZRU0NzT1hkQ0hQSWZ0RHduRUFGSXhOcUFiVnl3Uy9FR2tUdmliaW56QUlsbm55eGVQU2VwWnppK1F6ckJSeGgwekJxenJ6cytwUS85YVVVc1pCMjVSYWpDeW16NWNDOEZhMFFMYXlrM3hFUjlkdFZ0c2hyQWkiLCJtYWMiOiI1Nzc0NjVhNGIxYzVjNGVhYmFjZjRkMzQxNjhiZThhYjc5M2I0OWVlZDY1ODkzNzI1NjA1ZmQ5NzgwZGVjYjY4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:40:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkJ6VWYrNmJhN3dwYmFtT0dSa2k3cWc9PSIsInZhbHVlIjoidGtSR2dDMUdNTy9PZkUyeFZuR3p3SWt3UkdvTzJJa0NEM21hbVMyZ25oV0JaS1pSTTRyWW1GdTVwaGdVM1l0cTdRRE9MdUFocFlUNkVMdDBzSm05ME9sNVNDeVRlRis1YlhMQmZ1U0EwZUx3TTdQMVZDZGtvRkd0Zzk3eFBkdzRlSzRaaEYyOHh1ek5OaUVxVTdFWGRrOVFmdFZXZHIvWkRoQlRsK0xRcU5QVzU2a1phays0Umx3UFB4REZub2VWYlI5YmduK1BRT0p3ak93d2hRNXNlZHgzMTJZRVdrWnp3ak0wMEdwQW01WXNpQmxSZmtHcHJlZmtZRWZNakdMNTk0NlhWZDFJTVlhTzJaREZCOGVZcUpiN0ZXby8rcHFlVHZyaHVBQTVkK2VOOHRxdmRscXVIQ1EraTRnZGJCcWJ2VnNGQTM0MFMyUXZyd0EybW5nMi95TUplMEVYV3Jjb3lMeGsvNklWU2diR081QlRlcUUvTnc5TFhUcXJmd2R6YlJNd3VoNGNhUSt4bDRya0JtVTJuU25YSWRidDM2TDJjaXBpVzBiVEhycjhmTVg3M1NiZis4amNwc3dVU3V5cHk4TlRJdXNJcnNQZllWY2wzamtPdWdkRGRnMlF0ZVZwVnRQWWY1LzBpZkI4MFlJMFVmNlE0MUZCcFMveVdmU3YiLCJtYWMiOiJjODNkYWY3MzMyODAwZjI5NTczYjJhNDU1MTM4YzNmYTliZDQwOTQ0MjM4NDdkNzEwNzE1ODI1YjZkNmUxMjRlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:40:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1330160191\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-8******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8********\", {\"maxDepth\":0})</script>\n"}}