{"__meta": {"id": "X38b1b7c8fe7b9ec2a78a666f2abdf8bc", "datetime": "2025-06-30 15:34:30", "utime": **********.777207, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.32434, "end": **********.777226, "duration": 0.45288586616516113, "duration_str": "453ms", "measures": [{"label": "Booting", "start": **********.32434, "relative_start": 0, "end": **********.697157, "relative_end": **********.697157, "duration": 0.372816801071167, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.697174, "relative_start": 0.3728339672088623, "end": **********.777228, "relative_end": 2.1457672119140625e-06, "duration": 0.08005404472351074, "duration_str": "80.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45556512, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.016079999999999997, "accumulated_duration_str": "16.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7315981, "duration": 0.01525, "duration_str": "15.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.838}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7550468, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.838, "width_percent": 2.363}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7603261, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.201, "width_percent": 2.799}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-221893359 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im5XaVFGTkZqRU5yM0RsclFrY2xjaWc9PSIsInZhbHVlIjoiS3Yyc3MwZWlqdGZxL2puMlIyaWtucHA1dVF4UEFuMmFzRHB2eGVFZUVINjBkUnlCeXJxZE1lcmNGUk4zUGlrVDRXRHlqWTFPZzdrN1NxNXNXUWFBbHp5OEhXNzlYWU9CUjZ2M3lVV29jSTU5MVlOakRVNEl4REVvZWFqZXd4ZXI2TlI1UnZEWm1VbVNMRFZON0RIc1hrWDZIUE1HUnBuVFZkN20vZGlQK3hrYUdUVTVTUDVMYmJNeG9rZG9iWGEwWk1aZzMra2lVWnJSbWRtMzUzWXFrR25LRlhodGFuZmRZNlFNa1lmSlRPTk1Bd2FYR2NQMHRGQXQ1MSs3c3pPUjhBRUtxa1N6ZXVkS0kwOXVRUTdUcEhTb2FWWUNZaTE1ZXI3YmRrcG5wM1Q2VS9ySWxjbEM4ZUZITGQ2N0IyV0RYbjdnbHc5b2haVWlXR0EzeVk0bGdpTmZQcURGZTVPd1QrZEZ5TWJKRW9USCtkYkFYUFhOeEVPTi9IVzMwT3pkWXVWYUZLdXhSOThwcVZMQVZrRFJFWXZYWDhnb0E1ZEpTbDdYQTFjdHVYVmxmclg3VFZML2pmSVo0QVZZbEg2bldqejFTMFZIVEkyNU1PUGV3K0NwV0xabDFaTW5wV3BwYmNxcVZYUkRsdUxDL05zcGZCSGhKZFBzR3Y3VSt5eGMiLCJtYWMiOiJjNDExY2RhNzg0NThkZGJiNjBiYmZmZmUzMTI5ZmNhZGJjOGRlNDBhMzBmYmIwNzNlMGJhYzc2ZWZhYTlmNzYzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjY0MWZrMWdySVBlTUtyc3lsUllrZUE9PSIsInZhbHVlIjoiQ05oV1BpWDlHWGxXSmQzdHNQQTJ5dkxUNTBDVEpyQ2crT2ZyemdIT0RVbWFmM21aVW5zTVdoK0RSM0RKaUVUZE96NXpGVU5mZEpvb2pGcXNEOVY1aXltL0Yzbzc4RDRzeW5XQXZ3N2QvSndrcUN0Z3BMamJGc3ZqUVFmeWV2OHZwbGkxWEJtWUJpR0VJb1N5a1dIdHU1eW9aK1AwSVF5QUJscUI0N05SejlQdE4vTjlPNWpKWW95OGtsTjAzbFdndytCblBNd3U0Wi9KbXI5eEJCT3U5YStOM25iaWNUUCtQUUVYMUVmekg3MHZPc1lFNEFRN21DWXR5SXBnZVBUUTJSL0l4aUx1M0ViTXNnMXVwajBIZWp5RWFBNlROdW5rUjVwUDNRMHd5aFdmRGdUak9OZkNrNnNmaUpYVFlCeTZjcGRzeVNTYnpZZS9OajRIcnhCSk1ta1FKcXRaNHA5T3J6UlZncTNDVUZBTWFtM0c2Y0xrbFZtVTROcktIVVN5ZEZYa0lzemhvQXdUYWNrdVgyckxMaEhYVHdpeW1OOGJsS1RYQmxPeGZIQUtVR1VobGVnZDhGczV6U0VMcUE2ajk3aVBYTDVqWU8wZDhOWlVBVGF1VnV0dVVudi9lY3pBYjJZcUprVVZMcWJlc3RyN0Nvc2llM0VRbTRSSjR1eHgiLCJtYWMiOiIxYjBkMzVlOTViN2UzMzE0NjM4MGFmNWJlMzNmM2JhYzczM2M2MDVhODQ2MWI0NjU3NjRiZmNmODIyNTczNjk0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-221893359\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1016614189 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1016614189\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-703948145 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InkzQnRQZ3Mvb1N2L2xDYTljK2ZEekE9PSIsInZhbHVlIjoiRzBoanZ4cHVzNERKNUpua3N2empVZ2ZidUFCTHN3YlBUUmtJSm1idElwVHhNR3QvMHBzWlFPQTN5YXpuSmZIc2puU1NhN2M4ZG9Pc3ROakxoMVV1eDJyMGo1eU01d1M4Ny8wVDNGRVpCaWsvV0tSU0pqTkp1SjNEWHRDcW1YZXo5enQ2Q1JsRk9MMnU4cmgyM2JNcnYyU09RVUtnRnY2eTMzbnFPSjNZVDRFOGVMbWhDNzczTXNxRFNyZitwZVdETEFRczBWV0JuVFRVSXdTL3BSSENXazVNaG9RZWJRbXJYUC9CL0NTTXluQWxRbEQybTNyT0hqY1lYRlZ4YUFhTFVTLzBVOTFvOTZDQzZqelcza09HclBlNlFjSTdlSW1Gdklzek1OSFgreTNPWGhWRXFjRC9WOCtZQ1lXR0ppRTJaWlg5b1NSTWpPQkFzZDBpZXZFL1U1bjB5Rk9aU01MbTE1ZjY2NUFjQ3duOEpjaWdxT2FJN0IwUE1IaEttaXdhODZseTY0S0hwNkFCVWxaYnh5bXc4M0luOWlIWEh6TCtKZHlra1lTODB4TTI5RDlSaTAvZkx2bmU3MDgvRnZ6b1JWK0lYVHFsNjM2VG1pOS85Sy9LVFl6aFdndysrL0EweWxaVUQ2djkvWlFwMlVwNDVRUiswT3QrdnF3c1V1WnEiLCJtYWMiOiIzNTJiOTIxN2MyMjhlMDJhN2ZlZDE5Mjg1ZGI0YjgwZDJhZGRmODIxMjAzMTkwNDNjOWRlNTQ2YzAzMDg5Nzk5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNHQWlvUWplcTZDRmJXaEEwVVZTZEE9PSIsInZhbHVlIjoiZUdveXNST0tHVmplaGc3VU5oNkJodlpid3JPOEpwNGpLQ1ZJdEkxaFJkVEpNcHdvMVNTZERVM3dEUjdxNmh3Q0dVR2JCNlF0bGQ5bk0wRTgvMHUrcXN0K0ZVSEttTERBTFg4NUt0MmdWaWZxdnVDblZSd1VOZkV5aHNqMmVzR01ZQWhLY2VLQWhyNTB3RWI0OGhIdE1DLytDdUdPQ2ppZVYzZURNTGF3WUZUd2VPREpxUm9wQTluRUNHNjNpVXgxeE5lZjBuajdkeGlIdzE0VDhuKzg4ZGNwbDkvd3VLQ3lvUVhiRzhxaDl3NkZwVy9oUzJKSnlLSU9TWmRhVy9YTTZQSDFMaVQxRHdtMEVISVFkVWhGY3VXWVFaQzhEd0NVYlVjN3IwbDNOTkRIcXdoV05MVndPMWducTZvT09LeGZGWEczdTNWM0ozZllLcnZ3S0ZUY3oyVit4K1RKcFBDY29QbENrcXhnRVQzOTJJSDFkYWZaTFFhZEFLc3M4QXV4WE9haThSY1ZhY1FZU3M2OWdFZG0rQkdtM1F6emJDRFhlYklwL2RsWGhPNjV0b1hjQ291Q1BhUC9venFLam02Y2FBVFI4WHh2RjAzQjBiQ1EzNkl5L0tocWdGVDc1RGY3cVJWdXNZVVRrR00vTXVIeGpqeVplVlhCV2ZETzE0ZlEiLCJtYWMiOiJmZjc5OGNhMDU0NGM3MTY5ZjVmZjBiNzhiY2VmOGYwYzhjM2U0NTAzMWViYjdiMmU1MDQ3YmQ3MGMxMDhhZmU1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InkzQnRQZ3Mvb1N2L2xDYTljK2ZEekE9PSIsInZhbHVlIjoiRzBoanZ4cHVzNERKNUpua3N2empVZ2ZidUFCTHN3YlBUUmtJSm1idElwVHhNR3QvMHBzWlFPQTN5YXpuSmZIc2puU1NhN2M4ZG9Pc3ROakxoMVV1eDJyMGo1eU01d1M4Ny8wVDNGRVpCaWsvV0tSU0pqTkp1SjNEWHRDcW1YZXo5enQ2Q1JsRk9MMnU4cmgyM2JNcnYyU09RVUtnRnY2eTMzbnFPSjNZVDRFOGVMbWhDNzczTXNxRFNyZitwZVdETEFRczBWV0JuVFRVSXdTL3BSSENXazVNaG9RZWJRbXJYUC9CL0NTTXluQWxRbEQybTNyT0hqY1lYRlZ4YUFhTFVTLzBVOTFvOTZDQzZqelcza09HclBlNlFjSTdlSW1Gdklzek1OSFgreTNPWGhWRXFjRC9WOCtZQ1lXR0ppRTJaWlg5b1NSTWpPQkFzZDBpZXZFL1U1bjB5Rk9aU01MbTE1ZjY2NUFjQ3duOEpjaWdxT2FJN0IwUE1IaEttaXdhODZseTY0S0hwNkFCVWxaYnh5bXc4M0luOWlIWEh6TCtKZHlra1lTODB4TTI5RDlSaTAvZkx2bmU3MDgvRnZ6b1JWK0lYVHFsNjM2VG1pOS85Sy9LVFl6aFdndysrL0EweWxaVUQ2djkvWlFwMlVwNDVRUiswT3QrdnF3c1V1WnEiLCJtYWMiOiIzNTJiOTIxN2MyMjhlMDJhN2ZlZDE5Mjg1ZGI0YjgwZDJhZGRmODIxMjAzMTkwNDNjOWRlNTQ2YzAzMDg5Nzk5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNHQWlvUWplcTZDRmJXaEEwVVZTZEE9PSIsInZhbHVlIjoiZUdveXNST0tHVmplaGc3VU5oNkJodlpid3JPOEpwNGpLQ1ZJdEkxaFJkVEpNcHdvMVNTZERVM3dEUjdxNmh3Q0dVR2JCNlF0bGQ5bk0wRTgvMHUrcXN0K0ZVSEttTERBTFg4NUt0MmdWaWZxdnVDblZSd1VOZkV5aHNqMmVzR01ZQWhLY2VLQWhyNTB3RWI0OGhIdE1DLytDdUdPQ2ppZVYzZURNTGF3WUZUd2VPREpxUm9wQTluRUNHNjNpVXgxeE5lZjBuajdkeGlIdzE0VDhuKzg4ZGNwbDkvd3VLQ3lvUVhiRzhxaDl3NkZwVy9oUzJKSnlLSU9TWmRhVy9YTTZQSDFMaVQxRHdtMEVISVFkVWhGY3VXWVFaQzhEd0NVYlVjN3IwbDNOTkRIcXdoV05MVndPMWducTZvT09LeGZGWEczdTNWM0ozZllLcnZ3S0ZUY3oyVit4K1RKcFBDY29QbENrcXhnRVQzOTJJSDFkYWZaTFFhZEFLc3M4QXV4WE9haThSY1ZhY1FZU3M2OWdFZG0rQkdtM1F6emJDRFhlYklwL2RsWGhPNjV0b1hjQ291Q1BhUC9venFLam02Y2FBVFI4WHh2RjAzQjBiQ1EzNkl5L0tocWdGVDc1RGY3cVJWdXNZVVRrR00vTXVIeGpqeVplVlhCV2ZETzE0ZlEiLCJtYWMiOiJmZjc5OGNhMDU0NGM3MTY5ZjVmZjBiNzhiY2VmOGYwYzhjM2U0NTAzMWViYjdiMmU1MDQ3YmQ3MGMxMDhhZmU1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-703948145\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-4******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-4********\", {\"maxDepth\":0})</script>\n"}}