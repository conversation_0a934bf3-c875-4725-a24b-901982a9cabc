{"__meta": {"id": "Xfb3b6413de4441642d3dbd3f34b3255f", "datetime": "2025-06-30 15:35:00", "utime": **********.450546, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751297699.999612, "end": **********.45056, "duration": 0.45094799995422363, "duration_str": "451ms", "measures": [{"label": "Booting", "start": 1751297699.999612, "relative_start": 0, "end": **********.382253, "relative_end": **********.382253, "duration": 0.3826408386230469, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.382263, "relative_start": 0.38265085220336914, "end": **********.450562, "relative_end": 1.9073486328125e-06, "duration": 0.0682990550994873, "duration_str": "68.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43873664, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01853, "accumulated_duration_str": "18.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4107668, "duration": 0.01814, "duration_str": "18.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.895}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4318519, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 97.895, "width_percent": 2.105}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:2 [\n  1839 => array:9 [\n    \"name\" => \"ميداليات شكل محتلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"1839\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  1877 => array:8 [\n    \"name\" => \"cleaner Mop\"\n    \"quantity\" => 1\n    \"price\" => \"15.00\"\n    \"tax\" => 0\n    \"subtotal\" => 15.0\n    \"id\" => \"1877\"\n    \"originalquantity\" => 4\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1174811987 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1174811987\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1619590891 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297684545%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFpeVZXbG1IWXBEUVVaWlJZWkRTY0E9PSIsInZhbHVlIjoiMXAxS09HVnBZbzNBdDRqeS9oSzl3emkvYmRLWGN5dzBrZXlVUVdZVU9Ja2Jpc1YrNHZIdjc1dFNCNlE4Z3RVOUtPbHVrNDZhNnZmSmJ0dE4xZFhsaUV2YmlYNWZGZi9kMXE0bytva0w2Z2VpRjBqbDFnanY0NVF3bVUxYmFoU0RiTGJZUnBBRmlKNHE1TktqTXFiNGVDeU95VmFraU1ialNqdFUra0xyT2w3dzF3UCtaZXlYZ0hLWVZSQk9admkyclZQT0lkanRxSVc1T1BJSE9NTUJPRzJ4b21jNlN4bXV6U0o5N1VqVWZIa25iM3E2S2xuOTVHMVpML25NcEFzZkZua1kyeHU1cjJmU0dPVkRobUUxZDhtZTVCMnFmRTZpbzN4a2hGSm12OG5kNk9URlhaSGZPTC85OExGRVhSMW9uRWlWbldXOTlXbWpTWUVCK2RpZUVXKzVndmN2MW5HN0Qra1k3YUtMOXUvT3NWNGpKS3RKNmYrSm5LRUcwejl6WllOYjc5NU1SRW9ON0x3SVBZWjlTR0drYkdVYm1FN0FXb0JrUkMzQ0tabCtrM2cxRGV1ZkdEYkRpREJlcFNYbTRFSVA4WExmejh6OFJHUTZmN3JHdFZ0dUgzRjMvUjV4OXFVTWZlMXdQVEtGaTBPT0p0WU1aSW5CWlZRU25ocSsiLCJtYWMiOiJjNjcyOTc0N2MyMWUwYjMxMzEyNmYxMDFmYTdkNjY4MWNiODgzOGRiMDRjYTdjMmU1OTVlZWJmOWI4OGEwNjczIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdpN2ZlQ2ZvU1JiSThiRFRtK1BxcUE9PSIsInZhbHVlIjoiZ1ZSZU9XWFZyUFdOSGlONENnZGY4R3h3V1g0SWpML1RkVENtM2hXSEhxQWNPcE9UZkE3Q2NCSTFlZWFVNlhXcTczcGt1Tms2K1VMMU1USlRTZW5kaStDTnMwdUNOWEtsSDMybGxFMVBXeUFjdkErQlFyL2RBeGdQcUlFSDhhMDJEY0lEV0FPeUdzcEgrL3crL29RcVZhbXRUb0k4RnRRVnlvSFk3S2g2RzdhN0IwNG1XYUN6ekhid01UNWtkcU9jRjVqbFVDUzM5NmNnbW52OXlMQlhKeUZ0cGNWMlE3cS9xVC9xNGRSTUc2MVpIS3lXS2ZZL1JYSkgwM1MrN3RyeUV5Z0l5ZzFnZjN5K1hYaHp3SFNmcG9YQTZuWERvWXVrZjBNbFRFV0VGeVdqSytML1V4Q1pKS3VwZnV6MytLWEtzb0NPaWRtaEFvT3JLTEJBWFFJcldEb2tIVnpzenVXbGdWYUpuNUR0UzR5OHYzcmtKbzZxRzlnaVRXZlUyUDhYbC9meW12YUIyOFVteFk4Zyt4L1JERmExZEFXbVVQdXAzVWtmVDM0L1JzRW42N1M1azlEdFFzU0NRVE03UEZsdVBINEJJU1RDVHZpZVN2bkJnelBScngybFd1bWd5Q1FOanlGL2R2bmV6MmFQeHJqS2IyaHowbHFTZHp5dk9rcWYiLCJtYWMiOiIzMjM2ZGE2YjFmOWNmOTk1ODQxMWQzNWQxYjAzZjdiNTE1MGExMmJiM2NhMDZjYTNiMGQzZTA3MzM4MDVkYzE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1619590891\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-279047502 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-279047502\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-728291787 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:35:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpGQlBhcjU1dHRzdkppUGYxcVMzRHc9PSIsInZhbHVlIjoiOUJ4Z0pUS1dYM2d5M3dBSjBGN3FOQ1doRy9BT1E0bk9CNEFSbzdGTFB0Y2VLNjdzWjUrSzY1L2FUVG1hNWljaUZXTlpOLy9BRFh2c3U1TlU2Z2d2N3d1UGx5NU05a25ScWhabGR1amRKbUMwRnJ5bEdUa1pVTGJmS3hPd0hZK3hWK2Y5NmV1YzVQTlZvbWt6NU83TTY0SXdZMFRmVGQ4eTIzTlZWVmluYXZ1My9scDJHcVRsN3lKeDhOeWtZdEZQeTUra3BDNW1EK0tlQkd6S091TW1URmV1VnE4SFVCWEhnWkpGOWFSNWZxa0VybmkvQldFd0ZFMXhqSWhxdUdrWks2Z1RXOTNpNUFwSG9OcTN0Y1VCcTJmamxNc2NBR1BFMTlsOHExK3hsRFlPTnZxRyt1bS9nSXRTNWYvN1M4czJYVE51VEZ3amtLeTkxenJ3NGJTNzhhVERZWWw5YXozNmxIbTZRZ0lXZFBDdGZVQXA2WlNLU0VzV1YyajVmS1kvWkw1dnVVSmdxaWJtU3VMWFpYVzVUOS9lNDcxeVlHdW1EcTJNRHBaQkxrcHc2d1dES1cxODl3cjIveHRnNjEwQVg0WGdudndxS2hqeCs0R3RwV0EwdWJSS0I1RXBFeHBjRkZoZXNobDJqVk12UVJyQ3pkekhFN2ZjYzFaWjJHamgiLCJtYWMiOiJhMjUwMjFjOTEzMzYzMTM3OGQ0Y2M5ZGQ2ZTI5MWNmYjgxYmIwYjNjZTdiMDY4OGI1MzBjNWJlZThiODJhOGRlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:35:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik8vdTN0YkRMUkRIeGxWbzZlWDE0bUE9PSIsInZhbHVlIjoiOW9rMVUzOE04ZUU0L3dQeTlIWjQxYVdhaUlSaDJSMVdLb0l0ZjRobW1qZmZIaVZjRE9mdno2bXFtdXdRbGRXMjBqcHU0RUtjWDlFY2U4OEhnaHNuNEZROWh3NVFQRHN5MEpZTVpsR3hKSzM1YjZrUzZleFdSZ1VxNHM5c0k3UUV6ZkhNYythc3E3SHlURkEzajY3VUJ5eVFvQ1psbmFRaXNvY2g3TnlVeFpsVGhXQVZncWo2NzhGZzlsUWdMa2QxZk54eUt3aXJMakUzZTBDM3J0QXVSaWdtVmJ3QVlzTGpycTNWdVZ1aERPWEh5MnlDbmZoWnU1czFXVzdlck5YT0VuYnZHVDllSncxYVZFNzVJbXp4ejVkWkNYYmZOSVA4RVZrZkc5Q3ovV2FhcHpwMXVnNTBWcXZzZjZQMFlmQllBZmpGRld6S25Rcmg4RjV3ZHlVRVZBZUszdzVPU0VmUkVGa0k1Z3B5K1RyWVBZc2lqOEhnWStiU2V4di9sV1gwR3pIdHVqVmRWMkxEUzZEd2paVGU2SGxrVU14Y1BkanpKWGd1bWJtNzVqYmpjbnNhWS90U3NXdml1aDJRYlQ0WnpSakZaVDdQaFZiTXdUVjlSWVhEMmJHSUhiTmFPblVXWEE0WU13YVJ6SE1IUS9maTA3V0Y3NFVRK1EzeWdtNTQiLCJtYWMiOiIxMDAwYzZlZWRhZDA4NjIyOGRmNDcyYTg4NDk4ZGE2NDg1MmQ4YzZkNjM5NzMzZTMwMDVlMzc0MzRjNjE5N2MwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:35:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpGQlBhcjU1dHRzdkppUGYxcVMzRHc9PSIsInZhbHVlIjoiOUJ4Z0pUS1dYM2d5M3dBSjBGN3FOQ1doRy9BT1E0bk9CNEFSbzdGTFB0Y2VLNjdzWjUrSzY1L2FUVG1hNWljaUZXTlpOLy9BRFh2c3U1TlU2Z2d2N3d1UGx5NU05a25ScWhabGR1amRKbUMwRnJ5bEdUa1pVTGJmS3hPd0hZK3hWK2Y5NmV1YzVQTlZvbWt6NU83TTY0SXdZMFRmVGQ4eTIzTlZWVmluYXZ1My9scDJHcVRsN3lKeDhOeWtZdEZQeTUra3BDNW1EK0tlQkd6S091TW1URmV1VnE4SFVCWEhnWkpGOWFSNWZxa0VybmkvQldFd0ZFMXhqSWhxdUdrWks2Z1RXOTNpNUFwSG9OcTN0Y1VCcTJmamxNc2NBR1BFMTlsOHExK3hsRFlPTnZxRyt1bS9nSXRTNWYvN1M4czJYVE51VEZ3amtLeTkxenJ3NGJTNzhhVERZWWw5YXozNmxIbTZRZ0lXZFBDdGZVQXA2WlNLU0VzV1YyajVmS1kvWkw1dnVVSmdxaWJtU3VMWFpYVzVUOS9lNDcxeVlHdW1EcTJNRHBaQkxrcHc2d1dES1cxODl3cjIveHRnNjEwQVg0WGdudndxS2hqeCs0R3RwV0EwdWJSS0I1RXBFeHBjRkZoZXNobDJqVk12UVJyQ3pkekhFN2ZjYzFaWjJHamgiLCJtYWMiOiJhMjUwMjFjOTEzMzYzMTM3OGQ0Y2M5ZGQ2ZTI5MWNmYjgxYmIwYjNjZTdiMDY4OGI1MzBjNWJlZThiODJhOGRlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:35:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik8vdTN0YkRMUkRIeGxWbzZlWDE0bUE9PSIsInZhbHVlIjoiOW9rMVUzOE04ZUU0L3dQeTlIWjQxYVdhaUlSaDJSMVdLb0l0ZjRobW1qZmZIaVZjRE9mdno2bXFtdXdRbGRXMjBqcHU0RUtjWDlFY2U4OEhnaHNuNEZROWh3NVFQRHN5MEpZTVpsR3hKSzM1YjZrUzZleFdSZ1VxNHM5c0k3UUV6ZkhNYythc3E3SHlURkEzajY3VUJ5eVFvQ1psbmFRaXNvY2g3TnlVeFpsVGhXQVZncWo2NzhGZzlsUWdMa2QxZk54eUt3aXJMakUzZTBDM3J0QXVSaWdtVmJ3QVlzTGpycTNWdVZ1aERPWEh5MnlDbmZoWnU1czFXVzdlck5YT0VuYnZHVDllSncxYVZFNzVJbXp4ejVkWkNYYmZOSVA4RVZrZkc5Q3ovV2FhcHpwMXVnNTBWcXZzZjZQMFlmQllBZmpGRld6S25Rcmg4RjV3ZHlVRVZBZUszdzVPU0VmUkVGa0k1Z3B5K1RyWVBZc2lqOEhnWStiU2V4di9sV1gwR3pIdHVqVmRWMkxEUzZEd2paVGU2SGxrVU14Y1BkanpKWGd1bWJtNzVqYmpjbnNhWS90U3NXdml1aDJRYlQ0WnpSakZaVDdQaFZiTXdUVjlSWVhEMmJHSUhiTmFPblVXWEE0WU13YVJ6SE1IUS9maTA3V0Y3NFVRK1EzeWdtNTQiLCJtYWMiOiIxMDAwYzZlZWRhZDA4NjIyOGRmNDcyYTg4NDk4ZGE2NDg1MmQ4YzZkNjM5NzMzZTMwMDVlMzc0MzRjNjE5N2MwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:35:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-728291787\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1379039309 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1839</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1605;&#1610;&#1583;&#1575;&#1604;&#1610;&#1575;&#1578; &#1588;&#1603;&#1604; &#1605;&#1581;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1839</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>1877</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">cleaner Mop</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>15.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1877</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1379039309\", {\"maxDepth\":0})</script>\n"}}