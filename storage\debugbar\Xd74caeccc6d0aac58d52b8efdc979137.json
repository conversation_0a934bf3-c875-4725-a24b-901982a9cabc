{"__meta": {"id": "Xd74caeccc6d0aac58d52b8efdc979137", "datetime": "2025-06-30 14:56:49", "utime": **********.510888, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.044285, "end": **********.510908, "duration": 0.*****************, "duration_str": "467ms", "measures": [{"label": "Booting", "start": **********.044285, "relative_start": 0, "end": **********.451564, "relative_end": **********.451564, "duration": 0.*****************, "duration_str": "407ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.451576, "relative_start": 0.****************, "end": **********.510912, "relative_end": 4.0531158447265625e-06, "duration": 0.059335947036743164, "duration_str": "59.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00327, "accumulated_duration_str": "3.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.484886, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 58.716}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.495855, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 58.716, "width_percent": 20.795}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.50354, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 79.511, "width_percent": 20.489}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=2uy8by%7C1751295408578%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im1TZml2cGNWZERIUEZYOUtVTXVPSEE9PSIsInZhbHVlIjoiaFlpUjFQd0J0MmU2aE40YXVwYjJuVk4vR2ttM0pSWlNiZmtvbFV1TDE5dU9DOWt1YTNVOVpzbmxCZ1pDSXhPQ1JvbWR5Tm9wYmpScE1SZHZKZGF3V21lTXNEMWhZMGtZVWxTRXNtcmUwZWpURXVMeGNtd25IY3lOSnhHTkE3czd2eXpCN0pua3ZBNFFEMXZpQ01ETnZDMTRoN3JOblZWTHVPbldJQ3Azd2s2Vk83cUpyTmdoakk1UzBFS2xRR1o1MFdST1FhTHZoNE9MNW9udk9VOXdOcWlsS1hWODZuM0NuNWZDa3Y3MEZvK2UxYmViV1haYXR6clQ0d0FhS2cvVzFmWTV2WGF0SXJxQktnZlpkWDhmOS9yVk1GelplQ20vdUJGV2dkSXMvNXhINGFFenVsZUlBb0ZwNzAxUlpJRUpGQlUzSllub09EMVBCSk9HcVZjWURPTWVsK2hhUVkwK1QvcDA0Q1huTEp4UEJkazBpVFdETTdLdWxUNFVyTGtOb1RmNWNZRkJuMVlyVHlJczU0NDhVa2Zsck9sdUtCSDNKSkNIdnhUQmM2dnBmQ0l0Q0Y3V3VCT0dvdGZnM2RmTG9KbEEweWlpZ3hEbHZ0VjNJeUVsSzEranE3Myt0ZEtZNUN5VnFORDA3QXN6L0E4NnJxUnQ2ZlBxMEVUTTRORzIiLCJtYWMiOiJiMjAxOTYxOWNmODI5NDAyMjUwYjcxNWI4NjIwMWY2NzFiYThhZWNkNDA0ZGYwNTNkZjZhMzBmMDUxODYyNzA4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjR3WStWeU1VNXY3eFZySVdVL1A1UlE9PSIsInZhbHVlIjoiaFhxY3ZRS092NGJzMHVBaE55akwxSFRqWHU1cG1hSnRBajAyenhYeXkzbnhVeWtMcXh2WFhUWWxXMnE0YmxncXp6bEovWjdYbGU4WkYyNEdocDB1aTAzUEd5aHJpMCtuNkUxTksydUxwUTZpRk1YZ1BvM0dQUGJaQlRlTTM1VDA3N29TNDBxejdQUGRQTlJ3WGxlZEtoeDZhKzZrMjlFUStYU3UwMDFDTExrYVZzUTBiaWJzM0diMTBqMXJCT1dCNFd5aGFBRzdGc2dMdUlpNUJ3QmR2ZFdCM3gwNWhEakdweUgwMUNhL2JGNFZ0Wk5ta09sd05zdVEyZFQ1dVFIYVBFbktvTis1cGd1VE44U25pRXRibDNaQVNNVnpOdFNjUWRWL1J6a2c1WUt5L1greWJwUFpyNzdVMnlWZzFBM3ZMRWZiSmpWN1A4VkJMcHNiNU1rOWZncTl5L2NkdWpRMXhnTk5ENWprSHgyZDJyWVlCRlpGZmNaKy9FTVo3NUVMKzJ6Ym1ZeGpkRUoxYTc4ZFNnbnpmMktETWVZWDgraHlQM3BXOUhCdVNPM2RYU3RTVU85bWxUa0NJQlUyMlE2Qk1JblNMRWtOK2FlMXQrR0FaUGszNlFYQ09wd2JYdXp4QzBhdTY5ck9ualVrMXJ1Tm1IUXQrOVRMYmxjM1V3NTEiLCJtYWMiOiIxYzExODJkNTcwNWFiNWM0MTBmNTJiNmRlZTAwNjg1NzA0Nzg3NWVkNDNmMjNlZTY2YzAxMTk1NDRmYTI3YWExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1884152651 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rf3imG1lPKr2xfPRA9rNVzJMke6dPWK1Wb6HvZ8N</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1884152651\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1632168136 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:56:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpwdFhiZUlma3g4ZXNGd09SVGpiRkE9PSIsInZhbHVlIjoiSnNXNVVnTlI1ZW5TVjlTV0dNOFNkeEpJbDJjL1hTa0VXREN2ZWJEbXYyQnFEeExXV1BQVFVJU1BXMUJGRmVmdjViU0t2N0kxRUlwSGJ0M3JDZmkzVFpXaG1zK0J2Z3I5QUNseVRHSWJhNkpCQ0IrRzhjUnVPbzdLRFZIeDE5UG1SYmRGOFJOTmJpYzNINUlPOU5BZDRIZnQrQ2FkaGJVVFJ4eWs5RHJGZldvc2FDTVJkZmtsYW15K1U5WTkxd0lPSm9lQkwwOVZDTXE2bHl4SDBRdlljTkE4aFNkZURkcGdIR2NyVG9ZOWpQV1RwdGNBYkJ3R28wNXVFdDlIdkdwSWdORVcvcnBEM3VNOHhOK3VibDZIWU9leEZHNlNZWUM0QkQ0ODFFRGZ5eEZVblp1bExxMllFT3VKQ2dDVkk4K0Y1MHhQQ04vRllKNHFIaHJWZUN6QXlvcmRvdnhCVHVacGVZQXRaSGlicTB4SHpmb24zM1lraW9XN3BsL2JNS1FJbWNFbitRKzEwM01hZWIwZExPdFNVZnI0b3RUYStGeEtmVDN6ZHYySW8wdGFGVE5qRVZqUjZNeVREMENiOThVWFhyam5oV21rakpPMm9XSlhvcmlXZlF4ZUEweDJhL01iOWRQYnR4TjNmS3doaHJ1cnlYU2h6MEVDbHhwdXV4Ti8iLCJtYWMiOiJjMzZkNjI2NzA5NDc5Mjg1NThlMTQyN2EwZGY1ZDUxOGQ2MjBhNzQ4Mjc1M2U0NGEzZWNlNTM4MGYzMWM2MGU4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:56:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImM3aVBONkw0ZU1Iam1SSlliVVlNNEE9PSIsInZhbHVlIjoiNkdIOEhJdUwzVWpWeDFKYThPd2RCVzR1U2VnL2NoRk1Ib1NZZ3h1d0g2Z0ZiU1c2My9DZWRIU011V3A4WXdzeUZYMlBXaXFaTGlsZm12MHVHeUhsT0xzODFnZ1BZSUtKVHNhVENmRGtXMmJKdkxqa0ZtRERCa1NZU1duZXQ3a0NZYXlCOGZQYnJYMGdrQ09RdDBNVzRZejF0TUQ4VUN4SXRYTVY3aTRzeDFwZ2RpbjhEVVdFOWtKbURnbW5CZW1UTVN2UTJZQTd4MVlRbXM4ZThxbFFXb21rNldzbUNhMlZ3NkxTN3R4OE9aWHdMZHhpY25vcmpudzBtNytiSEJQVWZJUkQ0eHgzYUJWbWR5M2NzdTRIZ3JLd2VFUDM3NGUvcVV3WW51M0w2SFNlQVJHNEh5TjFaTzV6eWUxRjBnVThVWHZVa3JBZWhBTHl3UTM4WHhMV1JsNkRzT0w0cXZwOTN5Vy8vVk5ObktSOXlWSDVDU1I0Q3d3bTY2Vk5FNVZmTjJRSFkvdk1VRUdQQlpZYmtmMEpoOGdyQUtkRVgwYWxOVnV4VnZmaEdERStsSzdIUXBhSFQ2N1IxQUxvTVZQVytBSkNzaVY3bzlhdFEvMytDa2NSUFkrSllJTWVnRVZtdnlCTmo1SzRaZGw2ckRIdVVjVkIzcjVRVmtWeVZGbWwiLCJtYWMiOiI5YTFlY2NjNWYyMTFlNDBlNmNhODg5OWFkYjQyZTRlMzhlODRjY2Y4YTZlMjJiODQ2NzA5ZDRmNWU0MDI5Y2FkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:56:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpwdFhiZUlma3g4ZXNGd09SVGpiRkE9PSIsInZhbHVlIjoiSnNXNVVnTlI1ZW5TVjlTV0dNOFNkeEpJbDJjL1hTa0VXREN2ZWJEbXYyQnFEeExXV1BQVFVJU1BXMUJGRmVmdjViU0t2N0kxRUlwSGJ0M3JDZmkzVFpXaG1zK0J2Z3I5QUNseVRHSWJhNkpCQ0IrRzhjUnVPbzdLRFZIeDE5UG1SYmRGOFJOTmJpYzNINUlPOU5BZDRIZnQrQ2FkaGJVVFJ4eWs5RHJGZldvc2FDTVJkZmtsYW15K1U5WTkxd0lPSm9lQkwwOVZDTXE2bHl4SDBRdlljTkE4aFNkZURkcGdIR2NyVG9ZOWpQV1RwdGNBYkJ3R28wNXVFdDlIdkdwSWdORVcvcnBEM3VNOHhOK3VibDZIWU9leEZHNlNZWUM0QkQ0ODFFRGZ5eEZVblp1bExxMllFT3VKQ2dDVkk4K0Y1MHhQQ04vRllKNHFIaHJWZUN6QXlvcmRvdnhCVHVacGVZQXRaSGlicTB4SHpmb24zM1lraW9XN3BsL2JNS1FJbWNFbitRKzEwM01hZWIwZExPdFNVZnI0b3RUYStGeEtmVDN6ZHYySW8wdGFGVE5qRVZqUjZNeVREMENiOThVWFhyam5oV21rakpPMm9XSlhvcmlXZlF4ZUEweDJhL01iOWRQYnR4TjNmS3doaHJ1cnlYU2h6MEVDbHhwdXV4Ti8iLCJtYWMiOiJjMzZkNjI2NzA5NDc5Mjg1NThlMTQyN2EwZGY1ZDUxOGQ2MjBhNzQ4Mjc1M2U0NGEzZWNlNTM4MGYzMWM2MGU4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:56:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImM3aVBONkw0ZU1Iam1SSlliVVlNNEE9PSIsInZhbHVlIjoiNkdIOEhJdUwzVWpWeDFKYThPd2RCVzR1U2VnL2NoRk1Ib1NZZ3h1d0g2Z0ZiU1c2My9DZWRIU011V3A4WXdzeUZYMlBXaXFaTGlsZm12MHVHeUhsT0xzODFnZ1BZSUtKVHNhVENmRGtXMmJKdkxqa0ZtRERCa1NZU1duZXQ3a0NZYXlCOGZQYnJYMGdrQ09RdDBNVzRZejF0TUQ4VUN4SXRYTVY3aTRzeDFwZ2RpbjhEVVdFOWtKbURnbW5CZW1UTVN2UTJZQTd4MVlRbXM4ZThxbFFXb21rNldzbUNhMlZ3NkxTN3R4OE9aWHdMZHhpY25vcmpudzBtNytiSEJQVWZJUkQ0eHgzYUJWbWR5M2NzdTRIZ3JLd2VFUDM3NGUvcVV3WW51M0w2SFNlQVJHNEh5TjFaTzV6eWUxRjBnVThVWHZVa3JBZWhBTHl3UTM4WHhMV1JsNkRzT0w0cXZwOTN5Vy8vVk5ObktSOXlWSDVDU1I0Q3d3bTY2Vk5FNVZmTjJRSFkvdk1VRUdQQlpZYmtmMEpoOGdyQUtkRVgwYWxOVnV4VnZmaEdERStsSzdIUXBhSFQ2N1IxQUxvTVZQVytBSkNzaVY3bzlhdFEvMytDa2NSUFkrSllJTWVnRVZtdnlCTmo1SzRaZGw2ckRIdVVjVkIzcjVRVmtWeVZGbWwiLCJtYWMiOiI5YTFlY2NjNWYyMTFlNDBlNmNhODg5OWFkYjQyZTRlMzhlODRjY2Y4YTZlMjJiODQ2NzA5ZDRmNWU0MDI5Y2FkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:56:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1632168136\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1532455702 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532455702\", {\"maxDepth\":0})</script>\n"}}