{"__meta": {"id": "Xecfe68a5ae7612ccf498da8dd8d06b34", "datetime": "2025-06-30 15:34:30", "utime": **********.782304, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.322338, "end": **********.78232, "duration": 0.45998191833496094, "duration_str": "460ms", "measures": [{"label": "Booting", "start": **********.322338, "relative_start": 0, "end": **********.714025, "relative_end": **********.714025, "duration": 0.39168691635131836, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.714034, "relative_start": 0.3916959762573242, "end": **********.782322, "relative_end": 1.9073486328125e-06, "duration": 0.06828784942626953, "duration_str": "68.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45363416, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01596, "accumulated_duration_str": "15.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.746976, "duration": 0.015099999999999999, "duration_str": "15.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.612}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.772305, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.612, "width_percent": 2.569}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7749882, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.18, "width_percent": 2.82}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-917873133 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im5XaVFGTkZqRU5yM0RsclFrY2xjaWc9PSIsInZhbHVlIjoiS3Yyc3MwZWlqdGZxL2puMlIyaWtucHA1dVF4UEFuMmFzRHB2eGVFZUVINjBkUnlCeXJxZE1lcmNGUk4zUGlrVDRXRHlqWTFPZzdrN1NxNXNXUWFBbHp5OEhXNzlYWU9CUjZ2M3lVV29jSTU5MVlOakRVNEl4REVvZWFqZXd4ZXI2TlI1UnZEWm1VbVNMRFZON0RIc1hrWDZIUE1HUnBuVFZkN20vZGlQK3hrYUdUVTVTUDVMYmJNeG9rZG9iWGEwWk1aZzMra2lVWnJSbWRtMzUzWXFrR25LRlhodGFuZmRZNlFNa1lmSlRPTk1Bd2FYR2NQMHRGQXQ1MSs3c3pPUjhBRUtxa1N6ZXVkS0kwOXVRUTdUcEhTb2FWWUNZaTE1ZXI3YmRrcG5wM1Q2VS9ySWxjbEM4ZUZITGQ2N0IyV0RYbjdnbHc5b2haVWlXR0EzeVk0bGdpTmZQcURGZTVPd1QrZEZ5TWJKRW9USCtkYkFYUFhOeEVPTi9IVzMwT3pkWXVWYUZLdXhSOThwcVZMQVZrRFJFWXZYWDhnb0E1ZEpTbDdYQTFjdHVYVmxmclg3VFZML2pmSVo0QVZZbEg2bldqejFTMFZIVEkyNU1PUGV3K0NwV0xabDFaTW5wV3BwYmNxcVZYUkRsdUxDL05zcGZCSGhKZFBzR3Y3VSt5eGMiLCJtYWMiOiJjNDExY2RhNzg0NThkZGJiNjBiYmZmZmUzMTI5ZmNhZGJjOGRlNDBhMzBmYmIwNzNlMGJhYzc2ZWZhYTlmNzYzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjY0MWZrMWdySVBlTUtyc3lsUllrZUE9PSIsInZhbHVlIjoiQ05oV1BpWDlHWGxXSmQzdHNQQTJ5dkxUNTBDVEpyQ2crT2ZyemdIT0RVbWFmM21aVW5zTVdoK0RSM0RKaUVUZE96NXpGVU5mZEpvb2pGcXNEOVY1aXltL0Yzbzc4RDRzeW5XQXZ3N2QvSndrcUN0Z3BMamJGc3ZqUVFmeWV2OHZwbGkxWEJtWUJpR0VJb1N5a1dIdHU1eW9aK1AwSVF5QUJscUI0N05SejlQdE4vTjlPNWpKWW95OGtsTjAzbFdndytCblBNd3U0Wi9KbXI5eEJCT3U5YStOM25iaWNUUCtQUUVYMUVmekg3MHZPc1lFNEFRN21DWXR5SXBnZVBUUTJSL0l4aUx1M0ViTXNnMXVwajBIZWp5RWFBNlROdW5rUjVwUDNRMHd5aFdmRGdUak9OZkNrNnNmaUpYVFlCeTZjcGRzeVNTYnpZZS9OajRIcnhCSk1ta1FKcXRaNHA5T3J6UlZncTNDVUZBTWFtM0c2Y0xrbFZtVTROcktIVVN5ZEZYa0lzemhvQXdUYWNrdVgyckxMaEhYVHdpeW1OOGJsS1RYQmxPeGZIQUtVR1VobGVnZDhGczV6U0VMcUE2ajk3aVBYTDVqWU8wZDhOWlVBVGF1VnV0dVVudi9lY3pBYjJZcUprVVZMcWJlc3RyN0Nvc2llM0VRbTRSSjR1eHgiLCJtYWMiOiIxYjBkMzVlOTViN2UzMzE0NjM4MGFmNWJlMzNmM2JhYzczM2M2MDVhODQ2MWI0NjU3NjRiZmNmODIyNTczNjk0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-917873133\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-99696584 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-99696584\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2142982856 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZoNFMzeTZVdnRRNFI2dzJuNGFRRlE9PSIsInZhbHVlIjoiYnFHNFZIWTA5MEI4aml1d0pCQnk3SnBlSzIzSlFKS2h3UXpjK2ZYbk43OE1kaUwrNG1CY2dPNG5rZyt2VXVEMkozekMvQzlRSFk1TjVwVkdUNkJITnVQYVhPTUZMTXhQeDFqNTAxcWJRKzRkZGxFdXhUT2VKTzhkTy9tZFMzdndlT1ZNcGtUVU5OWklmZWlVaHQ4d3VtdlZPbkRlODFwY2JNdGpKUDZ2Z05NV0xpNXBTa3ZwVUt6aENEN0dMdlFsZC9BZVZHU3pWWm9zUUNXdC90cnV2SGdXV0xCK1VJUldMT2p5eUI4K3ZxL2ZYM1RQR2ZJUkVGM1c5aC8wQzJkWUJsdnFPVVVvcW82NENUQXhIUElmSHZBU2xPNTUvVW9wM1RlNlBjRU02OVpwZXByVC90V1d0YStvMklaNVlmUEZTQjZHVndFRWNoZXRremNPV0d4L01OZy8xTWdJa3Q2Z01MNFV0cnYvVGgzanFWbzFRYWdXRW1mRzdvbzRPeTgzYUlIL1k0RXpzWUxKSDJSNG05bXdvck9abHlDWjlLVkFRRmNiNW5YQzlRTm9HTk5rSWZrN0NoMThnYmdZZzN2N0tGdk5jdkJTdVMya21sOUg1SnFuZWxwdW5wUzJCN0w1U3FnZVJBYmRMU3JGbG9UM3VUNjAwYkdnM1I1cTg0STQiLCJtYWMiOiI0ZmQ1ZWQ4NTQwNzgyNTAwOTIyNDFiY2JjMzljZThmZTExM2M0NzZlMzFlMjcyZjU0OWY5ODBkNTUxODEyNGVmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjM1dndocHJlMXNId0hkRURMaWpONHc9PSIsInZhbHVlIjoiSWI1Ujg1NWNXdWdOejRQTkh3WmRhVEZGSWlPeGlHVHU0NEZEMlBzNCtkOFB4S050dDRUZG1EUFo0TUQxdmVsdHJ6RytRVDNGak5uSFZxbm94c2FaYlJHSHMwUmx4dnpOZEtOUkxxZkp4V0F1UzRmZGVGN0lLd1BzZ0hVUUl0K21hVk1IYWRrZ1dsWjhHblkxTXVVeThQWkxCajM1a0RZVmR0THNwU09ncVhOeVF4T0sxSHp2SzlrdW9LQUNvNVBCdEFNTkNULzRJVXI2RVh4WlJuR3B0UzEzZlVBUGtIR3l4KzkxaUpnSk1iZ0lZc3NFNTFmN0lncC91RkR0eDNXMDdpaDVZL2ZCNER4Z0ZBUmpyRC9Oa0RhN2VHNHA3K0NMdmdQcjJSUSsyb3YyNmlWMUxhSXArcVZ5OWYrcGhsVzBFbWltYzhUTDVnTXAxUHFJVHA0bnFwRjhubmp1UWVzMzlZTEVCbXhURWZKNWJkQ3J3dFk0VFN2T1Y2TjhoN2R1VlRUa2loblRVcWUyb2J1V1VoU3RhK1pTV0ZHbG55ZnF1SFloaWZwd3ZpQThRNTRLbHduUGJTTDlPbmgvS3FiYXh0bGorRG9xNXRMbnBnRnEyRWw0UXFjZzFjRVlRVUpqNmV3czZKSDNBQlhGdmVIVlc4WGN6OURKb0VUZ0o3cWwiLCJtYWMiOiJmYmFmN2E0NTQyNTdkNTdlNDA4OTJmYmZhNDZkZDI5YmY3YzhjY2ViNzlhYzZlNTBmZWZmOTBlOWE0NjNjY2FmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZoNFMzeTZVdnRRNFI2dzJuNGFRRlE9PSIsInZhbHVlIjoiYnFHNFZIWTA5MEI4aml1d0pCQnk3SnBlSzIzSlFKS2h3UXpjK2ZYbk43OE1kaUwrNG1CY2dPNG5rZyt2VXVEMkozekMvQzlRSFk1TjVwVkdUNkJITnVQYVhPTUZMTXhQeDFqNTAxcWJRKzRkZGxFdXhUT2VKTzhkTy9tZFMzdndlT1ZNcGtUVU5OWklmZWlVaHQ4d3VtdlZPbkRlODFwY2JNdGpKUDZ2Z05NV0xpNXBTa3ZwVUt6aENEN0dMdlFsZC9BZVZHU3pWWm9zUUNXdC90cnV2SGdXV0xCK1VJUldMT2p5eUI4K3ZxL2ZYM1RQR2ZJUkVGM1c5aC8wQzJkWUJsdnFPVVVvcW82NENUQXhIUElmSHZBU2xPNTUvVW9wM1RlNlBjRU02OVpwZXByVC90V1d0YStvMklaNVlmUEZTQjZHVndFRWNoZXRremNPV0d4L01OZy8xTWdJa3Q2Z01MNFV0cnYvVGgzanFWbzFRYWdXRW1mRzdvbzRPeTgzYUlIL1k0RXpzWUxKSDJSNG05bXdvck9abHlDWjlLVkFRRmNiNW5YQzlRTm9HTk5rSWZrN0NoMThnYmdZZzN2N0tGdk5jdkJTdVMya21sOUg1SnFuZWxwdW5wUzJCN0w1U3FnZVJBYmRMU3JGbG9UM3VUNjAwYkdnM1I1cTg0STQiLCJtYWMiOiI0ZmQ1ZWQ4NTQwNzgyNTAwOTIyNDFiY2JjMzljZThmZTExM2M0NzZlMzFlMjcyZjU0OWY5ODBkNTUxODEyNGVmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjM1dndocHJlMXNId0hkRURMaWpONHc9PSIsInZhbHVlIjoiSWI1Ujg1NWNXdWdOejRQTkh3WmRhVEZGSWlPeGlHVHU0NEZEMlBzNCtkOFB4S050dDRUZG1EUFo0TUQxdmVsdHJ6RytRVDNGak5uSFZxbm94c2FaYlJHSHMwUmx4dnpOZEtOUkxxZkp4V0F1UzRmZGVGN0lLd1BzZ0hVUUl0K21hVk1IYWRrZ1dsWjhHblkxTXVVeThQWkxCajM1a0RZVmR0THNwU09ncVhOeVF4T0sxSHp2SzlrdW9LQUNvNVBCdEFNTkNULzRJVXI2RVh4WlJuR3B0UzEzZlVBUGtIR3l4KzkxaUpnSk1iZ0lZc3NFNTFmN0lncC91RkR0eDNXMDdpaDVZL2ZCNER4Z0ZBUmpyRC9Oa0RhN2VHNHA3K0NMdmdQcjJSUSsyb3YyNmlWMUxhSXArcVZ5OWYrcGhsVzBFbWltYzhUTDVnTXAxUHFJVHA0bnFwRjhubmp1UWVzMzlZTEVCbXhURWZKNWJkQ3J3dFk0VFN2T1Y2TjhoN2R1VlRUa2loblRVcWUyb2J1V1VoU3RhK1pTV0ZHbG55ZnF1SFloaWZwd3ZpQThRNTRLbHduUGJTTDlPbmgvS3FiYXh0bGorRG9xNXRMbnBnRnEyRWw0UXFjZzFjRVlRVUpqNmV3czZKSDNBQlhGdmVIVlc4WGN6OURKb0VUZ0o3cWwiLCJtYWMiOiJmYmFmN2E0NTQyNTdkNTdlNDA4OTJmYmZhNDZkZDI5YmY3YzhjY2ViNzlhYzZlNTBmZWZmOTBlOWE0NjNjY2FmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2142982856\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-4******** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-4********\", {\"maxDepth\":0})</script>\n"}}