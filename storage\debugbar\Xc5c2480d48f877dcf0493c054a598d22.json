{"__meta": {"id": "Xc5c2480d48f877dcf0493c054a598d22", "datetime": "2025-06-30 15:34:33", "utime": **********.676879, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.247065, "end": **********.676898, "duration": 0.42983293533325195, "duration_str": "430ms", "measures": [{"label": "Booting", "start": **********.247065, "relative_start": 0, "end": **********.597808, "relative_end": **********.597808, "duration": 0.35074281692504883, "duration_str": "351ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.597817, "relative_start": 0.3507518768310547, "end": **********.6769, "relative_end": 1.9073486328125e-06, "duration": 0.07908296585083008, "duration_str": "79.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45554328, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01785, "accumulated_duration_str": "17.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.62432, "duration": 0.01684, "duration_str": "16.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.342}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.6521099, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.342, "width_percent": 3.361}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.658212, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.703, "width_percent": 2.297}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-102184946 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-102184946\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-283653824 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-283653824\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-6567298 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6567298\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1016568711 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297671844%7C1%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZmK3FRRzJ2TkJrM3ZnQldrT2VweFE9PSIsInZhbHVlIjoia21uK2RJbU01UERGYzdnSDl2bUZlaU44cU5vTGl3MHdjS01nZXlEZGpzOVEvbGQ5MmhnOHpVazBVdW1leEEySld0dDlFUkR5UUlGNGlIaUFQL1pGMW9uMk9Wd1RLdngzdlNTT25JV1E1U0ZRdFNWZzRITExSeS9ZRUpKOWVMdFZDTlE4MTZyRk1SSFhUa3AyZ3VpZzJRUXdMblh1Y21HbytOWng1L0xGSkRDVHdXblhWQVl0V3RrUFVvOVRrMHhnWEVxbDZaMkhRZUhjcU93Mmt3UmR5NCsyU3puQkdPaUhrS2dGOFIvd2RLanNEMHZVdUQ2endsNnhUSDc2dGdGOXIwZnYzR3RKUDdZUVlIbDhrZW5NOWZzeWdGdnBqZEo1WFRhQzhhTUU4eStGMFd0YkNWL1cvTjk2bGpsc0FwZTJxSm9kQ2pCclJPOEJLTlEwRktreG45eWVOSnBsUkUwZVovV3JNVUtLUHJNSWJSVXVYZGVWWnlvK2R2d3UwWVRabktVck9oTGhXQmYvbCtRT2hPK1hvK3B4R1RFZkRQZlpla2FxR3M3SDA4YXFRZzM3V3d1cHZPbDJWMVNsaFNOTkFINUpqb3hmeHE3Ti9CdlBzOGFLMkM1N0hZSGFmTEVlUzRUa2RrdmhZcGg3TW13OTVoZGhPZVFFc3lhaVpsenkiLCJtYWMiOiIzNWFlZmFkYWRjYWZiNTAwMTQ2MTc4MmM0NDEwMmQ3YTgxZjI4YWY2MThlZjVkZmI3NDNjODdkYmU3ZjcxMTk0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZPRGFKL25RbTVhZDNaZTlQb3E0bXc9PSIsInZhbHVlIjoiQ3V6V05DZkdNbWIreTJpbzdOQzM3ZG5ONVJlTzBOSnBkZ3ZBc0hiQ0ZkR3BRVU9sQ2ZpV1JvMnozNmh0aEJvRmVyTW9lUC95Y3lIVjcwVXo1ZFNsUXlJSEVoMC9uUzBhMTBpZ0RnbmtUZjZPb1cvNHp1NjQyRzNBbFh4Qk1zOXpnTG5jSzhJTDVuQ3QybkxWQ1Nhd2JqRjFQTVp3TW94RkJUNThZNXRoMnUwZC9MVzdhRDdYbDBTbS9lZFB5N0Q1emdhdDFJaytaWVJqN2lJVUxGdktvV2pNTFE4SFExYjQxc1EyM2NnYUl0T0RXSnlTdHZyZ3RYa2pycGlKYUVvbVRPZStkbkhWcVh0VjBicmdPa3g3dklxUFlzRVRFaHA0RkFhVGlNR1MrWkpEWW43L2RpQWZ1WW96V0tvOGJZRUlabjhkckx0Mk1PWnVsUkVTcEZzc0xRaWIrR2R5dm0zVGllVkJ4di9PMVZFNkxiMEdqMXZqWWdoQWxmZ2pWQm1qZHB5cVIrQmI4UkwxQmpiUHZhTjNZMEhOT0lFVHdETmdGVzg3M1ljNkp3aEJkblNBYzVVcUlXNzNvbTVEWHZDTk1KZ1VvREx4SjJtY01JeldFZ005UnNwY2N2UFBYTjBHUDdWcGZqVGNYbENNZ3RYZEh5Rm1MUnhpbE1XT3BSL3AiLCJtYWMiOiJiOWNjMmVkNmFkMzY4ZDY2YTk3ZTI5ZGQzNDRlODYzYTYwYTFkNzBmZTYxYmU4MTRlOWU3YTc1ZmEzMzVjNzU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1016568711\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-793064478 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-793064478\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1992131600 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImgvQ09ORXFSMzF5SWVLTEJLQndRQWc9PSIsInZhbHVlIjoiL3dXNTkrTkNwbGVqb0xoclZCcEdKYmxmZytaZkdSVHZzTUk2YVQrQiszd2tWK0tlNUV0Wi9Cc21LeUdWY1FyQUgwU203b0VFWWVWV2Y4YXB2MER5QVZNNGxiTWppM3dqVTJSTEI5bWlBbkdibkZzS1pJVW1uTUpUbmpGQjM0djNoUFpnSC9SeUNyWEhYZi81V1M3Um11S1JIdkhnbU5iYlIzZWIzcHp4dEhDS0k0cGphN2ljazZmYmwvUjRVQ3F2cFNsUUVYNHk1MnFyVGtqdXkyVHp5dkxWYmRRRU90eDJYS1oyT1BSTENpekFFYTYreFhLNEY5bGR1YjNOeVNSUHg1V1p5bW1MekNmMkNhd0hhSHdpMFZ0cEJyNGZSVE4yQVg2UEZHVXEyWG5nTjA1YkFoSk92bG83Sjg3ZElISVJ4OTFZQkExTVg4QmV2M2R5aWdpOHYyYWhjcUdCSGRoeExrSkJNQ3ZiYmR5WTNRRnk2UkI5UjQ5bUxsV1loRHdNVzk5S04xYVA2dkFXWEwrWTgyYWd5Rkd6cVNMNkl5cmNGOWgrWm1pOXlTY3pkdlRPM3ZKWnVvTjh0K1N1R0hwZWhReVlPRnpyYnh5cDNtRWhyMThTblVEdXcyV0dTUHdwUENzbXk2ZDZ0SW9TS1R0QjRoS3hQN0VqQkxMQ0pISHkiLCJtYWMiOiI2N2M1ZjNjZGY0YWY4M2RkZjc3ODgyOWRhZThkMDc0YTUxMTlkZGYxOWIyZmVjN2E1N2NiMjU1MTI0MGRjMGZlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Inl3ejlwdjhVZWV5Z1BPbW8zb3d2ekE9PSIsInZhbHVlIjoiQ1pyZ1hhMkFBY1JVbEVxbitGeTQrTlFGVm16TzZocmpUS3Jab2tVQmJxMC9sNlZuVlR1VVVRQ1lqVzRidmE0L0ZhMGVSK2tsT3ZIak05YkdmakFSdEpQazBWbjZ6Mzd3NTJ0SGY5ZXBKUHpLQVpDT2pIaERsL2djenVlQStqM1dWbWx0a09zaGJYOUFibXVyM3VCdVBxalFIc2k4RkNYS3NyTjQxVjQvZ0tHVHZoekNUOXlmZlo0ZkhOWjZybmRtN1Vlcnk3aTFRUjVCc0ZCOU5RRTJDaEVsaDRzejZPSEhuZlVxRndBSVlJYVJmUUllRm10R05uZm9aci96cVl2bVdOajY3RnNCNlM3SFk3WVhvVDdOd09RanV6MndwMFBVcUtCeUx3cUNVZXBXeSsvUk5LY25OWWYzbGpoWlRjd3E5djJqS3FnWFM1WE1SdXk5TlFmU0ZIZUQwNlJBWDFib2F2bG1JK3VuaUYrcUd4OVcyVmlGdER4Tk15cjNWV3Z2M0hpTjh4ZXV6aEZjVXNMOEpCRERIZzhRbm9jZ0tNMGVESWJIcXFiT2g4eW5STjBWU2xRRnFoT0IweXcrM0FrSU9Td3kra1BrVWFUZGtpZ3krVmlHYStNcU13YXNwTSs5SFlPSER0UndLdnV5ZENEdStGUXJFZm1FYVZsYzN0T3EiLCJtYWMiOiIxNTAwZWVlNTlhY2VjOTUyZmFkZDlhYTVmMDc0N2JhYTEwY2FhYzI3NTkwNmNlYTc0N2UxNTMxMjY0NTdiOGJlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImgvQ09ORXFSMzF5SWVLTEJLQndRQWc9PSIsInZhbHVlIjoiL3dXNTkrTkNwbGVqb0xoclZCcEdKYmxmZytaZkdSVHZzTUk2YVQrQiszd2tWK0tlNUV0Wi9Cc21LeUdWY1FyQUgwU203b0VFWWVWV2Y4YXB2MER5QVZNNGxiTWppM3dqVTJSTEI5bWlBbkdibkZzS1pJVW1uTUpUbmpGQjM0djNoUFpnSC9SeUNyWEhYZi81V1M3Um11S1JIdkhnbU5iYlIzZWIzcHp4dEhDS0k0cGphN2ljazZmYmwvUjRVQ3F2cFNsUUVYNHk1MnFyVGtqdXkyVHp5dkxWYmRRRU90eDJYS1oyT1BSTENpekFFYTYreFhLNEY5bGR1YjNOeVNSUHg1V1p5bW1MekNmMkNhd0hhSHdpMFZ0cEJyNGZSVE4yQVg2UEZHVXEyWG5nTjA1YkFoSk92bG83Sjg3ZElISVJ4OTFZQkExTVg4QmV2M2R5aWdpOHYyYWhjcUdCSGRoeExrSkJNQ3ZiYmR5WTNRRnk2UkI5UjQ5bUxsV1loRHdNVzk5S04xYVA2dkFXWEwrWTgyYWd5Rkd6cVNMNkl5cmNGOWgrWm1pOXlTY3pkdlRPM3ZKWnVvTjh0K1N1R0hwZWhReVlPRnpyYnh5cDNtRWhyMThTblVEdXcyV0dTUHdwUENzbXk2ZDZ0SW9TS1R0QjRoS3hQN0VqQkxMQ0pISHkiLCJtYWMiOiI2N2M1ZjNjZGY0YWY4M2RkZjc3ODgyOWRhZThkMDc0YTUxMTlkZGYxOWIyZmVjN2E1N2NiMjU1MTI0MGRjMGZlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Inl3ejlwdjhVZWV5Z1BPbW8zb3d2ekE9PSIsInZhbHVlIjoiQ1pyZ1hhMkFBY1JVbEVxbitGeTQrTlFGVm16TzZocmpUS3Jab2tVQmJxMC9sNlZuVlR1VVVRQ1lqVzRidmE0L0ZhMGVSK2tsT3ZIak05YkdmakFSdEpQazBWbjZ6Mzd3NTJ0SGY5ZXBKUHpLQVpDT2pIaERsL2djenVlQStqM1dWbWx0a09zaGJYOUFibXVyM3VCdVBxalFIc2k4RkNYS3NyTjQxVjQvZ0tHVHZoekNUOXlmZlo0ZkhOWjZybmRtN1Vlcnk3aTFRUjVCc0ZCOU5RRTJDaEVsaDRzejZPSEhuZlVxRndBSVlJYVJmUUllRm10R05uZm9aci96cVl2bVdOajY3RnNCNlM3SFk3WVhvVDdOd09RanV6MndwMFBVcUtCeUx3cUNVZXBXeSsvUk5LY25OWWYzbGpoWlRjd3E5djJqS3FnWFM1WE1SdXk5TlFmU0ZIZUQwNlJBWDFib2F2bG1JK3VuaUYrcUd4OVcyVmlGdER4Tk15cjNWV3Z2M0hpTjh4ZXV6aEZjVXNMOEpCRERIZzhRbm9jZ0tNMGVESWJIcXFiT2g4eW5STjBWU2xRRnFoT0IweXcrM0FrSU9Td3kra1BrVWFUZGtpZ3krVmlHYStNcU13YXNwTSs5SFlPSER0UndLdnV5ZENEdStGUXJFZm1FYVZsYzN0T3EiLCJtYWMiOiIxNTAwZWVlNTlhY2VjOTUyZmFkZDlhYTVmMDc0N2JhYTEwY2FhYzI3NTkwNmNlYTc0N2UxNTMxMjY0NTdiOGJlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992131600\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1181993464 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1181993464\", {\"maxDepth\":0})</script>\n"}}