{"__meta": {"id": "Xb253080a0a75e8c05853634edf29502d", "datetime": "2025-06-08 00:08:49", "utime": **********.427241, "method": "GET", "uri": "/inventory-management/products/7?search=&status_filter=", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749341328.582698, "end": **********.427264, "duration": 0.8445658683776855, "duration_str": "845ms", "measures": [{"label": "Booting", "start": 1749341328.582698, "relative_start": 0, "end": **********.277526, "relative_end": **********.277526, "duration": 0.6948277950286865, "duration_str": "695ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.277545, "relative_start": 0.6948468685150146, "end": **********.427267, "relative_end": 3.0994415283203125e-06, "duration": 0.14972209930419922, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51447440, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x company_operations.inventory_management.products_table", "param_count": null, "params": [], "start": **********.405798, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/company_operations/inventory_management/products_table.blade.phpcompany_operations.inventory_management.products_table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcompany_operations%2Finventory_management%2Fproducts_table.blade.php&line=1", "ajax": false, "filename": "products_table.blade.php", "line": "?"}, "render_count": 1, "name_original": "company_operations.inventory_management.products_table"}]}, "route": {"uri": "GET inventory-management/products/{warehouseId}", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@getWarehouseProducts", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.management.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=39\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:39-106</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.024239999999999998, "accumulated_duration_str": "24.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.341113, "duration": 0.01559, "duration_str": "15.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.315}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.368033, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.315, "width_percent": 2.475}, {"sql": "select * from `warehouses` where `warehouses`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3728402, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:42", "source": "app/Http/Controllers/InventoryManagementController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=42", "ajax": false, "filename": "InventoryManagementController.php", "line": "42"}, "connection": "ty", "start_percent": 66.79, "width_percent": 3.383}, {"sql": "select `product_services`.*, `warehouse_products`.`quantity` as `warehouse_quantity`, `warehouse_products`.`id` as `warehouse_product_id`, `warehouse_product_limits`.`min_quantity` from `product_services` left join `warehouse_products` on `product_services`.`id` = `warehouse_products`.`product_id` and `warehouse_products`.`warehouse_id` = '7' left join `warehouse_product_limits` on `product_services`.`id` = `warehouse_product_limits`.`product_id` and `warehouse_product_limits`.`warehouse_id` = '7' where `product_services`.`created_by` = 15 and `product_services`.`type` = 'product'", "type": "query", "params": [], "bindings": ["7", "7", "15", "product"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.378428, "duration": 0.00564, "duration_str": "5.64ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:62", "source": "app/Http/Controllers/InventoryManagementController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=62", "ajax": false, "filename": "InventoryManagementController.php", "line": "62"}, "connection": "ty", "start_percent": 70.173, "width_percent": 23.267}, {"sql": "select * from `product_service_categories` where `product_service_categories`.`id` in (4)", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 62}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.391584, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:62", "source": "app/Http/Controllers/InventoryManagementController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=62", "ajax": false, "filename": "InventoryManagementController.php", "line": "62"}, "connection": "ty", "start_percent": 93.441, "width_percent": 3.589}, {"sql": "select * from `product_service_units` where `product_service_units`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 62}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.396174, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:62", "source": "app/Http/Controllers/InventoryManagementController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=62", "ajax": false, "filename": "InventoryManagementController.php", "line": "62"}, "connection": "ty", "start_percent": 97.03, "width_percent": 2.97}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}}, "count": 6, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/inventory-management/products/7", "status_code": "<pre class=sf-dump id=sf-dump-588526029 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-588526029\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1613112064 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>status_filter</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1613112064\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1390751410 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1390751410\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-671665306 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749341123215%7C28%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJVbnZGOTF2VFFna1lId2pEVnpBTmc9PSIsInZhbHVlIjoieGlYYkpJdTFCUEQ3OTNFMnpOU0FabmpnZlpLYkVBWkpLdEx0TG4vRGhqOVM4YldadkJ5WjBUa3N4aHZiU29QV1NDR3haUTZVbjNpMUNIR0k0TmdOUzJNTnE0V1c3R3RFTkdlSjNsUlhyV0F0UEMyTkpzVkxxenVrbGlOWGtMN3I5VDErOHZZQUh4UStqUWl1TmtnMnpqV3c0N1orZHhmb0xJNUZvTncxbXlaUDZENFdVTkZ5YjhvNWlGZ0FDa1p5WDI2cUNsY1pubUtaaGVuRTFMOEo2OTY4aFZnWDhDQjc3VzRNeGN4VDdLbEduY0hFcXlFQmhvVXVGU0lpMHk3SG14T2R4T3F6ajdvTTNCZDZDSWthOHNlYWs2bWJaaFhXa04zSFcrcnU0cVB4b2VGc3dUUFVST2RUaVcvSWpwTHpBVkdiYmhhN0pwMVV6b3BuQi9GU0VNQzIwbnphMEExRUpvdDJoOThkOE02ZnlOSGRmRGRDS0hHbjlWL0NUYlF3WEw4Tk5WeEozb0lmTXIrMUtpd3hvbFYzeitkb3V0NldWcWpBa3piUTlvNUxEMUJWeUZpRzhZcXpYV3lwL3hTNW1QMkN3MDgwdUdFL0xXOUlJZStuaVp3MXNoZERTUDNvNVhiNytIMWRQcUM1MStUUFdFMzFNSThLYm9VazFUNUUiLCJtYWMiOiIzMGYwODZkYWQ2ZGM2YjA0MzM3OWNlMjc3YmE4ZTZmNjVjNGFjNmU3MjhhZDQxNGM5ZjM4NWIwZTYxMDdmYzdmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im1ZYng2V1BQNjhKcUxwUkloNnZzSWc9PSIsInZhbHVlIjoidzNQek1CRUlaU0hiazRDcEgxeU9MV0JQeGh6RHZUUHFteXVvVFAyamE0N0F5U2o5WFdWY0NNc21XL2R3VHhDUU1hTEVPZG1yaUVLMVphSFZyemxDOGtCWUpwUThTd2N6cmxydlo5czh0dGFReWFobGxJaUhaWFJuNHVmR2oxVTlyb1pwbzBNNkozb09MdFJBWm1KWTZzUkJXMTJtOVBoL0h0dXI5b0JlN0FEMlpyZE5BOGtsU2p3WXplTVBzTmFJQ05LaHd2aG5yK1J1SXJsdXYveG04R294Q2tHR25aczBRR0dvYzNnYzh0aFdzeVFacE9QZHZNMkJpTDBla0ljOGJaVFhYaFlJTjhrUGFqM0dlNVBkLzc2Z0Y3eU5DSDdYYU1sbnM3elpTQlBYUXh6czdjK3JCbC9xMW5HRDFpclVWZWtUd1hhbVp0OEN3UFhiSlFSYWI4NzNGcUVJdWVGclE3QUcxbHdLZ0RZZWJrOTJueVBTU2xsR0I4ZUZIS2QweDVOTm5nSzVBQnlFdzhiczhZdTc3ZTJjeFdUNGpHc0pIZk5GM3dRTFR0cUdBT2JoNnUwN0hrUncrRWw1TjdHMTlDd3FscHE1MVBHVk9KdG8vcDdKNk1SUTlHaU1JZjRUM29wUFBLZkpZNWVta0x4VkxXdng3TW5WbC9KQUNvd1kiLCJtYWMiOiIxMjBjNTQyODFhMTBlNTYyNTI0MmMwNjdiYmFkMDUwMDQxODM3NmE4NDQzYTNmOGExMGIyYTVkYzNlZWUzNTNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-671665306\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1977371241 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1977371241\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1490080752 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:08:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iit6Y3dySG1ZRDZiMWhJcDRWK2JnckE9PSIsInZhbHVlIjoicnVKZkdXQklWZXZ4KzBPRnQwOEFVVGNWUE1yNmhXODB4K3BPeE5GaUtzbXdTa08va2o4UjdhKzZCTzR0SHVlaU1uOHhxaGdDL2xaNEx1bTM0cWRoRjJrbHhFOHFyeFUwZTd1eUZBcnBEMG1JdDhGM1pYNmtYUHdjMWpnUzlNRGxWNU5XTDdWdWl4MmtuTkhZMHV1RVhLY29aOGdrLzEzUzMyZHBNU2YyVURGaFYvaGttTE5UL09uQVp6UXhKOFBDWXFwNUpRRXJhblFseEdvVDNmVVhoNERRUlY1Qlc3dGJPczR5SDkrYjFMRXFhM0NxaEpBRlVhUWlJYzlsbzVER0I1MVFBdjNMbmEyMnpqbTBqSk9wazlBaDhMYjRPREMyS200Nm1Uc1ViY01IbTdHYzAwSE5PYVA0QUV0S002V3p1VDd0SlhBZ3V3RVZxUmVweWRxN0pjQkMvNS9WcGhCeEtXTTFPeVhSWXpvV0tET21xc29pZm1UWUxHNUZUcXNoMVdiNWhTa2RNYlYxQlY5eTF1MXJoUWV1NWdkWmRtOXR1S2ZJK2djUnlxU1hCUlEzbzgrc3RmT2dmNCtLL0pEOWNZT0dlaGVtNThRbDROZU5SLzF1YnhNa3NHQ0tHWGNja3kvK2ZEWHJoVjlrNjJpV3oxNE4zcnZVa1NUdkF4eGUiLCJtYWMiOiIyNmM4M2Y0ZGZlOGZlOTllNTk2MTIwOWNkOTE0YzI5Y2I5NGMwZTE0YzNhZmExMThiN2FkOTI2MTNlOGFlNjRjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:08:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkxjVWt4YUowQURTU3pBUDVpRklMVFE9PSIsInZhbHVlIjoiNE01U3czam5HalhLeE40UUhiWW1ndUtMSWlCV0p1U0J2RldZMVRMYzFZb2pRR3E5WjNXeGRHZVJsY3NSbXEyNTlKeUVxeWhTRGV3N1FEdDZhYytLaVhjWndtME5iVnlJcHFrY1F6a1NielExUVpyTVdJQ2RSNWh0S2w5enBoajIzaThaaXNxbzNLeGFVY2drVTR2NmdTdXBqa1ZoVmoyRWZiZHRxUVpIdzBKVWxSMU5wK1NtMjFYc284dWdVSkNlcTE1WVNRYllkRWkzaDFsV1FjRm14WWg1Vnc5d2d3dlRKcjA0SFY5dXg2bDM1QjJGQ095ZHl3YVRRUk80ekQzcktzMFB3Q2lpbisvM2FxTG5HVlhqUitZdUpHbUs0YXJDQ3J1TGNNSk83eWFmV25hWEFFMkpVM1pwYldYYXlUbFlXRUJYNG8xMzFCK3hwQ0xZdVR0amdURmlud203UTBITGpMUlFzRnlGOHlVNHJBVTAvS1kvTk03ZFFLb1piR2NsYXZLM2VrQ25IdVhQYUFZZ2NjY0w1dnR0RzRjSVRnYTF6Q0JPdU5TeVQ3QmwyQ0hjZlV1dnZIbnlOVTlWYVZkVlBmN0llYlZJQ29GSkFNQXUwdE1jQndBd0tSNkNBTWtCMnY5S1BQUjJhdkNLNk8wcGVpMUV1MmhHSmsrbG9CRnUiLCJtYWMiOiJjY2QxZDZlOTkwMjQ4ZmRhMTNkY2Y2MzBkMWNjM2E1MjVkYzFmMThlZGU0Yjc4ZDQ5MDU1NWQ3YmVlNGRhODI4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:08:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iit6Y3dySG1ZRDZiMWhJcDRWK2JnckE9PSIsInZhbHVlIjoicnVKZkdXQklWZXZ4KzBPRnQwOEFVVGNWUE1yNmhXODB4K3BPeE5GaUtzbXdTa08va2o4UjdhKzZCTzR0SHVlaU1uOHhxaGdDL2xaNEx1bTM0cWRoRjJrbHhFOHFyeFUwZTd1eUZBcnBEMG1JdDhGM1pYNmtYUHdjMWpnUzlNRGxWNU5XTDdWdWl4MmtuTkhZMHV1RVhLY29aOGdrLzEzUzMyZHBNU2YyVURGaFYvaGttTE5UL09uQVp6UXhKOFBDWXFwNUpRRXJhblFseEdvVDNmVVhoNERRUlY1Qlc3dGJPczR5SDkrYjFMRXFhM0NxaEpBRlVhUWlJYzlsbzVER0I1MVFBdjNMbmEyMnpqbTBqSk9wazlBaDhMYjRPREMyS200Nm1Uc1ViY01IbTdHYzAwSE5PYVA0QUV0S002V3p1VDd0SlhBZ3V3RVZxUmVweWRxN0pjQkMvNS9WcGhCeEtXTTFPeVhSWXpvV0tET21xc29pZm1UWUxHNUZUcXNoMVdiNWhTa2RNYlYxQlY5eTF1MXJoUWV1NWdkWmRtOXR1S2ZJK2djUnlxU1hCUlEzbzgrc3RmT2dmNCtLL0pEOWNZT0dlaGVtNThRbDROZU5SLzF1YnhNa3NHQ0tHWGNja3kvK2ZEWHJoVjlrNjJpV3oxNE4zcnZVa1NUdkF4eGUiLCJtYWMiOiIyNmM4M2Y0ZGZlOGZlOTllNTk2MTIwOWNkOTE0YzI5Y2I5NGMwZTE0YzNhZmExMThiN2FkOTI2MTNlOGFlNjRjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:08:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkxjVWt4YUowQURTU3pBUDVpRklMVFE9PSIsInZhbHVlIjoiNE01U3czam5HalhLeE40UUhiWW1ndUtMSWlCV0p1U0J2RldZMVRMYzFZb2pRR3E5WjNXeGRHZVJsY3NSbXEyNTlKeUVxeWhTRGV3N1FEdDZhYytLaVhjWndtME5iVnlJcHFrY1F6a1NielExUVpyTVdJQ2RSNWh0S2w5enBoajIzaThaaXNxbzNLeGFVY2drVTR2NmdTdXBqa1ZoVmoyRWZiZHRxUVpIdzBKVWxSMU5wK1NtMjFYc284dWdVSkNlcTE1WVNRYllkRWkzaDFsV1FjRm14WWg1Vnc5d2d3dlRKcjA0SFY5dXg2bDM1QjJGQ095ZHl3YVRRUk80ekQzcktzMFB3Q2lpbisvM2FxTG5HVlhqUitZdUpHbUs0YXJDQ3J1TGNNSk83eWFmV25hWEFFMkpVM1pwYldYYXlUbFlXRUJYNG8xMzFCK3hwQ0xZdVR0amdURmlud203UTBITGpMUlFzRnlGOHlVNHJBVTAvS1kvTk03ZFFLb1piR2NsYXZLM2VrQ25IdVhQYUFZZ2NjY0w1dnR0RzRjSVRnYTF6Q0JPdU5TeVQ3QmwyQ0hjZlV1dnZIbnlOVTlWYVZkVlBmN0llYlZJQ29GSkFNQXUwdE1jQndBd0tSNkNBTWtCMnY5S1BQUjJhdkNLNk8wcGVpMUV1MmhHSmsrbG9CRnUiLCJtYWMiOiJjY2QxZDZlOTkwMjQ4ZmRhMTNkY2Y2MzBkMWNjM2E1MjVkYzFmMThlZGU0Yjc4ZDQ5MDU1NWQ3YmVlNGRhODI4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:08:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1490080752\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1147763463 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1147763463\", {\"maxDepth\":0})</script>\n"}}