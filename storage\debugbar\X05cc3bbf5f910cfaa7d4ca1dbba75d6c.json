{"__meta": {"id": "X05cc3bbf5f910cfaa7d4ca1dbba75d6c", "datetime": "2025-06-30 15:34:59", "utime": **********.992544, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.612481, "end": **********.992556, "duration": 0.38007497787475586, "duration_str": "380ms", "measures": [{"label": "Booting", "start": **********.612481, "relative_start": 0, "end": **********.92804, "relative_end": **********.92804, "duration": 0.31555891036987305, "duration_str": "316ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.92805, "relative_start": 0.3155689239501953, "end": **********.992557, "relative_end": 9.5367431640625e-07, "duration": 0.06450700759887695, "duration_str": "64.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45140096, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022729999999999997, "accumulated_duration_str": "22.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.954015, "duration": 0.02199, "duration_str": "21.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.744}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.983989, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.744, "width_percent": 1.672}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.986432, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 98.416, "width_percent": 1.584}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:2 [\n  1839 => array:9 [\n    \"name\" => \"ميداليات شكل محتلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"1839\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  1877 => array:8 [\n    \"name\" => \"cleaner Mop\"\n    \"quantity\" => 1\n    \"price\" => \"15.00\"\n    \"tax\" => 0\n    \"subtotal\" => 15.0\n    \"id\" => \"1877\"\n    \"originalquantity\" => 4\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1275731870 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1275731870\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297684545%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImpFMVlYZGgybXJDS0x1dkVyRS83OUE9PSIsInZhbHVlIjoibXFqMzFub3hnSWZqeVhHaWpFdHBqMHlHcUZPbERmUnB3cWthTHFZd2pldzd4ajF5MzJZQmpjZFNiUFFpUnZwd21yeGo2cWtQNkFiM0laWmo4RUtTWjdVb2dqdHpLcUFmeUZWVUVqeGtTckxpejY3NkVmcy9NSWJldkxGM0dYeThiYVBQNTY2YXVKRDhIRm93Qko0NXkwVmxJYnczNlZTUlk3K1VLODJSZUROb2lVdVlzL0ZuVnYvb3Fab2RoSjZvU1JCNXNnZCt6bldCekJPQ2o0UFN1T0JjZ2VBeHJ1Tk9pd3Y1ZWFWa2NvQXdwSTliTzM0Qkg5ZjRJQTBtR0NELzgzcGdHWUJEM2dDMTFqUmVqSzQxRGRwOUwwcVJIUGlxaHJKUTVQV205MTJySGtGQnFqQnl0T3NxM3duRHRwSEwzYzA2NGJidUZvdjZ3TEVvcU9sR2ZDSzNvOU1sUmhXVXM5cFBzSWNnNG1RWWJvRHVhY1Q1TUtXYWw0ZHJENk1JTXRtMVNOT2l1THViOWszdkFTckNBUzlkMG1jY0VwUE9zUEF4cFZNMEZ6dlM0U2NQOTZnTTVmM2FMZXFJdnM1dmVtQldwcjJCdUN5THRKZG1jT2FmTkl4NTRvYmpEVVBScnZwVmxrU0VxM1ZuS3ZmajVnK3pNNURpTnlGNXAvUGYiLCJtYWMiOiJlZjI2ZmE4M2E0YmYyZTQ5MWQzMzc3ZjM2MDI3YjE0MTdkOGUzNTAzMDdmMmMwM2MwODBlMTZkOTY3OWVmYzAwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkxZenczbVB5K2M2TW1HRlFGRmZFNmc9PSIsInZhbHVlIjoiUlNOUFo4aDR4NDNKSkRMQmVxYzBYZlNmQzlBdExkTmpzbDZUWVkrajVuSFZQSnhWUGlBR1E5STZQdFAzOGJXa0JkcVBPVVpOdU9ZVk1jN013NjV1MFdQYTcwUU1IcGpqZFk0ekNRbEZDNk03bmdxcGFlR2JyejUzUXhwcTlwWUNZYzFURC9IQW45eHh0TjBXWWZxSkVvaTFKWlhiS21GT2xsbUozN1FVcTZWeFVIMnlLc0lXNWRFc3hmaWFLTXUyZ2RpTUE2TXE1cHc5NE9FOVYxVzNvN1NjaHFRWHFkYThzTTdiWlJ2N3V4Mk4wNGdodm1RS0dSM0ZsdzQzMVMxV1hGOWc1UnRYVzlMSFJTWTJBZ1JEcCszN0N0U3VLVGFSMzNCb1VFUTZlRVEvN2tNM2RncXdCNDVac2VDQzd5NVBZTDE3QysvU2hBTG0zT251SmsxK2JYWjF1RTFFZHA4U2lBZ0FnZEJ4NHlYUDZqL3NlL240YkdVTHNNVGNIN2lpOFVFSHFaVlZ5UUFsOSttQ0NLaUI1c0w0L2VIcHM3R0FTZEQwNHlQUUFKYTVlWnR5YmhoSmErSmxnOE5rWFozdGhlck9xTFljNk9peFhhNHoxUXk3NWppY01hZmt2QnBkTnVDT3lWeWJCNVhkclVLSG15M2h0cytZUGNkMUdQWW4iLCJtYWMiOiI2NWFlNmUzNTI2ZGYxOTI5OWVhOWFjODBjNGUyOThiYTZmNjNjNGZkOTM0MjMzZTRjYWQyMDM3NmNiY2MwMWVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2034658033 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2034658033\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-791287058 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFpeVZXbG1IWXBEUVVaWlJZWkRTY0E9PSIsInZhbHVlIjoiMXAxS09HVnBZbzNBdDRqeS9oSzl3emkvYmRLWGN5dzBrZXlVUVdZVU9Ja2Jpc1YrNHZIdjc1dFNCNlE4Z3RVOUtPbHVrNDZhNnZmSmJ0dE4xZFhsaUV2YmlYNWZGZi9kMXE0bytva0w2Z2VpRjBqbDFnanY0NVF3bVUxYmFoU0RiTGJZUnBBRmlKNHE1TktqTXFiNGVDeU95VmFraU1ialNqdFUra0xyT2w3dzF3UCtaZXlYZ0hLWVZSQk9admkyclZQT0lkanRxSVc1T1BJSE9NTUJPRzJ4b21jNlN4bXV6U0o5N1VqVWZIa25iM3E2S2xuOTVHMVpML25NcEFzZkZua1kyeHU1cjJmU0dPVkRobUUxZDhtZTVCMnFmRTZpbzN4a2hGSm12OG5kNk9URlhaSGZPTC85OExGRVhSMW9uRWlWbldXOTlXbWpTWUVCK2RpZUVXKzVndmN2MW5HN0Qra1k3YUtMOXUvT3NWNGpKS3RKNmYrSm5LRUcwejl6WllOYjc5NU1SRW9ON0x3SVBZWjlTR0drYkdVYm1FN0FXb0JrUkMzQ0tabCtrM2cxRGV1ZkdEYkRpREJlcFNYbTRFSVA4WExmejh6OFJHUTZmN3JHdFZ0dUgzRjMvUjV4OXFVTWZlMXdQVEtGaTBPT0p0WU1aSW5CWlZRU25ocSsiLCJtYWMiOiJjNjcyOTc0N2MyMWUwYjMxMzEyNmYxMDFmYTdkNjY4MWNiODgzOGRiMDRjYTdjMmU1OTVlZWJmOWI4OGEwNjczIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjdpN2ZlQ2ZvU1JiSThiRFRtK1BxcUE9PSIsInZhbHVlIjoiZ1ZSZU9XWFZyUFdOSGlONENnZGY4R3h3V1g0SWpML1RkVENtM2hXSEhxQWNPcE9UZkE3Q2NCSTFlZWFVNlhXcTczcGt1Tms2K1VMMU1USlRTZW5kaStDTnMwdUNOWEtsSDMybGxFMVBXeUFjdkErQlFyL2RBeGdQcUlFSDhhMDJEY0lEV0FPeUdzcEgrL3crL29RcVZhbXRUb0k4RnRRVnlvSFk3S2g2RzdhN0IwNG1XYUN6ekhid01UNWtkcU9jRjVqbFVDUzM5NmNnbW52OXlMQlhKeUZ0cGNWMlE3cS9xVC9xNGRSTUc2MVpIS3lXS2ZZL1JYSkgwM1MrN3RyeUV5Z0l5ZzFnZjN5K1hYaHp3SFNmcG9YQTZuWERvWXVrZjBNbFRFV0VGeVdqSytML1V4Q1pKS3VwZnV6MytLWEtzb0NPaWRtaEFvT3JLTEJBWFFJcldEb2tIVnpzenVXbGdWYUpuNUR0UzR5OHYzcmtKbzZxRzlnaVRXZlUyUDhYbC9meW12YUIyOFVteFk4Zyt4L1JERmExZEFXbVVQdXAzVWtmVDM0L1JzRW42N1M1azlEdFFzU0NRVE03UEZsdVBINEJJU1RDVHZpZVN2bkJnelBScngybFd1bWd5Q1FOanlGL2R2bmV6MmFQeHJqS2IyaHowbHFTZHp5dk9rcWYiLCJtYWMiOiIzMjM2ZGE2YjFmOWNmOTk1ODQxMWQzNWQxYjAzZjdiNTE1MGExMmJiM2NhMDZjYTNiMGQzZTA3MzM4MDVkYzE1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFpeVZXbG1IWXBEUVVaWlJZWkRTY0E9PSIsInZhbHVlIjoiMXAxS09HVnBZbzNBdDRqeS9oSzl3emkvYmRLWGN5dzBrZXlVUVdZVU9Ja2Jpc1YrNHZIdjc1dFNCNlE4Z3RVOUtPbHVrNDZhNnZmSmJ0dE4xZFhsaUV2YmlYNWZGZi9kMXE0bytva0w2Z2VpRjBqbDFnanY0NVF3bVUxYmFoU0RiTGJZUnBBRmlKNHE1TktqTXFiNGVDeU95VmFraU1ialNqdFUra0xyT2w3dzF3UCtaZXlYZ0hLWVZSQk9admkyclZQT0lkanRxSVc1T1BJSE9NTUJPRzJ4b21jNlN4bXV6U0o5N1VqVWZIa25iM3E2S2xuOTVHMVpML25NcEFzZkZua1kyeHU1cjJmU0dPVkRobUUxZDhtZTVCMnFmRTZpbzN4a2hGSm12OG5kNk9URlhaSGZPTC85OExGRVhSMW9uRWlWbldXOTlXbWpTWUVCK2RpZUVXKzVndmN2MW5HN0Qra1k3YUtMOXUvT3NWNGpKS3RKNmYrSm5LRUcwejl6WllOYjc5NU1SRW9ON0x3SVBZWjlTR0drYkdVYm1FN0FXb0JrUkMzQ0tabCtrM2cxRGV1ZkdEYkRpREJlcFNYbTRFSVA4WExmejh6OFJHUTZmN3JHdFZ0dUgzRjMvUjV4OXFVTWZlMXdQVEtGaTBPT0p0WU1aSW5CWlZRU25ocSsiLCJtYWMiOiJjNjcyOTc0N2MyMWUwYjMxMzEyNmYxMDFmYTdkNjY4MWNiODgzOGRiMDRjYTdjMmU1OTVlZWJmOWI4OGEwNjczIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjdpN2ZlQ2ZvU1JiSThiRFRtK1BxcUE9PSIsInZhbHVlIjoiZ1ZSZU9XWFZyUFdOSGlONENnZGY4R3h3V1g0SWpML1RkVENtM2hXSEhxQWNPcE9UZkE3Q2NCSTFlZWFVNlhXcTczcGt1Tms2K1VMMU1USlRTZW5kaStDTnMwdUNOWEtsSDMybGxFMVBXeUFjdkErQlFyL2RBeGdQcUlFSDhhMDJEY0lEV0FPeUdzcEgrL3crL29RcVZhbXRUb0k4RnRRVnlvSFk3S2g2RzdhN0IwNG1XYUN6ekhid01UNWtkcU9jRjVqbFVDUzM5NmNnbW52OXlMQlhKeUZ0cGNWMlE3cS9xVC9xNGRSTUc2MVpIS3lXS2ZZL1JYSkgwM1MrN3RyeUV5Z0l5ZzFnZjN5K1hYaHp3SFNmcG9YQTZuWERvWXVrZjBNbFRFV0VGeVdqSytML1V4Q1pKS3VwZnV6MytLWEtzb0NPaWRtaEFvT3JLTEJBWFFJcldEb2tIVnpzenVXbGdWYUpuNUR0UzR5OHYzcmtKbzZxRzlnaVRXZlUyUDhYbC9meW12YUIyOFVteFk4Zyt4L1JERmExZEFXbVVQdXAzVWtmVDM0L1JzRW42N1M1azlEdFFzU0NRVE03UEZsdVBINEJJU1RDVHZpZVN2bkJnelBScngybFd1bWd5Q1FOanlGL2R2bmV6MmFQeHJqS2IyaHowbHFTZHp5dk9rcWYiLCJtYWMiOiIzMjM2ZGE2YjFmOWNmOTk1ODQxMWQzNWQxYjAzZjdiNTE1MGExMmJiM2NhMDZjYTNiMGQzZTA3MzM4MDVkYzE1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-791287058\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2116024091 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1839</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1605;&#1610;&#1583;&#1575;&#1604;&#1610;&#1575;&#1578; &#1588;&#1603;&#1604; &#1605;&#1581;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1839</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>1877</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">cleaner Mop</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>15.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1877</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2116024091\", {\"maxDepth\":0})</script>\n"}}