{"__meta": {"id": "Xcf123a580685607e7593f4be3b823cc6", "datetime": "2025-06-08 00:05:38", "utime": 1749341138.018898, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.448847, "end": 1749341138.018918, "duration": 0.5700709819793701, "duration_str": "570ms", "measures": [{"label": "Booting", "start": **********.448847, "relative_start": 0, "end": **********.905903, "relative_end": **********.905903, "duration": 0.45705604553222656, "duration_str": "457ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.905914, "relative_start": 0.45706701278686523, "end": 1749341138.018921, "relative_end": 2.86102294921875e-06, "duration": 0.1130068302154541, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45975800, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.972086, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.979858, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": 1749341138.007006, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": 1749341138.011035, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.01827, "accumulated_duration_str": "18.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.938947, "duration": 0.009710000000000002, "duration_str": "9.71ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 53.147}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.951511, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 53.147, "width_percent": 23.481}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.959535, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 76.628, "width_percent": 3.175}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9727838, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 79.803, "width_percent": 3.339}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9810512, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 83.142, "width_percent": 3.448}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.993501, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 86.59, "width_percent": 4.324}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.999002, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 90.914, "width_percent": 3.065}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1749341138.002391, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 93.979, "width_percent": 2.791}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": 1749341138.008092, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 96.771, "width_percent": 3.229}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LcLdUuv9ncxCQAwxbWudm7IigIdCqOvxHs1ba2vd", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1085096589 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1085096589\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1315603668 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1315603668\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1177809492 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1177809492\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1734 characters\">XSRF-TOKEN=eyJpdiI6InVhMVBxaVNoV0tYeXlQYWVDV0dkTHc9PSIsInZhbHVlIjoiTldrL2tIMitqd21JMkk4UEVaVFJIVnlKMFhIUVVXcGkxM2hLZ1ZKZ1BDWm5TNEFlS2l5dmdSR3A4ZlFBRG44SXB6KzlUVXRTaTk5ZnhGNVIyYW96amhsczlCeG5XK2VOYm9Yam4xV0c2d1M1RUNLaWp0VVlUUXYrdG1GQkZGd0tvSThCNmp0bWJGNjVJR1JxTEZ6K0VtNDhycFZ3SlBNRklGRkZYUk5BTGZ3WFFCYy9IRXl0WFp3Qm0zRkMyUTdLYVNaWURGMFNSWTU4NXVrdy9NdzB4MUVHTFRLWlVtaHJSSXNqejJuNnIyanp3VmZvMHF4QzlLWEFrYXdlRlpHQ3lIQWJ1MzZibG9hcnA3dnI4dFYyMzVOZ2V6cnBzZk8xTnFoc3lpa3hpWlYwcFREZ0ppZXNrK0tVK0JwRXZDMVo4MDVqR1FSYWZKd3RPbExYcWlpRHZwc3k1d3gxdzB0Q0VHMGkwTmwxN0MrSk5HM2dJR3J4OTRTeHpDRFJ0YVFaVnhiZ0Y0dTA1M0VkeTBpcFJ5bmhqNlVsTWFsYWdNaGkzcEpRRE9MMUh5eFhIemZWV3k3ZThxMFY2bitsUE5uZzd1RkVFK0dySkQrMVczSy9pdzR4QjVzZ0U3Q05ONGh0QTFJSkZoc1k4QzhaT3RCdmNoOUR6MmNYYnNHajVNRnQiLCJtYWMiOiJjMjYzMTBmOWVhZWZkMzk2MzZlNGNlNGQxYzM1MWNiYmQ3MmVmMzQ3YmM4MGYwOGVmODllMjE4NmM5YzIyOWRjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkdxbHFuN2VBVTlXYWgzcGx3OTBQSHc9PSIsInZhbHVlIjoiSmxLbWZ1bUQ1SEZHaHRLeGRnN2lSUlF3ZStiSUE1V1RDZFdab3hqdFNEVjdEY3drSnBDSCtBMjhKOHNvVyt3S0dqaG11TFNsSGxKUEkrQnRxM2ZRWEdFQkdxb2huNS9tdGhKSm5zVUVsRlhha1JPdDVod3hvdEsyNzlYZk9IRmNzdHpmd2JGdjdUbXV1NlZSaFJLSDRCQ2YvUVBUZTZyV1VoQWdHN1MxRnZjUjllbFlyRnZXSmpZZFY4ZHRyUmRXQlYvenRzaTkrOWlmMHNFRG42RXA2QlB3dkRJenQ5MjBJdmxjRDhmLy9zUStBZ0orY2h3YVdUOVZRVEx5TCsrcWpheUVMSmhZYUZlRHUyYjNRS1VtREpxWXY5ekEzYXV1UlRsMGVrWEF2Z1p5Snl5cDd4eGJMckk3ekVrTlNqWnM2WVRHMGJrQlU2Rlo0NFBSYXRHNFpsWFN2ajdvQ3MwbkIwUFFtL3AvMDNVaUNqc3hsbkNIMDgrOSs1ZlF1dnluQllzNmorVlZmbVhiOThDNTZIVlQwQ2Z5UjJTS0pjUXhFdE5LbmRycXlmM251NGNxTW5RbklIakpqOTQ2NmxjbUkrOTdkMW1lQ1F2RjdDd3N2ODZGQXhZLzNLaG1YeTl6RWNVTGJRa081TStYaU4wOGV5RUtCOElsKzFqdHVRZkYiLCJtYWMiOiIzYzI2NzE2ODI1NzNiMzRjMzhkNzg1MjAxZTZlMmY1ZTdlNjUwOGZmNjJjYTdlZDJjYTM4NmM2ODI2M2Q0ODUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LcLdUuv9ncxCQAwxbWudm7IigIdCqOvxHs1ba2vd</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">b9LUIYkS5r7s0TKIxljNhvEpa6hyCmvNy5dG1y4I</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-947860263 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:05:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlIveWxYNmgxM0Y3SXY1cHpRNlJza1E9PSIsInZhbHVlIjoiaHZhbC9uTVNjc1AzRlZTSThnTlFDWko3QmpGeVpSM3pVdzg0NjNCcnBhY2hpVVZ1bFYvOU1SemwzZ2EvSkxDZUpIa0xSaklLSFI4YXlyeUZKZzVlVHZONFV6K29HRFJ1RUlVMlNrejNMYkt6bjhRR0RseVM0VmxvV01UWlNaQTJ0K3hQa25EVitROGQweWFyblM5aWg0UHNra256dFJmczJBSzMxS1Fub0FWK1c4UWp1eGxsdVp1anZvTk01ODJHY0xGVjM2d0paSm43Q3BPWnd6dUdySTd1d2tscVV4MzFaWW9kOUFLSVlTZ081eXdDQk5VV2QrZHM3QXkyblVkdzFTM0pZaUYvUnZ4Z2xOOW1iSVJpVnkvN2ltcU9ObmEyTXNsdlBQWk5lQkMwaEg2WFA1VjdhUjdvRjVxU1ZjbGNTa1krMU5PcldndkxJbVZtM3JvTy8rdHhMNXh6eFdlZVY4OWgzSGp5SnZRUTVlV0Q2WEVLRk9wNnozUVd5L3RBUGNJcjJzL0xEbWw3d3pLTlZjaENVMTNxaFZNOTJTOTR2UVcrelFRdzl2L2RMZUM0bkVNejhVaHdidFVnT1dUV2V5NDJpWjluNFl2UDFqUlQ4dnNjU3k0TWRaM3Q2VjZpOG9QNEpCUHlEUG5tRW1pSFpGTjd6YkVKL1JqTHBuWkIiLCJtYWMiOiJhNDEwMmRmZmZlNjMyYmJhMjZiYTM0YTUzN2Y3MGE1NDIxN2EzZmU0YzhhZDlkODE3OGM1ZGYyNDI3NmQ5NzFiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:05:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFrQVg3VVZOeFh3aTRIQlNtazdaS1E9PSIsInZhbHVlIjoiY1pLdzJmT2V5VzB3TTFMcHM3N0lPdC9weHMvM1hPTnMwQlpraWU4TFBRZUFiczlhOWxaRXdoNlRacTltTDJscDhwRVcxaTZnOU14VVRwTnlSa3VBcDJKN1JiZ1FwdkgvbUNFNTAyWU45c2ZDNWEzUGRUckFoUk5iR1VMejRJZXg4bE03UU1GWFkvZExSZE5jUmozak9jbTEwZWE1L0hEZFdVTmNIR2hmbmswYjJ6LzRwOVd0RisyZU1odFVoM2xBeENFaVZMV2FyS0JvUTFhcEUxT2d0RDY3OGdYaE9JRUN2UGkwZ3NyVnNIeVhjNW1yRG1mNFk1eEJTZkt3WFhmenhjVUxvUGpmT3JZT0RnMVd6b3hBdWNqSUU1dHJGem0yWUx4azh4SURjclZ0U2l4MHY1azk4b1ZEWmY0RFUzeHpZb0lFUWs3SUZwczllbkNydkxpYVZULysydVZNL3RTYm5IbW4zbSsyVGFJMTlUM2d6TVNyR3lWOTVpZHpQNUY2S3Z5RE5BS3FVZjNqY3RnczhZOGdBMWgvR1hCK0htalRaZ2JWblBFejVZMm45S1M5RFJjQ0lEcGswZjBFaVVFUUlQdDhKeSswYzZlTDNOeWFCb2txbVRTRmt5U3lwU2d1Tnd6eStHN2RzSjlEbkVqUTVhNG41SU5tcTlGRldxemUiLCJtYWMiOiIyYmNkNGM1YmJlYjY3MWVhYTk5YWRjMTMyZTk1Nzg4YTBkYmFkOGY2ZGNhYzFjMWI2NDA4ZjA0YjMzMTczZmUzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:05:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlIveWxYNmgxM0Y3SXY1cHpRNlJza1E9PSIsInZhbHVlIjoiaHZhbC9uTVNjc1AzRlZTSThnTlFDWko3QmpGeVpSM3pVdzg0NjNCcnBhY2hpVVZ1bFYvOU1SemwzZ2EvSkxDZUpIa0xSaklLSFI4YXlyeUZKZzVlVHZONFV6K29HRFJ1RUlVMlNrejNMYkt6bjhRR0RseVM0VmxvV01UWlNaQTJ0K3hQa25EVitROGQweWFyblM5aWg0UHNra256dFJmczJBSzMxS1Fub0FWK1c4UWp1eGxsdVp1anZvTk01ODJHY0xGVjM2d0paSm43Q3BPWnd6dUdySTd1d2tscVV4MzFaWW9kOUFLSVlTZ081eXdDQk5VV2QrZHM3QXkyblVkdzFTM0pZaUYvUnZ4Z2xOOW1iSVJpVnkvN2ltcU9ObmEyTXNsdlBQWk5lQkMwaEg2WFA1VjdhUjdvRjVxU1ZjbGNTa1krMU5PcldndkxJbVZtM3JvTy8rdHhMNXh6eFdlZVY4OWgzSGp5SnZRUTVlV0Q2WEVLRk9wNnozUVd5L3RBUGNJcjJzL0xEbWw3d3pLTlZjaENVMTNxaFZNOTJTOTR2UVcrelFRdzl2L2RMZUM0bkVNejhVaHdidFVnT1dUV2V5NDJpWjluNFl2UDFqUlQ4dnNjU3k0TWRaM3Q2VjZpOG9QNEpCUHlEUG5tRW1pSFpGTjd6YkVKL1JqTHBuWkIiLCJtYWMiOiJhNDEwMmRmZmZlNjMyYmJhMjZiYTM0YTUzN2Y3MGE1NDIxN2EzZmU0YzhhZDlkODE3OGM1ZGYyNDI3NmQ5NzFiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:05:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFrQVg3VVZOeFh3aTRIQlNtazdaS1E9PSIsInZhbHVlIjoiY1pLdzJmT2V5VzB3TTFMcHM3N0lPdC9weHMvM1hPTnMwQlpraWU4TFBRZUFiczlhOWxaRXdoNlRacTltTDJscDhwRVcxaTZnOU14VVRwTnlSa3VBcDJKN1JiZ1FwdkgvbUNFNTAyWU45c2ZDNWEzUGRUckFoUk5iR1VMejRJZXg4bE03UU1GWFkvZExSZE5jUmozak9jbTEwZWE1L0hEZFdVTmNIR2hmbmswYjJ6LzRwOVd0RisyZU1odFVoM2xBeENFaVZMV2FyS0JvUTFhcEUxT2d0RDY3OGdYaE9JRUN2UGkwZ3NyVnNIeVhjNW1yRG1mNFk1eEJTZkt3WFhmenhjVUxvUGpmT3JZT0RnMVd6b3hBdWNqSUU1dHJGem0yWUx4azh4SURjclZ0U2l4MHY1azk4b1ZEWmY0RFUzeHpZb0lFUWs3SUZwczllbkNydkxpYVZULysydVZNL3RTYm5IbW4zbSsyVGFJMTlUM2d6TVNyR3lWOTVpZHpQNUY2S3Z5RE5BS3FVZjNqY3RnczhZOGdBMWgvR1hCK0htalRaZ2JWblBFejVZMm45S1M5RFJjQ0lEcGswZjBFaVVFUUlQdDhKeSswYzZlTDNOeWFCb2txbVRTRmt5U3lwU2d1Tnd6eStHN2RzSjlEbkVqUTVhNG41SU5tcTlGRldxemUiLCJtYWMiOiIyYmNkNGM1YmJlYjY3MWVhYTk5YWRjMTMyZTk1Nzg4YTBkYmFkOGY2ZGNhYzFjMWI2NDA4ZjA0YjMzMTczZmUzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:05:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-947860263\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1099482760 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LcLdUuv9ncxCQAwxbWudm7IigIdCqOvxHs1ba2vd</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099482760\", {\"maxDepth\":0})</script>\n"}}