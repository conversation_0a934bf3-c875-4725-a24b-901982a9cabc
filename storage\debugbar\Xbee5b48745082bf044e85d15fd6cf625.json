{"__meta": {"id": "Xbee5b48745082bf044e85d15fd6cf625", "datetime": "2025-06-07 22:50:08", "utime": **********.178162, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749336607.154521, "end": **********.178211, "duration": 1.0236899852752686, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": 1749336607.154521, "relative_start": 0, "end": **********.03062, "relative_end": **********.03062, "duration": 0.8760991096496582, "duration_str": "876ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.030647, "relative_start": 0.8761260509490967, "end": **********.178216, "relative_end": 5.0067901611328125e-06, "duration": 0.147568941116333, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45578760, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00732, "accumulated_duration_str": "7.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.110515, "duration": 0.00501, "duration_str": "5.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 68.443}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1361828, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 68.443, "width_percent": 17.76}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1517432, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.202, "width_percent": 13.798}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2076534971 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2076534971\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-12365299 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-12365299\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1967578763 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1967578763\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1695832506 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749335947329%7C7%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNkRVVkWFhPdGRUY0hKMk96OVhDV1E9PSIsInZhbHVlIjoiaXljS1Z1NmxZdFJyaWlaZ25PaGRxWExUWDFGL1J0M3lNMlB1RTQxQ25LN0VGc1VCU1ova0xPRHU3ODlnVjQ3WUtUVE9VUS9KdTY3d1ltMkpTd0NSTGxpcGR0TmM0RE4vWHRwck9zY3F4T0dYWktaaHNjd3NYK1hWN2pJWC9ZdEYzUFVabTBvUnZPZDhhdnpMR1ROWGJiVmc3Z3dkL01YbmNMcXA0ajRReXhzUXVBS1pLdDBaYUphMGdraFBFNERXYmR4NjdqbGt3ZTB1R01hdmx0UUpla1VYVUhEaE55dXU4TU5MRFo0VlQvbVE5UVBKR0R2Z3lEaHBxOWtWbk5sclV4VVFlVEZaQWhZd2dpUW9nMlIzclc4bEpuSFZHaFU1MHhnM3F5cDJXb0hNaGdzRi9lY3J1S3BoOWhvWlJFb0U2Y25UKzNaTGxKZlBCbngrdzZEc2hzRWpzWjFreUJmN09GZ2h1MTNwUTRrSTBqVW5uWnJtVnBvYXNKNWlKUGxOaWtsVDlPdDV2c2c5WDVCaVBOYm9WbGtsWlUrQTJzck5nLzdoN3JPZmdrUlRUYThiaThiS1FjRGg2Z05sVGUzYmYrYnZXN2FPbGlSWjJwZkJZdWVqTVpvRnc4bjgwamxGZ1gwQWlwNnZKVi9FU1JtY2lnZU5ZQ3RIYVN4MWEzMFQiLCJtYWMiOiIwYmRkZjEwMTdjNDE3NDI0ZmNlZjk1MTgxZWZiYTFmNTM4NjE2ODQxMzNhMTVlMGIyZGVmYjdjM2VkNGFlZDQ4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImViN0syRXRzcjNkK21HaWdVeXFKWkE9PSIsInZhbHVlIjoiaWJmKzVHOVNMN1NKQmpmajQyclQ3OUxJZ2FGbmtFWDUxZllkMXgyYm9MYVBYa0Y5clpBU1pQaXRQSEVSQmtDSnZlSXpUU1dTWHUvL3hTcjBQVkJnR1kxWkFCS0dtc1JpTkFCcWpvOUJ2b0Qxcm5wWnI3eVp2blZ1bVQvZmYzSjdYOGFoancxOHNHYXdPcTlOYXN1NHJ2a01NajYxQ2pwTDNaU2NRSHZJQ0NpY3ZXRVk2UDMwNDliZlRqMlUwTkdiZklQbFN5SHNtV3RKaGViTTNsb0lYemdySVNuUnY0M2d1K3ZKbXFrRlZwKy9aMVhYbjl6di8zSnROSU9USXN2Q29Wd0NaeS9NQmJRenN3YmE2VVV2cHRkWHE1UFZiS0c4dDZLdy9XLzRwZGpyY2U2MzNYL3RVL2xacnNJWTFEQjVRY3BkNndEOXVEZlM3ajNybUd0ZDV6UFMrd1lyTTI3RXpoSVVyZW02MjdGVldjSFk3SGpoK3pGNFgwT2wwV054bW9rTTJERnV2ZlpTK0ZMSkQ3QktzdmtySUhLYnNSMlBvVWxuQkI1d01LUE9GelJJWnBVMEJ1MTE2Vll1VkYvMGVkTUFCQktOaG9KUWgvdlFHbDdMby84ZzVUN0tkQW5VUklrL3YrQ0hCSDQyd1drZElQNkRYMmNFeUk2TVpXdk8iLCJtYWMiOiI3OWI3ZGI3MDlmY2M2MjU4YTYzNmUzODM5ZThiMjU5MDY3YTA1NmY2NTRhZDJjZjA5ZDQ5MmJlZTViZDQ5NzlkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695832506\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:50:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFGektxMzA2SWt0MnlBUHh3Vm8vY0E9PSIsInZhbHVlIjoicUdvVElxSzhQWFJhSzNQNFBsZHUzczJPam9sYnpEY3laZnNQQmFrM24ySnljQTBFbnZiRXBZNkRjcklOZ1ZrOWg2VDhIYi93QUl4ZFQ0V3pUalBLZDB6RmVxUUtleEVLUVVRcGNzZWpBV0tiTFZkMUtMOGVpamo0Q0Foc0VHYjdiWVZVTDBDbmY4L3h5ZmxqU1hFWlRmQ3BqcE9vejVlZ2IvTnlaempVcDlWNndKK2RsSmp6Zk53Sjd1aGQ3SXpSNktydHI5dnBUeDlvOHN4YnJNcko2WG9Ud3hldEFlcVpldnpseDhVTGJyVVlaTlFFZFR6c1o2UTNpNnh2cmRJaXl0SmN4Yk96Yk5tSm9BTldXcXBHY1NTR2wwb3d6T1MxVGpHeFlBbEdSYThYZ21YOTRmTmJVYmZmRGlaVTV3UzRtWXBuOXZManAvU2JNeHREUFBPSWJsUmRZcmJLRjd3WGdCd3ExdHZ0QkhnditxdWk5QzliQWZncFErYTZpdGVLVHRQVVFxVUhDNUsyaGRiMGIvYjFrcXU2VkhwYS9LVHA5T2gzeFBXUmtQSGZIdGNKbFhCWjllZmRmSGsrTW9KbE04eC8xUm9ITnNLc0FLRk1PZnhVY1JTKzFOcmVsRVFlUlVwcHFSdFZ6NHdLR1RGSTBsUFg2Nml4TzZMNk93UHUiLCJtYWMiOiJjYzg0ZjgwNGJjNzRkOTQ5NjMwYTVhZTlhYTU1ZTY3YzUwMDkzODNiN2QzMmVjMWQzMzFjMmZiNjc5NzMyMDU1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:50:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IktsN08xN04yTDFlYlpQZFFZekZFaUE9PSIsInZhbHVlIjoiOEVkQ0trdHZoUm5LWGVnWTQ2V1lna0pKMi9hTzdtbHlzT3pVbFVmbnhscVdFTEcycHdaSk9GajMwb0RHU1Rub29DNVhrMjVTMEo5Y2dkbXBXSXc5Vjl6RkI4VUhGYjFKcGxrMUlBa3JGUVZPUWxXT010ZlM1ZUlmYThPZjZkQWRyeG56eWpDc0hocVJzZFlmQngrVS9IR0pCUmFKUVhYMzZ2K0pVMXdoaTdwcEpFdEpFeXFodFZKMFkwU0xKVDlmV282S01JQ2k1ekt4NDRkZ1laMmNTa0llZFlIVnpiYWVoVnVFV3FLeVpGZCt0V2pRL1JpTCt4eHJIYUJHS3UwQVo4K3BBZVhXMSs3WnJsSjFxMVNYYitiTFZaV0FuclRLYlEvS0w2Y09rWEQ3ZEN3a3ViLzZtR1Nlbjk4SmJMc0FYbTZURVZ6c09GZkpRbEJsMWNpTjB0ejdxV3JzY3lrV2g2cDZweTJvM3ZYelVwbWVaMWFIUnBwQUFpUWJObU5RZkxVaThpN0xzRlBac3JKWkovWEl3d013M0pWV2FFTGJ4cHF1RlRDYVR6RzM2SjU0VVRqOE93SGVmTkdsa1h2dVNFNTNWeGhOTGhBZEQzYS9tQmFsbkU4R0FyK3czQ3E4UFpkNnpMSDRtTy96SmhaL2VqR1hBelI5bWV2UzdjcG8iLCJtYWMiOiJmZjI1OGM0OTM5MWJmNjA0Mjc1MDlhODNkOTI5NTRiNWU3NjU1ZDVhMWZmZDY2MjlhN2Q0OTlkMGRlYWI1NGM2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:50:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFGektxMzA2SWt0MnlBUHh3Vm8vY0E9PSIsInZhbHVlIjoicUdvVElxSzhQWFJhSzNQNFBsZHUzczJPam9sYnpEY3laZnNQQmFrM24ySnljQTBFbnZiRXBZNkRjcklOZ1ZrOWg2VDhIYi93QUl4ZFQ0V3pUalBLZDB6RmVxUUtleEVLUVVRcGNzZWpBV0tiTFZkMUtMOGVpamo0Q0Foc0VHYjdiWVZVTDBDbmY4L3h5ZmxqU1hFWlRmQ3BqcE9vejVlZ2IvTnlaempVcDlWNndKK2RsSmp6Zk53Sjd1aGQ3SXpSNktydHI5dnBUeDlvOHN4YnJNcko2WG9Ud3hldEFlcVpldnpseDhVTGJyVVlaTlFFZFR6c1o2UTNpNnh2cmRJaXl0SmN4Yk96Yk5tSm9BTldXcXBHY1NTR2wwb3d6T1MxVGpHeFlBbEdSYThYZ21YOTRmTmJVYmZmRGlaVTV3UzRtWXBuOXZManAvU2JNeHREUFBPSWJsUmRZcmJLRjd3WGdCd3ExdHZ0QkhnditxdWk5QzliQWZncFErYTZpdGVLVHRQVVFxVUhDNUsyaGRiMGIvYjFrcXU2VkhwYS9LVHA5T2gzeFBXUmtQSGZIdGNKbFhCWjllZmRmSGsrTW9KbE04eC8xUm9ITnNLc0FLRk1PZnhVY1JTKzFOcmVsRVFlUlVwcHFSdFZ6NHdLR1RGSTBsUFg2Nml4TzZMNk93UHUiLCJtYWMiOiJjYzg0ZjgwNGJjNzRkOTQ5NjMwYTVhZTlhYTU1ZTY3YzUwMDkzODNiN2QzMmVjMWQzMzFjMmZiNjc5NzMyMDU1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:50:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IktsN08xN04yTDFlYlpQZFFZekZFaUE9PSIsInZhbHVlIjoiOEVkQ0trdHZoUm5LWGVnWTQ2V1lna0pKMi9hTzdtbHlzT3pVbFVmbnhscVdFTEcycHdaSk9GajMwb0RHU1Rub29DNVhrMjVTMEo5Y2dkbXBXSXc5Vjl6RkI4VUhGYjFKcGxrMUlBa3JGUVZPUWxXT010ZlM1ZUlmYThPZjZkQWRyeG56eWpDc0hocVJzZFlmQngrVS9IR0pCUmFKUVhYMzZ2K0pVMXdoaTdwcEpFdEpFeXFodFZKMFkwU0xKVDlmV282S01JQ2k1ekt4NDRkZ1laMmNTa0llZFlIVnpiYWVoVnVFV3FLeVpGZCt0V2pRL1JpTCt4eHJIYUJHS3UwQVo4K3BBZVhXMSs3WnJsSjFxMVNYYitiTFZaV0FuclRLYlEvS0w2Y09rWEQ3ZEN3a3ViLzZtR1Nlbjk4SmJMc0FYbTZURVZ6c09GZkpRbEJsMWNpTjB0ejdxV3JzY3lrV2g2cDZweTJvM3ZYelVwbWVaMWFIUnBwQUFpUWJObU5RZkxVaThpN0xzRlBac3JKWkovWEl3d013M0pWV2FFTGJ4cHF1RlRDYVR6RzM2SjU0VVRqOE93SGVmTkdsa1h2dVNFNTNWeGhOTGhBZEQzYS9tQmFsbkU4R0FyK3czQ3E4UFpkNnpMSDRtTy96SmhaL2VqR1hBelI5bWV2UzdjcG8iLCJtYWMiOiJmZjI1OGM0OTM5MWJmNjA0Mjc1MDlhODNkOTI5NTRiNWU3NjU1ZDVhMWZmZDY2MjlhN2Q0OTlkMGRlYWI1NGM2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:50:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-302097815 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-302097815\", {\"maxDepth\":0})</script>\n"}}