{"__meta": {"id": "Xafb48318ad124e0c18f4d8078a26ce34", "datetime": "2025-06-08 16:24:12", "utime": **********.092307, "method": "GET", "uri": "/printview/pos?vc_name=7&user_id=17&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 163, "messages": [{"message": "[16:24:12] LOG.warning: Implicit conversion from float 160.79999999999998 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 186", "message_html": null, "is_string": false, "label": "warning", "time": **********.050994, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 1.4 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.051287, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 3.5999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.051426, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 3.8 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.051557, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 7.199999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.051693, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 7.399999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.051828, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 13.2 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.051993, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 13.399999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.052123, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 16.799999999999997 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.052253, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 16.999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.052391, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 20.399999999999995 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.052525, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 21.799999999999994 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.052644, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 26.399999999999995 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.052762, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 28.999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.052878, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 31.199999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.052998, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 33.8 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.053117, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 37.4 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.053235, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 39.6 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.053354, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 39.800000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.05347, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 44.400000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.053587, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 47.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.053702, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 49.20000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.053826, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 50.60000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.053961, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 52.80000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.054091, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 54.20000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.054212, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 56.40000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.054333, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 59.000000000000014 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.05447, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 61.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.054605, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 61.40000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.054733, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 66.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.054855, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 66.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.054975, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 68.40000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.055103, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 71.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.055222, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 73.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.055353, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 77.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.055488, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 79.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.055625, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 80.60000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.055765, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 82.80000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.055912, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 84.20000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.056032, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 87.60000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.056152, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 89.00000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.056268, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 92.40000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.056388, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 93.80000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.056504, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 97.20000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.056625, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 98.60000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.056749, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 102.00000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.056878, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 103.40000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.057007, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 105.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.057139, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 105.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.057272, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 108.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.057406, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 111.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.057533, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 114.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.057664, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 116.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.057797, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 118.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.057927, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 119.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.058057, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 122.40000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.058187, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 125.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.058316, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 128.4000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.058448, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 129.8000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.058573, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 132.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.058704, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 133.4000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.05883, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 135.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.058959, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 138.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.059089, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 140.40000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.05921, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 143.00000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.059326, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 145.20000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.059452, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 146.60000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.059572, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 151.20000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.059693, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 153.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.059817, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 156.00000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.059938, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 156.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.060055, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 158.4 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.060173, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 159.8 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.060291, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 160.8 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.060408, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 159.8 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.060522, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 160.8 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.06064, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 159.8 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.060754, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069505, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069744, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069887, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.07003, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070179, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070315, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070454, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070589, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070725, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070863, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071023, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071166, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071302, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071436, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071578, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071714, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071859, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071997, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.072122, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.072252, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.072377, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.072501, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.072633, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.072761, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 24.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.072888, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 25.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.073026, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 26.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.073159, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 27.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.073284, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 28.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.073416, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 29.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.073551, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 30.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.073689, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 31.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.073831, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 32.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.073966, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 33.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.074104, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 34.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.07424, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 35.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.074378, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 36.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.074527, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 37.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.074666, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 38.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.0748, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 39.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.074936, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 40.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.075074, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 41.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.075213, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 42.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.075343, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 43.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.075468, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 44.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.075608, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 45.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.075737, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 46.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.07587, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 47.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.075996, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 48.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.076121, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 49.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.076247, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 50.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.076373, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 51.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.076498, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 52.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.076628, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 53.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.076764, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 54.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.076894, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 55.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.07702, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 56.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.077162, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 57.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.077298, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 58.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.077424, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 59.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.077555, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 60.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.077683, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 61.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.077811, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 62.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.077938, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 63.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.078065, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 64.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.078192, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 65.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.07832, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 66.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.078445, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 67.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.078571, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.078699, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.078825, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.078952, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.079082, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.079208, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.079335, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.07948, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.079606, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.079731, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.07986, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.079985, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.080111, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.080242, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.080373, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.0805, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.080637, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.080775, "xdebug_link": null, "collector": "log"}, {"message": "[16:24:12] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.080903, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.407326, "end": **********.092549, "duration": 0.6852231025695801, "duration_str": "685ms", "measures": [{"label": "Booting", "start": **********.407326, "relative_start": 0, "end": **********.87929, "relative_end": **********.87929, "duration": 0.47196412086486816, "duration_str": "472ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.8793, "relative_start": 0.47197413444519043, "end": **********.092552, "relative_end": 2.86102294921875e-06, "duration": 0.21325182914733887, "duration_str": "213ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54663640, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.printview", "param_count": null, "params": [], "start": **********.038092, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/pos/printview.blade.phppos.printview", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fpos%2Fprintview.blade.php&line=1", "ajax": false, "filename": "printview.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.printview"}]}, "route": {"uri": "GET printview/pos", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\PosController@printView", "namespace": null, "prefix": "", "where": [], "as": "pos.printview", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1306\" onclick=\"\">app/Http/Controllers/PosController.php:1306-1408</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.030079999999999996, "accumulated_duration_str": "30.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.924051, "duration": 0.01789, "duration_str": "17.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 59.475}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9545271, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 59.475, "width_percent": 5.785}, {"sql": "select * from `customers` where `name` = '7' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["7", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 1314}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.961099, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1314", "source": "app/Http/Controllers/PosController.php:1314", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1314", "ajax": false, "filename": "PosController.php", "line": "1314"}, "connection": "ty", "start_percent": 65.259, "width_percent": 2.394}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 1315}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.964738, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1315", "source": "app/Http/Controllers/PosController.php:1315", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1315", "ajax": false, "filename": "PosController.php", "line": "1315"}, "connection": "ty", "start_percent": 67.653, "width_percent": 2.527}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.986888, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 70.18, "width_percent": 1.895}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.99023, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 72.074, "width_percent": 2.36}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 517}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 1318}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.996944, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "PosController.php:517", "source": "app/Http/Controllers/PosController.php:517", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=517", "ajax": false, "filename": "PosController.php", "line": "517"}, "connection": "ty", "start_percent": 74.435, "width_percent": 2.959}, {"sql": "select * from `product_services` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 1393}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.015636, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1393", "source": "app/Http/Controllers/PosController.php:1393", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1393", "ajax": false, "filename": "PosController.php", "line": "1393"}, "connection": "ty", "start_percent": 77.394, "width_percent": 1.928}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 1402}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0195432, "duration": 0.0054, "duration_str": "5.4ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1402", "source": "app/Http/Controllers/PosController.php:1402", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1402", "ajax": false, "filename": "PosController.php", "line": "1402"}, "connection": "ty", "start_percent": 79.322, "width_percent": 17.952}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "pos.printview", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/pos/printview.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.040538, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 97.274, "width_percent": 2.726}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-587609555 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587609555\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.995772, "xdebug_link": null}]}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"originalquantity\" => 30\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/printview/pos", "status_code": "<pre class=sf-dump id=sf-dump-1644620407 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1644620407\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">17</span>\"\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1210493974 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1210493974\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1925042747 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=u1db3a%7C1749399485619%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldCaUl4dE1wdzJ5cWZLZHZVT0lOYWc9PSIsInZhbHVlIjoiUXdNRWdBWG1YREQzbXVCVXVxY2R2eFB1WmVTV25XSU82MmljdTM4M3JqbjBVb2hLdy9jMWxEaUgxOVVCb1VXRzljd09BTVpiWm01cUkwa0paMDZXa1pZaVkraElnc3RhRDdLTlhvSUFRUXgyYml1bktBZHdjSjBBNXFwcWJFUWpmemxNbGF1NEJKbGt6aW9ZaFNGUnh1YjhkNHQrZldUTDA0K1doRlhDeFlEc2FOSHc0N1ZPYUg0REprUG5MdDdwMTRzbCtqWkE4Z3dDSUVpN09udUErRTBOKzdhQUZrM0NVQmtib1g1YXM5N2ppRE9XdHhLdm8rYWFhWXk0MWU3VVFHS0Q4R0kxWnFGSWVWNHdDb1BlTWZUcHFBRm9URmFWZkQ2T3djMFhUMDhGdjBaQitBVW1UQSsvblpmeC9nN3ozZUsvM2FZNUFSWWo1RWRSN09kY3dhU2tGdnowWE9JU1ZSazlRdmNDL2Z1R0ZnQ2FucnlhalVDRWo0WDdDa1A4NEVyRzVkS2xabDJHZnkwRkNka2lhblR1Z3ByZ1IyOGp4U1BGUW8zMlBGemhXWDJ6dExyQlpnYTBubjFJV2lDWU1WNlVzd01oOE9KYmVWTVlYUGRsdEp6VVgrZHpFd3lMQ08xNWJlWEczbFE1ckxDcmdwQVBCMHJXT3BoUlpjRVMiLCJtYWMiOiI3Yjk2Mjg3MWFmNTlkMzEwMjhmMWU5MzQ4MjExYmMyM2UxY2Y2MWZkMzNiZTNlZWVjZDUwNmYzNDlhOWRiYzNlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlZGZHJKa3ltZDN5UUxYM0s5MjkwelE9PSIsInZhbHVlIjoiOWw4b0U2Sng4dXhqcjN5Wm1YNmV4UXoxVVhWejFNR05VZ1FLankrZllMVi81V09Ma0JhZU4rRGo4RXE2Y3VERFQ0c1cybEdlSVpWUExIZHBjWEw0YytkN0NROEx4cXpaZDBDR0NzTnFibEt0WHNvQlRWZEV1dGQ1THovalBQVDBJT3ZodlBMUXFOdHd2UU9aK0RaUGVaK0RKRENjN3RaaVpIT2NOaWFoUlZiZlowQVJCVWREaG9JWFdwS3ZtZzJXRVRuQUx2WnEyN3o4Z0ZWN05pWlNSNzU2cy9SNklkMmVjTkZyMFdtMFYrQjdBSTZiNmJCUlp6ODZ1MG1IbVpvdXUvMFVya2pKVVNyYU55eC96UGZDcW02aGhINmlPZTcrYlViTGxOU2NpODdQekZIaTlXMTNkcktkWE1yUDZDalVIczgrOERhTys3NEZPd1Y5UGtMS21FWmdFRnAzU241dC8zd1BpZmhoSndoUGUxcytOTHozQjExa3FnaUx3SDhZSHFicXViejFycGduMUR3SVdkM2c2dGkxUVpTRXQvUjNqdkpsTm5HVSsrZXBPeHlodEg1cUNib0VNcTB0Q3NjUFpGRTJhUlRaSWJqZ3VNN3Budmp1eHlhZmlUZ0V3WFRCS0E0amxmcjNKa1VLUkhvd0M0NHFvRUM2SXdiU01UeUoiLCJtYWMiOiJkMzQ4YThkMmE2YWFhZDI5OWVlODBhZDc0MmQ0YjY3MmFkYWQxN2YxOWFiYjFlYzFjZjAwZGEwZGUwYjhkYTI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925042747\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-793580843 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-793580843\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1313182728 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:24:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZKeXRJOW9pT1o1cS9Hc3V3T096YWc9PSIsInZhbHVlIjoiSlVVKzMyYWtnY0hEdzNvUjRwRDNCUG9neVMwRDJFU0ttc2dTV1BLNkFYZWJOL0RuZDZVQ3V3dVhFR3B4TURuZEdWdVJHL2JpS1IzNzlQVjlrWXFQMUR1K1hOTTNzN0NCSWpQeXJMMnNpd2hXLy9XN3luODlmWWhtckRHWXZhdUx1WFZGWjJpTU9iSWJKb2ExKytGVWJLYytnYzRJSmFBbk54UWxLdTRtSVFEQmZsSHk3dXY0MW9wOVhERFlveVRnNzBmVW51VGR1dXQ0TFZ3QTh2ZnlqSUU3Uk1Najk5UU9sQ1pKRzhEWGlzZ2dEd1FFNVRRZzhqOUhTQWM1RkJtNUwrR0J5YnhNMEtOTjdQMloweHFnSFptTGNjaStrbEhHWGhXYlZ3UGh1UE5QckhJcnBOaDhhZ3VYOERNWE04OGUzSFpNQ3Y0Vk1NakJQbTZGL1VQMTF0czcvL1ZCYnV5UW9ZTVBkYTdBTkVPZ1o3Q0R0R2tOYUNDZkEybm1Qck05VWVWem9rQ3dZdC9PWDFFNjdSWXI1Tkh6YzhqdFJRd25ZaTNsbGhoSnVPSlpkWUFSTUZqd0xDUGordE16QmQzcExSYXBvYU5mNXNNV01VOG9pMjRHc1VTdGd5UGcwSWpiR2svdWs2MTBobGdqaW1TWmRHbFFaeUJmdk1uMkovSFoiLCJtYWMiOiI4NGZlN2NkZTZmMjFlYzYyMTFmZmIzZTQxNjJhODZmY2QxYWU5NGNkNzI1YWM3YjJiZGQ3NDdiNzAwMjU3ZmQ0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImFMKzF5Y0k2Wm42YmZTOTNLcU96dlE9PSIsInZhbHVlIjoiZUN3RU85YklzV0xaMk5qV2ZHNUxweHZmUkc4MENXZU9pL3VMNlJFYkhONjAwc2JLSVpHTFVpakxNZGFHODh4RVhPNmR2UnM5bHpqMGM0WHh5Y0hFbHZ3b3doSjdZak8xMi93NHcxZkN2SUtQRzdURUw1STRwUm00Si85Q1FQVVJldnRIK3NBaEtDODRwOUFJVTBzTEV6N0VmWlNPTTl6b09VMnhnSHdZN2Q0M3I1Mmc2Y2xWT3lIRnVHb0dGY3YzdXdJUkREdzMyOHljbW1YV3NXUS9HVmRjU3E0QWQ1bkxIVlZmeFIySm1TcFo3SjZSRUdEK1U1QjNka2poSnBrM2xveFltdzI3VVcxT0tlUlNGK0Q3d0pmU3JEVmtSc2ZNbjkvbjlZUXlpcmM3UUc1KzZzbE5qdmdScHdXTGh6U2pBRDJiUnB2NkxTS0ZPU1FRUkxJSFkyZjdPMXFXbnlpWVdKZUVYNmVqc05PRFRnN3J5RHQxMXpEVi9HRHZxeUdoQ2tncDN3ZTEyV1RQRlIxY2tSenhqcG5kRVVMd3hpVVBOWjRISlRTcngyUW9QdUVtK2NyYldSZ0ZidWp6NGRmNlZpQ01NVG44Y0VLeUh0NGFIQWFxeHkvZWdGRTVnQVkvbXhycHFuK2pOM21yZExLVDV2QlcvWkZ5YTdtYllLaXAiLCJtYWMiOiI1MjdkMmYwMzIzODU4YWFmYmIxMWQzYWU0MzNiNTdjMWI3Y2E4ZjY1ZmQ0YzNjNzA2MDkxMzFiNjJjNjVmY2ZhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZKeXRJOW9pT1o1cS9Hc3V3T096YWc9PSIsInZhbHVlIjoiSlVVKzMyYWtnY0hEdzNvUjRwRDNCUG9neVMwRDJFU0ttc2dTV1BLNkFYZWJOL0RuZDZVQ3V3dVhFR3B4TURuZEdWdVJHL2JpS1IzNzlQVjlrWXFQMUR1K1hOTTNzN0NCSWpQeXJMMnNpd2hXLy9XN3luODlmWWhtckRHWXZhdUx1WFZGWjJpTU9iSWJKb2ExKytGVWJLYytnYzRJSmFBbk54UWxLdTRtSVFEQmZsSHk3dXY0MW9wOVhERFlveVRnNzBmVW51VGR1dXQ0TFZ3QTh2ZnlqSUU3Uk1Najk5UU9sQ1pKRzhEWGlzZ2dEd1FFNVRRZzhqOUhTQWM1RkJtNUwrR0J5YnhNMEtOTjdQMloweHFnSFptTGNjaStrbEhHWGhXYlZ3UGh1UE5QckhJcnBOaDhhZ3VYOERNWE04OGUzSFpNQ3Y0Vk1NakJQbTZGL1VQMTF0czcvL1ZCYnV5UW9ZTVBkYTdBTkVPZ1o3Q0R0R2tOYUNDZkEybm1Qck05VWVWem9rQ3dZdC9PWDFFNjdSWXI1Tkh6YzhqdFJRd25ZaTNsbGhoSnVPSlpkWUFSTUZqd0xDUGordE16QmQzcExSYXBvYU5mNXNNV01VOG9pMjRHc1VTdGd5UGcwSWpiR2svdWs2MTBobGdqaW1TWmRHbFFaeUJmdk1uMkovSFoiLCJtYWMiOiI4NGZlN2NkZTZmMjFlYzYyMTFmZmIzZTQxNjJhODZmY2QxYWU5NGNkNzI1YWM3YjJiZGQ3NDdiNzAwMjU3ZmQ0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImFMKzF5Y0k2Wm42YmZTOTNLcU96dlE9PSIsInZhbHVlIjoiZUN3RU85YklzV0xaMk5qV2ZHNUxweHZmUkc4MENXZU9pL3VMNlJFYkhONjAwc2JLSVpHTFVpakxNZGFHODh4RVhPNmR2UnM5bHpqMGM0WHh5Y0hFbHZ3b3doSjdZak8xMi93NHcxZkN2SUtQRzdURUw1STRwUm00Si85Q1FQVVJldnRIK3NBaEtDODRwOUFJVTBzTEV6N0VmWlNPTTl6b09VMnhnSHdZN2Q0M3I1Mmc2Y2xWT3lIRnVHb0dGY3YzdXdJUkREdzMyOHljbW1YV3NXUS9HVmRjU3E0QWQ1bkxIVlZmeFIySm1TcFo3SjZSRUdEK1U1QjNka2poSnBrM2xveFltdzI3VVcxT0tlUlNGK0Q3d0pmU3JEVmtSc2ZNbjkvbjlZUXlpcmM3UUc1KzZzbE5qdmdScHdXTGh6U2pBRDJiUnB2NkxTS0ZPU1FRUkxJSFkyZjdPMXFXbnlpWVdKZUVYNmVqc05PRFRnN3J5RHQxMXpEVi9HRHZxeUdoQ2tncDN3ZTEyV1RQRlIxY2tSenhqcG5kRVVMd3hpVVBOWjRISlRTcngyUW9QdUVtK2NyYldSZ0ZidWp6NGRmNlZpQ01NVG44Y0VLeUh0NGFIQWFxeHkvZWdGRTVnQVkvbXhycHFuK2pOM21yZExLVDV2QlcvWkZ5YTdtYllLaXAiLCJtYWMiOiI1MjdkMmYwMzIzODU4YWFmYmIxMWQzYWU0MzNiNTdjMWI3Y2E4ZjY1ZmQ0YzNjNzA2MDkxMzFiNjJjNjVmY2ZhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313182728\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>30</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}