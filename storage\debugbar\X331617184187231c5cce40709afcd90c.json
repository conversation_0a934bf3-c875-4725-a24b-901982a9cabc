{"__meta": {"id": "X331617184187231c5cce40709afcd90c", "datetime": "2025-06-30 15:33:27", "utime": **********.199109, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751297606.736747, "end": **********.199125, "duration": 0.46237802505493164, "duration_str": "462ms", "measures": [{"label": "Booting", "start": 1751297606.736747, "relative_start": 0, "end": **********.113596, "relative_end": **********.113596, "duration": 0.3768489360809326, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.113603, "relative_start": 0.37685608863830566, "end": **********.199127, "relative_end": 1.9073486328125e-06, "duration": 0.08552384376525879, "duration_str": "85.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48082016, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00712, "accumulated_duration_str": "7.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1487498, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.421}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1598759, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 25.421, "width_percent": 5.758}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.175312, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 31.18, "width_percent": 5.337}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.1775181, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 36.517, "width_percent": 4.635}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.183281, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 41.152, "width_percent": 41.433}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1893709, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 82.584, "width_percent": 17.416}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-931059045 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931059045\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.182027, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  331 => array:9 [\n    \"name\" => \"طقم عدة\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"331\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 9\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1089948410 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1089948410\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2140226157 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpxVTFFd1BXZDdGMEJoNmhUdyt0Nmc9PSIsInZhbHVlIjoiMEsrdmg2bGZBUlpqU0M1aE9tck9neFF6ODNxK1lPRFEyZ09VY216eVRhaFVDL01nbmo5Z3lyNEhDVlhSNXIzZFlMVGo0c0t2RzZLeTNZMGNEQWRMdDBGTkdDaXZvS2tUZ1Vxblo2VnFWZExaNm5HeHBPSDhHbzFKeE95OXhudHhOZnJ0Z3dBblhyVWExc24vNGJYcXg0WEsxTCtMVWhodjlVUVRSWWE1am5sQUF0eVgvQXZIbDk3NHVDTlZ3RE5kMlRkODJHZGFXL2E5aHUyam5kMDJqMVgrVzQ5bC9Ha21wdjcvMjh1a29QdDdHQjBtazJHajhxUnErWUxvbUxHNlBvZVdNRUN2aUJOUkplcW5nUnZwUXJSbUNQbGtFbzA0dDRpa2hEbWJya0JpMWdHMVB1VitjQjB2R1JPbG5LSXhyZkxZck9RZjBnT1h5cG4vSlJBK0Jad1l4YnRhU1BWSG5LRzBqMFpWOUpDRVUwVWFTRkZvYm5KUk8wTnZXL2l5UWRsYkpiKzJYbHd3eEFmbTlxWUVuSThyTXBGWkFMVzF2ekRzV2hoL2xIWUdDZXV2cmlOVzZhb1pQelNCZy9VVTQ5aE1kV2t0ZXovZzlSWlBuT1hLQkoxd3B3WkdyeStZc3pTam9uQ2pQWlE5dHhZaFF2YS93QXp4U0tUZWJoRjIiLCJtYWMiOiI2YzM2ZjUzNzYxYmZkN2NjYjY3NjJlZDI5NGE4NDJlNTAwMTIzODlmZjgwZTUyYjQzNjE2NjI3ZGU0MGI0MTQ1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhmQ3NUTWt1Qk9EVkltY1JZeko1L1E9PSIsInZhbHVlIjoiaUNwdE1SRXZVbE9uSWQ1Wkl6MXRvU1UzbGR1ajlmeDRncENHMHZNb2JFUGhHQURtVTFTTmF0ek50SjQ0UFJlcVppWTJBTGZWUk83OUN1OVZuNGdlV3E2a1Bld0xVTCtWTlBsMllYNDJuSUhhYW1DN2RwOXVYUS91MFUvczZaVWE3azh6NmFETmdrdEpRUWdGWkV6MW1TQTRhV2JPZ2lSeXFCRDdOdS9GSy9Sam5LQ2xIUmRuZEhEMm5UTlhJcmFFQ2ZQd3lLRHQvbXcvSEkrRUYwNmZrZTA5RWRvdmZkdFQ3UDFxWTFPaHpxbFpxSzVoS1haTVlaZS9RdmVScTVvaFZvZlY0b3NjUUo3U2NZRk9NQXlXN2x4VFU0TVRJK25Fa1Q0bklkaHc4YU5Ub3JOU0Vkd2oyUHhkTEsyYUkxWmNGeTJmLzB5dndqOVNlQno5WmlBZ3RQSkJiNWxnL0pNOFpqTEYvQWxvUFpjcFhybWNod2xaVzk5Z3ZvcHdxZnV0enlUYzc5RzRkOEsrUlB6elpMM2JYOE5KSVJ2UWxpT1B6OFR1MStjOHZSNCtnRHJiUnFkY0M5aWpoeXJvQ08xTS9oWm5uR1A5bm9pS0U0UzhML0ptdTRJTFM0djEzWFFXQnl6d1prM1FYY2JLQm9qbEJPbUZLeFpiQ2R1Zmpld3ciLCJtYWMiOiJhY2Y4NTcxZjI2N2IzZDBkYWJiNzJmM2IwNDkxYTdhOGEwZDY4NGFlOGM2YTg3MjcxNjk3NTQ0NzhmN2M4MzE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2140226157\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2091454237 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091454237\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-988182502 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:33:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVPL2FIUjRLQlB6OXFZRzVLS0lRbXc9PSIsInZhbHVlIjoicS90SjgrTnJTMmJjTUhCOGdCVGdDeGU0WnFqZkl1UC96dGlkeGQxY2FuV2MvN204RDBjdlZRTmpldTBaVjR3cStjZUFzekd3Qkw3QUVVWUJFYTRvd2t2RW9FRk5oMkRBREIrMU1XWHhtUHNubmlZTTc3N1ZlUUVHRThYS3l1MU9DbDNRRldFakJQb0J1RWw0OG4weU1aSFhtNlVwVitaQTA3NUZWZ1NKTkdJR0wxSXlXaVA1dWFxWmp0SzFHaTJrTkx2UTIxVlhlcEFLcTlZRVVGQ0gySlpXaGtFZVRmMDRoeWVmemZsQ1drcFAvMjg5QTZGSzNoR0dIdTByOU82OTIzQ1FRbHhiSUZzR21BRGk0MW9DSC9RVU9hUXFhV1Fsb2NzNEFZOENBZ0txM1VHQzFSdFdjZEFvZ0VqZmIyVloxOG9oaDEvSU9qeHplQkRwZWZ0Z0sweVQrQm5UQVRlRUhvb3RxdGJZVzh2MnBmQWl6RVdWNExFazJTQjBIeDNiTXRpSHNIMnUvSW5WOWxYR0Z5ZU1rU2xsdEN3R1NXUjhGMnhTYU52SUNVQ2JSeTEyT2FGaEZlc1Z6MFZ3ZHZVZVkzNUpWWFN6ZDhYMWpDaU9qL2JWOG9EN0NoMzRIRFRpM21Hc0hiVGEwcmlFU2V3UHl0akp0dnlaTk1URjYzTVkiLCJtYWMiOiJiYTYwYjY2OTA0NGY4YTFlOTAwOGU1OTNmOWIzODFiODNjYTI1NDQxNzI0MWFiNmJlMzhhZDczZmRmZTA4NmQ3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:33:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlVSdmZPWmtmaUpaK2hydWlLSHlTcFE9PSIsInZhbHVlIjoiSC96RDZSZ05UTk5NZ2pqUDdHcFhqNDdtV05zVW16RVNvY0JJQzVoR3ppRkI5RzllVDFNY0NGa1FGcUEzdWVjeUJ1dW9mSDdQbm5sUlgwTjVqVFk0MWFlMFFVaE1KNllRb2hXV2FBcjVkRjFlajlMTXdlbVJ5bEdqNDJwTitnazh6OStNcFZLbDJLZVd3cWJKR05hUEg2dmNLMnJLU0NtbEtvRHd3cGhreDlPZkNDU3FoU3QrQVQvV2IxK1lqbmlrQ204VlB3RjJyQjFDTktSditIVTNPdU83OE9GdFA1NjBYNTlVanhyQXhSSUk0R0ZZdEttQksvMUNkQzZPY05ETW1MNE5DQTM2TUVFb3h0aFpWM2R0d3YwQUtLeUtzMjFFNmVzckNIZVVFU0txamlEbTdidytjaEkwKzlicUtMOEtqL1JQdUhDZjlkU2hmdWtTVTMrcjdpZzJHbXl0RlRGeVRLOW5aNXUzUTNUV2dicVdKN0ZTYVduSTRuVXBORDExRVYxK09DVmc5Z0JQVVplWk5IRWV5aEVDa2IzSHYxNndMOUZmSE5OTGFGclJaUysrbzdBN3R5ZlNGeFFsaHFFR1d2S1BDVm5zdVlPNlZLTG5uTGcvVlNLS0V2ckNtZXVPNmlFKzkzc251eUNzaGtpOWJTOFVrOUhZWHJEaFNBcTYiLCJtYWMiOiJiNzc4NWNjMzhhNzY2MmVjOGU1OTllY2IzMzRkOWU1NjVkYzA2OWE0YTJlNDU1MDY1MGEyMzEwYTZmODZmNjIxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:33:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVPL2FIUjRLQlB6OXFZRzVLS0lRbXc9PSIsInZhbHVlIjoicS90SjgrTnJTMmJjTUhCOGdCVGdDeGU0WnFqZkl1UC96dGlkeGQxY2FuV2MvN204RDBjdlZRTmpldTBaVjR3cStjZUFzekd3Qkw3QUVVWUJFYTRvd2t2RW9FRk5oMkRBREIrMU1XWHhtUHNubmlZTTc3N1ZlUUVHRThYS3l1MU9DbDNRRldFakJQb0J1RWw0OG4weU1aSFhtNlVwVitaQTA3NUZWZ1NKTkdJR0wxSXlXaVA1dWFxWmp0SzFHaTJrTkx2UTIxVlhlcEFLcTlZRVVGQ0gySlpXaGtFZVRmMDRoeWVmemZsQ1drcFAvMjg5QTZGSzNoR0dIdTByOU82OTIzQ1FRbHhiSUZzR21BRGk0MW9DSC9RVU9hUXFhV1Fsb2NzNEFZOENBZ0txM1VHQzFSdFdjZEFvZ0VqZmIyVloxOG9oaDEvSU9qeHplQkRwZWZ0Z0sweVQrQm5UQVRlRUhvb3RxdGJZVzh2MnBmQWl6RVdWNExFazJTQjBIeDNiTXRpSHNIMnUvSW5WOWxYR0Z5ZU1rU2xsdEN3R1NXUjhGMnhTYU52SUNVQ2JSeTEyT2FGaEZlc1Z6MFZ3ZHZVZVkzNUpWWFN6ZDhYMWpDaU9qL2JWOG9EN0NoMzRIRFRpM21Hc0hiVGEwcmlFU2V3UHl0akp0dnlaTk1URjYzTVkiLCJtYWMiOiJiYTYwYjY2OTA0NGY4YTFlOTAwOGU1OTNmOWIzODFiODNjYTI1NDQxNzI0MWFiNmJlMzhhZDczZmRmZTA4NmQ3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:33:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlVSdmZPWmtmaUpaK2hydWlLSHlTcFE9PSIsInZhbHVlIjoiSC96RDZSZ05UTk5NZ2pqUDdHcFhqNDdtV05zVW16RVNvY0JJQzVoR3ppRkI5RzllVDFNY0NGa1FGcUEzdWVjeUJ1dW9mSDdQbm5sUlgwTjVqVFk0MWFlMFFVaE1KNllRb2hXV2FBcjVkRjFlajlMTXdlbVJ5bEdqNDJwTitnazh6OStNcFZLbDJLZVd3cWJKR05hUEg2dmNLMnJLU0NtbEtvRHd3cGhreDlPZkNDU3FoU3QrQVQvV2IxK1lqbmlrQ204VlB3RjJyQjFDTktSditIVTNPdU83OE9GdFA1NjBYNTlVanhyQXhSSUk0R0ZZdEttQksvMUNkQzZPY05ETW1MNE5DQTM2TUVFb3h0aFpWM2R0d3YwQUtLeUtzMjFFNmVzckNIZVVFU0txamlEbTdidytjaEkwKzlicUtMOEtqL1JQdUhDZjlkU2hmdWtTVTMrcjdpZzJHbXl0RlRGeVRLOW5aNXUzUTNUV2dicVdKN0ZTYVduSTRuVXBORDExRVYxK09DVmc5Z0JQVVplWk5IRWV5aEVDa2IzSHYxNndMOUZmSE5OTGFGclJaUysrbzdBN3R5ZlNGeFFsaHFFR1d2S1BDVm5zdVlPNlZLTG5uTGcvVlNLS0V2ckNtZXVPNmlFKzkzc251eUNzaGtpOWJTOFVrOUhZWHJEaFNBcTYiLCJtYWMiOiJiNzc4NWNjMzhhNzY2MmVjOGU1OTllY2IzMzRkOWU1NjVkYzA2OWE0YTJlNDU1MDY1MGEyMzEwYTZmODZmNjIxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:33:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-988182502\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1206620769 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>331</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1591;&#1602;&#1605; &#1593;&#1583;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">331</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206620769\", {\"maxDepth\":0})</script>\n"}}