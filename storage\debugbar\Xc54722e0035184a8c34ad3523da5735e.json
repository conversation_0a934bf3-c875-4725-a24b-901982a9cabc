{"__meta": {"id": "Xc54722e0035184a8c34ad3523da5735e", "datetime": "2025-06-07 22:57:12", "utime": **********.507208, "method": "GET", "uri": "/add-to-cart/3/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749337031.810575, "end": **********.507238, "duration": 0.6966629028320312, "duration_str": "697ms", "measures": [{"label": "Booting", "start": 1749337031.810575, "relative_start": 0, "end": **********.36535, "relative_end": **********.36535, "duration": 0.5547749996185303, "duration_str": "555ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.365366, "relative_start": 0.5547909736633301, "end": **********.507241, "relative_end": 3.0994415283203125e-06, "duration": 0.1418750286102295, "duration_str": "142ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53610704, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1236\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1236-1493</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00661, "accumulated_duration_str": "6.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4200702, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 46.596}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.437005, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 46.596, "width_percent": 10.439}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.462266, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 57.035, "width_percent": 10.439}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.4661071, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 67.474, "width_percent": 12.254}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1240}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.475178, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1240", "source": "app/Http/Controllers/ProductServiceController.php:1240", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1240", "ajax": false, "filename": "ProductServiceController.php", "line": "1240"}, "connection": "ty", "start_percent": 79.728, "width_percent": 11.346}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 3 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1244}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.482697, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 91.074, "width_percent": 8.926}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-293827789 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-293827789\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.473635, "xdebug_link": null}]}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 17\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/3/pos", "status_code": "<pre class=sf-dump id=sf-dump-1791063938 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1791063938\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1184673365 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1184673365\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2045905102 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2045905102\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749337003065%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndFSkQvZ0xTOEc0TWFmOTVDL0tmVFE9PSIsInZhbHVlIjoiL2ZRQ2dVWVhwQWR4ZlFHT3pwd1ErSDVkSXppSnV6SWtUdDZuM1VzYlV3NUpDRHdwK2ZFM1lnQnlZTUZqcjNnbWxQZ2VxNEsxaHFCUjIyd1hOcit3SDRyY1VycFJka1hORDNBRnNCMmRhQkZXQ1hxWHdyeldERnNlUzFoNUdNeFhiOU1mYTBkUnA1d29ZYzcwakFGa2VUZjZuNCsvZXpHekUvNUo1ZHlXSkZjbzdPR3JzeDBWdDNxTmI2OUV2T1FvazB6cUJ0QzBCTGZWNjFTVHVZTHBlaVFVUlZnOE1ROVZnL2VESVBpM0w1VUkrV1dyZFJNSTlTTlBRdSt0VXdKN2FOSFZmNm45VFovK211UEFMd2srbnp5VVMxVkFhMHVpeXVNWVlCNytoSXNvNU5VbGpDRVphL0doaWJhbEVWbkVhTVNUMURVU3M5NGFHYVV3M29aRnBPcXRBMlBQazVJbk5qeGxCSkdTZWJBOVJIRW0yT3duNWVoeDRzb3FrekhjbVZsaGN1K1Q5ZEhqMEhrVzlOaHN1Vy9rRWxjWVJOUDZuTStQSUNpck1HWmZFSmpjOHdKdXBIeldJSjViSzJNMmYwT1JhbHJPRVFQTTlFejVtOUZ4UWZxTUxCeW9EK1JIcEVITEdPZFV2SWdOb0RVY1hTbTdXM0UxNUh6a3lpeWMiLCJtYWMiOiJjODVkNzk3MzJkNjA4NGUzMjk3NGIxZjA4MTRlZmU2YWMzNjFhN2FlYjlhYmQyODhkNTI2YTJmZmJkZjliMjZmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Iml4WDhJN3A5dUZQSnMrVElCSlFJVGc9PSIsInZhbHVlIjoiVGpZMlhBVVAxbWp5eHI3VE9qa3UvSStPcHpZY1VHbVpIRGc1VCt5VXcxSElNaHBMT2hEbkdjdVJNUFczSmpKTXYrb0YzdGhsMFl3dy9iOVcxa1VkWExYVDNCWC9HUFRuV1FwVmtNalhyM21QSGZZcmlEM0xaK1hnUm5pWVZhdmhTTmQxcVR6THlJRE9CUXdxdTBrTUdVNi9lNVBrSnNaY0VrNnhTbndoU0JUVkxYR2FsaDZXUy9qdE9uWnV5RDFsOWZQV1QreE9lbDZZWEtvdW9peDYvSkpaak1OUStqY2xlRnVhNFVrSjhaNGVVbi9nbm9aTW1JVmxMY1NMOEhxQjh4OFFsWUVBTzYvbm1FQ1ZzWDUybUloaHk4VUJLaGZUaEkyWmVxRmlFVi8wS09jSkdpZDNNYU5mTHRMZ0RVTHMzV05OK3lVR0dFZ0Z1UUtuOFA2TjhWeVBta3ZzYUU0dXBQcGcvM3RLN3JKRXJHWmdOcjlTbTJPRkZXc1hyb2V3VkJ2WloyTHpZWGlzYW1oV0FiMlBiUjZUd1RRWjBxMGt6UG9tUDg4Wlo2dGhqZ2dBdGVzYnF4OWR3d2lxendKNUdxT1g0dnBmRFpKL2IwN1d2NUNMTnNJRm1iRDV2OHo3RUNSRnRwMmJBQ2ErTWR4d3c3MjZITURKbXU2UEZLRHUiLCJtYWMiOiIwN2I1MDU5YzUyMDA4N2U0ZjcxMTliZTI3ODIxOTA0MjJlNGZkYTRiZjRlMTdiMTVkZjY4YjAzNDhmMDEzNWIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1975303046 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1975303046\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-109136184 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:57:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNjK2RsSkloQTFLUWJ1MGZmdDBQZEE9PSIsInZhbHVlIjoiQTBVbUtJNDhEU0xmbVJmWWNjOGwwRktyUzcwZGpIN21aSjNxS2pLWG9Oc0RWOFRhaG9YQ0dmTUVhMEtyN3dpS09wUTM2aDZsekIraVlPSWdUc25wQXR0T1QzWGJVdTI5d0l5S1ZjWnN4aXczQjVockxWSkt3S3lNMGNSTGlPWW5sb29tQ0wyNlhCczFQT0RkWk5xV2ZUUnNsam9FeXU4QklHbFNLR2VkU0NnTEJkbEZoTmI3eEltQnAwZHQ1V0lGUXhuYlBvaHoxcGpzNktIRVA4NXBPRWtLYnZ2REIyVmJ0Um4rb1V0eXVFNmora0lBWWUyTkQ5NCtjNER4SENKaVlHVG9MdU5FNHMzYVJVZ095ckptUUZvcDBKQkFBUzVXZ21lQy9NTlhhSUZmVEExcXlicVVEUE5rMWgvWjZOV3BWY2dIcTI1OE1TRDFzeFp1WE1QRDR2bE1wL1YrK01DSTIrOXlNZW41VUlpZFk0UzJwZEpONHE1Y1FVclQ2L0hkbGVDNHc0b29kbStSTjlRalU1YXVxQWk2dlJkc1BlREVjZ0kwMzhWdmFGazBLSzY3blFpeFR2am02c1FwdjdhMDJEU0tMYXNvTlNpY3lqOUQwK2ZNdTd0RVI0QitEbElybkx6cFpRMG16cmdNQUk1YzYra2tFVlkxWXNxa001VUEiLCJtYWMiOiJkOTU5NjZjNTE4ZjAwZmI1MWQ0NDI1YjQ1YTYxYzMwMmQwMjc3M2ExNjQ2YmU4NGJhNTFiYTAyNWJkZTVlYTdjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:57:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjQ1bEZ3MnFSU2dCbDZnOHV1eWhmeGc9PSIsInZhbHVlIjoiWmI5Y3FsMm5UMFBhRWRkQk5NSkVDL3hzcUJMRjVGeFdHQ0REdTVnZDAzcnBDeE11L0FIY2lsZkdQbTZTOFhMRGZFMTFFcnFKKzN5MHk2M1dTVk8rQVRCSzFXUFRTOExnQWZ5SGd6MWtjaWZWQkZ1K2NMSzkvMU9Ldmk2L0phVEgzeEIxT2pVVis0QkQ3M0FkczJuVVlFR1NRalN2Y0ZDZzlSb2FEeThpR3N6SE84dk9YeUxtSlg0ajh4cUdNL2xUbktyQ1lOQm5CRlpRbnF3U29jeW9YeDhsdnNpWFVidWdhK3ZVczcyT25UdzI5bk5nZ2pTSG0zamdpZ0ZIWTBqVjl2MDl1Qjcva0FRR0R3UnVPZHp6c3I2RjNDcTJzY0tSUnhiRDd3bVVCbmg2V2hBTVhJak5kMklYT3JPYjhSa2NMNUJTY0Y5a2cvMGNYMjRXRENXRXdNR1haSitzSG1nS0ZhOXhrMUVvYTNheFdKa3FMdmJsK2t0SGc0QVE2NEpmMTV3c3ZZd0p4c1BDK05yb29NdTV2WFRkeC9IZk5oU1dkSzlNTy9EeEJkdi82YlZPcmQ0azltSHptaVBJNVJ2aFM3NEgvN29VRjUzMjZFR3lJY09IdkEyVWVQU3VZemtSNm01eSsvWG1qbksweE5WSGx2SUgrRVdkcnBTUTBZQi8iLCJtYWMiOiI0OGViNGEzM2QwZDRmMmExMTgxYmI1ZDc0OWFlMzIzNDFlYmNhMWRlYWEwMjkyOGIyNDk2ODEzNWJiYmE5ZDNlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:57:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNjK2RsSkloQTFLUWJ1MGZmdDBQZEE9PSIsInZhbHVlIjoiQTBVbUtJNDhEU0xmbVJmWWNjOGwwRktyUzcwZGpIN21aSjNxS2pLWG9Oc0RWOFRhaG9YQ0dmTUVhMEtyN3dpS09wUTM2aDZsekIraVlPSWdUc25wQXR0T1QzWGJVdTI5d0l5S1ZjWnN4aXczQjVockxWSkt3S3lNMGNSTGlPWW5sb29tQ0wyNlhCczFQT0RkWk5xV2ZUUnNsam9FeXU4QklHbFNLR2VkU0NnTEJkbEZoTmI3eEltQnAwZHQ1V0lGUXhuYlBvaHoxcGpzNktIRVA4NXBPRWtLYnZ2REIyVmJ0Um4rb1V0eXVFNmora0lBWWUyTkQ5NCtjNER4SENKaVlHVG9MdU5FNHMzYVJVZ095ckptUUZvcDBKQkFBUzVXZ21lQy9NTlhhSUZmVEExcXlicVVEUE5rMWgvWjZOV3BWY2dIcTI1OE1TRDFzeFp1WE1QRDR2bE1wL1YrK01DSTIrOXlNZW41VUlpZFk0UzJwZEpONHE1Y1FVclQ2L0hkbGVDNHc0b29kbStSTjlRalU1YXVxQWk2dlJkc1BlREVjZ0kwMzhWdmFGazBLSzY3blFpeFR2am02c1FwdjdhMDJEU0tMYXNvTlNpY3lqOUQwK2ZNdTd0RVI0QitEbElybkx6cFpRMG16cmdNQUk1YzYra2tFVlkxWXNxa001VUEiLCJtYWMiOiJkOTU5NjZjNTE4ZjAwZmI1MWQ0NDI1YjQ1YTYxYzMwMmQwMjc3M2ExNjQ2YmU4NGJhNTFiYTAyNWJkZTVlYTdjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:57:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjQ1bEZ3MnFSU2dCbDZnOHV1eWhmeGc9PSIsInZhbHVlIjoiWmI5Y3FsMm5UMFBhRWRkQk5NSkVDL3hzcUJMRjVGeFdHQ0REdTVnZDAzcnBDeE11L0FIY2lsZkdQbTZTOFhMRGZFMTFFcnFKKzN5MHk2M1dTVk8rQVRCSzFXUFRTOExnQWZ5SGd6MWtjaWZWQkZ1K2NMSzkvMU9Ldmk2L0phVEgzeEIxT2pVVis0QkQ3M0FkczJuVVlFR1NRalN2Y0ZDZzlSb2FEeThpR3N6SE84dk9YeUxtSlg0ajh4cUdNL2xUbktyQ1lOQm5CRlpRbnF3U29jeW9YeDhsdnNpWFVidWdhK3ZVczcyT25UdzI5bk5nZ2pTSG0zamdpZ0ZIWTBqVjl2MDl1Qjcva0FRR0R3UnVPZHp6c3I2RjNDcTJzY0tSUnhiRDd3bVVCbmg2V2hBTVhJak5kMklYT3JPYjhSa2NMNUJTY0Y5a2cvMGNYMjRXRENXRXdNR1haSitzSG1nS0ZhOXhrMUVvYTNheFdKa3FMdmJsK2t0SGc0QVE2NEpmMTV3c3ZZd0p4c1BDK05yb29NdTV2WFRkeC9IZk5oU1dkSzlNTy9EeEJkdi82YlZPcmQ0azltSHptaVBJNVJ2aFM3NEgvN29VRjUzMjZFR3lJY09IdkEyVWVQU3VZemtSNm01eSsvWG1qbksweE5WSGx2SUgrRVdkcnBTUTBZQi8iLCJtYWMiOiI0OGViNGEzM2QwZDRmMmExMTgxYmI1ZDc0OWFlMzIzNDFlYmNhMWRlYWEwMjkyOGIyNDk2ODEzNWJiYmE5ZDNlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:57:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-109136184\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>17</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}