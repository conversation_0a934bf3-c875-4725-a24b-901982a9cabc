{"__meta": {"id": "Xe898327e6ba136c4a289d0449dda0a7b", "datetime": "2025-06-30 14:59:43", "utime": **********.762359, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.2539, "end": **********.762373, "duration": 0.5084729194641113, "duration_str": "508ms", "measures": [{"label": "Booting", "start": **********.2539, "relative_start": 0, "end": **********.699924, "relative_end": **********.699924, "duration": 0.44602394104003906, "duration_str": "446ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.699933, "relative_start": 0.4460330009460449, "end": **********.762375, "relative_end": 2.1457672119140625e-06, "duration": 0.06244206428527832, "duration_str": "62.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45226840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0025399999999999997, "accumulated_duration_str": "2.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.737502, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.709}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7540522, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.709, "width_percent": 19.291}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1587715483 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1587715483\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-104199325 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-104199325\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-869437911 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-869437911\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-12050531 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhzOGFFclBhVVFzNHR6WXJxN2JhREE9PSIsInZhbHVlIjoiMk4vU0NxMjk0ZDlZVmt2c1BjVUN0Y3JwNnRiYXdSaktHZnMxQVlGcHBCTlFvQ3NKR1pMN1h2SjU5d245YnVWLzIrUWQySlhBSzVoRkVLdnhDL2FCbHFXczMwcnJiUVJhT2VLOSt2YzJ5TTdrK2xDb2gyNGpvL2JxL2xpWWxEZXYzWWFTR0tJRWNpQ0xHTXJCRXlPRDZxTU9TMkRMMzRlQ0I3UmZkd0ErM3FJbUd3U0xLMVdMR1Y0S3pUSktSRXZTSThUemd5Y3UvSC9zdkNldFdFRUVrQTlhb0kvWVlKa1UvM1BWVUFFSGZENlVIZHZKcHBpT25jV3kwWTBoRkdhSHZHTThHT0VkN0Nldmtsb29ZZWxEUWdHTW5uR3Jra0NuTFBwd1dQRjhNV3pqRVQrbW1mSEk0SDRFYlB5NHZnQkJZRkJVRWVTVTRucFo5dndCNW1kQkN0WFdEYmd6d0ljclVBc3BLYUdZKzBPS3Uxc0hkdFNPZGQrOWtCTjY4Z3dxMHhmV2EwZ0hQQUwvYnhHQTNjZzJsSGdMLy9STVk2cHJNM2tSblV2djdpNktHb0VWeTYxQXJubTdMY3pJNThRY1I1MEt2TjdkTkVndU1DM2dQNk44bWduaXhvR2hnY3JrL1FubDhIZ0VrcEE4eE5vazFoQzcrT3NwZnNocE51ZFYiLCJtYWMiOiJjNzQzYmE3YWJiN2JjNjFiZmY5MWM0NjIzY2QwNTQ5ZWUxOGI3ZDZmYzM1YzVjZTEzMzM3NmU1MTdjNTY1YzAzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkkyNWRWNDhxb3ZBSlNzOXU0WGVjL3c9PSIsInZhbHVlIjoieXpYM3dBRVNFejFvMkFRUGt0ZnRNbUdHN1ZCaktlMEtYb2JpUWFtNVZ4c0NBbkx2OVdueUtRWnlPei84SFg1cklPT1FwWExnQkx1TmFFMFk1VVM5NzBobFFyd1JmOWRWRTRhRDNnazdIWDUrUFUxaEVTWlduNlRYNW8rR0cwUXh0UjRrL0xtaE9kWXF0d1VlV21ya2tXSUlCZzBCcVVJUUpnM0JmaFJNRWlmMmJySFI5UVBvTmhab3MyalBLRUhPdGt5SUFZbGFYUDhyVUt2OU0ybEhTNkZSdVNQMmtkR0x2TlJabkFNYmFKZWxjUFAyNHVZNHJBcllTWithbTFUSWRLSjQwU2hRNHlLSjQzYzFucHRISDZqcUpRWXVOUHkvR1hoUFVHRVNRZXRScEk1MkRBZkhDQlZ6ODBZRG1RWVQ2QklObHJYVy9RU01HNTZLaFUreUcwSldOUUxlbVRkb1RNZGhiYVpkY0N6dUplRC9SejFiS29qblpEZlBqK2daM2txVThnWXZMSnl6TDNFMW9RU1JQandpd3QyUTZMQWl2SkdRMU9iWWVYRmFCbjdOdmJiNGV1V1RFRHRPKzRVL0wrZkxnQkdpTkNnSEt0d1Bmb1pHaFVrTlo1YjQzQ2lBbStkVHpON05WQlAzbHZDUnpJcGorYTN3MU5aRWZua0EiLCJtYWMiOiI2ODM5MzViYTk5MmM4YTdmZjRkNTgzOTFmOTVmYjdkOTMyZTJkOWQ2YzdmODY3ZGE0NDIyYzliZDE0ZjRlYzIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12050531\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1240533971 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240533971\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1942411908 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:59:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndxZGZtMWJhQWNlS3AvZXFGQnJNbEE9PSIsInZhbHVlIjoidlVSczU2bTFSRjIzOWZBK0JGcjdwTG5DdGtpUStZdTFMRFFzVWtwR28yZUdYV0tVeEFlbEhXMHorQkdabHNlby9iTGp6NFNUZkMwL1FkemRGdm9TV2diSXhucU5GcE9xNzVvWXlhVyt6RTdoVzYrS2NSR0VaNHdtRk53QjNhNStud3dSb2NySDRRczlPRVcxNGQ4QU5KM3ZYMkJqS0pBd1hLWUxHT0xCbGxqditUUFhKN1YyM1grRVozMFhxUDdpL2ozL3l5dmRraHo3b2FxVndvQzlTQS9hSzFnS2VZY2hRZ2RpaEluVEs5VWlqRzFhRFBvNW5iVjg5ODR5Rkw0SnkybTVCQWYwNWY3c1JldDJPTkxmZFhVcGZSOE44cFlXeVlMMjdrbm5EaXpxZ3l4WTQwYnZvcWlld1ZOTm5uSlhQVTB2MmJpdW1ZVkNJOHBwb2NMYmxwMlVwSGhsbjQ0V2I2dDJscDJGa2RoY0Q5SDhuelhOajR4b09ScTh4ZTh5WmNWU3lhemhNVVVEdEgrM21Ec3ZySHlWNVhmUVhYQ2tMRy9tcCtRNDU4bXNweXJTR1BYMytmZU1SRFRyNW5GTmNmOTN5MlpZSVJrWElZRnE0aE9BT1RYQ05pNC9iRWZXTUFsN1FTUmpJVW8zUmRNWXVkczQ2Q1JsVkhJVEZ6bmIiLCJtYWMiOiJhMDFjYjM1YjUzN2NlOTZiNjk3Njc5OWRiNmRhZWM4OWI3ZjUxMDc1MzFmYTRjNTUxMWJhNzc4YjY1OWZmZjE5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:59:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImlZSEpKYWp0Rms5YW5rT1lLekRWTVE9PSIsInZhbHVlIjoibnJNNEU4eXNlTm1KOFZxWUhiTjJ1RDg1OWxVVkpYZlQwRnEvQWFpZ1JvTnllRXNmMWdPWUp4bHJ4VGlOcEx4Y2g0QmFWNGZVUmUxSjRNMElRVzJGRHBEVkNGdnY1czlMdndIRTVHVWkxVWRhdG9JWjJYamRLOHNiOWtBVk5yY0h6MmxGSWFKZEtRTkI0Tk5kWnBjeGdLL1RISnFENC94aXFJWERvMjk5QjQ2Y1FtdWxjbE5Ua216VlFRRHpnR0pEckp2VGQ1cXBMWEFIRk8xZVE1bmlDMEZMeStFUHh6WTBhR3A0eEp3cHVRWm5tc0xGU09ZTHo0S2k4eVpNamJBbEhweGU5WVVXUHM4eGdsemVwR2FiQzB6bjNUNDRYQjU3ekd2NjRMa1pjaDg5YlhDOHFIYmd4d1ZDTU5leG9LaW0wRGFBZ2txZnBzVVZDaVlVZlE2TFFoUWhBZ0w1STZob0lzYSttWFM4VElFRHZ4Um1WTVBCRUd4aURTQTU4RzdOSC9lczErMzA3aTZPZEkzV2ZYaVpuWUFPUmdvOGRuSHlVOEF5V1BFQlo4VGhtOFZ3dDBHc0t5UFdOUXpFZlVTQXZ0ZjRoNmFFb3kyZmIwZ2duckZJNWpUalhWWUpvb2kxdVRSdU00RStiK3FMa1JwOGVLQkxFV0pUMzVuYktpb3giLCJtYWMiOiJiZDNmMmM3NDRiNTdjZGEwZDkxOWU4OTFmZjljNTA3NGEwMmE5ZGIxMDNkYTk2ZjM2NDdjOTFkZDk0NTZhMzUxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:59:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndxZGZtMWJhQWNlS3AvZXFGQnJNbEE9PSIsInZhbHVlIjoidlVSczU2bTFSRjIzOWZBK0JGcjdwTG5DdGtpUStZdTFMRFFzVWtwR28yZUdYV0tVeEFlbEhXMHorQkdabHNlby9iTGp6NFNUZkMwL1FkemRGdm9TV2diSXhucU5GcE9xNzVvWXlhVyt6RTdoVzYrS2NSR0VaNHdtRk53QjNhNStud3dSb2NySDRRczlPRVcxNGQ4QU5KM3ZYMkJqS0pBd1hLWUxHT0xCbGxqditUUFhKN1YyM1grRVozMFhxUDdpL2ozL3l5dmRraHo3b2FxVndvQzlTQS9hSzFnS2VZY2hRZ2RpaEluVEs5VWlqRzFhRFBvNW5iVjg5ODR5Rkw0SnkybTVCQWYwNWY3c1JldDJPTkxmZFhVcGZSOE44cFlXeVlMMjdrbm5EaXpxZ3l4WTQwYnZvcWlld1ZOTm5uSlhQVTB2MmJpdW1ZVkNJOHBwb2NMYmxwMlVwSGhsbjQ0V2I2dDJscDJGa2RoY0Q5SDhuelhOajR4b09ScTh4ZTh5WmNWU3lhemhNVVVEdEgrM21Ec3ZySHlWNVhmUVhYQ2tMRy9tcCtRNDU4bXNweXJTR1BYMytmZU1SRFRyNW5GTmNmOTN5MlpZSVJrWElZRnE0aE9BT1RYQ05pNC9iRWZXTUFsN1FTUmpJVW8zUmRNWXVkczQ2Q1JsVkhJVEZ6bmIiLCJtYWMiOiJhMDFjYjM1YjUzN2NlOTZiNjk3Njc5OWRiNmRhZWM4OWI3ZjUxMDc1MzFmYTRjNTUxMWJhNzc4YjY1OWZmZjE5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:59:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImlZSEpKYWp0Rms5YW5rT1lLekRWTVE9PSIsInZhbHVlIjoibnJNNEU4eXNlTm1KOFZxWUhiTjJ1RDg1OWxVVkpYZlQwRnEvQWFpZ1JvTnllRXNmMWdPWUp4bHJ4VGlOcEx4Y2g0QmFWNGZVUmUxSjRNMElRVzJGRHBEVkNGdnY1czlMdndIRTVHVWkxVWRhdG9JWjJYamRLOHNiOWtBVk5yY0h6MmxGSWFKZEtRTkI0Tk5kWnBjeGdLL1RISnFENC94aXFJWERvMjk5QjQ2Y1FtdWxjbE5Ua216VlFRRHpnR0pEckp2VGQ1cXBMWEFIRk8xZVE1bmlDMEZMeStFUHh6WTBhR3A0eEp3cHVRWm5tc0xGU09ZTHo0S2k4eVpNamJBbEhweGU5WVVXUHM4eGdsemVwR2FiQzB6bjNUNDRYQjU3ekd2NjRMa1pjaDg5YlhDOHFIYmd4d1ZDTU5leG9LaW0wRGFBZ2txZnBzVVZDaVlVZlE2TFFoUWhBZ0w1STZob0lzYSttWFM4VElFRHZ4Um1WTVBCRUd4aURTQTU4RzdOSC9lczErMzA3aTZPZEkzV2ZYaVpuWUFPUmdvOGRuSHlVOEF5V1BFQlo4VGhtOFZ3dDBHc0t5UFdOUXpFZlVTQXZ0ZjRoNmFFb3kyZmIwZ2duckZJNWpUalhWWUpvb2kxdVRSdU00RStiK3FMa1JwOGVLQkxFV0pUMzVuYktpb3giLCJtYWMiOiJiZDNmMmM3NDRiNTdjZGEwZDkxOWU4OTFmZjljNTA3NGEwMmE5ZGIxMDNkYTk2ZjM2NDdjOTFkZDk0NTZhMzUxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:59:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1942411908\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-984663499 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-984663499\", {\"maxDepth\":0})</script>\n"}}