{"__meta": {"id": "Xf70d5b477a54f591cab1d8c9fec08edc", "datetime": "2025-06-08 15:34:17", "utime": **********.605414, "method": "GET", "uri": "/roles/19/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396856.589526, "end": **********.605438, "duration": 1.0159120559692383, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": 1749396856.589526, "relative_start": 0, "end": **********.2734, "relative_end": **********.2734, "duration": 0.6838741302490234, "duration_str": "684ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.273412, "relative_start": 0.6838860511779785, "end": **********.60544, "relative_end": 1.9073486328125e-06, "duration": 0.3320279121398926, "duration_str": "332ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 55587704, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "1x role.edit", "param_count": null, "params": [], "start": **********.433863, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/role/edit.blade.phprole.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Frole%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "role.edit"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.511924, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}]}, "route": {"uri": "GET roles/{role}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "roles.edit", "controller": "App\\Http\\Controllers\\RoleController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=99\" onclick=\"\">app/Http/Controllers/RoleController.php:99-127</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.032959999999999996, "accumulated_duration_str": "32.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.315343, "duration": 0.02148, "duration_str": "21.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.17}, {"sql": "select * from `roles` where `id` = '19' limit 1", "type": "query", "params": [], "bindings": ["19"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.343859, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "ty", "start_percent": 65.17, "width_percent": 2.397}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3563838, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.567, "width_percent": 2.913}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.3807662, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 70.479, "width_percent": 4.066}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.385362, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 74.545, "width_percent": 3.307}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 2", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 114}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.393041, "duration": 0.00491, "duration_str": "4.91ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:114", "source": "app/Http/Controllers/RoleController.php:114", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=114", "ajax": false, "filename": "RoleController.php", "line": "114"}, "connection": "ty", "start_percent": 77.852, "width_percent": 14.897}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "role.edit", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/role/edit.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.4968019, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "ty", "start_percent": 92.749, "width_percent": 3.823}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 19", "type": "query", "params": [], "bindings": ["19"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 996}, {"index": 24, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 938}, {"index": 25, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 911}, {"index": 26, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 888}, {"index": 27, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 851}], "start": **********.514346, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "FormBuilder.php:996", "source": "vendor/konekt/html/src/FormBuilder.php:996", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fkonekt%2Fhtml%2Fsrc%2FFormBuilder.php&line=996", "ajax": false, "filename": "FormBuilder.php", "line": "996"}, "connection": "ty", "start_percent": 96.572, "width_percent": 3.428}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 521, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 525, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1688090372 data-indent-pad=\"  \"><span class=sf-dump-note>edit role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1688090372\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.392135, "xdebug_link": null}]}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/roles/19/edit", "status_code": "<pre class=sf-dump id=sf-dump-446775175 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-446775175\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1970713434 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1970713434\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1127061363 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1127061363\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1862587039 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396852693%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9Fc3FMbWNCQm81b05kU3IzY0loSGc9PSIsInZhbHVlIjoiOGJ5eGF5L1pTcDl6bTRnUy9HRHdnYy9tY0VHMjl1STFJSGFqTnNCUnlEazk1eHc5dFZsZFhxWG9pOW1hSHFqcm0wUitvRThBc0dmcU0xdERYUkNEZ0Q4MElVdmRiU2k0UTRIY21ia1VjR0RPalIvQmV4K2ZQaUR4YlJLTElsVExLVjIzbzRTUThTSEV4Nm91Y3l5Z0l3aXNhSGhYTUlZZUdobnRacFFoNkRyd09GN2ptWnZxbUgyTlZ3UytjQUZIbDYxTlBoQTVlTS81MnpYRWJ2MkJ4TUFPbmxVWlA0ZTd5QVNlQkFyMzlpTzJBMGlaN2I4ZXdvU1E2Wk1iNzVnaEVyN1ZCUGhGazg3dWhzd2wwT0lKQnJjR1c5TEh3UmQwOERjV3hkWXAzTkU0dkxqckh1YUdDSFZLa1pCenU5eGhXLzh1QW9CQUcxRkkzVXdBYkZPbzdTZU5SR3hFT0trdlNVZUxPZ0UxSmtzUlNYbDV1S3VXanptSWFScGtMcDliUktyU1ZvUWNwaFhsR2hubmxEYmdXL2loWE03eWtzK1pscXVjT0ZMMVFQR1BGbktMbE1MdDlaVHhuSDNpRGtzcUhIcERJYkZRRWVOaHFzdUZXNmZpWmkxT1l0TWpkWC8rZG15MEtjL1Y3c0p2R1BiTnptRVo2NjZ2cjk2R0xHV2ciLCJtYWMiOiJkZjAxMTQwMGI4NGUzYzVhMzk5YTgzN2JhYTYzYTA4NzUyMjQ3Nzk3MGVjNGNhNGYxYWQ0Yjc0ZTI4ODEzNWM1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImMzdHM2NVZrQWkvWnZDeTNpN2dQVmc9PSIsInZhbHVlIjoiaE1TMlB1dlVZOU5GQ1FJRGZNSGV1bUQybFZsdzZhOUl0dUF1M3ppdkVENWpUMjJyN3VIeDdVNVd5OXFHb2pxdUs3TCtOY1l1cWEra0htZjBxNWFYZ3VSTStwZ2k2anhUWVRFRS93NFdZQzdRclBUWmd5clR4MldJOWZKbUNhTzMwNWFzQTRGVEsxUU5GNGFZQmFSNllneDA5WFkxNkJaOEVTNkVyUFI2ZTBBb0Z3Rlo2OVFSUXdZYTdPbjgyNk0rcnprbVdMelFsZ0Q4a2IzVTRXTGRwYTZ6SkFqMzdSYUFZNTdsZGpianlIUVJuOFU3YlByQlFoSDlHRnlRTFo5SURuUXB4eHVJcktQeEsrcStHZ0pKVFJqV2V1WlZHN2hpRWQyV25Tdm1Nc1RUUTlqZm1LVy94SUd3RTBwbFBtRXF4czB4TklmcWFkVlk4WFg4VHVoRjFNSzFPb3UycmdiQW5WS3hQMTZyMnRNcXExUlRnVlRXS1Fpam9rWFZ6YkwweEE0aG9mb2tDekFwT2FHUG1hWDZJcU5EU2RGdU1SR2NGQlp4QnExSi9mUEtUOHlRUWV0N0lOOGpJbTZ1WWh0V085alN2blhRT1YrMy9nMnRrdjFWOUg2cVB3WEpsYWppS2ZDbzZjcjgwam5pd1hXb1lOVTZiMXU3Um9yTVRGZ0wiLCJtYWMiOiJhMTE5OGExNjkwNmU3YmEyZjEzNTg4ZDI2NGQ3OGQwNGQ0NTNhYmYxYjZiYWE3MTZkYjQ0OTYzZDQ1ZGFlMjU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1862587039\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1338601758 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1338601758\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:34:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllSV0oyS2NZNzZSNVcyYXVnRGZ2Vmc9PSIsInZhbHVlIjoiNUgvb05LT3FwWGZCQXQ1Rjh6NmZ4MFh2R1VpQk1ZYzNwdmUvR01sZ1NkUnRVTlA3NDlud1F6ejhRRkg1Z2RLQ2lSVDJ0Qlppbk9LUHE1QTVQRlJ4NGtBdXVlTTlOUUlSSHRJUEFNa1dHZDBUbE9Ed1BNMDBDdHg0aFVwWmYxeHV3SHBFVGhnUjNkVWIzY3hWNG5YRTFJSW55c2hQR2RNMmUxTUxOWlRZRlFFTWFXWmpSN1ZCRmhxd2o3NW9jVmErYW1pVWp1ckYrOG9jM29MRTlaTitObW1KRGZsQ09GNmJhazE0eHV1bEU3OFRuSGVLWnVpd2pjU3JBZXBTbDFKR3BMd2FraGYzencybWttZWlqblJVRXRxWkd0b0QxWEU5WlpZTDN6b0l5aU5DeHZGdUg5WVJMR1NkWW4rZlkrRmZhdjZaaUh0anNaL2FJL1pGbXg0bVJOTmtoTFMyaWRSSytIdWhkOEx1bXc1WFE1Szllcmk2VUZqa05mTWpUNGJOOXp0SSsrVFc5aGhTNlBIdFlJMk9MY2IyT3NVd2NleG9sVm81ZkdjTkZseXZPUVZoUGZMWEV2ejE1Q09FQStHQXlXaDN6cjVZdHkyNWdKTS9ZNXg5OFRKK0hFV1JnaE1nMml2WDV0Zkt3Smk5TGU5b3RlRnRBYjkzTDJ1dmt5dVUiLCJtYWMiOiIzZTFiZWYyYTM3MDk4ZDFmZGYwMDUyZmRkZWVkNDkxYjE1MzE3Y2YwZWIwOTc4ZjA5OGM2NDg0N2RjMjkyNWMyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:34:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlBPSVYyME1SKzJQVE1qVC9GOGc0a3c9PSIsInZhbHVlIjoiLysrMjd2Q011NDlYblB5azBKbHFxZjltdFZiYWJNNjdiU00yV0pGUzhwbTJacmQzWktNUnZhakpTRXJtcE9iYlBYM2N5Rll0WFgxQmRiczlDRkhDQ0pUcXlzMlpNSjJqR3lNaHo4S24rWEFhYjNlWW4yQ2RZT1NEcUM2c3V0cWdCZkZrdGw0eHVoZGVleG9rY3J1QzZ4d3RORkpHOVNBZmlzcHpqVUVtbDF0NFE3TWc0eVJqdlNWTC9pczlDcHZqWHNZZTN0d1Bqb01tTmpFY05kV1Zra1ZUWW1oeEx6R093YWQ2V0ljNUh6Q3ZTNzlPVC9FdWNsc3E4UnZCc1FVZVA4NlU0VU85MzRiR3RsNnlFbjNtSXduZE9CcGJZNUZwRjN0MTJBelpBdHA2TCtYSDF3K1d5bUN5alp1YUg5R0tZa2c5WkMycko0UzU1eFZiU1dnYlUzTkJUOWJmaGdiUFZQOXFaRlBjSkxMdVREZ295OE13V2tTY21TdlJhYThvRmJnM3haMHAvZHp0Tmo3YmhMaDRuZ3ZaRy8wckZISHJ2THUrMFZHcnZSOUdFRmxuZ0RQSnJqUTAxeDFMcEVVUTU0UmloMW9lSzEzSmczMUF5d2lEZmErdXBhL1k1NkJmWGk1R2wrbUNWUndXYlBGMit3dWpNN3dQUHViNDY1SzIiLCJtYWMiOiI4NDA3ZDcxOTY0ZTMwYWRiYzNiYzI4NWEwYTJhNzU2YTMyZTQ5MDMwM2Q0NjI3NjAxMjhjNjZkNTUxMmRjNmU0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:34:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllSV0oyS2NZNzZSNVcyYXVnRGZ2Vmc9PSIsInZhbHVlIjoiNUgvb05LT3FwWGZCQXQ1Rjh6NmZ4MFh2R1VpQk1ZYzNwdmUvR01sZ1NkUnRVTlA3NDlud1F6ejhRRkg1Z2RLQ2lSVDJ0Qlppbk9LUHE1QTVQRlJ4NGtBdXVlTTlOUUlSSHRJUEFNa1dHZDBUbE9Ed1BNMDBDdHg0aFVwWmYxeHV3SHBFVGhnUjNkVWIzY3hWNG5YRTFJSW55c2hQR2RNMmUxTUxOWlRZRlFFTWFXWmpSN1ZCRmhxd2o3NW9jVmErYW1pVWp1ckYrOG9jM29MRTlaTitObW1KRGZsQ09GNmJhazE0eHV1bEU3OFRuSGVLWnVpd2pjU3JBZXBTbDFKR3BMd2FraGYzencybWttZWlqblJVRXRxWkd0b0QxWEU5WlpZTDN6b0l5aU5DeHZGdUg5WVJMR1NkWW4rZlkrRmZhdjZaaUh0anNaL2FJL1pGbXg0bVJOTmtoTFMyaWRSSytIdWhkOEx1bXc1WFE1Szllcmk2VUZqa05mTWpUNGJOOXp0SSsrVFc5aGhTNlBIdFlJMk9MY2IyT3NVd2NleG9sVm81ZkdjTkZseXZPUVZoUGZMWEV2ejE1Q09FQStHQXlXaDN6cjVZdHkyNWdKTS9ZNXg5OFRKK0hFV1JnaE1nMml2WDV0Zkt3Smk5TGU5b3RlRnRBYjkzTDJ1dmt5dVUiLCJtYWMiOiIzZTFiZWYyYTM3MDk4ZDFmZGYwMDUyZmRkZWVkNDkxYjE1MzE3Y2YwZWIwOTc4ZjA5OGM2NDg0N2RjMjkyNWMyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:34:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlBPSVYyME1SKzJQVE1qVC9GOGc0a3c9PSIsInZhbHVlIjoiLysrMjd2Q011NDlYblB5azBKbHFxZjltdFZiYWJNNjdiU00yV0pGUzhwbTJacmQzWktNUnZhakpTRXJtcE9iYlBYM2N5Rll0WFgxQmRiczlDRkhDQ0pUcXlzMlpNSjJqR3lNaHo4S24rWEFhYjNlWW4yQ2RZT1NEcUM2c3V0cWdCZkZrdGw0eHVoZGVleG9rY3J1QzZ4d3RORkpHOVNBZmlzcHpqVUVtbDF0NFE3TWc0eVJqdlNWTC9pczlDcHZqWHNZZTN0d1Bqb01tTmpFY05kV1Zra1ZUWW1oeEx6R093YWQ2V0ljNUh6Q3ZTNzlPVC9FdWNsc3E4UnZCc1FVZVA4NlU0VU85MzRiR3RsNnlFbjNtSXduZE9CcGJZNUZwRjN0MTJBelpBdHA2TCtYSDF3K1d5bUN5alp1YUg5R0tZa2c5WkMycko0UzU1eFZiU1dnYlUzTkJUOWJmaGdiUFZQOXFaRlBjSkxMdVREZ295OE13V2tTY21TdlJhYThvRmJnM3haMHAvZHp0Tmo3YmhMaDRuZ3ZaRy8wckZISHJ2THUrMFZHcnZSOUdFRmxuZ0RQSnJqUTAxeDFMcEVVUTU0UmloMW9lSzEzSmczMUF5d2lEZmErdXBhL1k1NkJmWGk1R2wrbUNWUndXYlBGMit3dWpNN3dQUHViNDY1SzIiLCJtYWMiOiI4NDA3ZDcxOTY0ZTMwYWRiYzNiYzI4NWEwYTJhNzU2YTMyZTQ5MDMwM2Q0NjI3NjAxMjhjNjZkNTUxMmRjNmU0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:34:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1345190774 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1345190774\", {\"maxDepth\":0})</script>\n"}}