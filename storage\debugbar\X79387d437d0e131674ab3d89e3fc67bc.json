{"__meta": {"id": "X79387d437d0e131674ab3d89e3fc67bc", "datetime": "2025-06-08 00:28:17", "utime": **********.477285, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342495.681257, "end": **********.477312, "duration": 1.7960550785064697, "duration_str": "1.8s", "measures": [{"label": "Booting", "start": 1749342495.681257, "relative_start": 0, "end": **********.246881, "relative_end": **********.246881, "duration": 1.5656239986419678, "duration_str": "1.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.246903, "relative_start": 1.5656459331512451, "end": **********.477315, "relative_end": 2.86102294921875e-06, "duration": 0.23041200637817383, "duration_str": "230ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45269856, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01768, "accumulated_duration_str": "17.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.412084, "duration": 0.0147, "duration_str": "14.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.145}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.454161, "duration": 0.00298, "duration_str": "2.98ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.145, "width_percent": 16.855}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1221072004 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1221072004\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-185565378 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-185565378\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-413910695 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-413910695\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-838863784 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJjZWdybTFPZE1rK1JnUHY1NTljV2c9PSIsInZhbHVlIjoiUE95STVlMW9RYmoydUhFN2FwQ1E1MUNWRWFiTXE5TGxsQzNZKzlXeGdxQ3VuRWhUTm9hRmhsam00a25uWXVEdXZjSWVFT3IxamFXMDRLeHV6N3QrTE9kRUdYejhPR3lzUGdMV3J3RUJaOHBKQWM5Vmh4b0M5Yk4wVUpJNHFaaXZOK01VbUY5ZTJLL05xbVdDcG1IRGkyWDBOeklvK2JZaE5NTGxaRllhajFjOWUrbFZ1ZFFhbG92cnVOUE82R2hQaVFmaHd2RkJ3c2ZzZTJhNjFuSUg4MVZjMGVXaFJIRHhtZVpybFZ6N2dSTllvbkhCa1czOCt5eVl6NkJBeFkzRzlwZFFTZWhZdXN1ZENnbWdGNHpBQXRaSWQ2TzdianE0L3NXYTVqQ3BUUDhrMWYxQ3dCR1dqeGxwUVp1M095SUVHS2FjYVJjNjY2ZmxzSGtuL2xCWGcwMmpUUEJuNE51enhCVlVCbXBuU29BOGVRWDR0aDkvVndIbUJMV2ZnNWFVcjR3U2I0U0VEaWM1R2p4TnBaVkVwbGhPSnNrVFdaZlB1emtoeWJ5Zk9sa0dWcHhlZm56WkpuSmd5K3R6dGowQkptV3RQVEdmVmtTdDBRcHV5ZTcxbDZ0ZDQwL2dxVDZxTHJoRFVFNlgrTGdWMVBzS3VldWk1NktGK0NhZXVwVmoiLCJtYWMiOiIyNGFlMWRmNTRjZWY4ZDU1MTcwNzgyYjAzYzEzZjlmMjk2NWM4NzMyNTU2NTlmN2MyZDNiZTg5ZTlmOWNjZGE0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IldTRzBiM28raW52OGxlMlM0NlBOTlE9PSIsInZhbHVlIjoidVlJRndTRkloY0hKN0FBM051UEdtUzdhSTE4VmpPdkpKRGE5QjhhN3E0RXl6aXo3MHpldDZseUUvUEJ2RmFHenZhQVEzczhJMC9lNXNtREpoUXplWDBvT2RXcjB5NTU2dGQwTjVqNnJVZ0cxeXFGWUJEeGlDNUhYY1hMbmJNb1pEekV0WHVRemZKa0NoOEZveEVIMy9lNm9rZ1V1cW01eFhWNnhZRG10NTNvbHZoYUV1M3ZiZUkwMzRzaFF1T1NDbWtsTDg1eURhek5GQUVKWkV6MUwybnVpTGZ4R0dYeUZuLzF1eWF6aUNRVzZicU95ZGdmTlJldmNsL3N4YW8wYkVCNWZuT1VLd2g4aXhwY254VGN4dkRINWVKNkkrOUVIODhCRWVsTEJ5U2ZLVVJxU0N5K0FZR2xNMFZLNTJjL2FydzRMdlZVTzhtNkZ6cElwVGdQNUc1SUlrWVZTc0gweUduYTJjbk9WZW5QdXE0S3kwS0pscGJ4MVBoVDJSNVNEbjNsNm96czYybjlzVGhCeWxiT2RFUWJmM3JTVzFidnZKQUo3OEhLaThIdE1DKzRWVUZLMUV3U1FyRlZBelBnTUZ1SnNWM1lFRXRPd3JLVmxUNGRZSnYwaVIwWnpIRUJoeWJabE5iWTRpNUEzQjJKQ3lDNHA1RC9GWW52QU42clgiLCJtYWMiOiI2Mzk4Yzc2MjdlOWUwZTcxMThhZWVkZDlhOWQyNmVhN2ZkYjIxMzc3Yjg3ZWU1NzA4YmExYzk1YmYzZWE5NmY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-838863784\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1246863354 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:28:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBNQUFHVEMyeGd5YXBkczJlaW4vcUE9PSIsInZhbHVlIjoibHp3WUwyeVF5SmFJV3FwSjQ5RWtOQVEvdG1YbFBQSzVhTzRzWWJ2TnliZ0lrQXlrVkJJMXVsTURucHhVYU91cW9RUm9ZcTRldTFNaG9XNURuMEhVNXpMSGFodWVTMmJqRFZlYTNsT1Y4R3NQOUpTM3lrMUNWbWM3cEk5d1NGL0N4VGpLbE1ja3ZtNW4vUTB1aDFzWC9maTZZQkZuT1lyUlY2YTA0SkQrTnFlTXEwMCswdkRDSGl6cEtNR0pGV0xPemJBMVgyMk5NeHJRUFpIRUZBc2hMU1V3c0k4eHBmZlFZdHpINVA4Nmo1VVNqdjA2cWErQWFTTzdwalFIRlVlRjJGZVJaaEw2VnBicEZ6MkdHYjQzSHJOdnlHblVWbUhXeEFWSU5zWFlqR0h2ZE1mS3VHZmFiMlB4QVZiZzd6UVZjL3FJcDZDVXg1eHJBbEl4NUpId1RYb2tjMWNQK2pYQlM3UHdiSHdDRk1vUXY1UXczZTltTVJrNmpJbXR0MVdocVVpUi85RVFoWFVLc2hucFdsTEhSTUlRUjJjRFVoOGowUHNaM2dvblRkL2RjQzQ3S1Yyek8rQ2VsZ3haOXZabXpaU1cvTlFaNDEwRmJmY3FRRmU1MGhGSnZpdUJZNTVCNWNYeEl4WGFXczV5NHIzeS92UXl1WFVEblJZbkhucUwiLCJtYWMiOiI5OTQxMzM0MDlkNGVlYjM2YjdmYWU5M2I4N2RjYzQwOWExN2RjOTVhY2VhMmJhNTRmYjhmNzYwYTUxZGMyZTIyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlY4UkxpQlh5UGxpOTVFdXYzbjNLVmc9PSIsInZhbHVlIjoiS2RLTFVBVGVYcHFya0djc3gwKzN6TFU0Nk1SMEYrVVM4OEorbXF1YXlNWlFnWUlpNFZyQlkvNGNMeFRHV3JKZXlnRGZPQklFeG9ZM3BMM2NKYXB3Vkc1V3h6Y2hnQXF4VnNGZnZxS2JON0tsR2xZVkpvRWNYYlFsVmkyTHo3ZnZmL3hiTFlMZFdLZ2d6aEVpYUdiU3I2Z0NOdUNLT0h5bU5HTENMMUpmK09RNjB0MmdyYlp2QzIreTdiUm95dEZzT1lvc0NxQ1FKejI3cVB4ckhmUE8zZ3puZER1QmxRL3hUYTRZYUE0T0ZUWE9LbHdDZTV3MzJqVTN5Y0FzNGtCNk5YMHBVdFNDZ2pNb3ZiVE1FcGJlMGxjRHAyMW9wYlpuRTcveXM0cDdZVm5tczRKODVQR01aZkhyM1hYOGVIbk1tMkpOTHFLb0tOM09teGRaaUp6WW94ZHBaV1pVK1N3dmxsSFhNZ0dIVEprL1lkQmdldW85Nk5yWk1qT1M0UjJQZGRpcEFxdVhLYWhwbFBoWHJBbHh6eWxneGdER2R4bDlrOU83ZWV4NHJKZ1c0b1pFMnl1SXM5dkh2aHQ2K3crT2JtZjdxMlhQektsUU9rZGVzdFl1WGUzUmdYMjR2OWNQaTJaVWREWWM0c1FxNm52clNpWEdVRmFaTXc4Y2MxdXYiLCJtYWMiOiJjMWQ1MjA4ZTc1MDQ1MTMxMTM4MjFjMDI1ZDJkZWEzOGZkNGM5OTE3MzczZTkzNjk3ZWRhNzY3OTU2OWM2N2U1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBNQUFHVEMyeGd5YXBkczJlaW4vcUE9PSIsInZhbHVlIjoibHp3WUwyeVF5SmFJV3FwSjQ5RWtOQVEvdG1YbFBQSzVhTzRzWWJ2TnliZ0lrQXlrVkJJMXVsTURucHhVYU91cW9RUm9ZcTRldTFNaG9XNURuMEhVNXpMSGFodWVTMmJqRFZlYTNsT1Y4R3NQOUpTM3lrMUNWbWM3cEk5d1NGL0N4VGpLbE1ja3ZtNW4vUTB1aDFzWC9maTZZQkZuT1lyUlY2YTA0SkQrTnFlTXEwMCswdkRDSGl6cEtNR0pGV0xPemJBMVgyMk5NeHJRUFpIRUZBc2hMU1V3c0k4eHBmZlFZdHpINVA4Nmo1VVNqdjA2cWErQWFTTzdwalFIRlVlRjJGZVJaaEw2VnBicEZ6MkdHYjQzSHJOdnlHblVWbUhXeEFWSU5zWFlqR0h2ZE1mS3VHZmFiMlB4QVZiZzd6UVZjL3FJcDZDVXg1eHJBbEl4NUpId1RYb2tjMWNQK2pYQlM3UHdiSHdDRk1vUXY1UXczZTltTVJrNmpJbXR0MVdocVVpUi85RVFoWFVLc2hucFdsTEhSTUlRUjJjRFVoOGowUHNaM2dvblRkL2RjQzQ3S1Yyek8rQ2VsZ3haOXZabXpaU1cvTlFaNDEwRmJmY3FRRmU1MGhGSnZpdUJZNTVCNWNYeEl4WGFXczV5NHIzeS92UXl1WFVEblJZbkhucUwiLCJtYWMiOiI5OTQxMzM0MDlkNGVlYjM2YjdmYWU5M2I4N2RjYzQwOWExN2RjOTVhY2VhMmJhNTRmYjhmNzYwYTUxZGMyZTIyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlY4UkxpQlh5UGxpOTVFdXYzbjNLVmc9PSIsInZhbHVlIjoiS2RLTFVBVGVYcHFya0djc3gwKzN6TFU0Nk1SMEYrVVM4OEorbXF1YXlNWlFnWUlpNFZyQlkvNGNMeFRHV3JKZXlnRGZPQklFeG9ZM3BMM2NKYXB3Vkc1V3h6Y2hnQXF4VnNGZnZxS2JON0tsR2xZVkpvRWNYYlFsVmkyTHo3ZnZmL3hiTFlMZFdLZ2d6aEVpYUdiU3I2Z0NOdUNLT0h5bU5HTENMMUpmK09RNjB0MmdyYlp2QzIreTdiUm95dEZzT1lvc0NxQ1FKejI3cVB4ckhmUE8zZ3puZER1QmxRL3hUYTRZYUE0T0ZUWE9LbHdDZTV3MzJqVTN5Y0FzNGtCNk5YMHBVdFNDZ2pNb3ZiVE1FcGJlMGxjRHAyMW9wYlpuRTcveXM0cDdZVm5tczRKODVQR01aZkhyM1hYOGVIbk1tMkpOTHFLb0tOM09teGRaaUp6WW94ZHBaV1pVK1N3dmxsSFhNZ0dIVEprL1lkQmdldW85Nk5yWk1qT1M0UjJQZGRpcEFxdVhLYWhwbFBoWHJBbHh6eWxneGdER2R4bDlrOU83ZWV4NHJKZ1c0b1pFMnl1SXM5dkh2aHQ2K3crT2JtZjdxMlhQektsUU9rZGVzdFl1WGUzUmdYMjR2OWNQaTJaVWREWWM0c1FxNm52clNpWEdVRmFaTXc4Y2MxdXYiLCJtYWMiOiJjMWQ1MjA4ZTc1MDQ1MTMxMTM4MjFjMDI1ZDJkZWEzOGZkNGM5OTE3MzczZTkzNjk3ZWRhNzY3OTU2OWM2N2U1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1246863354\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-336092572 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-336092572\", {\"maxDepth\":0})</script>\n"}}