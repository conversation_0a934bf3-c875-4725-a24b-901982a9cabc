{"name": "google/apiclient", "type": "library", "description": "Client library for Google APIs", "keywords": ["google"], "homepage": "http://developers.google.com/api-client-library/php", "license": "Apache-2.0", "require": {"php": "^8.0", "google/auth": "^1.37", "google/apiclient-services": "~0.350", "firebase/php-jwt": "^6.0", "monolog/monolog": "^2.9||^3.0", "phpseclib/phpseclib": "^3.0.36", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.6"}, "require-dev": {"squizlabs/php_codesniffer": "^3.8", "symfony/dom-crawler": "~2.1", "symfony/css-selector": "~2.1", "cache/filesystem-adapter": "^1.1", "phpcompatibility/php-compatibility": "^9.2", "composer/composer": "^1.10.23", "phpspec/prophecy-phpunit": "^2.1", "phpunit/phpunit": "^9.6"}, "suggest": {"cache/filesystem-adapter": "For caching certs and tokens (using Google\\Client::setCache)"}, "autoload": {"psr-4": {"Google\\": "src/"}, "files": ["src/aliases.php"], "classmap": ["src/aliases.php"]}, "extra": {"branch-alias": {"dev-main": "2.x-dev"}}}