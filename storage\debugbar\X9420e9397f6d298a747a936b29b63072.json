{"__meta": {"id": "X9420e9397f6d298a747a936b29b63072", "datetime": "2025-06-07 22:37:55", "utime": **********.925964, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.215986, "end": **********.925996, "duration": 0.7100100517272949, "duration_str": "710ms", "measures": [{"label": "Booting", "start": **********.215986, "relative_start": 0, "end": **********.850937, "relative_end": **********.850937, "duration": 0.6349508762359619, "duration_str": "635ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.850954, "relative_start": 0.6349680423736572, "end": **********.926, "relative_end": 4.0531158447265625e-06, "duration": 0.07504606246948242, "duration_str": "75.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43602480, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.011, "accumulated_duration_str": "11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "guest", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9020882, "duration": 0.011, "duration_str": "11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-2107462 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2107462\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-425233942 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-425233942\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1455360551 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1455360551\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1441327513 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749334872942%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFiK2ZMQnAxa1FDWjVTOFVidmF6T3c9PSIsInZhbHVlIjoiM0tvVlZBeVIxWi9VajUvUUZmdE1nQmUyZStNNDJabkNFZkxPdzEyZUpYRjlEcHpIZDMwVkduNVJMYzdtUjJPV1ppMEM3NS9tRGZ6VkU1aWFGUHMwQ1NOZWRKNE1USXhWWlBsTEcxdW1ZSGJlVjRLNFJiNE1MdCtjL3lteHd0eEpMZ0Q1T0Q3Zk03Q0VSRHZ5NUFlZFBLWDU2RnNwa0FoNzV5ZWkveG81UFp1QXpzK1RFTzhUQnpmTXpxNnVkRkFlYUQ2UHl1azArMk02QUtjM3FYak5VQkZ5czlxcnVsa09qTm5qNElXUm5IRmNSckYzbTMxcmlXTnBYNklwNmx2TlNMNW9SendxSnBCN2tvSndsUWJLejZZdDJEaWgrVTAyOEplM3JBS2xYNllqMExIdHNoZEgxSTVsdG5IcHc4d3ozL2tHN0ord1VKTXcyWTN0VWhDSFRscFpQOEUrU1FmN1p1dkJiTHdPaEZFeW1MenNsUjRNdUhXTFFjTDBpSkVIdzdJU2dWOC93OGpTT2VMK3RVdWU2Z1JDT2dqdTZWWjdOWkpFVmc1R2hrTDdsTGtpY01UdWcvZk92T1llQUxjMTNWalQ3YXNWNW5IVUk1L2hZbzVZY1VLdlJHUmx0WFB1aTE5aWRPYnlVL0lhV2ZKckpXZ2lRN2huQzNlRlBjYXYiLCJtYWMiOiIyNDE4ZTg2NmFmMDdiMWYyOTAwZDQ0Y2U4NDQ2ZmVmZWVlNjNiMjYyMzE1MTEyZTkzZTM0ODhjZjYyNTY1MGY3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZKQWFzNHVQcWxyWEROYVNwMHlqbEE9PSIsInZhbHVlIjoiUWtIOTc2cDZKOXFDUHJaaGR4cUVkeHhWeGdLb0pEQjdmb3VsWUE0R1lhbzRxWFEzaUU1OEJXNGlNQlhyTjBhaVFqUmJIM01mSW9kdXkrVG5rOWFoaHZNNHN5Vkdoa0pQZ1BXN0o0WTRMelpsVFV4MittVDRPam5vMnp4ZzJIYnhLUyt0T2JQUDlrS1Q3dHJjUVVpcGN6TGdGM3lmeXV0Z0lSZHdYOFhXZC8wak9zRlNoYVh1Z05DU1NGRjUxMHk5MU9kbHRQbDN2VW5NL1BWREFTRW05TVgvUmplQ1U0b1BlZjQ2cDZwVWo1U1lmU2Nnd0pqTUZXcDdFMXRQZm9rb3JYRXlITElVdWdHSjZzcUFPWkJqb0xqVlRyaUpXVlBHVTdBbTNqamw2Q0lZVW1VTHBQRmsrSWZ1ZTNHeEVKWVc2cmcwNyttOVB0cGtJUkFwUFp5UkZhUm12Z2djd21GTVQwZHVOMkpnQUdNZ3M0c0ZGL09ISHZHbm9KU0NEcHJQTmtEbHlDaVpFOGFVMzY2bGZ5MzlSeU1uQnpUa0lvS1NtMzVjTVJCUm42R0h1VVB0d3MzQXJSYzNsazVJb3VZa21oNXZkWGVHaENJa3JZUm82Yi9ZRGdnZnJ4eDliSitnVHJ6UmQxQXJvMytKY0RFZjZ5NXJ1Y3B4emcvcnFURUIiLCJtYWMiOiI4NmUzYzRmYzZmNGQ4ZDA3ZDMzNDIwYjM2ZjdkNWQyY2ZlMmQ2NTdiYWJlYmQ0MzQ0OGJhNzAwMTViMzhhMmEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1441327513\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1070170909 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1070170909\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-958567346 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:37:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InE4anZCdWFrQS8ySlgwaHF0bXpUdGc9PSIsInZhbHVlIjoiVExweWRVVzVJcTk5Wm5xRHBrL0JZcVpyZnVER3dMbHhEamwxSk5PSFRWbkU3M2RCcWJQQlVnNldOcWtLN2hjMnAyOXNpT04wdFFvZkROYUxCdEdSTE9JN0dISzFGL0ZEOXBBUm1YRGVuYW9iVmRpQUhDZzZjKzJoeVZnZGNwb3lKYWNrQTBpQ0VyQ0J3dVhjTlJhK3phMXJoLy8wNnRVU1RPTmcrcEhSU20xaDVZVHZXeFJNclNwQWxVZ0ZhSUY5dXM0ZDJ4Y1RReXd6Q3YwSlppYXRTYWxhaTlvcnExUG83L2JYSENOWlJTc3lPcmNxTkpERnYrZjJGSXR6L0xqQUl0YmpuQ3RzSzU0NG9xTDBEdnlpdC9DaWttaXZUenZkWGdWa2VrUk9xeW5hUkgzMVZTeXVURTBsUG5CZTJDayswQk9KZmtOYkd3dURRZ3R1YW0yVUxjY0Qxd0w3TlNkcTdobWlNRGFySjU2SFBhZXY5L0xPVkNVMEQ3a3NGbnc2UVg1VDBNMy81ak1MM3p4a1VCRm1iaGtBWUQ4NkNtTmQ0cUQrOU9wRXBTcGlqMTArbDVFQUVBTVpDZzA2M095dStrWmIwMUQ4elhYZUYwM2grVUhNU1hpNDlDRE5zbDY4Q3gzRVhLSHZXZjMyMFVUY3UyMm5GSmduYkxjaXBkaTkiLCJtYWMiOiJlMjE0YWE2ODI2NmYwZTY2ZThjZThlMDYzMTEyNGY2ODBhYmNhYzU0YTE0ZjYzMTE1ODA4MjM3MzhmNWQ3MTk3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:37:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjBjNEpBMERmazczS0VZVFFENXhaZUE9PSIsInZhbHVlIjoieC9za1Y5Um5lb0dDbFhJbVFvTUQxWUpxdHFJTmlpUDdEcTlzWGRWaFFWLzBxdHZBck5nV3RhZDZqWmxxTGEveWNvODlXL0szZjQ3UzVNK280cWdqNjR3dWo2K1BJb2lUOVNZSzNZL21xZENRZDlSbUNMdjU1TEZVUDV0MkdGMUc4RlNKN3ZCSDlXYjVJaFcrYjN4QW4rbkFBTXV1ekVhNW9WS1ZEMHJMZnA0eTFqdWtXSEpqTHI0V29kUGlnVVllSklMa282cGJWUk5PdWJtdEs1RVpzRUI3TmhkMUl2L2NrOFljK1czN0YyQ1gzbGVXYk5aWnR4K0xuNHZxV1pMc0JwUTRWNFI0dU5xMVQ0enc5c002Y2dESVFEcG4yWTJtS1F4dFF4UGFWOGgzd0k5L2RkV0p0WXp6bitmM20wQ3JZVHIra0VuRTNJNFdPQldaU25GeVBOck5Fc3ZGTXhqVEtnNEQ0ejBueXRwb0MvR3BRVTlLaFc1YlpMcGNCQXJHdmRLUUI0Y0Jtd0tQbXVJR1Z2UG82bXU1Q0lKQzB2Z0E5ajdORXBGSEYzU093RGQrb0hqTmdKSXVsWFVWY3FQR2dCb1VOT0c1T2p3cU1STzErSU1jMEF6eWlhUGkxQ3lvNGpKSy9odHhrS1U1N1B6aWVTZUIybGFjZHJKUVF0K1IiLCJtYWMiOiJkYWJlYTVhZDQ1MWQyY2MxYTNjM2VjMTg1Y2NlMWFhYzEzMGEwOGEzY2I5MDQwNGRmYzJkZWIwYmNiZjI0ZTNiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:37:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InE4anZCdWFrQS8ySlgwaHF0bXpUdGc9PSIsInZhbHVlIjoiVExweWRVVzVJcTk5Wm5xRHBrL0JZcVpyZnVER3dMbHhEamwxSk5PSFRWbkU3M2RCcWJQQlVnNldOcWtLN2hjMnAyOXNpT04wdFFvZkROYUxCdEdSTE9JN0dISzFGL0ZEOXBBUm1YRGVuYW9iVmRpQUhDZzZjKzJoeVZnZGNwb3lKYWNrQTBpQ0VyQ0J3dVhjTlJhK3phMXJoLy8wNnRVU1RPTmcrcEhSU20xaDVZVHZXeFJNclNwQWxVZ0ZhSUY5dXM0ZDJ4Y1RReXd6Q3YwSlppYXRTYWxhaTlvcnExUG83L2JYSENOWlJTc3lPcmNxTkpERnYrZjJGSXR6L0xqQUl0YmpuQ3RzSzU0NG9xTDBEdnlpdC9DaWttaXZUenZkWGdWa2VrUk9xeW5hUkgzMVZTeXVURTBsUG5CZTJDayswQk9KZmtOYkd3dURRZ3R1YW0yVUxjY0Qxd0w3TlNkcTdobWlNRGFySjU2SFBhZXY5L0xPVkNVMEQ3a3NGbnc2UVg1VDBNMy81ak1MM3p4a1VCRm1iaGtBWUQ4NkNtTmQ0cUQrOU9wRXBTcGlqMTArbDVFQUVBTVpDZzA2M095dStrWmIwMUQ4elhYZUYwM2grVUhNU1hpNDlDRE5zbDY4Q3gzRVhLSHZXZjMyMFVUY3UyMm5GSmduYkxjaXBkaTkiLCJtYWMiOiJlMjE0YWE2ODI2NmYwZTY2ZThjZThlMDYzMTEyNGY2ODBhYmNhYzU0YTE0ZjYzMTE1ODA4MjM3MzhmNWQ3MTk3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:37:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjBjNEpBMERmazczS0VZVFFENXhaZUE9PSIsInZhbHVlIjoieC9za1Y5Um5lb0dDbFhJbVFvTUQxWUpxdHFJTmlpUDdEcTlzWGRWaFFWLzBxdHZBck5nV3RhZDZqWmxxTGEveWNvODlXL0szZjQ3UzVNK280cWdqNjR3dWo2K1BJb2lUOVNZSzNZL21xZENRZDlSbUNMdjU1TEZVUDV0MkdGMUc4RlNKN3ZCSDlXYjVJaFcrYjN4QW4rbkFBTXV1ekVhNW9WS1ZEMHJMZnA0eTFqdWtXSEpqTHI0V29kUGlnVVllSklMa282cGJWUk5PdWJtdEs1RVpzRUI3TmhkMUl2L2NrOFljK1czN0YyQ1gzbGVXYk5aWnR4K0xuNHZxV1pMc0JwUTRWNFI0dU5xMVQ0enc5c002Y2dESVFEcG4yWTJtS1F4dFF4UGFWOGgzd0k5L2RkV0p0WXp6bitmM20wQ3JZVHIra0VuRTNJNFdPQldaU25GeVBOck5Fc3ZGTXhqVEtnNEQ0ejBueXRwb0MvR3BRVTlLaFc1YlpMcGNCQXJHdmRLUUI0Y0Jtd0tQbXVJR1Z2UG82bXU1Q0lKQzB2Z0E5ajdORXBGSEYzU093RGQrb0hqTmdKSXVsWFVWY3FQR2dCb1VOT0c1T2p3cU1STzErSU1jMEF6eWlhUGkxQ3lvNGpKSy9odHhrS1U1N1B6aWVTZUIybGFjZHJKUVF0K1IiLCJtYWMiOiJkYWJlYTVhZDQ1MWQyY2MxYTNjM2VjMTg1Y2NlMWFhYzEzMGEwOGEzY2I5MDQwNGRmYzJkZWIwYmNiZjI0ZTNiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:37:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958567346\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1368402350 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1368402350\", {\"maxDepth\":0})</script>\n"}}