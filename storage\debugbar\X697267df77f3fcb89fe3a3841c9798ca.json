{"__meta": {"id": "X697267df77f3fcb89fe3a3841c9798ca", "datetime": "2025-06-30 15:34:43", "utime": **********.558639, "method": "POST", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.057347, "end": **********.558657, "duration": 0.501309871673584, "duration_str": "501ms", "measures": [{"label": "Booting", "start": **********.057347, "relative_start": 0, "end": **********.407755, "relative_end": **********.407755, "duration": 0.35040783882141113, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.407763, "relative_start": 0.3504159450531006, "end": **********.558659, "relative_end": 2.1457672119140625e-06, "duration": 0.1508960723876953, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50820360, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@SetOpeningBalance", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=134\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:134-167</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.08712999999999999, "accumulated_duration_str": "87.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.435171, "duration": 0.02307, "duration_str": "23.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 26.478}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4663868, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 26.478, "width_percent": 0.494}, {"sql": "insert into `shifts` (`shift_opening_balance`, `is_closed`, `created_by`, `warehouse_id`, `updated_at`, `created_at`) values ('100', 0, 22, 9, '2025-06-30 15:34:43', '2025-06-30 15:34:43')", "type": "query", "params": [], "bindings": ["100", "0", "22", "9", "2025-06-30 15:34:43", "2025-06-30 15:34:43"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.477303, "duration": 0.05806, "duration_str": "58.06ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:147", "source": "app/Http/Controllers/FinancialRecordController.php:147", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=147", "ajax": false, "filename": "FinancialRecordController.php", "line": "147"}, "connection": "kdmkjkqknb", "start_percent": 26.971, "width_percent": 66.636}, {"sql": "select * from `financial_records` where (`shift_id` = 60) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["60"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.538132, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 93.607, "width_percent": 0.953}, {"sql": "insert into `financial_records` (`shift_id`, `opening_balance`, `created_by`, `updated_at`, `created_at`) values (60, '100', 22, '2025-06-30 15:34:43', '2025-06-30 15:34:43')", "type": "query", "params": [], "bindings": ["60", "100", "22", "2025-06-30 15:34:43", "2025-06-30 15:34:43"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5402908, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 94.56, "width_percent": 3.305}, {"sql": "update `users` set `is_sale_session_new` = 0, `users`.`updated_at` = '2025-06-30 15:34:43' where `id` = 22", "type": "query", "params": [], "bindings": ["0", "2025-06-30 15:34:43", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 162}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.544187, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:162", "source": "app/Http/Controllers/FinancialRecordController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=162", "ajax": false, "filename": "FinancialRecordController.php", "line": "162"}, "connection": "kdmkjkqknb", "start_percent": 97.865, "width_percent": 2.135}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "success": "Opening Balance has been set successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-811328870 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-811328870\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-850508315 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-850508315\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1517691557 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>opening_balance</span>\" => \"<span class=sf-dump-str title=\"3 characters\">100</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1517691557\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1859162551 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">67</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Ii9mNkNsblkzSXMzdzFRdnJsbmxIc1E9PSIsInZhbHVlIjoiQ3REVjlkOUgzVVdtSGxJUXhsNlU0QTdSY0FGbVRXSDRJWHlOS0IvQzZxS3JrR1JQak1scUVzS2lsYXZaVGJoZHRTNi9nN3Z4dzByU0xFblZQMDhESVF0aSt2b3c5Mk1zN0c2WjVhS3NEUUNXSWdWQjRCTlpoZ09yM3NYbmhtaUxsa3ZReDVsTFM5bkk1c0V0OUc2ZE5nU2RISWxRL29EaER4eG9vSG50ekhjWmRaWjdqVjBoQ1JPdFd1RWcrMFU1WXljY1Vaait0NU1kbDF3bEpVd1RXOTMvQXlBb3VXcElPRlNLTW44RzVFQzNoSFJzY0xCOS9lL1Y4RC9leUhDd3d6TGJBSGpQUWw3bjMxVlVMN080aDAvaEdrYkNzQ3Y0OEd2d2psMkJ2TS8rTjRPSVRtUnRYS1pub3VGMzcwVjJCckNoTmFvdFZ6ekZpOHk5dHZ5a1VRampKQW52Vk1JZ1g1ellCR1pnVytlZ2YycFcrWkxSQjhVVHhybHZ2aWVkeWYyRWRKVFlQamF6aEx1cUNsSGxRdS9UY3ErVTUvSHczcVhvRjB3ampMaU9PMngyYm5saVc2c0tYdGNqNms4dmQ3K2wzamhmdTZDOEJDL2N0Undac3dhbnpTQXlKV2Y2ekdid0NWOWZFSzZiWG5mRVJKSlU4OTFTNk1MV0cwcWMiLCJtYWMiOiI0ZDk4OGY4ODFjNTcyMmNjNDI2Y2Y2NTRhNTNlYzVkYjQ3NTQ1NGM1Nzc0YTI3Mzg5MGFmOTVjZjViMjU1YmNkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkJXcTRDUy9JdEZhQmdmNTFEOWI3emc9PSIsInZhbHVlIjoiQnBqaGt6dVB4L3g2RG1XOEhnMmc5My8zUXJiWm55WE5XNzhtWFU3SEkzUnFHeWJNNXYydDhLdU9nWU5TRXVmZS9zYkRUVWo3UW9PcG9Dc20yYjQvOFNJR2dseVZnd3dvWTJXakZZQ2dmYXF0bVZlaFlTZHl5bUlwQkpwL1dJWTMrcnlRTWpiYitvc21WeVJ2QXBZcWtRc1RMd0JTakxMWXBBRG9CRitjSE9CUjZOZWJJSlNyNmdXY0cwcjlXNisrZ3h3T1drMGVtZFowMlBQOTJqeXZDWnE2SVluT2JEaWhxb2pwbUdpb1BSYVY3aVlHcUU2dm0xV3Q4Y3RoVTUzL1pIN0hCMWFTNVdCVHV1dXZwaFdUZFV6UElRUzZEWWdpcDJTWllocVNTZ2VVVnlmTlJlSmVmdWs0WTFkUlFCWDVyd09xNHp2ZTdoTllYTTRYdDZ0RmN5VkI2YStXUE15YUEwVGd0QVl6YnhpeElsV3lUZ0xPdTlYdW1nbHg0MEFKRjBKQVptQkd3MDN6Z0svQUtyUzUvZHQ0bDRES3krcWpBYXdlQ1VXRTh1YUlpbVVqNkg3Q3hNNHhyOG5sOEk0a25uR1BRZnFiTTBPSmFlakE3OWplU0dSZURRaitCdFY5RjQvdjluTjJBSjBxT3p6K0pnSmxmMnNQUW52dXNPdnAiLCJtYWMiOiJlMDQyOTNhZGNiZGJlZjA3ZWEzNDE3NmE3Njk0OTI1N2Q3ZWU2NDA0NWQyMDM2N2IwZmEzMDYxYmQyNjg0OGY1IiwidGFnIjoiIn0%3D; _clsk=ffd61i%7C1751297680604%7C5%7C1%7Co.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1859162551\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1350366495 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1350366495\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-291133424 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjgrMFJiRERhcFI5c1Fya1AyamZpTVE9PSIsInZhbHVlIjoidkI5ZnlrSldxN0V3ekZUWTJBNG1JNk85YWZDNWZuY0ord1NVNnNZanY2bjF5ck8zd2llQ2h5WHNtQlFCbmZHQ09VRUZqU1JOWnp5VzByd1g5OXJTb0ZtckdobEE2b1BZUGpBNUQxRnRiSzdYaFo3djFxUzVTUTRYcXhMeW5zMTlJNjhpaUVjVmZnaFcwMkhXbWpzNjRPM3l2NGZxbGZSV1JwTEwrS3RXWlFnS1FwMGFOcjJrek5pbEtwaXBJMDBZdHVjb1kzQWhyK3JXcndTUC9qT0RkZWowKzdnaHdtT0FmeWI0d3RTOVNZOXNPa0FVUzc3STQ1N1NjWXE0NFQ1QjFKbnA4ak9QY0swSzI5bmVZTnRUekt1aWQ5Y3hFenRtODhqUjdocEp1ME1xc1RzTjkvSUdGaFdiUm1QdVl6YzAvTHpMY2hIZmU1QWplWTNudkxlU3VNK0ZoRmlzMXVUbzJUaXJySXpxNitaRTVLS0VjVWtUUzljRnpRRzRuNEVBNWJQai96WUJTUm1VUThIbkdPNGc4aXhPVUovN2h1YUZoaWsybnBxclVoMGxXY2lMSWxVRU9YV2E5RWNVMElJSGQrMGVCQUNqZHJlR1p0VkxyRkQ5TnhuVmo4U25aVWhzeW1ieitZRk8ySVFUWmFJU1A3TUNFd1NqMnZUY1lEbkYiLCJtYWMiOiIyNTU5M2UyY2JiZGJkZWNhY2UxZjNjYjBlYjcyNGI0MTZjNGNmODVkNzg2MTQ4MzdjNWJiYzkwNzBjZmY3M2E5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InpWRVpZZGpabi9iQjFqejR4R1pFUFE9PSIsInZhbHVlIjoiV3BFUERqcERpTXpZZ0x5TERvdzhyQ2V1bEdmNkZUZ2RKL2JzUkhORkN3ekw3Vzg4djBIWXFZQ1R2Q3hSZHJwRk5DQzh4c3A4cVFaVDcvWDY0cUQ5bmYzcU5xeVBycnpTOTM4THJaRTBXbHVGSUNQdXlHVjhMcDBmQk0ralducFArSlVDaUlpbzRiYzhmc0x6M29rdjQzQlNrZHNSNVhMU0g3MmtUSkNBWHptOTIxWHErcERCNnp5SXNneUg2V04zcVFGMXFkbUhVQjRVQ1ZjemV5T204SVh0RTlrRGlzdVFGVHh5VHVlTlljSEtrWEFlVjgvWWRoWU1XeGcwSklIWG1BUjMyQ2dwK05SemNqRTRUQnB6UE9KNkowU2hrRTdWNHlSbUFnZXJKcWFPeHcyNExTNVVqblhBTkc4aitkc1NUNkx1WHVITGxnZzdlYXJJMGludGh3eTNVU1RnMVBTSWNhcGJQNG14TmxzdEI5a3RSak12K0hkVHk0NmJ5TGNmQnZGZG9kT3lvckliNkdsSXVtK3pCT3ZLRm5vQ0psQWJWOWtSRkdhZDc4b3c2VWJ1RHZtMmlzNDYzUHUvQ3lGWmJSUjIzd0FtTjArL28rVDlMRUM3dlhJWm9BZWo3T2xwcXVTbG9oN3ZlV2MyMDE4WTRaN2ZlVEYyV1l4ZkVITjUiLCJtYWMiOiJhMGZhMzgwYWNiNjFmMWM3YmM5ZTUzYTQ2YzgyNzQ5YTM1NzEzMGQ2YTkxMTEyYzE3YTE3ZTg0NzM3ZmNiZDI2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjgrMFJiRERhcFI5c1Fya1AyamZpTVE9PSIsInZhbHVlIjoidkI5ZnlrSldxN0V3ekZUWTJBNG1JNk85YWZDNWZuY0ord1NVNnNZanY2bjF5ck8zd2llQ2h5WHNtQlFCbmZHQ09VRUZqU1JOWnp5VzByd1g5OXJTb0ZtckdobEE2b1BZUGpBNUQxRnRiSzdYaFo3djFxUzVTUTRYcXhMeW5zMTlJNjhpaUVjVmZnaFcwMkhXbWpzNjRPM3l2NGZxbGZSV1JwTEwrS3RXWlFnS1FwMGFOcjJrek5pbEtwaXBJMDBZdHVjb1kzQWhyK3JXcndTUC9qT0RkZWowKzdnaHdtT0FmeWI0d3RTOVNZOXNPa0FVUzc3STQ1N1NjWXE0NFQ1QjFKbnA4ak9QY0swSzI5bmVZTnRUekt1aWQ5Y3hFenRtODhqUjdocEp1ME1xc1RzTjkvSUdGaFdiUm1QdVl6YzAvTHpMY2hIZmU1QWplWTNudkxlU3VNK0ZoRmlzMXVUbzJUaXJySXpxNitaRTVLS0VjVWtUUzljRnpRRzRuNEVBNWJQai96WUJTUm1VUThIbkdPNGc4aXhPVUovN2h1YUZoaWsybnBxclVoMGxXY2lMSWxVRU9YV2E5RWNVMElJSGQrMGVCQUNqZHJlR1p0VkxyRkQ5TnhuVmo4U25aVWhzeW1ieitZRk8ySVFUWmFJU1A3TUNFd1NqMnZUY1lEbkYiLCJtYWMiOiIyNTU5M2UyY2JiZGJkZWNhY2UxZjNjYjBlYjcyNGI0MTZjNGNmODVkNzg2MTQ4MzdjNWJiYzkwNzBjZmY3M2E5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InpWRVpZZGpabi9iQjFqejR4R1pFUFE9PSIsInZhbHVlIjoiV3BFUERqcERpTXpZZ0x5TERvdzhyQ2V1bEdmNkZUZ2RKL2JzUkhORkN3ekw3Vzg4djBIWXFZQ1R2Q3hSZHJwRk5DQzh4c3A4cVFaVDcvWDY0cUQ5bmYzcU5xeVBycnpTOTM4THJaRTBXbHVGSUNQdXlHVjhMcDBmQk0ralducFArSlVDaUlpbzRiYzhmc0x6M29rdjQzQlNrZHNSNVhMU0g3MmtUSkNBWHptOTIxWHErcERCNnp5SXNneUg2V04zcVFGMXFkbUhVQjRVQ1ZjemV5T204SVh0RTlrRGlzdVFGVHh5VHVlTlljSEtrWEFlVjgvWWRoWU1XeGcwSklIWG1BUjMyQ2dwK05SemNqRTRUQnB6UE9KNkowU2hrRTdWNHlSbUFnZXJKcWFPeHcyNExTNVVqblhBTkc4aitkc1NUNkx1WHVITGxnZzdlYXJJMGludGh3eTNVU1RnMVBTSWNhcGJQNG14TmxzdEI5a3RSak12K0hkVHk0NmJ5TGNmQnZGZG9kT3lvckliNkdsSXVtK3pCT3ZLRm5vQ0psQWJWOWtSRkdhZDc4b3c2VWJ1RHZtMmlzNDYzUHUvQ3lGWmJSUjIzd0FtTjArL28rVDlMRUM3dlhJWm9BZWo3T2xwcXVTbG9oN3ZlV2MyMDE4WTRaN2ZlVEYyV1l4ZkVITjUiLCJtYWMiOiJhMGZhMzgwYWNiNjFmMWM3YmM5ZTUzYTQ2YzgyNzQ5YTM1NzEzMGQ2YTkxMTEyYzE3YTE3ZTg0NzM3ZmNiZDI2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-291133424\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1916305552 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Opening Balance has been set successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1916305552\", {\"maxDepth\":0})</script>\n"}}