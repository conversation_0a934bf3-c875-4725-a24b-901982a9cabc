{"__meta": {"id": "Xcdb224e6992d049230a9da0a6646f6aa", "datetime": "2025-06-08 15:29:07", "utime": **********.727195, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396546.70788, "end": **********.727233, "duration": 1.019352912902832, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": 1749396546.70788, "relative_start": 0, "end": **********.59136, "relative_end": **********.59136, "duration": 0.8834800720214844, "duration_str": "883ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.59138, "relative_start": 0.8834998607635498, "end": **********.727238, "relative_end": 5.0067901611328125e-06, "duration": 0.13585805892944336, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152224, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019760000000000003, "accumulated_duration_str": "19.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.661169, "duration": 0.01794, "duration_str": "17.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.789}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.698644, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.789, "width_percent": 4.605}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.711587, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.395, "width_percent": 4.605}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-339084058 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396538091%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJUQzZYaXhaeU1HeWFORHh5VG5rOHc9PSIsInZhbHVlIjoid3o0RzllVUFoTGw5aXpKRWxUL2xCaDBTR0VjSmhRM0FTUzdNSVFwUTl5SzNKUUNjQ21kQUZhOCthQnBoWDF3N0Rta21qQUJlNG9vdUFLNG9hUXp5ZnFJMXRnT1FxcXJ3ZWFuZWRiWEcwUmFFaTQ2dHhLc2FPQm5uTFRmcTNWTkFOTGxVT2Jrc2szN0taaGZOZ0luOE04UnNweDZrc2lNVHdNbTBtOC9UMWRRR2l2QnhUTXA3ZDB5ZEtSMDVhcms5TG5FUHFjRDFpVXNMZ3NjV3YwalphOEFUdHZHK0FwV083Rm8yazR1WGN4QmhrNHRZaS9NQ2R1Yzg3Y0JDSXcvd3doZ0VDUkN1U0dxZUVIZTV2VUdsMUN3WUVhUk1PSVU3VWM2OHZ4WnVSWFVwdTllbGRQZzZmSUs4M3JIUml1M0hGOVlpVGM1Vi9uSENOVjZDR3RtRXN0cnd0TytxSmYxbDUvQmZmQnV4cVNidmQ5eXVUUlJMdXZSczNKUjlidXFaSnNPM3RRcndQVUgxbXp0MUo3cWI1c3E3dm9wRm41VDRZaWxHdVdrczgyMjMzaG9lMnA0ZDUyWmhGOG9QZForVW5icVBObjJFVmxCRUJVaGpMa29QRjdpZ05CSUNoMjBDOVpzVlJWTjhVNG45VEoxbDVDR0ZQbThrTHd3bERNK2IiLCJtYWMiOiJlMmRjNmRmM2ZmYjZiZGUxMWE5NmU5ZDNjYTFlMzUwYTFkOWYxZDcyOThkMjY0ODUxMGViN2QzOGIwZGRmNmQ1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik1hc3BjR0VHUWx5VUlUK2NYU2NOclE9PSIsInZhbHVlIjoiVE1sNHFGZjBCclVtZGM1M0g1dkUxOHJZWWtoZkNNWjF5MTJyVXJ6aUp2Yy9GYVpEZ1pORi8rcEhGZXM1NzRtTWJ1Q2pnMG14WXRSY0Y2WDJLUkNxU0FtY0h6NitQYTdCRjVodTB5UTQ0TkRoZ2tvc1lJdzdHa3dzNVA4YURsQXlyeEhUZ3M2bm15NGM0cXRMRmtDSURZQkk5WmFkSWF2Q0tVVVlQZFEvbjBJT2NBaWtZTytJRGwwdXErOHhYdHhxbWYySzRwaUsvbkxUSTQ0RmdvTFpRUFNTNWM4akdvblcvMkJrWktvRTlRT05aUmJrZ3FWMUcyTDVHV3R4dEJwQlIzcnYvU0dhZGxRd01vNXhpREhRd3hyL3NFTkJjMVVGNDZNNitKL1oxUVZsK2JTeklrR1dZRy9BeVpVMHgweU1Tc1VCUzFzQUpUSHBIcFVUTC9FMVpZamhJTnliTUU3UXc4LzJBT3B0QTN1WTF4T2EzcVZOSjFTdUYyTGxKTVRYOFdMMDZuSWVYSmFCT1ZqNlJLU091Q3oxYzd1VGM5Y2Rzb0JJWVRBMHJaYTljS0lVRTc0Yll4Mk5xMHpYZ3dRcHpDbGRiRzB4YUxMVlRleHpROUlWWkZYdHZ4VEVUVm5MOVhxS2xtNHJOT1FkTFZERlhOaEtjNVA4eE81bGkrWnMiLCJtYWMiOiI4NDNjMGU3NzI3NmMzNDA2NTk5MzU0MWY0YzZlODg1OTA0YTc3ODBjNTFiOThmZjhiYjg0MjlmMzY4MWU4ZGVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-339084058\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-459068595 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-459068595\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-987382444 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:29:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InROeFAxanFQM2NnMW5qUjFNYmFSZnc9PSIsInZhbHVlIjoiOENFTk5GWGpuZ0ViZUtnVjBYNVF0OExDUVdESlpGOFVKQTJodzBFR3BHMHg0cWJjVnE1b1dxcFREQUJ0WmtnQm9wRlUyV3hjdm1JZVFJYXYwY3JMWU5FQ3NndWx4a2xoN055WlFSUWpnakJkR0hpWlRqUHFyMnBsUDhPcmFqUjVMTEJUaURZZWljUG52MWxKOTZ0QnpYY3dQSEluT3ltQVR3SHRtT215bWhrTENQZ0doR01pVGhCYVozaW9FWFVaTHoybHpZRzRNTmRIRnJVQW5BYzJrNFNOTVE3N3ZGSE9uK3k5c3pTY04waVY4Q2VwdHZUbjEvNitQd3JFMmlrd0R0NGVnUDhwZjFHZUJRRDJQa09JdUVVazlMbVY5Q3hjZG51Ui9CdWhSZ2Z4cC9xaTJDSDE0dWtPSGtTbTB0aHRSRXBRdzlhRXdvdFNzYU9wQ29EUy9SQVBBWng5aUw1eXdROGx2RHdLM09hVWlvNEtaWnl0QUEyZ3p5dVdOTWxRNStjTTJXV25kcWhmRUdHMmxSdUEyZDVHY0dmZjhVekxlcVBOUkNDQmdRV3NjZENMRC9JdG1TK1RjUE80QmFiYlljOEovaDg4d3dhOEMrN2VzNUVueW9FWEF5cU5ERTZuck85RXJmU0ZLQTIrV3hxT0lvaVVRNm9NZURDV1FKVnIiLCJtYWMiOiIxNDhiYTkzOWFhNTU3MmUwMmIzNDlmNjZjMGViMjdjNTNmZWUxMDE1NjlhZmI1NWFkNTg3MGI4Y2QwOTJiMzdhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFQc1FxVjZMUzNycFdDdnJWL1VpVHc9PSIsInZhbHVlIjoibitGQ0ZQRCtVcCtkL096RjEyRzJzRzdOTnMxY0d3dmNucHArNzc1dnlwZ0NFWEl4VVRlaG16ekZqTzVZaG16MWpDOXVVU3QvMkFkOTh1UUU1UG9uUjBpQWM1ZDRnM2tNNi9jci9rVDNub2dXQnZFRVMxOW5DcXRpL24vdjJyeElXTmswcG50ckhwaTM5azFHOHA3WXB0eFdHOGQxOHlYNjYyZWNOaVNJRGdKUWVEcTM0ZzdWL3pMNVJ3MTRXMjg3WkNUdjFWeExsMFBISUFuV05venRYdk1QMGY2MldISWkrQWpsTXY5THBxYTlZT3EvbFRDYUt2M2tWV3RPZlpKUXJNK21UaUdMWFN3SEl4eGhiNWRDbEkzU3hQU0M1d092U3l5OGdITmRTVWtRVmtlR05TMklLRnhSdVBZMzRRNHNVZ1hhTjNFZEh2SkVxMVg0YmREWTlVNnVSbjVEUWlGK29LQkdObTUyQkJXZVd4VzZQeGp1Ti9pZElJWGE2L3lpWC90bXVnaHRTdDYwbW82Z1dNK2hRbUhoUUh0Z0JBSlBXTjI0czBRYXRQSHFMZGswbThLd1ljbTlBdnBuMDh1V2dleGhMdTlnczBTMjZ0WkFlOWlGYnUyNE5YVGJXbEtGVUdzbGREczN3UVI5ajJFQ3MvV21nT2J1Q293b2d1ZEMiLCJtYWMiOiJiMmY0OGE4MGNkMWUwZmFjNDM5YmVhNzkzNzRjNjJlNTQwMzA5MDFiYjVlNTA2MTc4YTgxNzA2MjlhZGI3OTBiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InROeFAxanFQM2NnMW5qUjFNYmFSZnc9PSIsInZhbHVlIjoiOENFTk5GWGpuZ0ViZUtnVjBYNVF0OExDUVdESlpGOFVKQTJodzBFR3BHMHg0cWJjVnE1b1dxcFREQUJ0WmtnQm9wRlUyV3hjdm1JZVFJYXYwY3JMWU5FQ3NndWx4a2xoN055WlFSUWpnakJkR0hpWlRqUHFyMnBsUDhPcmFqUjVMTEJUaURZZWljUG52MWxKOTZ0QnpYY3dQSEluT3ltQVR3SHRtT215bWhrTENQZ0doR01pVGhCYVozaW9FWFVaTHoybHpZRzRNTmRIRnJVQW5BYzJrNFNOTVE3N3ZGSE9uK3k5c3pTY04waVY4Q2VwdHZUbjEvNitQd3JFMmlrd0R0NGVnUDhwZjFHZUJRRDJQa09JdUVVazlMbVY5Q3hjZG51Ui9CdWhSZ2Z4cC9xaTJDSDE0dWtPSGtTbTB0aHRSRXBRdzlhRXdvdFNzYU9wQ29EUy9SQVBBWng5aUw1eXdROGx2RHdLM09hVWlvNEtaWnl0QUEyZ3p5dVdOTWxRNStjTTJXV25kcWhmRUdHMmxSdUEyZDVHY0dmZjhVekxlcVBOUkNDQmdRV3NjZENMRC9JdG1TK1RjUE80QmFiYlljOEovaDg4d3dhOEMrN2VzNUVueW9FWEF5cU5ERTZuck85RXJmU0ZLQTIrV3hxT0lvaVVRNm9NZURDV1FKVnIiLCJtYWMiOiIxNDhiYTkzOWFhNTU3MmUwMmIzNDlmNjZjMGViMjdjNTNmZWUxMDE1NjlhZmI1NWFkNTg3MGI4Y2QwOTJiMzdhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFQc1FxVjZMUzNycFdDdnJWL1VpVHc9PSIsInZhbHVlIjoibitGQ0ZQRCtVcCtkL096RjEyRzJzRzdOTnMxY0d3dmNucHArNzc1dnlwZ0NFWEl4VVRlaG16ekZqTzVZaG16MWpDOXVVU3QvMkFkOTh1UUU1UG9uUjBpQWM1ZDRnM2tNNi9jci9rVDNub2dXQnZFRVMxOW5DcXRpL24vdjJyeElXTmswcG50ckhwaTM5azFHOHA3WXB0eFdHOGQxOHlYNjYyZWNOaVNJRGdKUWVEcTM0ZzdWL3pMNVJ3MTRXMjg3WkNUdjFWeExsMFBISUFuV05venRYdk1QMGY2MldISWkrQWpsTXY5THBxYTlZT3EvbFRDYUt2M2tWV3RPZlpKUXJNK21UaUdMWFN3SEl4eGhiNWRDbEkzU3hQU0M1d092U3l5OGdITmRTVWtRVmtlR05TMklLRnhSdVBZMzRRNHNVZ1hhTjNFZEh2SkVxMVg0YmREWTlVNnVSbjVEUWlGK29LQkdObTUyQkJXZVd4VzZQeGp1Ti9pZElJWGE2L3lpWC90bXVnaHRTdDYwbW82Z1dNK2hRbUhoUUh0Z0JBSlBXTjI0czBRYXRQSHFMZGswbThLd1ljbTlBdnBuMDh1V2dleGhMdTlnczBTMjZ0WkFlOWlGYnUyNE5YVGJXbEtGVUdzbGREczN3UVI5ajJFQ3MvV21nT2J1Q293b2d1ZEMiLCJtYWMiOiJiMmY0OGE4MGNkMWUwZmFjNDM5YmVhNzkzNzRjNjJlNTQwMzA5MDFiYjVlNTA2MTc4YTgxNzA2MjlhZGI3OTBiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-987382444\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}