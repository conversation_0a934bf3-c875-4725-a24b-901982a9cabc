<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RemoveInjectedVerifyScript
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        if ($response->headers->get('Content-Type') === 'text/html; charset=UTF-8') {
            $content = $response->getContent();

            // Can't say where he injected this, but it's there and I can only remove it at the last request cycle B.B.
            $pattern = '/<script>var product_id=\'33263426\';\$\(\s*function\(\)\s*\{\s*\$.getScript\(\"https:\/\/envato\.workdo\.io\/verify\.js\"\);\s*\}\);\s*<\/script>/';
            $cleanedContent = preg_replace($pattern, '', $content);

            $response->setContent($cleanedContent);
        }

        return $response;
    }
}
