{"__meta": {"id": "X5726479930814e7eee3f0ef43277fae3", "datetime": "2025-06-30 15:30:25", "utime": **********.819631, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.425326, "end": **********.819643, "duration": 0.3943169116973877, "duration_str": "394ms", "measures": [{"label": "Booting", "start": **********.425326, "relative_start": 0, "end": **********.767664, "relative_end": **********.767664, "duration": 0.34233784675598145, "duration_str": "342ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.767674, "relative_start": 0.3423478603363037, "end": **********.819645, "relative_end": 1.9073486328125e-06, "duration": 0.0519709587097168, "duration_str": "51.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45137912, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00248, "accumulated_duration_str": "2.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.799116, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.565}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8096921, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.565, "width_percent": 12.5}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.812399, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 83.065, "width_percent": 16.935}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-998780829 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-998780829\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1720468938 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1720468938\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-4311284 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-4311284\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-404540716 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImQwOFJoT0V5MUZCVFU5WVJ2RE5jc1E9PSIsInZhbHVlIjoiR2tvR1YyazE4MmJiZkFJWDYvdXluczg3N2xpdTN5azBYSjRRNkFSSGFTWkQ2L2sxTmQ4cU1yZU40OG52UDZCcUM2TWlweUVnbG1ZUGdaUXA0bjJVZk1jamVieW1sdWNMWUhmS0xvUUxQY2NsNm8vS1FKWHJzZFdkWlBRSnI5QWJWSGJQWVlRV2Q3bmc2dFNMaHczc0VRdHFCM1RSN3lkWHM4cWw1MmsvUmMwVW5BOG9WUlB0bEdCaDc3eTMxN0pDdmtoTzljVCtEZzF6eENlUlR2c3hjUzFCSFBsVGhibE5RbTRKcVRFWFY5cnMwR1ZnYVhDRkl5Y3lmUFdnYlZsUlNVT3JzakpLZlRMaWhZbFdkdnNvZzV4NnlILzB2TXJiVk9YOE9xZEJmWlBvOHhxS0pvdGNnbEg2TFdndlZ6eGsveDBSL29rdmVrRTQrbGlLV3ZPMGd2L0Q4UFIrSUl1dnUxVitodUhYdTNQbVdXenI3c2JvM29PbXFDWmxGS1V3OHFLdTIxTUVCOS83SlhJRUdHV203NTRxUkk3bWJ1SllvVXh0cmxKdnpCSE94MWpIb0JGd0xTd3FhSXlMTS96NnhIM1FNTXc0ZkhoT3VuTzJkaWZVelBReUY1Q2wvUFRZWHQ0OEF3SFcxallseCtsVHI1aUxGV2d3bUlnVGRodmQiLCJtYWMiOiIyYTg5MGJmNjFjNzgwNzMzZDQ2ZGNmMmRkZWFjZThkNGRiOGZhODVlYmJlNTE4NzVjZWUwNzEyOWQwNTFhYmYxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImV5d0E4QlRmUzB3SVcyTENoWStsZ3c9PSIsInZhbHVlIjoiQWJNM04yZE9ud200RURuNnAxR3R1NHFyNUJKQWpJdVhpSExJb09rbWpiMmtMTXAxdXZUTHlER3dmdVdlbzMxTVdiTEMrNFRDWEVoWkg5TDZ4ckZnaisvQ1BNdXhtUnprazdPaVcrQU9IdFB3b2hxeDhyazFmSTBzTVFpSmkzMlpnUXlEdi8zTVpoYlB1RVhwSGZpQmxiVlFMYmhKb0FmRHBYalZxeGZ1NWF2c2lXaXoybzRremlLUEVLZm11NSszRHpuMXdZUFltcUdxTDJxZVR5YzVkRElwdC9TSEtBNjc3cEZUbm1YMVhzbS9zdXMzQ2pySjN6cmtzVmsvWWsyN0ZQRk41d1k5TjdLSUpuNzBQVy92TmpWa0gySEJYcUNzUVVjM3ZWK2hROW9lYlh1WkxJQThmWiszK0RjVGJtYzhVVnhUZGpVak82Zk1GSmVGWE9ZT1V4cktnV1BGRXF1dDY4bDQrLy9VMWtYblIzMlZ1NmNiV2k4aGpBRHVHSHMyZjNEc3lWa21FMWcvV0gvQSs1UGZpL3IxL0xqUlQ0UW9PdlBpRFd0WGRUellsVmczTzhqM1N0akdCeFFkeGhOU25oZmNFV2luUUFoeFhNd3FzYmpVUE5ET0QxSUk0WGhMNzJqalJZWHduTXZKQ1hFc0VrVWFpMytGcG5xcWdPWWQiLCJtYWMiOiJmMWRkMDJkYmY0YjAwYjRkOWM0ODY0Y2ZmYTM3YTdlNmYzMzBmNmMxYThkNDBmNDhmZjBmOTZhMDRkNmY3Njc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-404540716\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-392237879 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-392237879\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2111050678 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:30:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijl0NGZGZ0psc0FZOWxYYUhqb0NZL3c9PSIsInZhbHVlIjoiUWQ3QjZLYWptS3ZxdGIrZVM2d1JOazRCL2RheFQ2S0U1TSt5R1N3cks4MWJycEJDdG5uUHdjdmlBY2NqWFI2L3F1dlczdkJEQ0pVUU5nMDJWUTY2QW5OSDRzNUo0aWpXelVrbXlwc3ovMFB1OVp3dFlyWUpnVXRNdERQVUUyZTJPUU5vV0RXYWdRZnFwcEM4YTBCRHhsSjRaOXR4dHNsbDZiQ05KUURLMnAzdUhGeVBTcldyVVNDd2N6ajkwa1A3Z29lS0Yvbko3aUJmOGxsOGtwd25kRGRPcmltcDgyRnJJalhYYXM3b3NZVENmWm83WEtzM2pGOGZKY2lKZUZKU0JDd3lPR1YwWlVVVHRKaVhwYVN6UHpxV25jcUZtL3Fzc2pSUGdDZC95UlNmNnlrL3MzWW9ybmo2QlQ5MVZ0N2ZRdkZqRml5Q2FZcW5pdElQTWc3M2QvUFVPUm1ZTEJTL0xZd04vbjBmN3JRYUtRMERjNC9tL3U2b1J2WDhTMERUdmtVeGFEZEdQTkNGSTJqQVZMd1FYcmcyUFN5azYvSlI0YmFkSjJqMDZZY1h2SDUyZ3ZQQnQ4UmQydko3MytpWHlBU3c3Tnp6Z3RNWmVpWXJlcFJoVXdQM1lQZTV2VXZOWU54RXdHbFF3VUtvMnVyaUdyRnRHQ0lSMkZpK0tJTmciLCJtYWMiOiIwM2VjNWMyMjMwZWUxYWNiMmYwNjU4ODM1NWJmYzcyODZiNWRiMDQ5MTU5M2VhM2YzZDJiY2NiNmIyNWM2MGMxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:30:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InUyMWIxWVZMalpvdmthUjR4dUtEWHc9PSIsInZhbHVlIjoiOFFRV21FMkV3c3pPcmZqcXJSWVp2anE0aXZJZTAvYUdHUElWcjRyajJZK2FHckR4Rlc0U0VzbSt2bmphSEY3S2MyYWdSbkRHekREVTJjRTBZalprekJnMkdSbXA2cEpnVHk3RS8vbnoxT1kxQWRHL09maGxyQTdESDZ1WlAvYkpmOEF2OUVGZFAzQmdYTDM1cGlldjBXZWRQZzlzOTRKY1BVVVFoZHZRVHNqRWk5dXJsOUwxcjlyZFJxSzcxcllvYms2SWRtNVBCSGhabTdheDI1UVcrdEJvVjFSK05uZ3NXRUhSbmUycXY1Wlh0QTZUck1KTWF6WisxRGJFNkxlUnVCVW5aYXQ3Sm50UVdtL0NKb1NML2s3YjJIU2tXdVphQ1B5dzhWZWEzK3MwQk1lWXhrNGNXdjVMOXVBRXRuTHV2dlZtbU9oVGFwd3N5L0wraEhkRE5oVDFPZzUvSDJqK2RmRmlSRzZFNG5MRmlYK25QendZbGJ4a1FTT2pFYWFUZEIvQlRqRG9lTDdMQTV0aE9iWXRuTWJ6UDFtc0x1OGZUTjhIbEozQ1E5M1o1MVpyT2NqbURXZnJDanRFMDQveVhqUU52TkdwOCtuREZUb0tpQ3lCNzRoZG41Zm5xeWhPVm8zZHhMdEFIMkNwTGIwcUtaKzFrRGRGR1pvY0oydWwiLCJtYWMiOiJlOWQ4ZjVlOTM3NGNlM2NmNTc5NjJlNDI2NjExNjFiNzJiZTc0YWFkNjY4ZDJkNWIyMDY4ZTQwMDRhMWI1ODU5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:30:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijl0NGZGZ0psc0FZOWxYYUhqb0NZL3c9PSIsInZhbHVlIjoiUWQ3QjZLYWptS3ZxdGIrZVM2d1JOazRCL2RheFQ2S0U1TSt5R1N3cks4MWJycEJDdG5uUHdjdmlBY2NqWFI2L3F1dlczdkJEQ0pVUU5nMDJWUTY2QW5OSDRzNUo0aWpXelVrbXlwc3ovMFB1OVp3dFlyWUpnVXRNdERQVUUyZTJPUU5vV0RXYWdRZnFwcEM4YTBCRHhsSjRaOXR4dHNsbDZiQ05KUURLMnAzdUhGeVBTcldyVVNDd2N6ajkwa1A3Z29lS0Yvbko3aUJmOGxsOGtwd25kRGRPcmltcDgyRnJJalhYYXM3b3NZVENmWm83WEtzM2pGOGZKY2lKZUZKU0JDd3lPR1YwWlVVVHRKaVhwYVN6UHpxV25jcUZtL3Fzc2pSUGdDZC95UlNmNnlrL3MzWW9ybmo2QlQ5MVZ0N2ZRdkZqRml5Q2FZcW5pdElQTWc3M2QvUFVPUm1ZTEJTL0xZd04vbjBmN3JRYUtRMERjNC9tL3U2b1J2WDhTMERUdmtVeGFEZEdQTkNGSTJqQVZMd1FYcmcyUFN5azYvSlI0YmFkSjJqMDZZY1h2SDUyZ3ZQQnQ4UmQydko3MytpWHlBU3c3Tnp6Z3RNWmVpWXJlcFJoVXdQM1lQZTV2VXZOWU54RXdHbFF3VUtvMnVyaUdyRnRHQ0lSMkZpK0tJTmciLCJtYWMiOiIwM2VjNWMyMjMwZWUxYWNiMmYwNjU4ODM1NWJmYzcyODZiNWRiMDQ5MTU5M2VhM2YzZDJiY2NiNmIyNWM2MGMxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:30:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InUyMWIxWVZMalpvdmthUjR4dUtEWHc9PSIsInZhbHVlIjoiOFFRV21FMkV3c3pPcmZqcXJSWVp2anE0aXZJZTAvYUdHUElWcjRyajJZK2FHckR4Rlc0U0VzbSt2bmphSEY3S2MyYWdSbkRHekREVTJjRTBZalprekJnMkdSbXA2cEpnVHk3RS8vbnoxT1kxQWRHL09maGxyQTdESDZ1WlAvYkpmOEF2OUVGZFAzQmdYTDM1cGlldjBXZWRQZzlzOTRKY1BVVVFoZHZRVHNqRWk5dXJsOUwxcjlyZFJxSzcxcllvYms2SWRtNVBCSGhabTdheDI1UVcrdEJvVjFSK05uZ3NXRUhSbmUycXY1Wlh0QTZUck1KTWF6WisxRGJFNkxlUnVCVW5aYXQ3Sm50UVdtL0NKb1NML2s3YjJIU2tXdVphQ1B5dzhWZWEzK3MwQk1lWXhrNGNXdjVMOXVBRXRuTHV2dlZtbU9oVGFwd3N5L0wraEhkRE5oVDFPZzUvSDJqK2RmRmlSRzZFNG5MRmlYK25QendZbGJ4a1FTT2pFYWFUZEIvQlRqRG9lTDdMQTV0aE9iWXRuTWJ6UDFtc0x1OGZUTjhIbEozQ1E5M1o1MVpyT2NqbURXZnJDanRFMDQveVhqUU52TkdwOCtuREZUb0tpQ3lCNzRoZG41Zm5xeWhPVm8zZHhMdEFIMkNwTGIwcUtaKzFrRGRGR1pvY0oydWwiLCJtYWMiOiJlOWQ4ZjVlOTM3NGNlM2NmNTc5NjJlNDI2NjExNjFiNzJiZTc0YWFkNjY4ZDJkNWIyMDY4ZTQwMDRhMWI1ODU5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:30:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2111050678\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-114953452 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-114953452\", {\"maxDepth\":0})</script>\n"}}