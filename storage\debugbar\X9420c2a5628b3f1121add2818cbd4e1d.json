{"__meta": {"id": "X9420c2a5628b3f1121add2818cbd4e1d", "datetime": "2025-06-30 14:55:26", "utime": 1751295326.562947, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751295323.815521, "end": 1751295326.562969, "duration": 2.747447967529297, "duration_str": "2.75s", "measures": [{"label": "Booting", "start": 1751295323.815521, "relative_start": 0, "end": **********.241502, "relative_end": **********.241502, "duration": 0.4259810447692871, "duration_str": "426ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.241512, "relative_start": 0.4259910583496094, "end": 1751295326.562971, "relative_end": 2.1457672119140625e-06, "duration": 2.3214590549468994, "duration_str": "2.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50137408, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": 1751295325.295275, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": 1751295325.666181, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": 1751295326.417358, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": 1751295326.492651, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.39351000000000014, "accumulated_duration_str": "394ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.301643, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 0.465}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.305769, "duration": 0.2901, "duration_str": "290ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 0.465, "width_percent": 73.721}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.621403, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 74.186, "width_percent": 0.879}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751295325.569982, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 75.065, "width_percent": 0.183}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751295325.955495, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 75.248, "width_percent": 0.13}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751295326.2524672, "duration": 0.09348999999999999, "duration_str": "93.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "kdmkjkqknb", "start_percent": 75.378, "width_percent": 23.758}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751295326.38346, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "kdmkjkqknb", "start_percent": 99.136, "width_percent": 0.132}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751295326.385386, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 99.268, "width_percent": 0.091}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\erpq24\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": 1751295326.4884899, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 99.36, "width_percent": 0.64}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WfHnx50nFd5KEn6YaSnaUKikTZDCGXuQCgPdclMx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1101944178 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1101944178\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1227755794 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1227755794\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1208936006 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1208936006\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-934061108 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; XSRF-TOKEN=eyJpdiI6IlJ1c1dMUmFzalFBOWxLZ2t6YmlhT3c9PSIsInZhbHVlIjoiemo2OUJJRmZhaHh1ZTNQTk0zMzAzRy9pWU8xTldXSDUzZG1XOGJuN25BV28vTDFPdEwwU2R4cW5SckFoeWtwQllDUm9pOFhkZFdMUzdTU1lmTVNPejFNZTIwcmVTMDUrQjdvdG1aSXVlQkhUbzYyTFBmbWk5ZjF5R2ZMRG0vK1NqdHZHbllJVlFjdzIvZlBiUStUZ251djVLUm1pV3JBVDhPbzkyenRURElSVEVFem1PUVB4UVdnZjJldklsNHVVM2htbmprVENMWDZZKzJYVFdvUFZiSkNReEZUT0o5YXRac1hHM1hiUjliRjB2SC9aRy90aU1BKytPaWpJOUNFOWVISmZUOHFpN28wVURDMURHS0dEK0pCYjJKYXZPazEzT0tEcjRzVFNPbG5lTWNzbTEvcTBsUjNuR21uMERJNExaM2lUbzRhR25JSFZULzNFWHFJdUwwbGNUYm1lanQvVWl1Q1dkVmQvL3JSSEtCTlhUMlBrVGs3d3N1UGVIaXRaMlR2ajZRWEN3bURKb1BMTzhhZmxpbWIvbUhDNmdkNDB1dGF3RysxYzFNNmg4VWJGL3dYT2FCOEhGSmY1ZGpWOFRVLzdaQTVWQ3oxQ3ozRWpUNi9qWHVpK2VxdExZdWVuSkJJM01wUi9TV2VQa2pHSG9ZWVRVYTdYZEhyMGdxUjUiLCJtYWMiOiIwNjI5YjhkYmZhYjYxM2QwY2FjNzUxNmQ4OTI4NDJjYmRlMDdkOTQ1MGMzNTRkNTgxZDA2YzNkMWRmNWNkZjllIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkszNHVmMmpENXhGdEl0aG9reWZsTnc9PSIsInZhbHVlIjoiR09KQmkvWHFPeXcwd2RBMmpKZ1ptWXdveUM5Q28vY2xjck9xRWl0eVZiOUlhY2lnN1NKZ2hKc3RncUhsZG1udmlHYVRYdE1QODNYUGpjZWVVcmxGeExqUDRhc3ZFb21XdG5WSUxWSDZBYURQWis5bGhnQmR2Zms2QmV4bzVueHBCeXZWVzI4aTQ4NkZhU2RUaVZQNDhKQVFCaEhHcWZObU9LTmhuZ1gxeW1hWEp6aGcyWDBHQlYwUld5eE5TRmpRRmkwQU5reFkwQUZ4ZERJM1lxZTd5QnF6OUhyRFpIV0pudkpZUjFycXlKSnhxQ3gvK1JKUEV1UXlkWjdyRnFSSmFLQ1VjRjZNc1Q1eTlaWXY3WFQ2SW1JMHlqOTNBamZNaE1TK2NmbVczaVpHZGt2WFpSa1cvVkljd1JHSTJzR0ZUcURZTDc2S1I0RzFsYUpHTUkwSE92Si9sb0dXNVd0YURRZGdJdzU5M0NBM1g4Tlkxa0ttM1YyNTZiYmNoT3VoY1BrNExiMklOZUZOa1dLTVhrRThnTWczalZvYzIvN1RzRWNxNXJIdzJRSDEwekJxV21CenV5NzVxcUxDMzRwQVc4cGJUdTh2eWZTTmZmWW54dVZtanlpS3lhTXVBWG1yQkZSS2lEZVU0SUEyRnFqRnFaS2pRck1zRkY0ZUxtSTYiLCJtYWMiOiIxYjM4NjljZTU0MmViNGFiYjAyM2UzOTcwZjU3NmRkOWM2NDI4YjViZGI0N2MzNjU1N2M0MTc1YTM3MGM1MzI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-934061108\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1337334794 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WfHnx50nFd5KEn6YaSnaUKikTZDCGXuQCgPdclMx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FrGG07pA4trS7a4s5P41ttyuvhXtJqjpB72BLBrN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1337334794\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:55:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNwYTQ1bGNONFlvR2JpdEU4RnJHU2c9PSIsInZhbHVlIjoiRWk1Q3ZsZHM4UWdTRTN3cGR0UVNvUmYrdll1RlNOZHJUODE3VXZzOURoZnI0Umt1WDVxalU1RCtUd3ZvdEgyWVpSSEVNak9Ud0xrQjFUdHhnOGRwc2RqVlVubDRNRm9lQmU0UlhXakhacm9WblJVdlFsMDcreEg5VWE0ZGdIVHNYLzFPVDNsb1ZEODVxYkxvWEpOUlhpazhGNHZUY0dLOXVVcXlTMlUrTTVJTmp0QzIvbHpGTXdQU01sMjV4Z1Faclo3ZE9XY1Y3RmVSM2pwOGhsNzRXb0F2bFgweFQrT0pQTFlPRlppN01wU1VCcTVIK2ZXaHBKbE9GZ1BFN3dvV0NuUXBGbkdZa1lOMUZ1bkRPM21kUVFaK0xpQVR6VU9uVW5OTlRhdEUrcUkvTXVvd1JLWjY2dFFKTTRnU0dtSEtDOTNWWFlUdzlxSGhQWWpMNWVLVWVSNnVrU2dFK2JOVlZPSm91cHlPOVVkMnU2UkM0ekptRkdzeW9lM3dzWHBFNDdmamN5eDJQOHN0bmxLdCtFNTRqeElFUXM0QTgwN2dUeW5zYzBJcVN1WkFCbHI0dmlWNitVVGZQbW8yRCtZTWxhSlllZ1JlM1VUeGhkVXR4akhRTlE0VXFDOU0wOTBSZ2cyNWlndm52dDEwVG45ZndRdGZBZGxFdmlDTzFsTDMiLCJtYWMiOiJlZjk2OWI3N2VmODQzODVlNzc3ZDY1NTg2M2IwOTg0ZWI0MjFhMWZjMjNmNDVjN2JhOWZkOTc0YjI0YzAzNjA3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:55:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxFVm1sQkhDazhoaWFsMWZIRm1hdEE9PSIsInZhbHVlIjoiZkxqYkVMUHByNE9SMjB6QzdCQnJLQndaNGpWbW1hbjZkbkNHQzdGQUVGQTNLUjNHZGFDTkVNSWtQVkdCU1pybXU5emNFWlUxRmlQWVpjN25iS0F5cEs0am92V1lIb1BQSlF1Nm5Fa3R2d2h5aisrM2xkbCs0QUZveUtROWcvMDZobHFZeFVXcnNlb3V4WjJ3RTlaQUFXR1lqcEhIV3hYR3drSWZvVUFKby90Sjl3OHR2WXdFM3VrYlBHQmM3OXJocGV6dVFwY1d6TTN1WGtGUCt2RzVRKzJOVWYzcjEyZEdWVmMzSE5tdnh1UEhsbTJCNlpCT2NMaks0L2R4Qy9jc3FZejloa2ZQL0R3Ym5FZzBDMjVHeUpFZUcwS1BMZzZNaGFFWHl4WEE3akpMckxPVis5VGl5V01odUttbjNEUnhpZ3JQTlNZZFpicWpGdjM0dWNwY2JmeW9GL01rMmRCZW9DRlpSNFQ4cG8wODYxRSt5ZzZuQUhOOUJONklIZzNVcXhIcndLWFZrSERGVjBkWnJYYUovSXAyZjZmVFJrQ3hpWHZvaWFjRzQ2bW9oT2FHTTZLQjdiQVdJeFVCU2pBN09jeGlmWENlTUpvMW1IOUl3SmZSMkRkR0hLR1d3VFo0Nm1PbCtzRitQWTJrWWdkUXB0OVlSY1ZHUTlNSnlKNFAiLCJtYWMiOiI5MzJkNzE3YzI2ZjY0MjMwZTAyM2U1NDU2MDFmZWZmNzA5OTk4NzJmNmU5YTkyM2FmODY1MWRmZTA5YjM4NmUwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:55:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNwYTQ1bGNONFlvR2JpdEU4RnJHU2c9PSIsInZhbHVlIjoiRWk1Q3ZsZHM4UWdTRTN3cGR0UVNvUmYrdll1RlNOZHJUODE3VXZzOURoZnI0Umt1WDVxalU1RCtUd3ZvdEgyWVpSSEVNak9Ud0xrQjFUdHhnOGRwc2RqVlVubDRNRm9lQmU0UlhXakhacm9WblJVdlFsMDcreEg5VWE0ZGdIVHNYLzFPVDNsb1ZEODVxYkxvWEpOUlhpazhGNHZUY0dLOXVVcXlTMlUrTTVJTmp0QzIvbHpGTXdQU01sMjV4Z1Faclo3ZE9XY1Y3RmVSM2pwOGhsNzRXb0F2bFgweFQrT0pQTFlPRlppN01wU1VCcTVIK2ZXaHBKbE9GZ1BFN3dvV0NuUXBGbkdZa1lOMUZ1bkRPM21kUVFaK0xpQVR6VU9uVW5OTlRhdEUrcUkvTXVvd1JLWjY2dFFKTTRnU0dtSEtDOTNWWFlUdzlxSGhQWWpMNWVLVWVSNnVrU2dFK2JOVlZPSm91cHlPOVVkMnU2UkM0ekptRkdzeW9lM3dzWHBFNDdmamN5eDJQOHN0bmxLdCtFNTRqeElFUXM0QTgwN2dUeW5zYzBJcVN1WkFCbHI0dmlWNitVVGZQbW8yRCtZTWxhSlllZ1JlM1VUeGhkVXR4akhRTlE0VXFDOU0wOTBSZ2cyNWlndm52dDEwVG45ZndRdGZBZGxFdmlDTzFsTDMiLCJtYWMiOiJlZjk2OWI3N2VmODQzODVlNzc3ZDY1NTg2M2IwOTg0ZWI0MjFhMWZjMjNmNDVjN2JhOWZkOTc0YjI0YzAzNjA3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:55:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxFVm1sQkhDazhoaWFsMWZIRm1hdEE9PSIsInZhbHVlIjoiZkxqYkVMUHByNE9SMjB6QzdCQnJLQndaNGpWbW1hbjZkbkNHQzdGQUVGQTNLUjNHZGFDTkVNSWtQVkdCU1pybXU5emNFWlUxRmlQWVpjN25iS0F5cEs0am92V1lIb1BQSlF1Nm5Fa3R2d2h5aisrM2xkbCs0QUZveUtROWcvMDZobHFZeFVXcnNlb3V4WjJ3RTlaQUFXR1lqcEhIV3hYR3drSWZvVUFKby90Sjl3OHR2WXdFM3VrYlBHQmM3OXJocGV6dVFwY1d6TTN1WGtGUCt2RzVRKzJOVWYzcjEyZEdWVmMzSE5tdnh1UEhsbTJCNlpCT2NMaks0L2R4Qy9jc3FZejloa2ZQL0R3Ym5FZzBDMjVHeUpFZUcwS1BMZzZNaGFFWHl4WEE3akpMckxPVis5VGl5V01odUttbjNEUnhpZ3JQTlNZZFpicWpGdjM0dWNwY2JmeW9GL01rMmRCZW9DRlpSNFQ4cG8wODYxRSt5ZzZuQUhOOUJONklIZzNVcXhIcndLWFZrSERGVjBkWnJYYUovSXAyZjZmVFJrQ3hpWHZvaWFjRzQ2bW9oT2FHTTZLQjdiQVdJeFVCU2pBN09jeGlmWENlTUpvMW1IOUl3SmZSMkRkR0hLR1d3VFo0Nm1PbCtzRitQWTJrWWdkUXB0OVlSY1ZHUTlNSnlKNFAiLCJtYWMiOiI5MzJkNzE3YzI2ZjY0MjMwZTAyM2U1NDU2MDFmZWZmNzA5OTk4NzJmNmU5YTkyM2FmODY1MWRmZTA5YjM4NmUwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:55:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1730318069 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WfHnx50nFd5KEn6YaSnaUKikTZDCGXuQCgPdclMx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730318069\", {\"maxDepth\":0})</script>\n"}}