{"__meta": {"id": "X9f9d82c1264f6471ba820ea9fcaa70d2", "datetime": "2025-06-07 22:18:09", "utime": **********.251436, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.908715, "end": **********.251478, "duration": 1.****************, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": **********.908715, "relative_start": 0, "end": **********.030913, "relative_end": **********.030913, "duration": 1.****************, "duration_str": "1.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.030943, "relative_start": 1.****************, "end": **********.251483, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "221ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.029509999999999998, "accumulated_duration_str": "29.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1297932, "duration": 0.0259, "duration_str": "25.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.767}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.186168, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.767, "width_percent": 5.49}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.223873, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 93.257, "width_percent": 6.743}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-4681169 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334686693%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1OMHFSbXhGdjBiZnRiajZMNVFpd3c9PSIsInZhbHVlIjoiTEI3SjgvVUhxUkE2aEgwUEFCSXlleHNZZ2ZTMWNMeHVmVXVHOWVtU1IwdkhrQ3hFTWpiOHNZVmViZklTbnFHbGFhSHNscnhZZU5DZkk4SmpFZXErMDJKMVNVeE5EVUtVNjY4UjViM2tFUVRqL1RrYVFLK0FUTk1Ha3U2TXhnWkRhb0ZONnpJWU9rSzZiNWlGVWdRL2FTU0h4Y2EycUkwb3ZXRVVNMjg5RGhjK0JiSmVIK3VXNmdaZzcxVCtrWjE4ZUxMNEorR09Fa0N3MVJvRHVILzdaM2o0LzBIQm1mT1locVowd1p6VUFmT1BaVUYrQ0RHcU1GZTd1Z2wwRVAweFJqbmQ5NlRuSkFCbTZOdmJYYjAzS2ROeE85eEhtb3h0MG9td1lFMzNsK3FyTE5rNWJ2d0VUQVZ5N1J1cUtPVEw0Z0J4eFFkR3o0Tm1sbzNRMkU5a0FmbWs5TEY5Rno4RGN1TWZYS1lKQ0JQVk9yeGNUZFJZMDEwTHViM0hHQzJqWDVWS2hpSFNWTXYvdWlVWEhidGtXWFREQmQ0cXpmVFhXaHQzai9HSldkOGZXdENMQ0tRN3dJTUo3eEpkVEZoRHByWHBWSktuKzArTE82MDNsVFZ3RnI1T1MvWDR6VnRmOXdxNi9nTTFYcjBsQ0lDTEJLRW5KUytYTUZ6SXBHMngiLCJtYWMiOiI5YmUyNzIxYmMyMmZhYWIwNjY0MmNlNDEwYzA3OTUzZWQ1MzhlM2NiZDcwMTAxODY0ODc1YjI5MGY3NGZjNDBmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Inhyb2JTNXdaOXFxb21PRHltWHFPZXc9PSIsInZhbHVlIjoibHl2RXdySWwzbHZUQjF4aElBR09EUnRGaFA3eENuK1ZBSGw1cDFLK2s1SXBxVXVXWjJBTmlDZjZNczlFdStMVW9jdlNuVHhscVBqZWVjTDVVOGxmcTFTaktIN2R1dGJaNW01Z3FFY2RLMzhtTDBhbHhDSk5Hc2tyWmtGSlpUTU1ic1d5MjVHNnVjMHFiS1QxTFJXZFZEVFZnUjhhUjk2ZHphMGhiQk1obGR6S2NnTWk3T0NPQTlUV0NzZk9CbDFNVTNpTmF0d1JOM1BBV1l3c0lQN2dlUlBvaXROSys0RjhNN2pxYkY5dldhcUNWL29XcHlGVTBjS0tmZCtCcGhyelpWS1JJdStZbnY5bUR1aWpxVjMrMDB1Sjg0TG5IOEFvdm5WR1VnR01sM1ltSmVRL1VzL1dJSys0Q3dJYThNRlZ2NE5mQXUreitqcTdtdS84allvanZqY1JxU29CSEE4SnNuWVZPSWpiTklzQVU3N1E1UlM4QjJWUHpFUjcwZlBGb0duMTJYUWhzOTBiSGtrb3UzUURzNnV2cHZiWFU3S3JhelpidG9hcnJINzcrQ0JVODdvMlFVNmhPbGEwdGZSRkphRDh2bG8vTnUvSzNCMU5jakVtbnluSUxLM0tPSDE4elBINDRaYjNGRncrL3kxZ3l0VjU2T1p4SWVmRjJpcUgiLCJtYWMiOiI4ZTRkYTVjZmJjNDlmMWQ5ZWFmYWZiMWFmY2JiNTVjMGFlMzRkNmE2MjkzNzAzMWVkMGU5MjUwZDA3ZTgzZjgwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-4681169\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1951013369 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PmFRgUqysTWH0qB2ROlhITKoLIETZGW2KykVXtOx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1951013369\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:18:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFJN0MvSGE2MFdHbUN4RlV1ODZFL1E9PSIsInZhbHVlIjoiM2h4cGVqa1VoSWQ4b3V1ZEZ3a3FGamdnZnJwT2Q1cm5LaVovRkR4dFVWRERLT3hNditUT1pUbUEwSHdvWE81UnZZSzB0VGxZZ3hjRlFKSm9CVnR1VXFtMkU0cnMwMjNmbUxuRVp3N3dQUHRvbUNPYTg0UWVnNGF6RkdMbUx6dlZUN1VpUVBIZFpzeTlwMkxLWHcxUTlhNGhuREM0SWlqYzBRSEF6ZEZOZmVaN3JxdCtFVjgrSVo4Z1ZVMHVycjBvd00rTHNwZWZjdGFCampxdjUzZ1dSelVhQjdqNytMTXFTaVZvdW9mZU1MOExoaFUydTlpQ0xiUnBSMCs2aHVSdXJaRWJoREIrM3hFTEgzYklIV3pLZi90aXpzR3FUNlViNGhHQmpDejRiUnJXZTByakk0aXNxeC9JYTExSFhTWFgxV2VZVXAwNURxYXZncDJjL0F3ZzBRYmd1dVlQeFI4YmpEaXRpVVA2b1JBdkFtays4bXdXQUhvWmhYc0s4OGV4TnB1dUdFTWxjenFqcWZ2cXlyNEw0VDJPTVlQSlhQVHlqWTdaVDZpUUJSYWUwTXFscy9lbjhXaHBMUWNwT0twdzI2RXVaZG5xOG9JOThndWZGZlFwaDRmT2tHamorc3dJYUhSeTRiQ1RLY1Z3TFpuSXZIS2NMK1RJRTJmTHpGZDkiLCJtYWMiOiIwYmQ3ZTAyZmM3ODVjZmZiOGNjMDFhMmQ5ODE2YjQ5ODJlNjQwMzliYzkxNGZmMWIyZDRlNjI1OWI4M2M1NDc0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InVOL0tQcGtkT1RwNTBNRUt1dXlDSHc9PSIsInZhbHVlIjoidnBZeEx0akhkOWRidUlORFY2cDB2eVpzSFhBWnV3aUV1eG4rZE1ORGpldmlOMkY2QlJtc3ZXU2xCSC9Lc1hiTGRSRTJCWmVaS1BYUHpNY2hxSTcwTUc3K2NxMXZqMjN0UmwvUllyeE5MaVFwQnpBQ0pQQ3dhNCt5dVNCazZxakdFaVdhTGQzODhhQ0QvcHBXWTNyaVZ1blFRVFNSWmoraXB6WEQrN0gzN1c5UFVjM1NsSmxxUFIxWWpDYTc1QVRQV1I3dEd6OGg0OENScFJydTFNYnhFeE00QnhmOFlEc1laY3JMNE5sSnRmSVY5dStjb21LanFOUkZTNG9MV2thRnNuOGVsR0ltdzgxV2p5Nkt2bGVZK2ZUMDd2TmxLalAxT2Z1WFJLOVMyY1p1MXNBU0lhallPVjVGaEV5NU5jOTQvQTRiL0dhQlRaWE56NUNuRGNYbEhYdnNrbk80RVAxNW4rOWhVQjUwa3ZlM0VIaHVqd3ZvVXlza2Q0Y1YxOTZML3lmMWxyeEJhNUVDenUzWFV1WW9nejlWN21GMzdZeWxhTnF1RjFJeU9xVlpFSEp4Y053a3Zka0dTb1FwZ0ltcW53cWpkZXM0amExVGhoODBwYXBWMVBhUXl2TXIyeUp0MUkwTkdEL3pLa09vQ1BNTzErNzlSQzdvendwQVMvUWgiLCJtYWMiOiI1YmIwNzlmMGI4Yzc0ODhlYjdkOGUwNGU2MDFjNDA4YzI3YTVlMzFiNDNhZjgxMDRmYmMzYjRmZTNlODAyMGQzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFJN0MvSGE2MFdHbUN4RlV1ODZFL1E9PSIsInZhbHVlIjoiM2h4cGVqa1VoSWQ4b3V1ZEZ3a3FGamdnZnJwT2Q1cm5LaVovRkR4dFVWRERLT3hNditUT1pUbUEwSHdvWE81UnZZSzB0VGxZZ3hjRlFKSm9CVnR1VXFtMkU0cnMwMjNmbUxuRVp3N3dQUHRvbUNPYTg0UWVnNGF6RkdMbUx6dlZUN1VpUVBIZFpzeTlwMkxLWHcxUTlhNGhuREM0SWlqYzBRSEF6ZEZOZmVaN3JxdCtFVjgrSVo4Z1ZVMHVycjBvd00rTHNwZWZjdGFCampxdjUzZ1dSelVhQjdqNytMTXFTaVZvdW9mZU1MOExoaFUydTlpQ0xiUnBSMCs2aHVSdXJaRWJoREIrM3hFTEgzYklIV3pLZi90aXpzR3FUNlViNGhHQmpDejRiUnJXZTByakk0aXNxeC9JYTExSFhTWFgxV2VZVXAwNURxYXZncDJjL0F3ZzBRYmd1dVlQeFI4YmpEaXRpVVA2b1JBdkFtays4bXdXQUhvWmhYc0s4OGV4TnB1dUdFTWxjenFqcWZ2cXlyNEw0VDJPTVlQSlhQVHlqWTdaVDZpUUJSYWUwTXFscy9lbjhXaHBMUWNwT0twdzI2RXVaZG5xOG9JOThndWZGZlFwaDRmT2tHamorc3dJYUhSeTRiQ1RLY1Z3TFpuSXZIS2NMK1RJRTJmTHpGZDkiLCJtYWMiOiIwYmQ3ZTAyZmM3ODVjZmZiOGNjMDFhMmQ5ODE2YjQ5ODJlNjQwMzliYzkxNGZmMWIyZDRlNjI1OWI4M2M1NDc0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InVOL0tQcGtkT1RwNTBNRUt1dXlDSHc9PSIsInZhbHVlIjoidnBZeEx0akhkOWRidUlORFY2cDB2eVpzSFhBWnV3aUV1eG4rZE1ORGpldmlOMkY2QlJtc3ZXU2xCSC9Lc1hiTGRSRTJCWmVaS1BYUHpNY2hxSTcwTUc3K2NxMXZqMjN0UmwvUllyeE5MaVFwQnpBQ0pQQ3dhNCt5dVNCazZxakdFaVdhTGQzODhhQ0QvcHBXWTNyaVZ1blFRVFNSWmoraXB6WEQrN0gzN1c5UFVjM1NsSmxxUFIxWWpDYTc1QVRQV1I3dEd6OGg0OENScFJydTFNYnhFeE00QnhmOFlEc1laY3JMNE5sSnRmSVY5dStjb21LanFOUkZTNG9MV2thRnNuOGVsR0ltdzgxV2p5Nkt2bGVZK2ZUMDd2TmxLalAxT2Z1WFJLOVMyY1p1MXNBU0lhallPVjVGaEV5NU5jOTQvQTRiL0dhQlRaWE56NUNuRGNYbEhYdnNrbk80RVAxNW4rOWhVQjUwa3ZlM0VIaHVqd3ZvVXlza2Q0Y1YxOTZML3lmMWxyeEJhNUVDenUzWFV1WW9nejlWN21GMzdZeWxhTnF1RjFJeU9xVlpFSEp4Y053a3Zka0dTb1FwZ0ltcW53cWpkZXM0amExVGhoODBwYXBWMVBhUXl2TXIyeUp0MUkwTkdEL3pLa09vQ1BNTzErNzlSQzdvendwQVMvUWgiLCJtYWMiOiI1YmIwNzlmMGI4Yzc0ODhlYjdkOGUwNGU2MDFjNDA4YzI3YTVlMzFiNDNhZjgxMDRmYmMzYjRmZTNlODAyMGQzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1781989270 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781989270\", {\"maxDepth\":0})</script>\n"}}