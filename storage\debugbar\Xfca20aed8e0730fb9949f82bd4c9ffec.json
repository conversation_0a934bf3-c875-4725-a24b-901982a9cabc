{"__meta": {"id": "Xfca20aed8e0730fb9949f82bd4c9ffec", "datetime": "2025-06-08 15:30:58", "utime": **********.583863, "method": "GET", "uri": "/printview/pos?vc_name=7&user_id=17&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 163, "messages": [{"message": "[15:30:58] LOG.warning: Implicit conversion from float 160.79999999999998 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 186", "message_html": null, "is_string": false, "label": "warning", "time": **********.533377, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 1.4 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.534942, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 3.5999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.535392, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 3.8 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.535569, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 7.199999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.535719, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 7.399999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.535873, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 13.2 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.536026, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 13.399999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.536172, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 16.799999999999997 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.536318, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 16.999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.536467, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 20.399999999999995 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.536619, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 21.799999999999994 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.536763, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 26.399999999999995 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.536913, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 28.999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.537054, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 31.199999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.537198, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 33.8 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.537344, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 37.4 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.537478, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 39.6 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.537623, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 39.800000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.537755, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 44.400000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.537909, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 47.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.538044, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 49.20000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.538181, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 50.60000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.538338, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 52.80000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.53849, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 54.20000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.538626, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 56.40000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.538782, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 59.000000000000014 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.538919, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 61.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.539067, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 61.40000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.539215, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 66.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.539362, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 66.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.539508, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 68.40000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.539647, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 71.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.539782, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 73.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.539925, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 77.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.540075, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 79.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.540214, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 80.60000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.540358, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 82.80000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.540505, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 84.20000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.540649, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 87.60000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.540794, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 89.00000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.540935, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 92.40000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.541081, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 93.80000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.541244, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 97.20000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.541398, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 98.60000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.541541, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 100.80000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.541689, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 102.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.541835, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 105.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.54198, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 105.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.54212, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 108.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.542264, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 111.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.542406, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 114.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.542551, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 116.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.542699, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 118.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.542842, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 121.40000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.542983, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 124.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.543128, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 125.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.543271, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 127.20000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.543419, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 128.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.543561, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 132.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.543707, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 132.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.543854, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 134.40000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.543996, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 137.00000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.544137, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 140.40000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.544283, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 141.80000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.544459, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 145.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.544622, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 146.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.544765, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 151.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.54494, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 153.80000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.545083, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 156.00000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.545233, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 156.20000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.545377, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 158.40000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.545537, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 159.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.54569, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 160.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.545849, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 159.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.546016, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 160.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.546201, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 159.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.546364, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.555424, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.555687, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.555855, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.556018, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.55618, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.556341, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.556491, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.55664, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.556789, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.55694, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557089, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557245, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557413, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557562, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557721, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557881, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.55803, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558178, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558327, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558484, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558645, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558795, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558958, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559113, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 24.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559268, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 25.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559429, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 26.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559603, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 27.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.5598, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 28.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559961, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 29.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560129, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 30.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560311, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 31.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56048, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 32.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56063, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 33.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560784, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 34.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560941, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 35.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561096, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 36.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561249, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 37.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561402, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 38.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561567, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 39.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561742, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 40.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561904, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 41.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562067, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 42.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562225, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 43.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562397, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 44.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562564, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 45.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56272, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 46.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562877, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 47.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563036, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 48.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563193, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 49.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563383, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 50.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563559, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 51.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56373, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 52.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563896, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 53.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564057, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 54.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564221, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 55.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564387, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 56.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564555, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 57.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564738, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 58.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564901, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 59.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565067, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 60.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565239, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 61.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565406, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 62.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565576, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 63.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565753, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 64.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565936, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 65.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566122, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 66.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566301, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 67.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566485, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.566653, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.566821, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.566993, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.56721, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.567364, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.567518, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.567684, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.567878, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.568035, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.568206, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.568375, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.56855, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.568738, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.56891, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.569077, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.569243, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.5694, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:58] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\to\\newo\\3\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.569559, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1749396657.78209, "end": **********.58416, "duration": 0.802070140838623, "duration_str": "802ms", "measures": [{"label": "Booting", "start": 1749396657.78209, "relative_start": 0, "end": **********.299521, "relative_end": **********.299521, "duration": 0.5174310207366943, "duration_str": "517ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.299537, "relative_start": 0.5174469947814941, "end": **********.584162, "relative_end": 1.9073486328125e-06, "duration": 0.2846250534057617, "duration_str": "285ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54807208, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.printview", "param_count": null, "params": [], "start": **********.510039, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/pos/printview.blade.phppos.printview", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fpos%2Fprintview.blade.php&line=1", "ajax": false, "filename": "printview.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.printview"}]}, "route": {"uri": "GET printview/pos", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\PosController@printView", "namespace": null, "prefix": "", "where": [], "as": "pos.printview", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1306\" onclick=\"\">app/Http/Controllers/PosController.php:1306-1408</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.03645999999999999, "accumulated_duration_str": "36.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.361733, "duration": 0.027, "duration_str": "27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 74.054}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4045348, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 74.054, "width_percent": 3.044}, {"sql": "select * from `customers` where `name` = '7' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["7", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 1314}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.411112, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1314", "source": "app/Http/Controllers/PosController.php:1314", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1314", "ajax": false, "filename": "PosController.php", "line": "1314"}, "connection": "ty", "start_percent": 77.098, "width_percent": 3.703}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 1315}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.416524, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1315", "source": "app/Http/Controllers/PosController.php:1315", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1315", "ajax": false, "filename": "PosController.php", "line": "1315"}, "connection": "ty", "start_percent": 80.801, "width_percent": 3.044}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.445947, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 83.845, "width_percent": 2.935}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.4513178, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.78, "width_percent": 2.606}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 517}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 1318}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.461167, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "PosController.php:517", "source": "app/Http/Controllers/PosController.php:517", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=517", "ajax": false, "filename": "PosController.php", "line": "517"}, "connection": "ty", "start_percent": 89.386, "width_percent": 3.84}, {"sql": "select * from `product_services` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 1393}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.488925, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1393", "source": "app/Http/Controllers/PosController.php:1393", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1393", "ajax": false, "filename": "PosController.php", "line": "1393"}, "connection": "ty", "start_percent": 93.225, "width_percent": 2.331}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 1402}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4936361, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1402", "source": "app/Http/Controllers/PosController.php:1402", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1402", "ajax": false, "filename": "PosController.php", "line": "1402"}, "connection": "ty", "start_percent": 95.557, "width_percent": 2.222}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "pos.printview", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/pos/printview.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5194502, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 97.778, "width_percent": 2.222}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2097696533 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2097696533\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.459602, "xdebug_link": null}]}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 12\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 33\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/printview/pos", "status_code": "<pre class=sf-dump id=sf-dump-21969242 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-21969242\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1580516063 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">17</span>\"\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1580516063\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-103618696 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-103618696\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1689040471 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396510751%7C76%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRVNk5yOUd3TC9KelIwTlp4cHRGR3c9PSIsInZhbHVlIjoidERnNUh4T3pyMVVIY3c4N3FaSXcwYVJJeHV6L002aEhaS3BxOVVOektEN25Fbk1BVTFuMGV5dGFWWnNWQnhwZVc5UWljL0ErbkpLa255Wk83dEZWaFdBV2I0bGNGVUJxUFcvcU5nR2oxYzR6Y1pzcXZoYlFiMXlyZTdpTWE2ekV6N0ZUVjdtaWgzdTVhZ1B0LysvNm5nK0F4TkxQc2N6RFhtdGVnOStFY1J3UFdxL2tZaXZQZnVORkw4NVFUTDNsUGxOeXl3WHhLVmJtd0FrMmNCaGt6R1N0VCtpUi9pN0NUenhFSmVUNXk0czRnWHhKZmNUVFFlZXgxdW9Ycmo5c2NlR1BnMTdkb2lWNENSMFVxY0FPNVZrQWREQlNERXE3VHNVWVhLa2xrdXYxdzQxbk52TndDN0dtVlI2ejVmNWZTRURaMU1tVjV0SkpJbmp5WTJVVVRHaFYrZHdNUmZEKzF5bFVTWHE3bW1GZkVicGcza3A1UzVxYlc5QUN2cnVSQWlWem0zK1paVFpLbWZ5SXpFZ0RsbGpRRW5SeHlTU1Vzc0JBNFYwOGo1ZmVyczA0K3hxeVlSclk0WWpFQlcrTm5FUjhsdE5adWlZYlRyQ2Fsd1VlbmttWUJza09yQmViT0czQUpRSG4yU0pBQmh5TzQzZlBLeDFoYWV5U3JsMHkiLCJtYWMiOiI1MTAzMzFlYTEwMmNlYjBlOTY1ZTBlZWRmYTBjOTNkZmMwYzIzZmNjZmRjOTJmZWRkOTJiZjJiY2YzNDJlNWNmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IitQMEFxL1N2eXpGRGNBd3ZnSURBOFE9PSIsInZhbHVlIjoidjZtWnhtTWRaZGErZk5QWnd6ZEd2dDUwS01wRnNSYnR6a2s5OVdiRlZwdlo0bjZCRytIQ3F5RWxyRngrNmZuMlJXQnc0cDFDWjJSR014SVlDMWZRL2tuOUNLVldUbU0zN2djYldhdm96c1dWd0FJRXl6emZQL3ZsQjBUeTV3dm5yaWp6MUR5V3hUamM1UU45elZreWRFU2JnVm9PR3VZL01kcDhKZys4V0RtSEZTR0h2ZitWM2xWcXB2ZmV5WjVuNE05Qk1aZHRwUGU3MHBBblhEOVVoajNrWVhiTkhOdTBqM3dqMy9HSHlFZkZMK0JCcmF3ZE5IamlrWWQ3c2YxNStGbnZ6NjJjT2luWUl0MVgvbWx0QzFLMVM3T29XcmRrZGJJQUREdkZoTzJ0Ylh4TEQ3Z25nc3pHVnUwQjNuSjlwRlpoeE1iMWViWDMwcDNKREJackJJZXp0bmhoYVdZYUNvNnFDS1lTbFRjTSt6TGdJajhsOFZKekREMzQ1WTlWVW84b1RDeUhxNmJyU0Y5K0xGVHl0NzRUb2FCV0k5ZmZVbGExbytQZ1E1UVh6akZiK0k0eFVRYWdoYnRyQ01XcjVXQjR0M2h5Qjkyem56eCtBK1dST0puWlNYT3kxRzUxY3JtdGFxS2ZUUXpoQW9KKytXNm9LU1J0ZmtiTnpVeEIiLCJtYWMiOiIyNGM0YWI0M2RmMjVlZTc2MGI0ODk3NGNkZGMwNzQ0MmZjODdjNTQ1ODI1ZDEwMWVkZWQwOGZhNjcxMTk3OTExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689040471\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1295256735 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1295256735\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1006075539 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:30:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpsS1hIQVVNSkt6NVBBSkRUaDQ2OFE9PSIsInZhbHVlIjoiVmF3NTNkVmZBbXdReU9iMVBoM2VuRFRFT1BZRnFMS0hSZ29pbnVrcWhsTm1kN1JveG1IeUxEaGRXRDA1MnZTNGRJWEF6UEJEYVNTRXJ0a3V4YnloODVkRXMwSk1HcjVBZ2FiOTJGUDJhcEk0Y0RyZVNrNnNac0t5OVlnQ01qbytiaVJDQXc5NjdLQVlMd2FqRzBkSEM1TGlUdTZhcmdRM01EZSszS3RnZFdNbGlScGhmMXZRdjB4cFNuL1MzdzJLSm5Ncm92MzB0UWkzUGRNSWNzYmx2ZHp0Zk1YQlAyUm93ejN4dUlscGpqUTZvYXgyMzN5Y2s2NVprdU9DSW9SV21ZeHRvQlNSWnNFak1lVVlCcVo1S3BGSjFkTjJVR0VKeDZQUmpveG5hU0pDWDlPSktVSXY1clZqNFQvRU5PaVZrMU53NStVZm1KVWw1OWJ6T0tHeEx6NzdQQVA1cjBZZ1MrZUxzcW1renRBMGwzcmloNzdDK1k0eU43TGJBQUNtSFJOVzNOb0NDRGs3UW0vY2lzdzl3NXE5Vkt5eVE4SmNKcmExNFRqdkR3MjZ6ZVllV2Z6QXFWWU53T09UNzR3SDFuSWFFcmN2bTFkSnFBK0hjOG5pQW1TcWVZOEJTRlB0dXNOSG1jUjNiOHBJVjBLMmNyWitSbHFrK1BKcnhCMDYiLCJtYWMiOiIzNWY4M2Y0YmQwNjJmNDRlZDJkZDViNGNkYjRhMGMxNmMxODY0M2YwYjg3NDg3N2MxNjBiNWE2ZmQyMjA3YzAxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:30:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IktPejAybmI4bXlEVlUwVE1LU2hxNEE9PSIsInZhbHVlIjoiOUNtZTV1SXJKalBWZ2tuVEhMKzNrQUs3eDZXVC90dFhibVJtTVJEazg5Y0Y3M1pxREJyT2VVTVEyOUhCNDNWangrWGQ4TWN2UlpYdFIyZlpoWldkRzlSay9XZ2dpN2ZJK1lQY2xGcjQwVDBJZk5mcFMvMDRDNE81ZzVQR3U5bXNnUCs5WnJiNjdLSXVOYkxlcWdXWXNWT1BIcm14NWFRSCtoWUl6eGFwcnNNUWxLUVNDV2JkNGpmYkpiTG5sSThwQWwxRElTRGFVVUVmR3dOaVBoS3I3RjlSdGtMdnNnMTdDOGtZUHU1YVUyOGJ5RnlJYlJpQzBmMU55dXFuanMrOXBwWWRtblNuSDMyOE14V0tTcnB6TEx5UkJWZ1VCODdmOUd2SUJ4a0NMNEJ0MWlsUlp3bXo0Zk5qMXNrV1E3MDdFOXBiTDh6d011djRFZVhRWWR5NUQzY3paS3lmTlVtVndBTUgxV0EzOHBJaU0rU0x6SzdxWndpNS9PYVdoTTZVMFpJcEJUcDRmcHRjc1N4U0lwYnUyZmFtLzNFcGlTaGp2TGtMMWVQN09XZUxJQVQ2Q2VuY1p4SDNlekM5M3c2bFFzVnB4NWNzR3hsakZTZkN4YjdQYitpUitvU1AxTXA0YzF3WnRwOUt3ZlFPWVJtNmRLNzRwUEs2VmFscXZ6R1IiLCJtYWMiOiIxMWU1NDkzOGJhNmNjM2E4NjJmYTViOWZlY2FjNWViMzJjNDc0OGE1MDRhZGRiYzJjODNjY2I2ZGRlODY3NTc3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:30:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpsS1hIQVVNSkt6NVBBSkRUaDQ2OFE9PSIsInZhbHVlIjoiVmF3NTNkVmZBbXdReU9iMVBoM2VuRFRFT1BZRnFMS0hSZ29pbnVrcWhsTm1kN1JveG1IeUxEaGRXRDA1MnZTNGRJWEF6UEJEYVNTRXJ0a3V4YnloODVkRXMwSk1HcjVBZ2FiOTJGUDJhcEk0Y0RyZVNrNnNac0t5OVlnQ01qbytiaVJDQXc5NjdLQVlMd2FqRzBkSEM1TGlUdTZhcmdRM01EZSszS3RnZFdNbGlScGhmMXZRdjB4cFNuL1MzdzJLSm5Ncm92MzB0UWkzUGRNSWNzYmx2ZHp0Zk1YQlAyUm93ejN4dUlscGpqUTZvYXgyMzN5Y2s2NVprdU9DSW9SV21ZeHRvQlNSWnNFak1lVVlCcVo1S3BGSjFkTjJVR0VKeDZQUmpveG5hU0pDWDlPSktVSXY1clZqNFQvRU5PaVZrMU53NStVZm1KVWw1OWJ6T0tHeEx6NzdQQVA1cjBZZ1MrZUxzcW1renRBMGwzcmloNzdDK1k0eU43TGJBQUNtSFJOVzNOb0NDRGs3UW0vY2lzdzl3NXE5Vkt5eVE4SmNKcmExNFRqdkR3MjZ6ZVllV2Z6QXFWWU53T09UNzR3SDFuSWFFcmN2bTFkSnFBK0hjOG5pQW1TcWVZOEJTRlB0dXNOSG1jUjNiOHBJVjBLMmNyWitSbHFrK1BKcnhCMDYiLCJtYWMiOiIzNWY4M2Y0YmQwNjJmNDRlZDJkZDViNGNkYjRhMGMxNmMxODY0M2YwYjg3NDg3N2MxNjBiNWE2ZmQyMjA3YzAxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:30:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IktPejAybmI4bXlEVlUwVE1LU2hxNEE9PSIsInZhbHVlIjoiOUNtZTV1SXJKalBWZ2tuVEhMKzNrQUs3eDZXVC90dFhibVJtTVJEazg5Y0Y3M1pxREJyT2VVTVEyOUhCNDNWangrWGQ4TWN2UlpYdFIyZlpoWldkRzlSay9XZ2dpN2ZJK1lQY2xGcjQwVDBJZk5mcFMvMDRDNE81ZzVQR3U5bXNnUCs5WnJiNjdLSXVOYkxlcWdXWXNWT1BIcm14NWFRSCtoWUl6eGFwcnNNUWxLUVNDV2JkNGpmYkpiTG5sSThwQWwxRElTRGFVVUVmR3dOaVBoS3I3RjlSdGtMdnNnMTdDOGtZUHU1YVUyOGJ5RnlJYlJpQzBmMU55dXFuanMrOXBwWWRtblNuSDMyOE14V0tTcnB6TEx5UkJWZ1VCODdmOUd2SUJ4a0NMNEJ0MWlsUlp3bXo0Zk5qMXNrV1E3MDdFOXBiTDh6d011djRFZVhRWWR5NUQzY3paS3lmTlVtVndBTUgxV0EzOHBJaU0rU0x6SzdxWndpNS9PYVdoTTZVMFpJcEJUcDRmcHRjc1N4U0lwYnUyZmFtLzNFcGlTaGp2TGtMMWVQN09XZUxJQVQ2Q2VuY1p4SDNlekM5M3c2bFFzVnB4NWNzR3hsakZTZkN4YjdQYitpUitvU1AxTXA0YzF3WnRwOUt3ZlFPWVJtNmRLNzRwUEs2VmFscXZ6R1IiLCJtYWMiOiIxMWU1NDkzOGJhNmNjM2E4NjJmYTViOWZlY2FjNWViMzJjNDc0OGE1MDRhZGRiYzJjODNjY2I2ZGRlODY3NTc3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:30:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1006075539\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1120156162 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>12</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>33</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1120156162\", {\"maxDepth\":0})</script>\n"}}