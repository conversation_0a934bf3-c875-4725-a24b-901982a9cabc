{"__meta": {"id": "X975aa2393252a137aa36161240d352dc", "datetime": "2025-06-30 15:34:10", "utime": **********.144644, "method": "GET", "uri": "/add-to-cart/1839/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751297649.732228, "end": **********.144664, "duration": 0.41243600845336914, "duration_str": "412ms", "measures": [{"label": "Booting", "start": 1751297649.732228, "relative_start": 0, "end": **********.046819, "relative_end": **********.046819, "duration": 0.3145909309387207, "duration_str": "315ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.046827, "relative_start": 0.31459903717041016, "end": **********.144666, "relative_end": 1.9073486328125e-06, "duration": 0.0978388786315918, "duration_str": "97.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48582472, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.02893, "accumulated_duration_str": "28.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.07882, "duration": 0.02457, "duration_str": "24.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 84.929}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.111544, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 84.929, "width_percent": 1.21}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.124053, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 86.139, "width_percent": 1.694}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.12575, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.833, "width_percent": 1.279}, {"sql": "select * from `product_services` where `product_services`.`id` = '1839' limit 1", "type": "query", "params": [], "bindings": ["1839"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.129901, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 89.112, "width_percent": 1.383}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 1839 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["1839", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.133262, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 90.494, "width_percent": 7.985}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1367402, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 98.479, "width_percent": 1.521}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-390855571 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-390855571\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.129139, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  1839 => array:9 [\n    \"name\" => \"ميداليات شكل محتلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"1839\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/1839/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1798101623 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1798101623\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpIeFlrVFY2aVdZckRIYk9qbTh1Q2c9PSIsInZhbHVlIjoienNpdUFaQXVnZ0JEMVYrcE9rY1hCbDMwUXpyTStzbUpRNUVxQWFWNkRpdDA5cVk0ZlN2UTZVdmcwdWkxQmRoM0krQitTMG9wQjRSSkZyZFVPZHAvNjZCV0llSXJKOGF5bXkwMGt4M2gvNVl6ZWJrajFtZ043d3A3d0ZBd0pMUXVCeUJ3NWUybktBUlIyN2RnMmlXQmVoOFFET0VCbnhBL2xaTlNvd2l0aDY3clcrc1JOelY1YXppTEloZnFVTEF6cUQrcEZxOHppOGZwM2E1YkxCeG9FL2RudzNqSVRQVEZwUWVmOUd4bUZVZmx3V25kUDNHd2JjTGw2dU9ycGRQR0tmY1p4TmowUTVFVUMzblZsd2JvV1ZkK1F2dGg5d1JOL3B5K29zSWFBTUM4V2xmVk1GblErMndyU2RFeU0yM0JlVjJSRDRXSVVMTURNMkFBMHJWZUFEWERDUGdxcjU5UVR6M29rcGtaMGREaXhtM1dMWGJYbzhzU1lhVm5Rbk5ncy84bFZ2Z0xkb2JkdE1nQlpuVllGWjYzS2t2Ry9JdURENEVQSTBYSm5lVlpwT3pIc0ZlSS9QQ2s2UzBOcHJXbWJmaDhMVE84dFRVbGN1VFB6Zm1rQkZSd3ZDcThUV2poWTdIQnVLS0labzJOV28xSTl1MFQ0UllqaVo4cEE2TzgiLCJtYWMiOiI0OTAxZjcwMWJhOTE4MjAwYzg5NGRmYzc2Y2ZkMDg3Y2ExYjgwMjYzMjEzYjkxYTgxZGE1MTFjNjUyOGZiZWQ5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkxvR3MyeXVhdHp2RlRYUjNNUGVIaFE9PSIsInZhbHVlIjoieFluejF5bXV4QkJDeHdiN3ZQTXFnUkpzdnlzVGdTRitsMG5BOHhQM1JZMHJYd1hCUUNEaUhGWXd1SXNoK3UwVW1ET3BTU0o2SWkydXVMb3FpSlFESG5xcENVcGJlZG10SnlJSnhtVmJQTHI3RWFnNVR1Mnd1U014YjBTVDF5VlVTNWsvZWcrVHdtUXpYZ0REbENlVkxZcXFjM09MUmpEaU9hYkViYi9TOTV2TlBUVWg3TnpIa0pSQlp3eDBtTUhHanlIQWpFNjUvTlg1ejhHUFM3ZHlmeGpqdkFzN2d0MlRmcmFTK3Z0WjdTQ1ZPcVE1alBRM3phSDZ2bEdoM1lBUnlEWkNHc0ZzV1hyTU5JZkpZSHRDQVNseWQvaDFHV0g4SUpXZ2RjYXhKazB0aGc2d05PZUVhcVFUeWc1RkM4V0xnUkluaHlIb3ZKb0w4MExvWUtLN0JQUnNraEV1VFpKVTdmZjNMT0gwc0RWQXc2VWloazAzRGNhVjAyMXkyd3NPYUJmcmhmY3k5OG1OOC9lSzkxeVE2eW1TVVJqL0JUNm02MDRuZDFJb3ArVUM5Zm9TZGN0ZFpNU1NyeStRd1hNSXpxbm5EbGlqUjNVUlc5bzAxcDVUZWxQZThWWEpNNGs4QkV1bDIrR255UTBzSlo1VXpDYU5xS0ZUQm0zaERQQkYiLCJtYWMiOiIzNWZhNzIwMDE4NzEwMmVlNGQ5NDE2OGNlYjc2ZWNjNzAzYjJjZTM4ODM5MzM0NjNiMjQyNGNhMTA5YjI2MTQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-487933857 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-487933857\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1929051151 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5NNVdXOUx2VXNwQk5zQkIwZzB5Tnc9PSIsInZhbHVlIjoiZG5KMlh3dG01OXdYYTZJQSsrbFpTakNBVWN5MDZhcHRtSi9neUJwUWlwT2dqR2FLWVRldGdnMzhyYXRQTmFDcmVDTVNydGRYSlJXdlk5Z0lPUFMyTCtBTU5ITSs3RmlFOVd6UGV6NnZsc2U5bzVaMi9IamdtYWpmR0kzMXVaYjBEMSs2Rzc1RU85ZWpsaUR4TjlOYXNmaTBjQ254SzZweUxRSGVDUmh1UWZscW44bGlrNzJFRFJZM2hiSUN6Z0h1azBxSnhpWjhraEUyUjZWUU1nVVN5N2xXbnVTK2xBOXpvRkdNaWNWdTVPeFZUQU14bEZJejl3QXRXVU1CZ3plQnVocm0xT2s0b0cxODVOeHEySVR2S2ZJL3hNT0hsUmgyUVZNVE50TUJyRS8wNmwwTGpyL3IzUGNCaXkzeHBXbnY4S25ZU0VVOU55Q2I1SmREdVpxTXJHVHVHOGFxV3g3TUtoUjRGdDIwQUxlUDB4UDdIaVlpTmhGUjR4Wm5hVHRoYjlhU3dtejJoK0lBSVZVQnp4Q2VVeDd2U20xRkNnN0pzbGxkUWpFaXAzT2J2eEFoK29wdVFHZ0VuSENlRnIrbi9vWGRPT0JuTHVHd0E5ZjFwQXFxTmJ6REZtU0lWaGdnRWp3c0p6WkUvWTlXdWNweEhNemtONHhWRllRblJ5eVQiLCJtYWMiOiJhMzI0ZGI2MzRlMjMxY2FkNzM1YjJlNDRhNDY5YTcyZDBlN2I3Y2I5ZWQxNzk4ODEyMjFkNGRlMmM2MjY3Nzk5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlRpeXYrby8zdkpsakN6MmQ2RDVaTWc9PSIsInZhbHVlIjoibTBpcURHWlBHWm1JaXI3Y01NQXFDc2hPZVljOVAwbUVSd2FQejl4YmxNaU5nVzM0c2Y0WnpKME5hWXZZWkhMWDg4Y1Q4cE1OejZpVjA4bGp4eU9oVUoyaytleEZSU1crc0dDWVFmcGdXZWhoZnlwVkVjZUoyVHY3TlZKOUMvU2o3eGFNZnZmYjNwZTJxTzZ1WDRqUzA5RFd0U1AybVl6ZmVVdVExYWdacHN6TFExV1IxQkpGVnZOOERYSEFieFU1M3h3RTI0V2wrRzdpcEpscUxEZFBNZXl0VTk1Mkd1RXZCVXN5SEtDb29BQWd1d08vZlVpU3lBQnhOWVA4ZjZRUnN4ZEdoVndKd29EK2ppSDFCRjVtdXpKN1lRR3QzdkFwSUdnYzBrcVMrWWV0Z2lxdnVZZ1YvS1krdzJ5bkxMVXNqZkd0Z084SU02b2pPbm1xQXU2VDBadXdpdUtFU0ZVYTdaUGpBemdNSDBybVNGUGEydHJwVHUvM3ZwMzN6aUc4dWlMWjVNUjgwVzEvaEhaNFNabjVQdFQrbGNGUjNISEJWR2MwZ2FpNFdNUEVWbnhxWEpjUzAzT05FaXpTS3ZWZ3V2cE5oUlQ1em9WSVFuYkdsb0c2R1lObGJUZVp5MkZ1QnlvOFpremVRaW9rSWxCTGQzcGRkaEltK3hSSlJQNnMiLCJtYWMiOiI3MWZhMDJjMjdlNDkxMWI2MjEwOGFiNmFiYzg0ZmNiMmYzZGJiY2YzYmY3ZDhlZDBmNTY5NDIyYTA2MTRmOGM2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5NNVdXOUx2VXNwQk5zQkIwZzB5Tnc9PSIsInZhbHVlIjoiZG5KMlh3dG01OXdYYTZJQSsrbFpTakNBVWN5MDZhcHRtSi9neUJwUWlwT2dqR2FLWVRldGdnMzhyYXRQTmFDcmVDTVNydGRYSlJXdlk5Z0lPUFMyTCtBTU5ITSs3RmlFOVd6UGV6NnZsc2U5bzVaMi9IamdtYWpmR0kzMXVaYjBEMSs2Rzc1RU85ZWpsaUR4TjlOYXNmaTBjQ254SzZweUxRSGVDUmh1UWZscW44bGlrNzJFRFJZM2hiSUN6Z0h1azBxSnhpWjhraEUyUjZWUU1nVVN5N2xXbnVTK2xBOXpvRkdNaWNWdTVPeFZUQU14bEZJejl3QXRXVU1CZ3plQnVocm0xT2s0b0cxODVOeHEySVR2S2ZJL3hNT0hsUmgyUVZNVE50TUJyRS8wNmwwTGpyL3IzUGNCaXkzeHBXbnY4S25ZU0VVOU55Q2I1SmREdVpxTXJHVHVHOGFxV3g3TUtoUjRGdDIwQUxlUDB4UDdIaVlpTmhGUjR4Wm5hVHRoYjlhU3dtejJoK0lBSVZVQnp4Q2VVeDd2U20xRkNnN0pzbGxkUWpFaXAzT2J2eEFoK29wdVFHZ0VuSENlRnIrbi9vWGRPT0JuTHVHd0E5ZjFwQXFxTmJ6REZtU0lWaGdnRWp3c0p6WkUvWTlXdWNweEhNemtONHhWRllRblJ5eVQiLCJtYWMiOiJhMzI0ZGI2MzRlMjMxY2FkNzM1YjJlNDRhNDY5YTcyZDBlN2I3Y2I5ZWQxNzk4ODEyMjFkNGRlMmM2MjY3Nzk5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlRpeXYrby8zdkpsakN6MmQ2RDVaTWc9PSIsInZhbHVlIjoibTBpcURHWlBHWm1JaXI3Y01NQXFDc2hPZVljOVAwbUVSd2FQejl4YmxNaU5nVzM0c2Y0WnpKME5hWXZZWkhMWDg4Y1Q4cE1OejZpVjA4bGp4eU9oVUoyaytleEZSU1crc0dDWVFmcGdXZWhoZnlwVkVjZUoyVHY3TlZKOUMvU2o3eGFNZnZmYjNwZTJxTzZ1WDRqUzA5RFd0U1AybVl6ZmVVdVExYWdacHN6TFExV1IxQkpGVnZOOERYSEFieFU1M3h3RTI0V2wrRzdpcEpscUxEZFBNZXl0VTk1Mkd1RXZCVXN5SEtDb29BQWd1d08vZlVpU3lBQnhOWVA4ZjZRUnN4ZEdoVndKd29EK2ppSDFCRjVtdXpKN1lRR3QzdkFwSUdnYzBrcVMrWWV0Z2lxdnVZZ1YvS1krdzJ5bkxMVXNqZkd0Z084SU02b2pPbm1xQXU2VDBadXdpdUtFU0ZVYTdaUGpBemdNSDBybVNGUGEydHJwVHUvM3ZwMzN6aUc4dWlMWjVNUjgwVzEvaEhaNFNabjVQdFQrbGNGUjNISEJWR2MwZ2FpNFdNUEVWbnhxWEpjUzAzT05FaXpTS3ZWZ3V2cE5oUlQ1em9WSVFuYkdsb0c2R1lObGJUZVp5MkZ1QnlvOFpremVRaW9rSWxCTGQzcGRkaEltK3hSSlJQNnMiLCJtYWMiOiI3MWZhMDJjMjdlNDkxMWI2MjEwOGFiNmFiYzg0ZmNiMmYzZGJiY2YzYmY3ZDhlZDBmNTY5NDIyYTA2MTRmOGM2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929051151\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1839</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1605;&#1610;&#1583;&#1575;&#1604;&#1610;&#1575;&#1578; &#1588;&#1603;&#1604; &#1605;&#1581;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1839</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}