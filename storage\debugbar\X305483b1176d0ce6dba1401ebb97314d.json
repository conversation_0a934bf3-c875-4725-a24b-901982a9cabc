{"__meta": {"id": "X305483b1176d0ce6dba1401ebb97314d", "datetime": "2025-06-30 15:34:48", "utime": **********.273015, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751297687.848585, "end": **********.273028, "duration": 0.42444300651550293, "duration_str": "424ms", "measures": [{"label": "Booting", "start": 1751297687.848585, "relative_start": 0, "end": **********.182041, "relative_end": **********.182041, "duration": 0.33345603942871094, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.182048, "relative_start": 0.333463191986084, "end": **********.273029, "relative_end": 1.1920928955078125e-06, "duration": 0.09098100662231445, "duration_str": "90.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48095528, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.026899999999999997, "accumulated_duration_str": "26.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.212464, "duration": 0.02156, "duration_str": "21.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.149}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.241456, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.149, "width_percent": 1.375}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.254096, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 81.524, "width_percent": 1.933}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.25581, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.457, "width_percent": 1.264}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.259757, "duration": 0.00283, "duration_str": "2.83ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 84.721, "width_percent": 10.52}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.265127, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 95.242, "width_percent": 4.758}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1211071236 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1211071236\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.258856, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-7003117 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-7003117\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1935187128 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1935187128\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-558945376 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-558945376\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1417454610 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297684545%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdjSS9UOU1YcC9ZZDUyVWd5dUZ4Y3c9PSIsInZhbHVlIjoiZW9rYUtJS0M5RElLQzVZbUszZ3AwQVNvWlFSSUJtaGVvSG0vc0FBWjA2R0htQkhxcmNyV3hYUzlLbW1KbFcxYVZTUkM4OUxJZkhjd0VmMWdlNllLa1pqV05uRFJ2STBOd0ZMd1F5MTRWVDdSNHJNdmNOZEVGQ2tHM3JxZDZ6YllYVkhTbzRRR0VwemM0eE4yVUJZMW82NGNCS2RJdzl6SnpaNUt2RVc2a1NTZS9ycENvZGhPQjhSNExaWlpWRSswRWF4SFVSY1RXZ1U0Zm9kR081THFJZmtINmgwKzF6Sld4QXBUTGVlZUJlc3RhR3JvUXFvYVlsZzJ5U0t0cFlNYVlGOWxEK3NWY0Y1cGxkQ21JcHhNRitza1pydk9FaUZ3NFJNeGdpSlQvVVNxZWQrWVVwNnRDMWExK1Z2L3UzSHMrTUpZY0lhYjk5YUswRXliN1FiVzd3K1NQSUhKWkVHdmU0QlkrMkRwcURzLytxYVovOStvUzJBUEx1N3ZCeVg3TCswY2txOWNFQ1FqdVpWOVdPbm1oZlY1TDVwUUdaUGJNVDVjS091SzlLbXlOS0hQV0VmTys1UGdjeFlUaWVnNlJjbU0yMCtUem1wajd6OGdxV0p2eE1TVDhuNXREenNOdURyTkxEUlhkNFpiZEY0SGxLNS9tRjJua0NHNHROWHEiLCJtYWMiOiJmY2YyNzUxYWQyNWI2ZGNmNmE4ZTgwZDY3ZDNjOWVmODEzYWZjNjA2Yjg4MDY2N2IyMDBmYWMzZWQwZDBlZDUxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1WOWp5TERVaW1sYmJaUWdBUGxIUXc9PSIsInZhbHVlIjoiRXJvaW91N3MzdjRENEluTWtpcHZvU1djT3RlcHJTS2kwQmxUQi9PQTN6SVNkQ1kxL29JalVWa2JMNm56RkswbksrRHJYNVJvYjlUOEVJa2EvQzFDSzA4NlZnYTQrNCtjOGFLV2E2aklZM2RBRFZ0cFRXdThESXFWTk9kUEJDSVRWNmpQSXJOVjJDaHZiUHFKVCtQaW9EWDdQcmw2Z3g0Ump0RnJrbGlUSXozWi9Hb1AyUHVUbFJLZWt1THFFbzRIY2R0Q1Rma1BiV3RCT0lteHdGbUxycVR1ZmJMMWtmQVpHTG1NZGVIUHNyNlFMZjh0THcwUW9vOThIVU9YL3BWdnNFeloyYUZvcHNPdDhCY1dKU1hHd3JMa2k5djBublFUb0ZTV3gwV2JYMlZYUUxmSktjUGFNRDV4Z05Xc1ZEUyt0OGMzeUpTR0YvSDdlNSt3UkZta2lXTHVXeDk3ZldGU25LU1BCbGJlc3ZJV3k3d1NSbDNVNStXdEF6c2VZWEhYNmM4N2dpa05kSHhDYnA0b2Z1dXRjTnlYYXJOV0h4b0FmSzJ1ZkRWQ1VGcTAzZ0VBaU9yUEhTc25weHpKWE8xdDlZejVxSitnRGxPSkhoSDgxVGdlMTl1QnNPcUNKZDZWNGJmdlRUZFdWM1dYR3haSmdlcXBBakE2VjRXaHU0cEoiLCJtYWMiOiIxZGJiM2RkYjdmZmQ1NDllZmMzZTg5ZGNiZTQ5ZmZhZTYxMjNhMDY5MjM5ODlhN2JjNzQ1YWYyOWEwNGZlOGU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1417454610\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1992397165 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992397165\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-978599201 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVzMEtsS2hobHozNlVFa2lJZWhDcWc9PSIsInZhbHVlIjoiaTBkWDNnLy9QaVlBRSt2YW40MFFONk1lc3VGNEpLVFVDUVM1TDJ2bHVOWUFUbHk3dEdFNmRyWWpLSlIvbVIvcjlaM2c0UmYrMUNZZmVzT3lMMmZxcnhoMzlvK0MrVFBlWjBNZWtkTEU3YmxYRytmTis0cnNTQWpYVnA1N1psNXg1OVlkcGpkNzZIK2hpQlJQazRLalVHK3hKNnYxdDN2S2NLSjR0blF1TEpYZGlMT1U3ME5wNEdBVW9vMTM2NkRML2k0Z0Ura1ZJYVg3SW1LUmxWZlZjSzNSUU9ZWEl6ZUt4N1ZiOW9YTkZVZlNKMkpsU2o3ZnM2Tm02bUdtRnl2QjJtK1NVVTYybEIxVUdDR0dIejg3VEdjK2JXVE00WW8wcUdITktpTVpHbngwNHlFNzhiNTM1QlhpaWNzbXVqRlR3NXpXeUM1RW4rdy9hTjFwSGJrTVpVOVpab3E0bURuZ0FuOWdUMEw4RjMzNnpTQk1pV1hKZzZWNFQ3cFFsSC9rV1JWT2pFYnFCN2dISFduODA2dzd4bUJCQ2tNRmpIR2NUc3U3bnh6TXRnR3EwRG1BYnNsbEkyazNDTmdIRU1CcnEwSFBQWmhwQzFKTFJkSWxLZmJGMXc2a0RvdmNhSU8vY2ZaT2tTVEhWL0ozdElwOTNpUndwVnN3bGVoaFFLT1IiLCJtYWMiOiJlZDNmMjY5NjMyYzcwNTgwNWEzMmNkZWM5YzM5OTdiOThlNDFmOGRiNmVhZGQ1YTI0NWNiNDVlMTY0NDljNTI0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImxqMzlWcDlkMGhtSUxBZUV2SGh3NXc9PSIsInZhbHVlIjoiZER5RnZCdk9DOXR0NnFSVkVzUXF1dmxIQkFhanYwRHRKZWY1dEZKRlg4RmFEc0VGRHN1VFpwSFNleW5mRmRLVFRIN0pCaXllTGRmTS9scitkRTVYdk9MeHRyaUtoTk5jNjZLR0w5REdTZXRMbDEzeFA2SC9NeDdIR3NNbExWT24zQUc4czZQc0ZpMzNta3MwREg4NHNxblNuMXJRUjF5U1dJamh4L2xUK0hsODM5MlljTU9tNi8zYUt4bEhXeG5mcG9iVm1zdE5rVjRsUStDc3dpZnhYanpKL2cvcEVUV0hsMEtvb1pQTHRLZyt5RzV5SzQ3ODJxSWxrOWZ4UkhuenZiQWVYZ1YzSUtPTHNlOTNxQTlNTFljYitMcmF5bE54SHQrK1d4SHZ2R1ZKQzRqL1h4M2tZTXE1MzdVZzFrTFMxK1kzWEhrM2NranFvd1EvbUhwdFZML0w3YXZEdjFyWURFNTQ3VTRUUlpkVzZVVCtaUFd3NVprY3YxMEFBWFVSNnNXbXhQdDI4ZUFjS2N5S1UyUEtZdnNEbnROZVZRaXZEQzh0enhzQ2h4SlF4VDZ0MGcxUjVMTFVxWkxUV2RxaDJtSkFOSG14SXFZc3ZrdFBnL3I2NFZobVNXa0swbVJmTC9OMlM2VWVXSERDMWJ2aGlBQ1dTVVMzVEl6K0VnTDEiLCJtYWMiOiJhNDg4OGRhN2RkOTg3MDk1OWZiOTY0ODcxMTUxNjYwZTU2NmU3OWVkZDE1ZThkNzk4OWY1OWY4MzFlZTViNTQ0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVzMEtsS2hobHozNlVFa2lJZWhDcWc9PSIsInZhbHVlIjoiaTBkWDNnLy9QaVlBRSt2YW40MFFONk1lc3VGNEpLVFVDUVM1TDJ2bHVOWUFUbHk3dEdFNmRyWWpLSlIvbVIvcjlaM2c0UmYrMUNZZmVzT3lMMmZxcnhoMzlvK0MrVFBlWjBNZWtkTEU3YmxYRytmTis0cnNTQWpYVnA1N1psNXg1OVlkcGpkNzZIK2hpQlJQazRLalVHK3hKNnYxdDN2S2NLSjR0blF1TEpYZGlMT1U3ME5wNEdBVW9vMTM2NkRML2k0Z0Ura1ZJYVg3SW1LUmxWZlZjSzNSUU9ZWEl6ZUt4N1ZiOW9YTkZVZlNKMkpsU2o3ZnM2Tm02bUdtRnl2QjJtK1NVVTYybEIxVUdDR0dIejg3VEdjK2JXVE00WW8wcUdITktpTVpHbngwNHlFNzhiNTM1QlhpaWNzbXVqRlR3NXpXeUM1RW4rdy9hTjFwSGJrTVpVOVpab3E0bURuZ0FuOWdUMEw4RjMzNnpTQk1pV1hKZzZWNFQ3cFFsSC9rV1JWT2pFYnFCN2dISFduODA2dzd4bUJCQ2tNRmpIR2NUc3U3bnh6TXRnR3EwRG1BYnNsbEkyazNDTmdIRU1CcnEwSFBQWmhwQzFKTFJkSWxLZmJGMXc2a0RvdmNhSU8vY2ZaT2tTVEhWL0ozdElwOTNpUndwVnN3bGVoaFFLT1IiLCJtYWMiOiJlZDNmMjY5NjMyYzcwNTgwNWEzMmNkZWM5YzM5OTdiOThlNDFmOGRiNmVhZGQ1YTI0NWNiNDVlMTY0NDljNTI0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImxqMzlWcDlkMGhtSUxBZUV2SGh3NXc9PSIsInZhbHVlIjoiZER5RnZCdk9DOXR0NnFSVkVzUXF1dmxIQkFhanYwRHRKZWY1dEZKRlg4RmFEc0VGRHN1VFpwSFNleW5mRmRLVFRIN0pCaXllTGRmTS9scitkRTVYdk9MeHRyaUtoTk5jNjZLR0w5REdTZXRMbDEzeFA2SC9NeDdIR3NNbExWT24zQUc4czZQc0ZpMzNta3MwREg4NHNxblNuMXJRUjF5U1dJamh4L2xUK0hsODM5MlljTU9tNi8zYUt4bEhXeG5mcG9iVm1zdE5rVjRsUStDc3dpZnhYanpKL2cvcEVUV0hsMEtvb1pQTHRLZyt5RzV5SzQ3ODJxSWxrOWZ4UkhuenZiQWVYZ1YzSUtPTHNlOTNxQTlNTFljYitMcmF5bE54SHQrK1d4SHZ2R1ZKQzRqL1h4M2tZTXE1MzdVZzFrTFMxK1kzWEhrM2NranFvd1EvbUhwdFZML0w3YXZEdjFyWURFNTQ3VTRUUlpkVzZVVCtaUFd3NVprY3YxMEFBWFVSNnNXbXhQdDI4ZUFjS2N5S1UyUEtZdnNEbnROZVZRaXZEQzh0enhzQ2h4SlF4VDZ0MGcxUjVMTFVxWkxUV2RxaDJtSkFOSG14SXFZc3ZrdFBnL3I2NFZobVNXa0swbVJmTC9OMlM2VWVXSERDMWJ2aGlBQ1dTVVMzVEl6K0VnTDEiLCJtYWMiOiJhNDg4OGRhN2RkOTg3MDk1OWZiOTY0ODcxMTUxNjYwZTU2NmU3OWVkZDE1ZThkNzk4OWY1OWY4MzFlZTViNTQ0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-978599201\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2081832287 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081832287\", {\"maxDepth\":0})</script>\n"}}