{"__meta": {"id": "X30b971b3edfdab31475bf8de7201b358", "datetime": "2025-06-30 15:44:07", "utime": **********.391133, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751298246.928005, "end": **********.391148, "duration": 0.46314311027526855, "duration_str": "463ms", "measures": [{"label": "Booting", "start": 1751298246.928005, "relative_start": 0, "end": **********.341395, "relative_end": **********.341395, "duration": 0.4133899211883545, "duration_str": "413ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.341404, "relative_start": 0.41339898109436035, "end": **********.391149, "relative_end": 9.5367431640625e-07, "duration": 0.04974508285522461, "duration_str": "49.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45363416, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00282, "accumulated_duration_str": "2.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.371653, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.794}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.381934, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.794, "width_percent": 18.085}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.38451, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 86.879, "width_percent": 13.121}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-559089050 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297684545%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjIxeHhUd1FGL3ROelJjWDBYcTFNNnc9PSIsInZhbHVlIjoiRGJodGJ0cHNiMEpGQXZ0UlQyeEVtZ1FZeFZPVHh6SUFUVS9rcGVXcmZpdDZWLzB5eHVmM25qckt3aGRIM3d1RlpHWVhSNmtDVjBybEs0MTNMbjdWNCsya2tBVWZ5VWw0U1hBa0FqRGlkWmRWOFVPTXQ0ejB3Z20yS3dieXoxT056QnZROU5YeFA2REZDb1VkWWoyWVBwY1FXd0hhSkU2ZGJZNUliTVU5QjRIUmxjVXhncjRDWmdDT1lWZGVXNVkyemhhRmdmUnN4MzgrSkZKU2RiRm40NDZ2Y3RvY2FmV1orWjk3OFEvQjg1NTRSanBNSDF6ZDMzVVJ3MDFHZk1sR05lcWUzbElJeThxenR6U1Z5ZWltMFhzNFFuWXU0blNVaVpoSEdCUmY3UWZNVlJoYmM5MUJ6RzJXZ3BYMmhJVGJRSHRYTndVR3dkV0dMaU85MVZ1MkJqYmM2Y0pMTGlYUTZwRndRUnRJNk4rRVYzMmZSSWk0UUJGcEZBUU85ekNVZ3B5VS93eFVOZWIxQTVOaXpFVlE5SEQ4dlZWNWFaMHJIZXR2OGd4ZDdoUGs2YTQvTGIxQnhNMWVMV2Y2K1F5eFRSVjdJcmdpU2tVM2lleFRmQkZsbmdiRnhqOEt4SDB2S2MrUE5PQVZMZjJaQlFCMC91ZXJvTURkNDg4bTBhUUgiLCJtYWMiOiI2OTQzNmVmZjg4Mzc3ZjQ1OTlhOGY0OGIzMjA3MzAxZmIwYjM3NTE4NTdhYTIwZjJmZmViYmY0ZTdhODYwOTI1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlY1T1A4KzVva3NNSDJqWDEzbDVFOFE9PSIsInZhbHVlIjoiUmtHWExoWDBkc2NvUTVqUFoyUHdiekhHOGkwS2s1dng1UGxBYXFZM3lEbWp1cVBrZlR4Y1Y3U0lnYWxDYVE1UmxUWmRIVU8rcTFwZGhkRHIyWmdTdnlZZ3NsMUl3VjhHZW9FTlNkN3p1ZmthclVHaVk1ZTJmczA1Rm93WnV6ZWF2MDlwU1pMZHE2N0pJYStDcGhmUHdTVU45dnd6RjJLWW5KSlZveU5nbkxKSGM0QzBjSWx0ZXFZVGhQMGdIMGY1ZUtoaG84djVnYjhiQmVMcWw2NjhoNy9nTVVZVXF6bURKZUhmQ00zUE5DMklGVERiVi9hY2NpR2dnMDlsYjFGWWJEVFpHSUNoMXVlVzJuL2pDQzNWKzBPeVY2UHBPYStnVFBTcVhOWmkwd1dkVjBrOGtXajdjY1Yrd3J1VXVDdzBod3lTNEFnTUVlRHdmUDdMRWtVSTJ4MzQzZDJSUmU2enY5L05Ta05ScElHNnIvT0VOWFhDdXFFKzRrTzJiOEVmdStTS3pxUVJiUFpab1hvd1ZaVTlJZmpaZTNteENvdjJ2WFM0RjhxdTFucmszdno5c1RTSWdZUUZmRmxqbzEydmd6RVBqTUxOMnlVbWk3ck4yMGJzbmtCOHN2aitUbG56T1M0eUdWMHVhejZ2bm8xbVhqYlVGZm5oUkFOZjNobmwiLCJtYWMiOiI5ZDBkMTQ5YjAyNWVkYjQwMmRlYTcxYTgyNzQzYWYxZjRhOWQ3ZmVmMTZhYmUwMTA4NTMxM2YyNGU3YTMwZTM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-559089050\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-644746312 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-644746312\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-256423456 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:44:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlliMlVhM2lMemQvbE80cjFqcHVJNXc9PSIsInZhbHVlIjoiYW92NWFSYnN2L3diV2wreW1hTElpNExTUzN3NHJKQld0SFJYMkZ5QjhTSzA2U0VJZ0pRb25ab20zTjNVVWRta1BJTkcyWnhycmpqS2pjeTRhYlRFcisxYk43bU1paHE3ckxUYUdMdit5L0lEKzc0dGU0bXdyTTAyeGdLMFRNeU51WlF6VFNydkZKTTQrMXdrR1hKWnViRDlJTzhHaEk5UVc0aUFMcUVWTnVJeE5heGVwRm9OWXhOZ0tDL3NRd2Rtc0t3VUlVUXFWeUE5MzRVTzl3R3gvalpWVE40djFuYXI5WXFCWm9Bd3lINXNRYm5XMkE3MVArMU5wOUpJbWlTUDNUemFody9sN0Rhdnk2TytxMG1wK0lKbkhYRjVtcEFMdEYzQW5jczg2Mkd6QmVGVUI3OTJhTVpnWVVLekRscVczclNKTDdWbzBmenIrcloyc29aUk1DdzFCK1lHMWRjK1NVV1M2bnVZNko3bi96VVZUWCtJT2VUZGZqcjBqdlhFdDdwZTNpOGlheHUvd3RSUkZqMFdIMDZLZDNhYVIzK3JtWldGNU9RaWZELzBEZ21xbzRZTHJIbVljKzg3SWtpendsV1hRMDBROThlMG1QODhFLzhyeFJVSXBDcDRQSlRUbWY5cUlNNTkyc25JQmsrWWJtTnVSazNCOXM1MXhGWlUiLCJtYWMiOiIyYzMzMTMyYjg1NzY5ZmJjYzlkYmU2YTA5ZWQ4NzhjMTIyNTVmODgzNTdjOGMyNmFhZmIyMmZmYWMzMWQxNjUwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpZc0ZTaWh1VURkT2l3MCtZNzlqN1E9PSIsInZhbHVlIjoiS05XQzZDaUxZdlQyVHB0WEFhZkw2ZFRFU3JYT2JGbTB1Mm5ORXZYT2VxbGxZUzAxWHYrRkVOOHFKTkdnS2Q5cWtRL0pqWTFLeFdOa1ZBaHdLQ0VGQk0zbVFJbm9NR0ZPaWR4RzJCK0h0bU9OcWVQQTRFY215ZG1OK0ZnWFJiUDRaSi9JSzVRYlpiTUtjNmNrMm04OWNxeDFsTmhZbHMvUkowS2ZSUnJCdE1xS1k2SnF5YXNlV3k0TUowTGtocGZIQ2lVQXhZSnl1ZEE0ckttaVAwUzMrQkYzY0E0RmUzcTFZVVZSbW1WRnhUcjJ6QlFyUVRhNFdXcUxWZlBwMXgyTzduU2hkdXFqTTBWa0NER2M5U0lYU3R2N1FqN0NRK1hqdXJpM2pmUU84aU1tclpTVzR6dEVHWjRheFdjK1BESXo2ZFJzNnNWSCtRc294MUFyQVF5a2ZNaGZWV0dUWnpsN29YelpGSDl4d2NQNkVla1NxL3I2Vk5tdkNzbmRKNld5NW5LbGpLZkpDNkw0ckR6dHB6cjRYYndVOW5iT24wVkwvK2lJdTErei9GQ3pKb01BNEFSdE1YWmk4dDh1R2RJYk8vZ0xHaFNSc3JGTmVjem1RRHNJSFZxblhMM3VLWlQyNk41dVFzN2lCdHpnaGJuWTEvTHZrQjIzY2ZWcTJ2YzQiLCJtYWMiOiI2ZmYzYzk4YTVlM2Q1OGFiNmFmODk3NDRjM2Y5Yzg0ZmQ3YmU1ZmI2ZGI1ZTExZDgxZjBjYjZhNDc0MDk2OWYxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlliMlVhM2lMemQvbE80cjFqcHVJNXc9PSIsInZhbHVlIjoiYW92NWFSYnN2L3diV2wreW1hTElpNExTUzN3NHJKQld0SFJYMkZ5QjhTSzA2U0VJZ0pRb25ab20zTjNVVWRta1BJTkcyWnhycmpqS2pjeTRhYlRFcisxYk43bU1paHE3ckxUYUdMdit5L0lEKzc0dGU0bXdyTTAyeGdLMFRNeU51WlF6VFNydkZKTTQrMXdrR1hKWnViRDlJTzhHaEk5UVc0aUFMcUVWTnVJeE5heGVwRm9OWXhOZ0tDL3NRd2Rtc0t3VUlVUXFWeUE5MzRVTzl3R3gvalpWVE40djFuYXI5WXFCWm9Bd3lINXNRYm5XMkE3MVArMU5wOUpJbWlTUDNUemFody9sN0Rhdnk2TytxMG1wK0lKbkhYRjVtcEFMdEYzQW5jczg2Mkd6QmVGVUI3OTJhTVpnWVVLekRscVczclNKTDdWbzBmenIrcloyc29aUk1DdzFCK1lHMWRjK1NVV1M2bnVZNko3bi96VVZUWCtJT2VUZGZqcjBqdlhFdDdwZTNpOGlheHUvd3RSUkZqMFdIMDZLZDNhYVIzK3JtWldGNU9RaWZELzBEZ21xbzRZTHJIbVljKzg3SWtpendsV1hRMDBROThlMG1QODhFLzhyeFJVSXBDcDRQSlRUbWY5cUlNNTkyc25JQmsrWWJtTnVSazNCOXM1MXhGWlUiLCJtYWMiOiIyYzMzMTMyYjg1NzY5ZmJjYzlkYmU2YTA5ZWQ4NzhjMTIyNTVmODgzNTdjOGMyNmFhZmIyMmZmYWMzMWQxNjUwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpZc0ZTaWh1VURkT2l3MCtZNzlqN1E9PSIsInZhbHVlIjoiS05XQzZDaUxZdlQyVHB0WEFhZkw2ZFRFU3JYT2JGbTB1Mm5ORXZYT2VxbGxZUzAxWHYrRkVOOHFKTkdnS2Q5cWtRL0pqWTFLeFdOa1ZBaHdLQ0VGQk0zbVFJbm9NR0ZPaWR4RzJCK0h0bU9OcWVQQTRFY215ZG1OK0ZnWFJiUDRaSi9JSzVRYlpiTUtjNmNrMm04OWNxeDFsTmhZbHMvUkowS2ZSUnJCdE1xS1k2SnF5YXNlV3k0TUowTGtocGZIQ2lVQXhZSnl1ZEE0ckttaVAwUzMrQkYzY0E0RmUzcTFZVVZSbW1WRnhUcjJ6QlFyUVRhNFdXcUxWZlBwMXgyTzduU2hkdXFqTTBWa0NER2M5U0lYU3R2N1FqN0NRK1hqdXJpM2pmUU84aU1tclpTVzR6dEVHWjRheFdjK1BESXo2ZFJzNnNWSCtRc294MUFyQVF5a2ZNaGZWV0dUWnpsN29YelpGSDl4d2NQNkVla1NxL3I2Vk5tdkNzbmRKNld5NW5LbGpLZkpDNkw0ckR6dHB6cjRYYndVOW5iT24wVkwvK2lJdTErei9GQ3pKb01BNEFSdE1YWmk4dDh1R2RJYk8vZ0xHaFNSc3JGTmVjem1RRHNJSFZxblhMM3VLWlQyNk41dVFzN2lCdHpnaGJuWTEvTHZrQjIzY2ZWcTJ2YzQiLCJtYWMiOiI2ZmYzYzk4YTVlM2Q1OGFiNmFmODk3NDRjM2Y5Yzg0ZmQ3YmU1ZmI2ZGI1ZTExZDgxZjBjYjZhNDc0MDk2OWYxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-256423456\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-14******** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14********\", {\"maxDepth\":0})</script>\n"}}