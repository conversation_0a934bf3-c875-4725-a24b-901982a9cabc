{"__meta": {"id": "Xe0aeb8c152c364fe971e2685ad057ce9", "datetime": "2025-06-08 00:29:52", "utime": **********.922667, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342591.922228, "end": **********.922693, "duration": 1.000464916229248, "duration_str": "1s", "measures": [{"label": "Booting", "start": 1749342591.922228, "relative_start": 0, "end": **********.803937, "relative_end": **********.803937, "duration": 0.8817088603973389, "duration_str": "882ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.803954, "relative_start": 0.8817257881164551, "end": **********.922697, "relative_end": 4.0531158447265625e-06, "duration": 0.1187431812286377, "duration_str": "119ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45576216, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00607, "accumulated_duration_str": "6.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.866821, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.25}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.888794, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.25, "width_percent": 17.298}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.903719, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 81.549, "width_percent": 18.451}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-891763511 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-891763511\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-813723931 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-813723931\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-25815560 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-25815560\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-219532020 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749342587944%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtTQ0t4bWI2UHJ6WC9Ld1dEOVUrS3c9PSIsInZhbHVlIjoiUWNVTzkyOW1vLzZBWi9vL2NTRjBsaG82bFVKYkRRb2xYUzMyUUhZcjc1S2cxemNNb2F1L2dZZXkycXFPaXZKMmNWWU1WZjl2eXdmeGpkcTBaNnlNY3UxS0dUZWthUWEvNkY0dVVQYUZ1MmlPOGhwOEQwZmNjbXBTU0phWHRma3MxQ1VMMjFGNUtrTlArUFI4ekZWUFlrZ0E5VnB4QXROcDZkNFJlMUhKSFhuN2dXeDdlRnNqajJxcUxjNkRBenVreXJCQmN6blVSaWwwOURzbnFmNWMzQXBSQnFHTGxkbU1oUzFOM0RGc3ZOK25WaXY3NjVYa0JHSnpsWDh3Mm42S0Z1alZKMTUyQmJIbVJMM2ZiUjlEYzhqdjNRenJReVUzUXB1OWJFYXZRRGxTSVJZNEQxTGErSHBmd3orczZFTjZPRzVrNUcyWmsyVHA2eFdFZTZWcDlkUS9wN2l4bVlOMXd3VVRBV0VmQ2RTb3cwdVFOQ0kzbzVzYzliQWtLaEs5ODJXdmhjNUJFYXl3VlE3eGJGMXdvZm1DejVpQ2lTMDQxVnArQitCcXk4Y1hDMmlkYzZTSUZ2R1liSXF5Z21ITllWVEdjckxpRjdST2dhdnh2d2VSRVpDZXV4cUNwTjUwcCtGYmhiUkVtd1UxM2hnSGNsa2o0eGxFQmRCNE1kcC8iLCJtYWMiOiIyNjU2M2IxOWIzZThkN2E5YTVlNmI3OWY3Y2Y3NTU1YmYyNjAyMGQxOGUxZjg4Y2I1Y2Q4YTE5ZDBkYTZiMzM4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikl3Z3BlZGpxdGlDSnprdzZpTlJXelE9PSIsInZhbHVlIjoiTE8yTEpVV0FjRFRMTUZrR0c4ekQrYTE5SmFzRGFJNlpSVlNycFpVWkszTkQvYW5MYU5sTjN2bEhuSTRvb3MvL05YOTE2NGQzakMvZUpqSC9oSHMrc2lTZnd6ZTJOUC9VNEZ4blFlTDdaV3RhYkVQZXVQL0dEdzV3SFN5Q0RzZDMwdG1ZbFEwelVabU12NW42bCswZGVoZ3NrcXBObk8zWk5DOGZySENjbHF5b09yTFFJL2twd0Q0dHFCWkFVd1hHdk1YZmRmVS8yeWhBbEZCZXN5NWp3c01qTldUZS85eExtdDdjRlQrRHk4RFV4ZjV6Z25kNGtkL0FCUjFTR0t4blRqNnJGY2F0SUZqeWJqWU5Uczk3QjRRSEQwR3JzNGp2RURIUHkwOEJ5aDhTVDB4WUhqR2J2cmk2aXQvbFltVU40ZlNXYkF1akJhOG1OODFmS0NmdDFaYkE5bk1GdTFlRHNIZlgyVDdBZEk4SnNKdlVKSGR0RUJNOUxzU0dmZ2JZRkJ5N1l5dG5aWXNTOVJJcW9Ed3ZMRTJiTUdmN2lVbWszbmhHTnRrMXhiSU5mRVUwZFpvcnY4anc4QjZvTVhNQWJSZGxCcC9zU1BtOHA5ZjM1eUZ3dnlmMWtIK0ZPaFFIcmkxdjlqT3ZTTHhXUER3NXpLQ2RrWkFtT3dCN3A3OXAiLCJtYWMiOiJmN2ZjOWQyYmMwYjQzODhhZTAzNzFmOWM5MjBiNjViY2QzZTllOGRlMTBkNDQyYTJjYmJkZTI1YzUzZTI3ZjMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219532020\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-941157291 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-941157291\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1947405128 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:29:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxPMUFUM2djOVZkZFBqTXJQNTZXRUE9PSIsInZhbHVlIjoiT1kwNXlzMlN3VlhsaTlrbWl1Qk1BRDlaalM5dktaR2xSb1I4TnhpaUlrNjhpR0FQUjQzc0t6VEhKRXVvUEJhR3UxeHhXLytXdzM3QlVBamROYURkOGtzUitTNmtmNlZud1F1SUUwK1pJdDNIVUVRaTRHanJSYkNpK2VGQnZ4S0JZaVlaM1JZdTNsMzd4WEJJYUVJZ3ViY1UxNzZoM2FSdWVibXVsMGI4QUVlbGlNTEV0TFVkWEtNaFdSSG1IMnAwMGNOaHVNU0tSUldWa0NZWTNkTmxVUStQeUppRFlWMWlaeW4yZHFBcDhRUFA0QXBqUW9XeHgzZ3dRZDR3L0VhNDhERlFLOEhlR24vLzdiZWhoanFLUTM5bWtwYmxOc1AzQjg1dWFnTjNPcHRPMHNlWCt3eTdmM25zQjljQkp5dUJWNjd0R25DaTQ4UkorL0VmWTJWcXV1U1JrWWorcHcxcjZhM2NXcjREeVdpVnhMaXJvelVYMXZCb0VDYnZSUmNJeGRQNVAva1lPZGpERVUreWpta2VadlVOMitWNTQyQ2tUUHZ3YUI0NmZYY0FnLzlkVExsR0xiNk5RKzlUUEtGOWNhc0NNVkxGdW1MVFV2NmlOVlQxNlUwZElwYXNMWkg2N3NRT0V1Q0VKUmU2dmtoSjRFZ1hQOEYwc1AwUFBvME8iLCJtYWMiOiI3NzE0OWFmMmEwMzViNDViNWQ2MWQ4MzkyMjc0ZDkzZmNhNGQ2MWU2NTVlODhmODEzMzg1OWZkM2Q0MWIxOTYwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:29:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im4wQ1lBZ0RPTXBwYTIrbEt3YURCUkE9PSIsInZhbHVlIjoic2l1eCtNZTZXT3NzbnZ1OW5TL2J4aVZObG4yVzd2dGtTY3pqQ2JpcTJZQzBpckpVOTRUZEhHQXd6M0ljNnp0cUZQOFBCT3o4Q0FSUERqTG9FTHVIR2Q1eDhzOUF3S2IwMEx1UkIyUUM4Z2lvaFJ1YXBxaC9mS2FadnFMM0I4T0N3U0plV3JlcDNpY3JJZ2VRWWVJSEtJZUV2aGh0K0FWKzJ4ZXNWb1VBK2NxM21VNjRZQTBLZE8xQ2YrWTAzWEIrbHh6cVFzNk92SU91WElDR2Rpb3B4eEpPTWNoYjM1MHpodkdYcHVVQnB5QWwxZkdDOS8xRlc1MVZOcmVOUEp5Um83RW9xckVQSGIySlprRFpFQ2FTMWdwZHRWaFUrb3F6S1NoMHphZ1lmdWUzZlhieGs4MHo0RUgvZ1pqcTZxcS9WaHJ1emY4d2trYnF5S1JyOUd5VldCYzdEdjFkeVRiK0tqanVOQmNTQUtOY3pHV1ZwYm41U05DUCtWRnhxMExhZGs3SU12QkFRNDRqRXVhQXh3cUFoTWFnSndGeDBSMGlUY3p6YUt4U215QkQxM3lGbG5GbHNFdGJ1bWUzSkJrajdWcktWdkNCb2ptaVdvRC9GbDRRUlNPSWdBbjBCNUEydHJTMEhBTFh5dDhPMXQvMXdTOEhTN09DZ3o3YWxoeEgiLCJtYWMiOiIxOTEyZDVkNWEwOTEwZTExODI4NWJjNTI2NmJjMDMwYWUwY2IyYTk0NTM1YTViNWExNjU4NmZmMzU4NDIyYjE3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:29:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxPMUFUM2djOVZkZFBqTXJQNTZXRUE9PSIsInZhbHVlIjoiT1kwNXlzMlN3VlhsaTlrbWl1Qk1BRDlaalM5dktaR2xSb1I4TnhpaUlrNjhpR0FQUjQzc0t6VEhKRXVvUEJhR3UxeHhXLytXdzM3QlVBamROYURkOGtzUitTNmtmNlZud1F1SUUwK1pJdDNIVUVRaTRHanJSYkNpK2VGQnZ4S0JZaVlaM1JZdTNsMzd4WEJJYUVJZ3ViY1UxNzZoM2FSdWVibXVsMGI4QUVlbGlNTEV0TFVkWEtNaFdSSG1IMnAwMGNOaHVNU0tSUldWa0NZWTNkTmxVUStQeUppRFlWMWlaeW4yZHFBcDhRUFA0QXBqUW9XeHgzZ3dRZDR3L0VhNDhERlFLOEhlR24vLzdiZWhoanFLUTM5bWtwYmxOc1AzQjg1dWFnTjNPcHRPMHNlWCt3eTdmM25zQjljQkp5dUJWNjd0R25DaTQ4UkorL0VmWTJWcXV1U1JrWWorcHcxcjZhM2NXcjREeVdpVnhMaXJvelVYMXZCb0VDYnZSUmNJeGRQNVAva1lPZGpERVUreWpta2VadlVOMitWNTQyQ2tUUHZ3YUI0NmZYY0FnLzlkVExsR0xiNk5RKzlUUEtGOWNhc0NNVkxGdW1MVFV2NmlOVlQxNlUwZElwYXNMWkg2N3NRT0V1Q0VKUmU2dmtoSjRFZ1hQOEYwc1AwUFBvME8iLCJtYWMiOiI3NzE0OWFmMmEwMzViNDViNWQ2MWQ4MzkyMjc0ZDkzZmNhNGQ2MWU2NTVlODhmODEzMzg1OWZkM2Q0MWIxOTYwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:29:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im4wQ1lBZ0RPTXBwYTIrbEt3YURCUkE9PSIsInZhbHVlIjoic2l1eCtNZTZXT3NzbnZ1OW5TL2J4aVZObG4yVzd2dGtTY3pqQ2JpcTJZQzBpckpVOTRUZEhHQXd6M0ljNnp0cUZQOFBCT3o4Q0FSUERqTG9FTHVIR2Q1eDhzOUF3S2IwMEx1UkIyUUM4Z2lvaFJ1YXBxaC9mS2FadnFMM0I4T0N3U0plV3JlcDNpY3JJZ2VRWWVJSEtJZUV2aGh0K0FWKzJ4ZXNWb1VBK2NxM21VNjRZQTBLZE8xQ2YrWTAzWEIrbHh6cVFzNk92SU91WElDR2Rpb3B4eEpPTWNoYjM1MHpodkdYcHVVQnB5QWwxZkdDOS8xRlc1MVZOcmVOUEp5Um83RW9xckVQSGIySlprRFpFQ2FTMWdwZHRWaFUrb3F6S1NoMHphZ1lmdWUzZlhieGs4MHo0RUgvZ1pqcTZxcS9WaHJ1emY4d2trYnF5S1JyOUd5VldCYzdEdjFkeVRiK0tqanVOQmNTQUtOY3pHV1ZwYm41U05DUCtWRnhxMExhZGs3SU12QkFRNDRqRXVhQXh3cUFoTWFnSndGeDBSMGlUY3p6YUt4U215QkQxM3lGbG5GbHNFdGJ1bWUzSkJrajdWcktWdkNCb2ptaVdvRC9GbDRRUlNPSWdBbjBCNUEydHJTMEhBTFh5dDhPMXQvMXdTOEhTN09DZ3o3YWxoeEgiLCJtYWMiOiIxOTEyZDVkNWEwOTEwZTExODI4NWJjNTI2NmJjMDMwYWUwY2IyYTk0NTM1YTViNWExNjU4NmZmMzU4NDIyYjE3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:29:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1947405128\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-507282948 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-507282948\", {\"maxDepth\":0})</script>\n"}}