{"__meta": {"id": "Xdf0bde94d8d227589d4693619b0c0279", "datetime": "2025-06-07 22:36:27", "utime": **********.53596, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749335786.702293, "end": **********.535984, "duration": 0.8336911201477051, "duration_str": "834ms", "measures": [{"label": "Booting", "start": 1749335786.702293, "relative_start": 0, "end": **********.385335, "relative_end": **********.385335, "duration": 0.683042049407959, "duration_str": "683ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.385352, "relative_start": 0.6830589771270752, "end": **********.535986, "relative_end": 1.9073486328125e-06, "duration": 0.1506340503692627, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48114888, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02148, "accumulated_duration_str": "21.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.442266, "duration": 0.015439999999999999, "duration_str": "15.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 71.881}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.471766, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 71.881, "width_percent": 5.633}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.500077, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 77.514, "width_percent": 5.633}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.5045419, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.147, "width_percent": 5.168}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.515114, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 88.315, "width_percent": 6.704}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.521554, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 95.019, "width_percent": 4.981}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1953223781 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1953223781\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.513169, "xdebug_link": null}]}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-979000481 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-979000481\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2000158148 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2000158148\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1459858757 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1459858757\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-262479875 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749334872942%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJDS213enEvbFZLUmIwelVJdnlRWEE9PSIsInZhbHVlIjoiNXBrQ1l6T3RTLzduWTMrbmhLOHh3ZVNyUlFsN0tZRVl6Mm5xMkoyZ00ydmY4ZFNuTXNWK0xuWlV1QVQwbWVlQkpOTEtNUEY4bE02d2JsUHpnc3RtcEw2bUhCd2lyUkxNeEtFMXZZaWJaT1BGU3ZCeUZ0YUFvbVE3bjdONytDZG1IUHJmUXV6L05VUUN2WW5IVXN2UWczRnpqMHZSWmJERng1Vkc1N1NSTUYzR25QR2c1UTlaRG1WMUxmbmo0SnRkQk5HejVIUE9Gc2phblRWWG9JM3ZKZUVpRVVSZ1J1c2RkSjlJa3BOdGRhaEdjbk1oSFVvM0JTK3hWQzZFTHpkNWtSSExVK2pXUnR6UUs4Q2ZVVUYzbjZMcTJuK1g4MWM3dXNSOU0vSThFUnMzSHhhYU1tRllmNlNaV0tET0h1YjVWUndPS3RXcXp4Z09UdzR2RGtmcDhSNnVvbGxBOW41UW9pOW5xNXFnbVY2N2dQTHlnWWRjWEdNM3RaNlQwcThsVXRHV2RZclQ2VHh5Y2lxTnJ3NHljK09Dc2VhejVXdnJNRHRxOW81TmVCc2RWZ3NnK092bWdkR2w0Vkc0VUhJRUtCZ0xrNmZkU3huYitrcnl6RjBEV2VwMUNVbVR2U2MrcHRpUDFPUXlrZFVHcHNmamczNjFJQ3NCUUpKUmhyYjMiLCJtYWMiOiI5ZDE3Nzc2NWYzNWQ3OGI0YWUxZTUwMjIwM2YyNDg5OGRhN2RkNTIwNzVmNTZlMWUxNGNlZTg5ODZjYzQzODU0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJ6ejVoK0pGZ2lRaFpEeUQ0c2lVenc9PSIsInZhbHVlIjoiM2lhK2ZkSmF5NFlJQkZqMXdEb2JBaVkvdHp2V1ZzSG1uZzRLMmpYK1lqSTlnNHYxakpoVnQ2ZDQrbER5WG1TMW01V3U4QVhTVDZNVkRYUnkwL2Rqa2tSSU5GamRGUUd3SDVRRkFSNzdwZUw0SHNNd28rd252REprWEsvcFh3bW44NXVjN2phalgwWXA1L1EzcTh3VVB5VzJmdEg1NmpFZ3pRN043ekRNbUltOVRCRmg5ek40VmI3eW1XYThiRGhqOC9DeUhMcEs4Y1V6eVN2NXpUNXFuWGJDQy9WVktQTTNpWWM1c2VEaTZiZU9XcEcrRzZtMFhUZjlmRThXTlVLSlpMSXdlV2VXNVQ4NXRteFBFRUd4d01tZCtSSktEWHdoR3FHNkdBT1oxWkV5Y0dqZm85Z2hRN2N5SG5LOEtiYnNXaWZybWJHczJTNzQwL2Q1QTduampkZFBrKzdPbEZSMnpIQ1hHb2FXejVoUDRkeDczZ2M4NnFUdGF3SkpzcG5wQitLR09jM1I4VmRER2dpNzBveDl5OFhQSmVJRWl6N0dPdEZNb3E0akxpS3I4MDZyY25PZ0lZQkowVWdKNGJoK0k0dDVxV2RBMFBNaWE5enFCRVRpNWRRZzYrRDB6R2kySzZacWZuVitFczZCZ0p1bnpkUU1vYTI0SnY4UnVRT0YiLCJtYWMiOiJkMDRiNzdhNzNkNGQ5MThkNTY5ZGNiMWJhMWJlZDJhOThlOWNiMmVlMDhmZjViNzgyYmI2NTFmMWRkZTE0MzRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-262479875\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-643021374 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:36:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJDMEVuSzhmSWVWRkJuSzNtS2I4cmc9PSIsInZhbHVlIjoiUEh4SS9WeVRlbFNxTU9Lc2lPQzBqdmV5YXJGaGc2TXdwWlVLUGZjVCtGQkZ6TlRhaU1BSW5GdC8rVnpsSzZPSTBuV2ZDQWt4VWZWZ3lLQ0dkNzV3QW5CNG5CNkxSR1hvWTJyN0sxcFBwbkNBSGFQZE8wT1JTUU1TL1lYV0VlWFBoU0F0aEhBdXhvS1VzRExnVnhsS3J6clJlNmUrbE1qdXpUU0k1Z2t0aXg4cCtmRFhuRnFRd2VnTncxQ3FDMVUzaC8xekVidDJmNlg0VEo2c01wNnZzV0FzVXBsVHJTUk9tanNEdll0SmVEcE9xY2EzMzRVZTk5VFJTSVE2ODU5ZDZYTndFR0MrQ01mL1JoS1JvVE5kUWZ5d2E5TEJaZ2E0bFc0Y0E5KzRiT0NEUkdVdDUrWlRaVGpxQlUzU3VsQ053V2p0Und5QXptclFjRCtzaXlsQkhkUUUvMzNQWk1EOE9yNklLUGlITzZhZy90bytpYUJvRENNTnliSjZFeG5HOFNWVERzc0d2TmFlMGZQQkExRHU3WHVFaStnWUtyWGdqMmtDM0pIMElyRmdtVytnRFNRdzdXcnBsZ1l1Vy9PMjB3OG9OcDV3cnVoZktYZjh6QUZDTExjZDdaY0FLam41U081SmZyc2R2RGcrK1U0OVFubUJNN0UyRHU2Mmc4SGkiLCJtYWMiOiJhNWExNTJjNDcyZmE1Mzg0ZGQxOTI3MmI1ODc1YWE5ZmIyNzk4OTVmZjU4OTRjZjZjZTNmNmRjN2MxODc1MGY3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:36:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklQMXBoYmprZFdZODcxQ2p3QTcvOUE9PSIsInZhbHVlIjoiVE9rUUwzaHJJUjg0bVJuL3FQVHZLK0sweVhPc01HSENtZmsxVjN6TTNqS1cvZ3FoeUZYakdqVXpaZ09UWmY1a2M5Q21JNlhVMFNxVEJaVXFqSzFhU2Q0Q1Y4WUthTmFsNzAwd1VSUVVHYjhDS3RWV3BncTdVOVVIWXN5dGUrdTc0cDFXQjh5eTlhdmxiaTZnUjN6Y2RnNnB5OTdaZnhpbHFUN3BjSHIxSDV1ektaRnZ4L3F5WDIwTWF2dmR4b1hrbnpMYk9rTWxET2ZrM29qZkpEUmpNb2hUclhBMUZFaVdaQUgwY3dwVTJJcU85V2UvcXhsRjJyVlhGb01lWXNsdUJLanUrRVJ4cmhyKzdjYUUvcG9XU3NtUVVBdUMyeDRkbXlUZFNMNDZuMVpkRjZIcVcrNklDeVBYN2FxMlh2bmxJTFdZeERpTi9Famhwd0lKdk4vclhobmE0ZWMwUzVvaWwvY3FSL3JsQnpCY1IzRVZXMStNUXZwL3hnUnR2UjN4WVNEZVRIT2o5UWRjdDVaaTQrcGVraC9xYklIUEhhejQyQ1hJWXpSSGk3OUg5Y0lSUWtJRTBZRk9WRjFON2ZhMmdEZ3lxdjk0UlRmSVFsWit4SU5QSjU5TlBmTGYrVzlaakV6RzFyL1FLQ0tjclp1VnlJcnhSYkV0U3hzelVDMWwiLCJtYWMiOiIwZTNjMTlhYTJjNzg1YzNiYTk0ZWEzOTFhMWM4ZDI3YjZlNTdjZjdlMDJhYjY1MjliOGI5NWRiMjkyODUyOTA0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:36:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJDMEVuSzhmSWVWRkJuSzNtS2I4cmc9PSIsInZhbHVlIjoiUEh4SS9WeVRlbFNxTU9Lc2lPQzBqdmV5YXJGaGc2TXdwWlVLUGZjVCtGQkZ6TlRhaU1BSW5GdC8rVnpsSzZPSTBuV2ZDQWt4VWZWZ3lLQ0dkNzV3QW5CNG5CNkxSR1hvWTJyN0sxcFBwbkNBSGFQZE8wT1JTUU1TL1lYV0VlWFBoU0F0aEhBdXhvS1VzRExnVnhsS3J6clJlNmUrbE1qdXpUU0k1Z2t0aXg4cCtmRFhuRnFRd2VnTncxQ3FDMVUzaC8xekVidDJmNlg0VEo2c01wNnZzV0FzVXBsVHJTUk9tanNEdll0SmVEcE9xY2EzMzRVZTk5VFJTSVE2ODU5ZDZYTndFR0MrQ01mL1JoS1JvVE5kUWZ5d2E5TEJaZ2E0bFc0Y0E5KzRiT0NEUkdVdDUrWlRaVGpxQlUzU3VsQ053V2p0Und5QXptclFjRCtzaXlsQkhkUUUvMzNQWk1EOE9yNklLUGlITzZhZy90bytpYUJvRENNTnliSjZFeG5HOFNWVERzc0d2TmFlMGZQQkExRHU3WHVFaStnWUtyWGdqMmtDM0pIMElyRmdtVytnRFNRdzdXcnBsZ1l1Vy9PMjB3OG9OcDV3cnVoZktYZjh6QUZDTExjZDdaY0FLam41U081SmZyc2R2RGcrK1U0OVFubUJNN0UyRHU2Mmc4SGkiLCJtYWMiOiJhNWExNTJjNDcyZmE1Mzg0ZGQxOTI3MmI1ODc1YWE5ZmIyNzk4OTVmZjU4OTRjZjZjZTNmNmRjN2MxODc1MGY3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:36:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklQMXBoYmprZFdZODcxQ2p3QTcvOUE9PSIsInZhbHVlIjoiVE9rUUwzaHJJUjg0bVJuL3FQVHZLK0sweVhPc01HSENtZmsxVjN6TTNqS1cvZ3FoeUZYakdqVXpaZ09UWmY1a2M5Q21JNlhVMFNxVEJaVXFqSzFhU2Q0Q1Y4WUthTmFsNzAwd1VSUVVHYjhDS3RWV3BncTdVOVVIWXN5dGUrdTc0cDFXQjh5eTlhdmxiaTZnUjN6Y2RnNnB5OTdaZnhpbHFUN3BjSHIxSDV1ektaRnZ4L3F5WDIwTWF2dmR4b1hrbnpMYk9rTWxET2ZrM29qZkpEUmpNb2hUclhBMUZFaVdaQUgwY3dwVTJJcU85V2UvcXhsRjJyVlhGb01lWXNsdUJLanUrRVJ4cmhyKzdjYUUvcG9XU3NtUVVBdUMyeDRkbXlUZFNMNDZuMVpkRjZIcVcrNklDeVBYN2FxMlh2bmxJTFdZeERpTi9Famhwd0lKdk4vclhobmE0ZWMwUzVvaWwvY3FSL3JsQnpCY1IzRVZXMStNUXZwL3hnUnR2UjN4WVNEZVRIT2o5UWRjdDVaaTQrcGVraC9xYklIUEhhejQyQ1hJWXpSSGk3OUg5Y0lSUWtJRTBZRk9WRjFON2ZhMmdEZ3lxdjk0UlRmSVFsWit4SU5QSjU5TlBmTGYrVzlaakV6RzFyL1FLQ0tjclp1VnlJcnhSYkV0U3hzelVDMWwiLCJtYWMiOiIwZTNjMTlhYTJjNzg1YzNiYTk0ZWEzOTFhMWM4ZDI3YjZlNTdjZjdlMDJhYjY1MjliOGI5NWRiMjkyODUyOTA0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:36:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643021374\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}