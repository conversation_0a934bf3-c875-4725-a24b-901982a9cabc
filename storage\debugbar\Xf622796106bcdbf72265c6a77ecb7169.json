{"__meta": {"id": "Xf622796106bcdbf72265c6a77ecb7169", "datetime": "2025-06-07 23:06:23", "utime": **********.468675, "method": "GET", "uri": "/search-products?search=&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749337582.412597, "end": **********.468703, "duration": 1.0561060905456543, "duration_str": "1.06s", "measures": [{"label": "Booting", "start": 1749337582.412597, "relative_start": 0, "end": **********.235941, "relative_end": **********.235941, "duration": 0.8233439922332764, "duration_str": "823ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.235959, "relative_start": 0.8233621120452881, "end": **********.468706, "relative_end": 2.86102294921875e-06, "duration": 0.23274683952331543, "duration_str": "233ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53114952, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1138\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1138-1234</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.030250000000000003, "accumulated_duration_str": "30.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3128061, "duration": 0.02364, "duration_str": "23.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 78.149}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.357122, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 78.149, "width_percent": 2.909}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.395683, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 81.058, "width_percent": 2.975}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.4010231, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.033, "width_percent": 3.405}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1150}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.412801, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1150", "source": "app/Http/Controllers/ProductServiceController.php:1150", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1150", "ajax": false, "filename": "ProductServiceController.php", "line": "1150"}, "connection": "ty", "start_percent": 87.438, "width_percent": 2.512}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname` from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `product_services`.`id` in (3) order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1166}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.422219, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1166", "source": "app/Http/Controllers/ProductServiceController.php:1166", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1166", "ajax": false, "filename": "ProductServiceController.php", "line": "1166"}, "connection": "ty", "start_percent": 89.95, "width_percent": 4.198}, {"sql": "select * from `product_service_units` where `product_service_units`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1166}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.434389, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1166", "source": "app/Http/Controllers/ProductServiceController.php:1166", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1166", "ajax": false, "filename": "ProductServiceController.php", "line": "1166"}, "connection": "ty", "start_percent": 94.149, "width_percent": 2.711}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 3 limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 267}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1173}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.439503, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:267", "source": "app/Models/ProductService.php:267", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=267", "ajax": false, "filename": "ProductService.php", "line": "267"}, "connection": "ty", "start_percent": 96.86, "width_percent": 3.14}]}, "models": {"data": {"App\\Models\\WarehouseProduct": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}}, "count": 6, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-986994720 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-986994720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.411198, "xdebug_link": null}]}, "session": {"_token": "nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-275799472 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-275799472\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-57470347 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57470347\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1427792554 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1427792554\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1171776877 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2811 characters\">_clck=erumib%7C2%7Cfwk%7C0%7C1984; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; pos_system_session=eyJpdiI6ImVNL2JmZ05iY3huemxuNWZOWkJqeHc9PSIsInZhbHVlIjoia2taNWtSajhLZmtWVmhqZzM2a1VRQ1ZpNk8zdVZaUHlpUGlZVkRwREY2dy80cVN5Rk55RGZldFJIempxdmFVSGdtRDVQRkxyMEtpNU84THFWSlpJQWVjL1p0MzZnSFBQbTFFanNPcytGU1R1cjV0bDAyQkl1RURud1JKeHBNVjZRcUN6WGF5cDhZYlV0UVBGVXY4dURZYTdIY0dZcHdUb2JPcHM0V3FuSFNHblJLSTVwTlZJazU1SG1pZzl6aGZMbHpkOFRTMnRGWDNKSVVrdWNIa29wcGlUMTFtWTI1bTducU92LzZIcXNFamFDZUVlN1FTVHB3bXJSWHh5MFVEVmYzZ3ZRL0EvNHg4bjVNUWhjTFJPTWlGL05GeDNUbUt4d3NJY29rVXVOVGxka2Fkc1cxR0Q4UGxpWGVGVTVMUnU0UTQ1bXBXcmE4ZGIzTnpuWjNtMnQ0ZEdsbjIzOERhTWpFaHNtbHFLRkZQcW1ZQW1zeXdINmNmMERIWDcyZFhTenUrWklDQllEa0k3TUJkM3Uxd3E5UWhZRU1vT283ODNXU3VSVVBvTFZGK04zVDE0MU5YdXNqZVdjS0VJVi9POVJRRU80dkxsYmRpZWlkYXlwV0g1cEZvTys5ckhNUUZFTThmc2VrQWp5RmhWMTUzdlNYY01henM1aXFRUFZFRHoiLCJtYWMiOiI3ZmVhMjFjNGMwODdjZWJkNDZjNzY1NGVkZDYxODkxZTUwOWExNWIzYTE4YmVmMDhiMzQ4YjkzNjM1NDE3YzY5IiwidGFnIjoiIn0%3D; _clsk=vcxus7%7C1749337576086%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlpdUhlSXVqZzdjY2ZwT2s3cENOU1E9PSIsInZhbHVlIjoiY2ZoZ1lOTnVoTFJkV0ZDSHRiVkRFZ3A5ZWFrNDNBM3NFYzNQY25ZUG9YNjBDMFNRUU5nenVLbExYYjdFMllKd05YWFlCY2hUUTZ2bW03blBlUHpybzRCQXE1VVVnUjlwWlRaL2pPZ1FzQXdvUjNNZUxoK3FCaTVOZ2Z3K2xWOUt2NWdPNEM2bGFDRkszbTVWTVUxYzhWSkRGTmtQb3BFN2hmd0tBY3YxVHlDTThRVE9kdE1hWnVRWUxFNmRDTnBBTGo2SU85Z1FOT25lMGE5LytqaWhNWjU0b2hXWi8vcWtleXVLN24ySzN3blU4TDBwUGNNWWNZSzJuS1laVjVIM0N6OWVxc2ZjUU1lc3J3a21TQ0dLOE9sKzZKUEJ5anFUdUozREtHY3BrZFhid0dSYlJNaGNlb0VDUnZnRkl6VENQaFp4YVU4dXoyQUlUYVRsMTVCaGJNYnBLWUo4aTA3RWROcWlhQXg1eEozckI3UE5nbkh0MHV3T0RNbVVyMW1UQTBHeHVSdnNnVUtXY1JBK2JuSHB4QWhCRnBKMVJKdW1hUTdhYmovNkJuYlUwd2tvTGNzVkNZNzhMYjQveFY1V3ZjUFd6bDYyRG5HNjVSNzV0bmsva2lJQ24xQXAzQlN3eXNjSGsrd1hoeXpiN0dnT3Q5dldoajduYkl6a3ViV2MiLCJtYWMiOiI5NTQ5MmQ0ZDNhMzJlYjVjZTRiZDQ4MWMyOTliZDRkMGVmYjk3ZWU1ODhlNGZmMDUwY2FjNmQyMjY4ZmU0NjAyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndyTUNGRlFkbzZsaEFTZy9VOElVeEE9PSIsInZhbHVlIjoiaHcyalJXcy9samZGOUVmMXVJU09RU1ZOT0s3aTBUL0t1RmI1VU1oUmU4YjlwOTB0NUU0bFhNUjZaSDdrUHlqN0xtQ0VrdXVXNEdaOFo5d054Q0NYN2VGZDBjVlpMYjJUNE9NSUEvaTJkNnlSWUZzd0xqMXVsb2NHMG1pbWU0RUI2N0VyemNOQkZWdmJWZlFLejFNY3FBR3lTcE1jOHJDT25DOUhDRlVNNDNGK1JxSjIyN3ZWMklZYmxhWi8vb0twc0dLb3pPbHNSR3FOdGZpQVJreEdjYURCYnV2RTBUWHJTTE8zWVdJRElBWkpJWS9mVE9ZcjZOVjU3d3RhU040WUZhYjlWdnhDWVpoNUN2N1ZXUnJsM3J3ekVhSDd3SlRGb2J1aDdnQW5ZSnhpN1dUaHB4eDlQRW1hOWh2cHFXWWNaVTlFV1d4MjJNT2JVUHZrL2pJUTY5SXNHVEluWlFqdldMMmp4cnpDWStnU1Q4ZloxUlRINy9TUTA3WnUzdEpodmRuNVh3NDh5eEFEOFRqY1grM3N0bjRrV1dBam43Zy9XNGd6bWwrSytTajdqN1hTTmluc1ppYTNpL21jK0hiYWFXdnU3LzVkWnR4MnRRMmEwcXEzc2FrYktDUTZlUEwxdi9mT04rK2Z3S2Nyby9rQldKcE5kcXgxcXpsY1NVeXMiLCJtYWMiOiJlNzAwNzRhN2E3OTczZTBhMzQ4MGI5NjQ2YzA3ZmE3MTFiMDJhMGYxMDJjMjYzZDc0ODZlOGYwNTcwMTM5NzRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1171776877\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1092595883 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pos_system_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k8SQqyhwBkgpW9lptEbMoTN8iNDNeZ6DzX94m50O</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1092595883\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-666980057 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:06:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZ3OU9yaFlMODlSUDRMbDZDUmZJRFE9PSIsInZhbHVlIjoiVTRwYmx4cW8zV2RwWjBRVE0zVHdRdG94UGgvVTRFc1NkeFMvRnFYTFVmMEVpL3h2bVV4WDBuQkluT0FNRkdLZTJxaE5MMjBESlg3MHJRU3oxcWxpV3Z5TFZoMTV5UFVwclZxNVdyVi9DeWNlT3hwbzBmRmtnRGFMTW81bEc4Z2hCbjdwSEdVTnBuSUgvMnd5T1lqam1uVFc0UEkrNm11OGpvTkRGd0lnU090ZG80NmwzaFZuMXlwL1BmSkp0MkZSTU5hcjdSZXhZUnNIbnRkQUJQd3doZ21UZWFCVUI4NzBkMCt2V0dRK1k1VEhGRlA2aFJva1JIOGV6ZUhTb3BPZVZDRmZuZUQ2OGlYYXRnY3lyQWZDdElhZlhnb0NzUDJoelJKQkFkTGw1WmNyMGlzVmY4Qk44N3VSVERqbkl3aDVHUDRSbXFvNzEvTklTUVdzWTE2bFBKWVpGQnJxL2dWYThFb1RhUXR3QmhzQjlNc05QVmRYcnQ0bTkrb2diaFdXVmF5UVRNa3pCdHd6REhKSFA3c3hrQ1JrUFBuMnhLNllQT3JzYkFiazV4N3ZITHkzdDFiVHNQUVVCSGhsZHlHemY4dk50SmRVa3NmOGpOZ3BBLzMwb3FlVmFkMkt4WTNueVg3Z2V2MjBsbnl6Y2g0VG54S0dqNjl1UExuLzVxQ2giLCJtYWMiOiIyNGM1ZmQ3ZDgwZjJmNGM3ZmQ2MGY1YTlmYWQ5ZGU3NjA4NzgzNTkzZTA1YzFjNWU1NDIwMTJhY2Y1Y2Y3ZTc1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:06:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjdBRC90V1JsVGJ2RWJlUEFmN3FMU0E9PSIsInZhbHVlIjoibTdiNVJUcG04Njg0eDNMM3J1YTdtY2JUT3B1d1dTOVV3WE96TUVJaWxBelc4dVZyNC93aGorTW1uNVJ5MmtkTHZOK2xrdHJ2OEQvUUo2a2Y1d21lQ0hMRkFwc09jN0h0Q0tsNGd4dkhmUlcrbzQzbEFDYUxHRkV5VEsvMjJ5cTBNdHhEQUl4Y0J2K3FvSzdRVmREaGVkMlZ4NHRZNkY5RUdLVjBJUFpnclZmL2gxMXBTOTcxVTFaL2lNQ29odkI2VUVpZ1E0cnYyc2hqQWdWN25zWk5rWGd6Z3dJb1hqeWVaNVA5NG5Va0lNQjQwaFBSMGdGR0dTNTBmZ3l3aUpxWGNRZVNHNlljWkRUYW9zMDdrcElPQzJtak9vR2YvakcyT1hyRlpQdVpJTXRxNVlJUHhTcjdYZ1BhdHJvTFJTTXVsM0lWVUpDaFo5Z0RycUtsTXJaZStiK25NUFNVSmxjSENJTERkK1FVNjVZbzFQUlhheTM3Y25CbkV0NEt1S2p3YzgvSjlDdmxNdEdtZElXcFBWOVRyZFRUTGNoT0ZHalgwTTdQcm5MUk9GZ0x1MTdCWTRNRkZZa3ExeFhobGJ5Mkw2RHNHYVU5amZnYnZCeXFzWnluZ256RGZGMkM2NmM1STJaNlQ0ZlZ3dHpadEJ6K3pNQnYrNU43OTV4dm0zL0oiLCJtYWMiOiI0M2IzMmEwZTY5M2IzZWRmMjMwNmFhM2M5ODIxMzU0MWJmMDJlYWJjYTEzNjFmMGY4MDFkODE3NmM0MmM2MDlhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:06:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZ3OU9yaFlMODlSUDRMbDZDUmZJRFE9PSIsInZhbHVlIjoiVTRwYmx4cW8zV2RwWjBRVE0zVHdRdG94UGgvVTRFc1NkeFMvRnFYTFVmMEVpL3h2bVV4WDBuQkluT0FNRkdLZTJxaE5MMjBESlg3MHJRU3oxcWxpV3Z5TFZoMTV5UFVwclZxNVdyVi9DeWNlT3hwbzBmRmtnRGFMTW81bEc4Z2hCbjdwSEdVTnBuSUgvMnd5T1lqam1uVFc0UEkrNm11OGpvTkRGd0lnU090ZG80NmwzaFZuMXlwL1BmSkp0MkZSTU5hcjdSZXhZUnNIbnRkQUJQd3doZ21UZWFCVUI4NzBkMCt2V0dRK1k1VEhGRlA2aFJva1JIOGV6ZUhTb3BPZVZDRmZuZUQ2OGlYYXRnY3lyQWZDdElhZlhnb0NzUDJoelJKQkFkTGw1WmNyMGlzVmY4Qk44N3VSVERqbkl3aDVHUDRSbXFvNzEvTklTUVdzWTE2bFBKWVpGQnJxL2dWYThFb1RhUXR3QmhzQjlNc05QVmRYcnQ0bTkrb2diaFdXVmF5UVRNa3pCdHd6REhKSFA3c3hrQ1JrUFBuMnhLNllQT3JzYkFiazV4N3ZITHkzdDFiVHNQUVVCSGhsZHlHemY4dk50SmRVa3NmOGpOZ3BBLzMwb3FlVmFkMkt4WTNueVg3Z2V2MjBsbnl6Y2g0VG54S0dqNjl1UExuLzVxQ2giLCJtYWMiOiIyNGM1ZmQ3ZDgwZjJmNGM3ZmQ2MGY1YTlmYWQ5ZGU3NjA4NzgzNTkzZTA1YzFjNWU1NDIwMTJhY2Y1Y2Y3ZTc1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:06:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjdBRC90V1JsVGJ2RWJlUEFmN3FMU0E9PSIsInZhbHVlIjoibTdiNVJUcG04Njg0eDNMM3J1YTdtY2JUT3B1d1dTOVV3WE96TUVJaWxBelc4dVZyNC93aGorTW1uNVJ5MmtkTHZOK2xrdHJ2OEQvUUo2a2Y1d21lQ0hMRkFwc09jN0h0Q0tsNGd4dkhmUlcrbzQzbEFDYUxHRkV5VEsvMjJ5cTBNdHhEQUl4Y0J2K3FvSzdRVmREaGVkMlZ4NHRZNkY5RUdLVjBJUFpnclZmL2gxMXBTOTcxVTFaL2lNQ29odkI2VUVpZ1E0cnYyc2hqQWdWN25zWk5rWGd6Z3dJb1hqeWVaNVA5NG5Va0lNQjQwaFBSMGdGR0dTNTBmZ3l3aUpxWGNRZVNHNlljWkRUYW9zMDdrcElPQzJtak9vR2YvakcyT1hyRlpQdVpJTXRxNVlJUHhTcjdYZ1BhdHJvTFJTTXVsM0lWVUpDaFo5Z0RycUtsTXJaZStiK25NUFNVSmxjSENJTERkK1FVNjVZbzFQUlhheTM3Y25CbkV0NEt1S2p3YzgvSjlDdmxNdEdtZElXcFBWOVRyZFRUTGNoT0ZHalgwTTdQcm5MUk9GZ0x1MTdCWTRNRkZZa3ExeFhobGJ5Mkw2RHNHYVU5amZnYnZCeXFzWnluZ256RGZGMkM2NmM1STJaNlQ0ZlZ3dHpadEJ6K3pNQnYrNU43OTV4dm0zL0oiLCJtYWMiOiI0M2IzMmEwZTY5M2IzZWRmMjMwNmFhM2M5ODIxMzU0MWJmMDJlYWJjYTEzNjFmMGY4MDFkODE3NmM0MmM2MDlhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:06:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-666980057\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1347551941 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347551941\", {\"maxDepth\":0})</script>\n"}}