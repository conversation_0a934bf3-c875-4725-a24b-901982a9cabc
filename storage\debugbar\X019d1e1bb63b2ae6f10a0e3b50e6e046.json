{"__meta": {"id": "X019d1e1bb63b2ae6f10a0e3b50e6e046", "datetime": "2025-06-30 14:55:23", "utime": **********.718967, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751295311.428198, "end": **********.718985, "duration": 12.290786981582642, "duration_str": "12.29s", "measures": [{"label": "Booting", "start": 1751295311.428198, "relative_start": 0, "end": 1751295322.060563, "relative_end": 1751295322.060563, "duration": 10.632364988327026, "duration_str": "10.63s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751295322.060585, "relative_start": 10.632386922836304, "end": **********.718986, "relative_end": 9.5367431640625e-07, "duration": 1.6584010124206543, "duration_str": "1.66s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43350584, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.09369, "accumulated_duration_str": "93.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.356678, "duration": 0.09369, "duration_str": "93.69ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WfHnx50nFd5KEn6YaSnaUKikTZDCGXuQCgPdclMx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1870334569 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1870334569\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-70934301 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-70934301\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-725983945 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"195 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-725983945\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1032769721 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1032769721\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2137929165 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:55:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJ1c1dMUmFzalFBOWxLZ2t6YmlhT3c9PSIsInZhbHVlIjoiemo2OUJJRmZhaHh1ZTNQTk0zMzAzRy9pWU8xTldXSDUzZG1XOGJuN25BV28vTDFPdEwwU2R4cW5SckFoeWtwQllDUm9pOFhkZFdMUzdTU1lmTVNPejFNZTIwcmVTMDUrQjdvdG1aSXVlQkhUbzYyTFBmbWk5ZjF5R2ZMRG0vK1NqdHZHbllJVlFjdzIvZlBiUStUZ251djVLUm1pV3JBVDhPbzkyenRURElSVEVFem1PUVB4UVdnZjJldklsNHVVM2htbmprVENMWDZZKzJYVFdvUFZiSkNReEZUT0o5YXRac1hHM1hiUjliRjB2SC9aRy90aU1BKytPaWpJOUNFOWVISmZUOHFpN28wVURDMURHS0dEK0pCYjJKYXZPazEzT0tEcjRzVFNPbG5lTWNzbTEvcTBsUjNuR21uMERJNExaM2lUbzRhR25JSFZULzNFWHFJdUwwbGNUYm1lanQvVWl1Q1dkVmQvL3JSSEtCTlhUMlBrVGs3d3N1UGVIaXRaMlR2ajZRWEN3bURKb1BMTzhhZmxpbWIvbUhDNmdkNDB1dGF3RysxYzFNNmg4VWJGL3dYT2FCOEhGSmY1ZGpWOFRVLzdaQTVWQ3oxQ3ozRWpUNi9qWHVpK2VxdExZdWVuSkJJM01wUi9TV2VQa2pHSG9ZWVRVYTdYZEhyMGdxUjUiLCJtYWMiOiIwNjI5YjhkYmZhYjYxM2QwY2FjNzUxNmQ4OTI4NDJjYmRlMDdkOTQ1MGMzNTRkNTgxZDA2YzNkMWRmNWNkZjllIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:55:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkszNHVmMmpENXhGdEl0aG9reWZsTnc9PSIsInZhbHVlIjoiR09KQmkvWHFPeXcwd2RBMmpKZ1ptWXdveUM5Q28vY2xjck9xRWl0eVZiOUlhY2lnN1NKZ2hKc3RncUhsZG1udmlHYVRYdE1QODNYUGpjZWVVcmxGeExqUDRhc3ZFb21XdG5WSUxWSDZBYURQWis5bGhnQmR2Zms2QmV4bzVueHBCeXZWVzI4aTQ4NkZhU2RUaVZQNDhKQVFCaEhHcWZObU9LTmhuZ1gxeW1hWEp6aGcyWDBHQlYwUld5eE5TRmpRRmkwQU5reFkwQUZ4ZERJM1lxZTd5QnF6OUhyRFpIV0pudkpZUjFycXlKSnhxQ3gvK1JKUEV1UXlkWjdyRnFSSmFLQ1VjRjZNc1Q1eTlaWXY3WFQ2SW1JMHlqOTNBamZNaE1TK2NmbVczaVpHZGt2WFpSa1cvVkljd1JHSTJzR0ZUcURZTDc2S1I0RzFsYUpHTUkwSE92Si9sb0dXNVd0YURRZGdJdzU5M0NBM1g4Tlkxa0ttM1YyNTZiYmNoT3VoY1BrNExiMklOZUZOa1dLTVhrRThnTWczalZvYzIvN1RzRWNxNXJIdzJRSDEwekJxV21CenV5NzVxcUxDMzRwQVc4cGJUdTh2eWZTTmZmWW54dVZtanlpS3lhTXVBWG1yQkZSS2lEZVU0SUEyRnFqRnFaS2pRck1zRkY0ZUxtSTYiLCJtYWMiOiIxYjM4NjljZTU0MmViNGFiYjAyM2UzOTcwZjU3NmRkOWM2NDI4YjViZGI0N2MzNjU1N2M0MTc1YTM3MGM1MzI2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:55:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJ1c1dMUmFzalFBOWxLZ2t6YmlhT3c9PSIsInZhbHVlIjoiemo2OUJJRmZhaHh1ZTNQTk0zMzAzRy9pWU8xTldXSDUzZG1XOGJuN25BV28vTDFPdEwwU2R4cW5SckFoeWtwQllDUm9pOFhkZFdMUzdTU1lmTVNPejFNZTIwcmVTMDUrQjdvdG1aSXVlQkhUbzYyTFBmbWk5ZjF5R2ZMRG0vK1NqdHZHbllJVlFjdzIvZlBiUStUZ251djVLUm1pV3JBVDhPbzkyenRURElSVEVFem1PUVB4UVdnZjJldklsNHVVM2htbmprVENMWDZZKzJYVFdvUFZiSkNReEZUT0o5YXRac1hHM1hiUjliRjB2SC9aRy90aU1BKytPaWpJOUNFOWVISmZUOHFpN28wVURDMURHS0dEK0pCYjJKYXZPazEzT0tEcjRzVFNPbG5lTWNzbTEvcTBsUjNuR21uMERJNExaM2lUbzRhR25JSFZULzNFWHFJdUwwbGNUYm1lanQvVWl1Q1dkVmQvL3JSSEtCTlhUMlBrVGs3d3N1UGVIaXRaMlR2ajZRWEN3bURKb1BMTzhhZmxpbWIvbUhDNmdkNDB1dGF3RysxYzFNNmg4VWJGL3dYT2FCOEhGSmY1ZGpWOFRVLzdaQTVWQ3oxQ3ozRWpUNi9qWHVpK2VxdExZdWVuSkJJM01wUi9TV2VQa2pHSG9ZWVRVYTdYZEhyMGdxUjUiLCJtYWMiOiIwNjI5YjhkYmZhYjYxM2QwY2FjNzUxNmQ4OTI4NDJjYmRlMDdkOTQ1MGMzNTRkNTgxZDA2YzNkMWRmNWNkZjllIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:55:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkszNHVmMmpENXhGdEl0aG9reWZsTnc9PSIsInZhbHVlIjoiR09KQmkvWHFPeXcwd2RBMmpKZ1ptWXdveUM5Q28vY2xjck9xRWl0eVZiOUlhY2lnN1NKZ2hKc3RncUhsZG1udmlHYVRYdE1QODNYUGpjZWVVcmxGeExqUDRhc3ZFb21XdG5WSUxWSDZBYURQWis5bGhnQmR2Zms2QmV4bzVueHBCeXZWVzI4aTQ4NkZhU2RUaVZQNDhKQVFCaEhHcWZObU9LTmhuZ1gxeW1hWEp6aGcyWDBHQlYwUld5eE5TRmpRRmkwQU5reFkwQUZ4ZERJM1lxZTd5QnF6OUhyRFpIV0pudkpZUjFycXlKSnhxQ3gvK1JKUEV1UXlkWjdyRnFSSmFLQ1VjRjZNc1Q1eTlaWXY3WFQ2SW1JMHlqOTNBamZNaE1TK2NmbVczaVpHZGt2WFpSa1cvVkljd1JHSTJzR0ZUcURZTDc2S1I0RzFsYUpHTUkwSE92Si9sb0dXNVd0YURRZGdJdzU5M0NBM1g4Tlkxa0ttM1YyNTZiYmNoT3VoY1BrNExiMklOZUZOa1dLTVhrRThnTWczalZvYzIvN1RzRWNxNXJIdzJRSDEwekJxV21CenV5NzVxcUxDMzRwQVc4cGJUdTh2eWZTTmZmWW54dVZtanlpS3lhTXVBWG1yQkZSS2lEZVU0SUEyRnFqRnFaS2pRck1zRkY0ZUxtSTYiLCJtYWMiOiIxYjM4NjljZTU0MmViNGFiYjAyM2UzOTcwZjU3NmRkOWM2NDI4YjViZGI0N2MzNjU1N2M0MTc1YTM3MGM1MzI2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:55:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2137929165\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1014792578 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WfHnx50nFd5KEn6YaSnaUKikTZDCGXuQCgPdclMx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014792578\", {\"maxDepth\":0})</script>\n"}}