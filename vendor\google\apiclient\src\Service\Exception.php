<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

namespace Google\Service;

use Google\Exception as GoogleException;

class Exception extends GoogleException
{
    /**
     * Optional list of errors returned in a JSON body of an HTTP error response.
     */
    protected $errors = [];

    /**
     * Override default constructor to add the ability to set $errors and a retry
     * map.
     *
     * @param string $message
     * @param int $code
     * @param Exception|null $previous
     * @param array<array<string,string>>|null $errors List of errors returned in an HTTP
     * response or null.  Defaults to [].
     */
    public function __construct(
        $message,
        $code = 0,
        Exception $previous = null,
        $errors = []
    ) {
        if (version_compare(PHP_VERSION, '5.3.0') >= 0) {
            parent::__construct($message, $code, $previous);
        } else {
            parent::__construct($message, $code);
        }

        $this->errors = $errors;
    }

    /**
     * An example of the possible errors returned.
     *
     * [
     *   {
     *     "domain": "global",
     *     "reason": "authError",
     *     "message": "Invalid Credentials",
     *     "locationType": "header",
     *     "location": "Authorization",
     *   }
     * ]
     *
     * @return array<array<string,string>>|null List of errors returned in an HTTP response or null.
     */
    public function getErrors()
    {
        return $this->errors;
    }
}
