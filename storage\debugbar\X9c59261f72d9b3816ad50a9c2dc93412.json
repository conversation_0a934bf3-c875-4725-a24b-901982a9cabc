{"__meta": {"id": "X9c59261f72d9b3816ad50a9c2dc93412", "datetime": "2025-06-08 16:17:49", "utime": **********.604742, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749399468.998571, "end": **********.604765, "duration": 0.606194019317627, "duration_str": "606ms", "measures": [{"label": "Booting", "start": 1749399468.998571, "relative_start": 0, "end": **********.504792, "relative_end": **********.504792, "duration": 0.5062210559844971, "duration_str": "506ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.504808, "relative_start": 0.5062370300292969, "end": **********.604767, "relative_end": 2.1457672119140625e-06, "duration": 0.09995913505554199, "duration_str": "99.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45137888, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021249999999999998, "accumulated_duration_str": "21.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.541904, "duration": 0.01911, "duration_str": "19.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.929}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5754068, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.929, "width_percent": 5.365}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5854511, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.294, "width_percent": 4.706}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1081780326 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1081780326\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-204579784 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-204579784\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2106249979 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2106249979\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-595674722 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=1y4k55%7C1749399464229%7C1%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkZjT0F5MkZpMDJycGE2VHhiaGUxeUE9PSIsInZhbHVlIjoibklpTGxsOWdZanNXdzl5VG1lVXNUaEo4bWdCT3I5UG9jL1IvVHd5am05U2EvcDhGNUxUQ2VrejNoZ2thMVhHVGllMlpYdEw1WG05ZzRVWllXakpEc3JiZTZ3Z2RUVnhRaWJ0SXhVQnkrQ2lyR3VJKzFRSm44b2EvdVQ1YzRkR2ZnVnN5RFVFdU0raC9mbllwdmxUREI2SnYxUVplR3YzWTFyalNGUmlCbmNWUEpva2xoNGJadFRGbmZKUGJraXNndkc1MTAyOWhMQWRlLzdZdDVYeUVMZkIzNFVuclJnWmJOV0xFMzhhdjBMRyszY3ZhUkt3T1JUb09qK0NVL0RjWVZQTmRPUVJmZm1TTFN1UjA4ZG1Pa1N5azVQS1U5N2F1RDlsSGhuY01YdTB5SFdGSUI3Y3djZXlxQi9kKzFqS3ZPSUp5L0tUa0VhUG9yRkZPMFlOYWZOWUJtZlQvQzhwNTJmbHpWYnlPMk9ERGtjY1NFVFo3YzJkSW1HWVhWb2Voa0M1Wm9MZXdobjVsQUdyNkgvTnMvOS8wendLL3hsODZLZFpvTmw3YmhyNFBkVUJvVHFIeU0rbEMzNUd2YUJBbUNndzZUZ0JlN0YycEd6anRKcjhKdHgzSWlQd2RncHIwamJod0IyWXNFdTY1ODFUS1hrMElmSnFkeXc3dlU1QVEiLCJtYWMiOiI3NGJjZmEyMDBmYjQ3NmU0MWU2YWFhN2M0NDQxYzVjMWIxODcxY2NkMThjZjUyYWI3NTcxZjVhNDEwNjZhNzVmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InhMNFJ4N0NTdlpTNzNsOXNTSUt0QkE9PSIsInZhbHVlIjoidy80U05IZzdjNnBYaTFkY25RT1BkaVdrNTVJSlhQYUV4TnNDZmViT2Nma2NYbkQyRHBRb0VRNVJKYjMxTnlWeDBXZ24yNytnY1FNWk1oaStZOWs4KzFEelZPYmdIaDZvRlc4OXVSK0d4TzkrVFYxYWhIMzVRRGljQk91bWQ1NlhsYnN1dzNvdldGMkNSeXdQS3ZFbGNCcm9tdnVlSFNTamVHY1ZmTUUrT2tMSUVTZzNGRWZsdHhYOHRYK0pCa2k3ZTNSZ2g1bmZiYWQzQzlZeUl6blBIanlWcWVwTVU0MkYySDFhNDViYnRvbk52NlI1dDhVS21LV3UwcEtTNDFHVnI3L0FjL1lNd01hNHB0VXlHanB5aUF1TGo5Y29yRk41UmowZzJlaGpHbGdWdzZVY2Qycnc4VStJNmpGaFZDRkJIcURpNjkyWDBEOGlEaXE1cFVzLy9adDFXTUhzdnBRU3RuSy9qUzY5S3FTWEw3UVVFeU5wRWxpVWRxekhlL0k4WW5zOVo4UTJCYW9nVzlQb2dkNk5jVTRBeDA2ZzhPU2NCcklrVlN5bCtHUXBaVzZ1dUxSRTBUOUFpbGl6cUE0TVRoQ2xuMGh0WWVZMU1leDlJUnY1OTBVS3Vtc010QS92K3dUaXcrTlAyQUc4TkE0YXhFbFFJK3prY0V2VzI2R3ciLCJtYWMiOiJlZTM5MzdhZjc4ZWY3MWZmY2I3N2ViNTVjOWM0NzAxNDllZjM1Y2VkNzJiYjk2ODBiOGI1ZDcyNmM1NTUwN2IxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-595674722\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1247806285 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1247806285\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1420280333 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:17:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhwcFRmblBXaDkveUdBYksyTmVWUkE9PSIsInZhbHVlIjoiMXpEVVBrVWI4S1RINjZNSWk3Q2xDQmVHellleXdmRERONlhKdFZkWjNpRDZNdXN2eTlRVkgvVS83ejJaWkx5VVVybEFWR2tXK0N0bkd1alVBQ1NuUHovV3pVYXdMQXZOdFYvSHk3ZHduVGZONUtsd1YyKzlzNmlxMTdXSHNMaVpTVmhrWDk5Vlh0U1R0b0hFYjk1VTF2b1hFTU80K0VsSXhUOWtLSkZsdld6SEp4UkJJVE81TGxkYXJ3ZXp1NFlneDJpQ1NTcERnZVNCa2JOQzdMK3F2bTF6WkdIOHZBYUhKb3hQeExPZ3k0dXNNV0ZhdnN1Ym9yV1pRdW5BMkRFWk9CVDR4L0paNUNLWUk4WEVwdml4eGJSaXZiSngrdTA2TDczL09BVXBsVUJ3TlBHRHhYcjN3OFN3MkgybnVxT2UzVEZla2dwZUxnK0NaKytMRUpQUGxoZUFhSmtmNXhwUlpPc0hEVml6ZHdMRGVnNlkzL2FqZ0pNSlQ5Y1ZieEJqamtpMHFMVGhsZnliV2tWSmlCSEhwTytncVBsSkJ3WlU0ZWl3SllxUnVyYy94enE2RXAzbU9SR0JoMFVkaFlkUVprK1hQMWhWRXcveGtPWHIzYW1HYnljaExXZXgyd083TWcxbUFzV1AvT1M0Q0RTOEFaODBtQ0NIQWN5eVRxM2EiLCJtYWMiOiJiNGZlZGJmNTM2ZDJiZjMxNTk3M2QyYTUzZjk3MzU2MTIyM2Y1YjI1YTVkNzU4YjE2MDkzMWQ5MWU1NmI0ZjkxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:17:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdoYUhWbmFEUjVwRXdzdmVZeWgxaEE9PSIsInZhbHVlIjoiQ2Ztbm1LZXpURzBoRnM1bjlQVmJib1NzMXNScEFtdjlqd1piRHYrYVFzaXJhaWRXbEZMRmc4bEJLbFZEQ3FwblUxaklmWWZMbHJqRWtxK0RsS0NWNTZDZGtTWVNqWEk5cHdDTkUvK2FBNXh3MnlMZi8zODcydlRkT1hEOGpzMmJpUWlqMmtkTThLbm5BQjVlR1psY25hd29rbXJGdDJmUVk2S014a25rampvYldHMmxVSnh0OW85VFhvcFlqU3FPNjdjeU4ySXgyUTZKbFZrQzJkRlhuZ3lQZFpWOXI0QjY0QVBtVklZalVtTWJyOC96V3N5Um1tV3RQSkpObFppS0xXNXZwWUpBZ3VnZUdUbmJSOElwRlFZTFlmZ3BZc2h3KzBuaGRUVzdyVWNGWDFKQUxHK001ZHR1L3REQ0FaQS96TG5ZR2R3MVRoT2haZml6ZStzVFg3dEcveEVMSks3T1hJTGxDaFAwQ3k4VHYyU0tHYVpIL3ptVDMzUjlpYkhDaXZjWTNtaHdRS2pxdGcxaGhpbXRpekxQN043UGdET3hNaFE3cW4zbUtmL0cxYzFtcEdqSlpIUm10bXlwMEZwYmR6L0JtU3NSTXQ2VHFxUWdiWk9RNHVwUTlLcm45UDFXN3h5dFBmaXZ1cTF1bXRuOWo1ZCtodFZBSnZaMFA1dzUiLCJtYWMiOiJkMTJiZTgxNzgwYTgyNjVkZTY1NzYxZDI0OWE1NmIwMzA5NDdhODQ0NWM3Y2M4MjMzM2NiN2FjMGZjNjMzOWE4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:17:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhwcFRmblBXaDkveUdBYksyTmVWUkE9PSIsInZhbHVlIjoiMXpEVVBrVWI4S1RINjZNSWk3Q2xDQmVHellleXdmRERONlhKdFZkWjNpRDZNdXN2eTlRVkgvVS83ejJaWkx5VVVybEFWR2tXK0N0bkd1alVBQ1NuUHovV3pVYXdMQXZOdFYvSHk3ZHduVGZONUtsd1YyKzlzNmlxMTdXSHNMaVpTVmhrWDk5Vlh0U1R0b0hFYjk1VTF2b1hFTU80K0VsSXhUOWtLSkZsdld6SEp4UkJJVE81TGxkYXJ3ZXp1NFlneDJpQ1NTcERnZVNCa2JOQzdMK3F2bTF6WkdIOHZBYUhKb3hQeExPZ3k0dXNNV0ZhdnN1Ym9yV1pRdW5BMkRFWk9CVDR4L0paNUNLWUk4WEVwdml4eGJSaXZiSngrdTA2TDczL09BVXBsVUJ3TlBHRHhYcjN3OFN3MkgybnVxT2UzVEZla2dwZUxnK0NaKytMRUpQUGxoZUFhSmtmNXhwUlpPc0hEVml6ZHdMRGVnNlkzL2FqZ0pNSlQ5Y1ZieEJqamtpMHFMVGhsZnliV2tWSmlCSEhwTytncVBsSkJ3WlU0ZWl3SllxUnVyYy94enE2RXAzbU9SR0JoMFVkaFlkUVprK1hQMWhWRXcveGtPWHIzYW1HYnljaExXZXgyd083TWcxbUFzV1AvT1M0Q0RTOEFaODBtQ0NIQWN5eVRxM2EiLCJtYWMiOiJiNGZlZGJmNTM2ZDJiZjMxNTk3M2QyYTUzZjk3MzU2MTIyM2Y1YjI1YTVkNzU4YjE2MDkzMWQ5MWU1NmI0ZjkxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:17:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdoYUhWbmFEUjVwRXdzdmVZeWgxaEE9PSIsInZhbHVlIjoiQ2Ztbm1LZXpURzBoRnM1bjlQVmJib1NzMXNScEFtdjlqd1piRHYrYVFzaXJhaWRXbEZMRmc4bEJLbFZEQ3FwblUxaklmWWZMbHJqRWtxK0RsS0NWNTZDZGtTWVNqWEk5cHdDTkUvK2FBNXh3MnlMZi8zODcydlRkT1hEOGpzMmJpUWlqMmtkTThLbm5BQjVlR1psY25hd29rbXJGdDJmUVk2S014a25rampvYldHMmxVSnh0OW85VFhvcFlqU3FPNjdjeU4ySXgyUTZKbFZrQzJkRlhuZ3lQZFpWOXI0QjY0QVBtVklZalVtTWJyOC96V3N5Um1tV3RQSkpObFppS0xXNXZwWUpBZ3VnZUdUbmJSOElwRlFZTFlmZ3BZc2h3KzBuaGRUVzdyVWNGWDFKQUxHK001ZHR1L3REQ0FaQS96TG5ZR2R3MVRoT2haZml6ZStzVFg3dEcveEVMSks3T1hJTGxDaFAwQ3k4VHYyU0tHYVpIL3ptVDMzUjlpYkhDaXZjWTNtaHdRS2pxdGcxaGhpbXRpekxQN043UGdET3hNaFE3cW4zbUtmL0cxYzFtcEdqSlpIUm10bXlwMEZwYmR6L0JtU3NSTXQ2VHFxUWdiWk9RNHVwUTlLcm45UDFXN3h5dFBmaXZ1cTF1bXRuOWo1ZCtodFZBSnZaMFA1dzUiLCJtYWMiOiJkMTJiZTgxNzgwYTgyNjVkZTY1NzYxZDI0OWE1NmIwMzA5NDdhODQ0NWM3Y2M4MjMzM2NiN2FjMGZjNjMzOWE4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:17:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1420280333\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1189632325 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1189632325\", {\"maxDepth\":0})</script>\n"}}