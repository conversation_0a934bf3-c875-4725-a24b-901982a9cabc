{"__meta": {"id": "Xf214afd958a7a48ad1fdb9bcf5c593f0", "datetime": "2025-06-08 00:04:00", "utime": **********.999743, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.336079, "end": **********.999768, "duration": 0.663689136505127, "duration_str": "664ms", "measures": [{"label": "Booting", "start": **********.336079, "relative_start": 0, "end": **********.911712, "relative_end": **********.911712, "duration": 0.5756330490112305, "duration_str": "576ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.911729, "relative_start": 0.5756502151489258, "end": **********.999771, "relative_end": 3.0994415283203125e-06, "duration": 0.08804202079772949, "duration_str": "88.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45037776, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.014390000000000002, "accumulated_duration_str": "14.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9516, "duration": 0.01238, "duration_str": "12.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.032}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.975343, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.032, "width_percent": 5.212}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.986412, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.244, "width_percent": 8.756}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-333191179 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-333191179\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1388077796 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1388077796\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1917679754 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1917679754\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1736080509 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749341026995%7C25%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpNMkE5TGlWMk9Obm5iTzBFL2tkeFE9PSIsInZhbHVlIjoieUdVNEp0UGRDMldaSnJrSkRwWHk3QTV2bHdUK3dJbFU5L0xYZFRaUzBYcC9Dc2I0UjFRS0d6Q1FDYitNeEhmKzZYbVJ1V2YycGxYNkxaRlNzUnlvM3YxN081QXFuSUxnRmxpRFI4Q0pjZUhhRmZlWFlOTXBrWjd3L05ubWRzN2xKVEhMMW5uRTlGMnh1UU9uZTc5VkIzNWlqMlZVZGt1cGVqeFRCTnk0YnhDVVZGdTFmdHArNlViMFVHdXV4V21hbWl4Z05TTUJBcURrQjhMYUVmN1FFRmVaTkt6VWdOeFpxK25ybVlReDlEdFh2Y1pVLzJkbEkzUlVBSEJRT3l1eHhrazFzeXVlZHZocll3WnZvYTVkOUZtTUl2TmRuTEJEcklRNVB5ZHJCY2hYL3MvbHB6L0tlVi9zaXllOXRxQ2NzUlJkWk5mMEx5V0pBWjJqZW5OY1NkSlZQdW9NbkhtcU5yYjJFTnJFRzRXTVRHTDFHRzl3aEkyVXhBOTNHNzlGN3hLeDd1dkdTWklqbUNZTVNLaE1HN2tVVHFxMHlYOGphSXRWbm1ocndCT1JxeVk0REF6bTZYRGh5alVZa1RsbnNLQllhRVltSGw0UVdzRXFoeTJMRk0wK3diRE1TK1Rlc05hYVdPdEJ5VGpVTVo5VitUbGJPLzJBWmdkNFkyWEsiLCJtYWMiOiJhZDI2YWIxMWIxMzM4MTVjN2M3YTFiNjI2NjJiYWIyNjA0ZTQ1NjMzYWE1MTc3ZGY3OWEyM2MwYTcwNWMwMThlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlNhV29VQjJrcFlYb2lSU2RGWG9VQ3c9PSIsInZhbHVlIjoiS1VXY3FaV1o2akhqUVBvYStON2RpVjZvemhZNDkvdytVU2w4UVVudFlrcG9rcWQvRmYxVXhlLzBIMzB4cFNQUTFqSm8zSGI2MG9Zb2lPbW0wU2dZMlEzdERSZEpBOThWd1JyZjhiTXZFQ3JzbUJ4RHNzZXN4RU56VHlGaWgwUFFmWEkxdGpyOGF5eWlySEs0enJjWEo3VWJuQ2Vid1dTbVNUZC83d2FTaG4vT1dTTk5sWDR2KzZJQkRURk5zQXJHZEhNdjlVSEdsTUd5WStuUnVrZnFGaFZTaldZWnAweVc0WUFWMytlajBMTzFkVHkybnBvSDNpVkdPa0d2cW4zZTJOQWE4bnNCM2Z5UlE4VDdmVHd0YU8rK2FwWmhxalRoMHFGZ09jT3dJeXdEUTcwYVRBK1A5THdKcm5WSmJIUzB2SlpERVVSZkVOWGQ1bUJvS01yRFhQVXNQa2VlZ2NicUtvb0tFd0pIYnpWRWJuZ3lrbUxveHpIZ1dFOVJBMWprd2JIdkdHbGZpc1BQR1lUcEJwSzJRbFFmNVFZeTJjNExYT29qSXQyTzF0eWNlUW40V3I0TFBSKzlJSVR6aklvTzFZQkhZcXdZS2hyUm1wNWxmWUlBai8vdU95SHN3OWxGZkp2dmN5clRaUk4wZndXVUN2VzIxKzBINno1dG1sMVUiLCJtYWMiOiJkODJjNDBmNDk2NjEyYjExOTFjZGJjZGFiNzEyMmU3ZjFkMmVjYjhlYTg1OTBkOGQzNDVhODcwNThlYTE5MGU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1736080509\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-919182505 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:04:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBWeStReVpLVHdlTDN4NWN0UWVQRnc9PSIsInZhbHVlIjoiRGhVaWxHUVFoOUZJZlJmejdWa1IvT2NrWGFkQ1hpOHA3M0x3a2tJb1RRYkR6T0psd3lQa0dJNHowbmJkUk1LMVhiWElIbTV0QXJNdHBHcHJ1Y2NFdE1aaldnU256L2RXM1RSdGNEUVhoS0laMy9GaFpheExOc2NmMGhaYTg4Y3A0M3QzU1E0VzFrb05udlVOcEx5QUEzUzQxWHNmTDlIUk1vU1J0MDNVT2RLOUYrSTZ3eGtPUElqNUUrUU9OUi9tUy92NXB0STV6a1VkVndIanZ4V2Y3VXVwQ1ZwMThNSXB2Rk5EWm8zZ081cFZqa3B6ZmFGUGc4Rm12cXF6ak41dGtQTVZTUlR5UmVGeVlKM3VNVXdrRzQ3TkM4VklDRnhndHRCcTNSb1BTZVZ3anF4NHN6ZEtyaGhKcVdPSjdDM2VWenFVdFpkUVUyTXJvM3k5dHlZS28yMDYvQno4bDkxQnEvVXdoc25NTWZZTlQvMEpRa0ZZWkNnbEg1Y3pTSStxYzBBcXcrMVhXbVhRR1hxVzVOZlZlSGsyYUJJSVIyY0toYmo3UUs4cGR3NkF6QWhleUplUUtZYzF1NW1PNFByQ2hxN0MwM0Z3bUhDOStJZkJ4WWdqYTVrV3lnNHF1bm1MdFQrQVZxNHVhS2lIWldCTnJoeWs4Tzd4c0FRajgwdkoiLCJtYWMiOiI3YjU0YTA3MzFkZGFmYzRiZTdiY2Y3NzViZTM4MDVhMGFkYmI4NDExMDZmYzEyZjRkOWRmNDhmYjQwYWI5NGExIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:04:00 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikx0SEU0SWhiY3JSQ2dXemRXbmlMMHc9PSIsInZhbHVlIjoiR1p6RG5BOFY5UzlJdkhLWlh3aXhQSkVKbCtXYndBUUpVMUxnU2o0ejdBakpGZDQyWlNEWXFMWmtqRk5YeDFwS1V4UFdRcE5IWVE3bXdnNm1TUjZmazM0QlRPeWRwKzd3bldGY0RBUm4xZ0xpZks4U2tFeWxQM2ZRY1FuTjhRWUxWejdyV0c0ZmRTU1lLNE5DYjhITHMwRE5YVjI2QWNXb1VBWlBmWFBvd2cvbCtqOTNuckxGYTFKYkpoWlh0bnRMaUc1TnJYbEJYYkxjQXZNVkJMZEFZSG9uUDlBOXU2c241USsrYjVxbVZuTElKbFIrbmVMRUM5OVpyRHlWLy9iR0l3R3FRaFZyUjFMclZHQ2JiV0NXbmJBb3VkTGN6OTZ3eGlTeEovajkybWNZclRFQlpteENhNmMrYmNtK2JsMmkvVmE5Q0lSNWFPMUhFUmx4bHBVb1NDSmV5QUFaQzU2SzFldTJxcmR6ek4yUkhUand2b0dhWlJiSGFrUUFFMDA5NTlrYW01MHJaVGkrV1V6bm1SbTJud1JJbjdUV3I2cjR6N1Z5ampoc0tqOVdMOWdETU9ueTZybFdHd25PVEtUYS9EQTlZdGEwTElCMGh1cVZlS1JQWEJLb09OYmxDdGhpVUlDTXJoWHhmTEV1b0wvMkNubHBURnM0aGgzYjJoVzUiLCJtYWMiOiIyZDA4MzAzOGQ1ZWIwNWRmZDY4MjQzODRhYTE2OWU2MjM4MjM3MDU4NDRhYTg2YjRkMDU5YmE4NWM3NDlkYzBlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:04:00 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBWeStReVpLVHdlTDN4NWN0UWVQRnc9PSIsInZhbHVlIjoiRGhVaWxHUVFoOUZJZlJmejdWa1IvT2NrWGFkQ1hpOHA3M0x3a2tJb1RRYkR6T0psd3lQa0dJNHowbmJkUk1LMVhiWElIbTV0QXJNdHBHcHJ1Y2NFdE1aaldnU256L2RXM1RSdGNEUVhoS0laMy9GaFpheExOc2NmMGhaYTg4Y3A0M3QzU1E0VzFrb05udlVOcEx5QUEzUzQxWHNmTDlIUk1vU1J0MDNVT2RLOUYrSTZ3eGtPUElqNUUrUU9OUi9tUy92NXB0STV6a1VkVndIanZ4V2Y3VXVwQ1ZwMThNSXB2Rk5EWm8zZ081cFZqa3B6ZmFGUGc4Rm12cXF6ak41dGtQTVZTUlR5UmVGeVlKM3VNVXdrRzQ3TkM4VklDRnhndHRCcTNSb1BTZVZ3anF4NHN6ZEtyaGhKcVdPSjdDM2VWenFVdFpkUVUyTXJvM3k5dHlZS28yMDYvQno4bDkxQnEvVXdoc25NTWZZTlQvMEpRa0ZZWkNnbEg1Y3pTSStxYzBBcXcrMVhXbVhRR1hxVzVOZlZlSGsyYUJJSVIyY0toYmo3UUs4cGR3NkF6QWhleUplUUtZYzF1NW1PNFByQ2hxN0MwM0Z3bUhDOStJZkJ4WWdqYTVrV3lnNHF1bm1MdFQrQVZxNHVhS2lIWldCTnJoeWs4Tzd4c0FRajgwdkoiLCJtYWMiOiI3YjU0YTA3MzFkZGFmYzRiZTdiY2Y3NzViZTM4MDVhMGFkYmI4NDExMDZmYzEyZjRkOWRmNDhmYjQwYWI5NGExIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:04:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikx0SEU0SWhiY3JSQ2dXemRXbmlMMHc9PSIsInZhbHVlIjoiR1p6RG5BOFY5UzlJdkhLWlh3aXhQSkVKbCtXYndBUUpVMUxnU2o0ejdBakpGZDQyWlNEWXFMWmtqRk5YeDFwS1V4UFdRcE5IWVE3bXdnNm1TUjZmazM0QlRPeWRwKzd3bldGY0RBUm4xZ0xpZks4U2tFeWxQM2ZRY1FuTjhRWUxWejdyV0c0ZmRTU1lLNE5DYjhITHMwRE5YVjI2QWNXb1VBWlBmWFBvd2cvbCtqOTNuckxGYTFKYkpoWlh0bnRMaUc1TnJYbEJYYkxjQXZNVkJMZEFZSG9uUDlBOXU2c241USsrYjVxbVZuTElKbFIrbmVMRUM5OVpyRHlWLy9iR0l3R3FRaFZyUjFMclZHQ2JiV0NXbmJBb3VkTGN6OTZ3eGlTeEovajkybWNZclRFQlpteENhNmMrYmNtK2JsMmkvVmE5Q0lSNWFPMUhFUmx4bHBVb1NDSmV5QUFaQzU2SzFldTJxcmR6ek4yUkhUand2b0dhWlJiSGFrUUFFMDA5NTlrYW01MHJaVGkrV1V6bm1SbTJud1JJbjdUV3I2cjR6N1Z5ampoc0tqOVdMOWdETU9ueTZybFdHd25PVEtUYS9EQTlZdGEwTElCMGh1cVZlS1JQWEJLb09OYmxDdGhpVUlDTXJoWHhmTEV1b0wvMkNubHBURnM0aGgzYjJoVzUiLCJtYWMiOiIyZDA4MzAzOGQ1ZWIwNWRmZDY4MjQzODRhYTE2OWU2MjM4MjM3MDU4NDRhYTg2YjRkMDU5YmE4NWM3NDlkYzBlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:04:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-919182505\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1907168375 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1907168375\", {\"maxDepth\":0})</script>\n"}}