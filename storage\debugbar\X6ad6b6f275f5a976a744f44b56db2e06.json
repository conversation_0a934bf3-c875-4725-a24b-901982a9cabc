{"__meta": {"id": "X6ad6b6f275f5a976a744f44b56db2e06", "datetime": "2025-06-07 23:27:43", "utime": **********.213082, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749338862.352262, "end": **********.213112, "duration": 0.8608500957489014, "duration_str": "861ms", "measures": [{"label": "Booting", "start": 1749338862.352262, "relative_start": 0, "end": **********.10782, "relative_end": **********.10782, "duration": 0.7555580139160156, "duration_str": "756ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.107839, "relative_start": 0.7555770874023438, "end": **********.213115, "relative_end": 2.86102294921875e-06, "duration": 0.10527586936950684, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45056272, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00532, "accumulated_duration_str": "5.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1651292, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 68.045}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1854408, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 68.045, "width_percent": 14.85}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.198149, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.895, "width_percent": 17.105}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-610098908 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-610098908\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1687422086 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1687422086\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2027011953 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2027011953\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-714762115 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749338822528%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5LSkNYSno2UTFZM0VvNUllc0hqL1E9PSIsInZhbHVlIjoiK1BPVXhTNUo1VFV6VjRtYUdEMHlUQTRhMlY3eXJmeUc0OThET2U3ZW5reUxQVjhBMVpMSmNRVWxzY0dETmx3R1RDRlhCVlliS3NOQ0E5ZzdEbjVwVWJ1MkpreDRrZDlkb0lMTUpNWmdrSzBzT1JLczVsaVFzdjRUUmRMd2VpMmN6MXVBZmZIMWU0UjE5d3dNbU03WTJXb25EQlBGcHViR3JLeW1GeURoK3R0dU0xL0hJa1JGaTJhOHQxcGdGcUZidjNLOXhuRVZjQ3ZySVZnQ2VmSWE5blp2c29UY3ZRaU9CWnhzdU0reE1RcE1QQWs5TkcrRkwrQUZnZjhWL1UwUENrMm42YWUxTmd5ZWlFMUNmS0t2N04yaGhidm9LR2VZbkNxZTdXS21sUHB5alR0bkI2M2tFMUV6eGxkc1VnRDlEczRsZ3dQRDFOOCtiblhjTWt6RWZybWlaeXF5eHcreTVUOHUvWnBQWE05cWZheFhxNitxTDBITlpQQ3p4b3MzMUVYS3c3NFpoc2dOYU5PK1RyVzNKRmRkL3V4ckt5VTlCQjg3bzZwbzFSbmtJbE9BdWk5MEVwb01EaGlkMTZOdTJ0QnNtc0tSR2NWU2JEUXY5VWtHUHN3QlVpWGxFN2NoTEhLVHB5RmxQUGlKQUloMDVRN3F0K2lVeDN1SVJIaFoiLCJtYWMiOiIyYzU1ZjVmYzE4YmExODBjNjc5YzE3ODUxNTk0N2I5ZWU0ZDUzNTY2N2Q5YWE5MmFlNWEyNmM0MGYxMjEzMjZlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjQwMm1kNmRXLytlVDlIcWJVRUJLcHc9PSIsInZhbHVlIjoidXYvZ09MeS9rTDh0WnhCb3RvMUNacXlGSllGL0ZTZGpLejgrZUNUdFRHT3BpeVMyNXB5WmJVbFMyblpKU3ZnZlFrbGFXeDZYUmVzQWd5ZFN6WkUrcUxzQUV2a29IWGdSbWE4MEtuZ2k5d0o1dkxEeTZOTVF0S1QrZUxzRVdhOWlOalJEUEdhSldDdTZpdFhrd2JjQnNhZVIvSGhMTENKSWc2OGw3dWQ3dklkMHFFRDBxUG9vVXlmMkxPb0k4L3kvQWdBeGx4S1BFSFNXREJ4S3pRbnlUQXNlKzZWY1NIb0NsMzNzS2NWbFFKK2s2Y1BicWpWTlVaZExaODl3SUxka2UxdnZWUUlFajg1b2lkT0FnT0JTZ3hmbXRIQ003MGVieUtBR0lmSVJvNDczMFhreGpLYzAyamVNTGRnOVVnT0hMeW16eUxiMldNMkVaeS9xOHh4cExWS09zSTB6OTd1NHBYcTBCQ3hCYSs1ZWNoTGxKWEZkV3dKOFBVdmw3T0FEQmhaVGxwNE40TUJEK20vT0RlRk1lMXc3dnIzYXpjcFhwUG8rM3JjQ1czbFh6TjAyNlI2ZE1lTksvZTBPbUpoSG82dTA5c0RRYzNBQUZFcWF1ZGxPVTByek1SNURidmZ1Z3owSS9lL2d3dEI3ZEZZZkw2YndKUm96K0hlcjFwa04iLCJtYWMiOiIxODIyODJlMGZjZjMxMmRhNmQ2ZDEwZTg1NjExNTg3MGVhZjE4YmZmZWNhNmQ1NjU4YTdjZGI3M2YxYmY2Zjc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-714762115\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1492731878 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492731878\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-342979867 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:27:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZXcmw4V1VXa3o3dndOYUhBd3g5ZkE9PSIsInZhbHVlIjoiazAxVXNidS9iZGVVT3BHbk4raTVkdWNuT3U2NHJ0U3NwNjVTNEgrQlhXZVd5K1RXSEtRMHVmd3lsOWhNT21Xd2JmbDdNRU53a25ZNEN6SXhLUGFvdjd3QjBlQ3Y5LzZiRDR2dWhodEtOTzV1YmF1TnRSNmNJY1loNm1KN1Y0MmJ1ZFhFZWQrejdjRDVvcGE1anRqV0FTdzBEc3AzdzBvWmJ6dUd2QVltZG90VjFBb0xqcTUxNVpkV0tKS1F0NGllZVFidjlST0tqdHpQTmpxTnFERzRaWDNtakpqZGRuREhBOEVpTFZSaGduZ0pCWnIzOVdHbDlTUXY1LzN0ZTJEUkxEbGZONlFGWmdwK0JLOEJwenBEeVkrdG8zWTN1NE5YZW9Ja3hXYWpUaWhRQk1pWlI5d2hyeUl6UmZMelF3SWZ4cUVDNkZUWHhoOUNNdm9uS3RNMUNxMEZlVm5XeEw2aTFsTTJlOVBscGZXZVhKVmVFTGRRamVsL0NBWHJuemJaMEdxc0p2OVp6a0pqWkc0ZW1FdGJ5bklidzc3THlpRWp0aW80UmZZOU9uZ3JnenJLYzk0ZnUrbnc4akwrdTBPOGo5WTZ1cFB3dWFjZDdmRVc5VXB6NC9PZlRwRzlPYWY4M05lU3hSalpDSy9CWWpuR3RZOGFjbkkzdGxVdUhPdUQiLCJtYWMiOiIzOTUzY2MwNzNiYTRhYTQ1M2RjZWJkZTMyYTJhNDUxM2MwNGNjOTk1NDMyYTQxZDYxMDZjYjA1NzU3MzMyZjhjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:27:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InVaeFdvYlpqR3Z4b1dVMVRHL3Fzdmc9PSIsInZhbHVlIjoiVnlCTE04eXRPV3NIUXA4NnJUU1RZbWZPMlFORHordkx1SzRSWmFFVUkwajYvSVltbFU2M3pkcUNVWkEyVUcrTnVRSHgxSlI1TnoydFBCV21LeDJ1OFdjN1ZQOC9Rby9OOG9ZeE1XWUlWRG9UeHlZYlJhMDhjMjduTll4SExab2YrdHhuRHlGUEs1Rmw3dVF4VUhPUXYrS1RJZGtVbDZpTTJTUjNaZ1FRTlphTTk1ekxRRk5OSWk4ajZzQjRDNmQveFVyTEo1RVdCUkFsTE5GQlozVFZqQVVUUjhsdEo4ckJXTnptSW5uRTdCVWdGNHFXbkt3a05MU3JzVDc4SnFpdUVkRC9ndlpFemQwVjlPS0dYeVBGU1NXU1ZkWTZmRkVOc3VsQ29KR3BUZ3ZVUndtN0ZoS1N4d29XNXUxVzJaM1NOdW83LzBkUEYxOExXQmxLRnJvbDhVOVlsTmwxV0FUbkZiNUQ1Ly9CSUoyK240ZHhLNUhRK2xGRCtxZ3J0Ky9lTzNTUGt6OFpCazgrWnBKaTMreHpOTWJzTFhSN0ZJN1NEc0ZnckVMYjgwZnRDTGF5YkorZm13YmJMOHBBdUs3N0x0S2Fob2d1MGg5ZEdNUC9aZlZqQ0tRN1Z1d0lmRHhQRnFaaFZyZG1QYUVnNzdmZWwvWnpiNmZ6WmxLeVJBRTgiLCJtYWMiOiI5MTRhMzM2YjhlYzRkYjliYjMxYjRhMDM5ZTY2MjkyYTdmZjhmNjMxMWNiNzdlY2Q3OTYxNDIyYzFjMTQwYWE5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:27:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZXcmw4V1VXa3o3dndOYUhBd3g5ZkE9PSIsInZhbHVlIjoiazAxVXNidS9iZGVVT3BHbk4raTVkdWNuT3U2NHJ0U3NwNjVTNEgrQlhXZVd5K1RXSEtRMHVmd3lsOWhNT21Xd2JmbDdNRU53a25ZNEN6SXhLUGFvdjd3QjBlQ3Y5LzZiRDR2dWhodEtOTzV1YmF1TnRSNmNJY1loNm1KN1Y0MmJ1ZFhFZWQrejdjRDVvcGE1anRqV0FTdzBEc3AzdzBvWmJ6dUd2QVltZG90VjFBb0xqcTUxNVpkV0tKS1F0NGllZVFidjlST0tqdHpQTmpxTnFERzRaWDNtakpqZGRuREhBOEVpTFZSaGduZ0pCWnIzOVdHbDlTUXY1LzN0ZTJEUkxEbGZONlFGWmdwK0JLOEJwenBEeVkrdG8zWTN1NE5YZW9Ja3hXYWpUaWhRQk1pWlI5d2hyeUl6UmZMelF3SWZ4cUVDNkZUWHhoOUNNdm9uS3RNMUNxMEZlVm5XeEw2aTFsTTJlOVBscGZXZVhKVmVFTGRRamVsL0NBWHJuemJaMEdxc0p2OVp6a0pqWkc0ZW1FdGJ5bklidzc3THlpRWp0aW80UmZZOU9uZ3JnenJLYzk0ZnUrbnc4akwrdTBPOGo5WTZ1cFB3dWFjZDdmRVc5VXB6NC9PZlRwRzlPYWY4M05lU3hSalpDSy9CWWpuR3RZOGFjbkkzdGxVdUhPdUQiLCJtYWMiOiIzOTUzY2MwNzNiYTRhYTQ1M2RjZWJkZTMyYTJhNDUxM2MwNGNjOTk1NDMyYTQxZDYxMDZjYjA1NzU3MzMyZjhjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:27:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InVaeFdvYlpqR3Z4b1dVMVRHL3Fzdmc9PSIsInZhbHVlIjoiVnlCTE04eXRPV3NIUXA4NnJUU1RZbWZPMlFORHordkx1SzRSWmFFVUkwajYvSVltbFU2M3pkcUNVWkEyVUcrTnVRSHgxSlI1TnoydFBCV21LeDJ1OFdjN1ZQOC9Rby9OOG9ZeE1XWUlWRG9UeHlZYlJhMDhjMjduTll4SExab2YrdHhuRHlGUEs1Rmw3dVF4VUhPUXYrS1RJZGtVbDZpTTJTUjNaZ1FRTlphTTk1ekxRRk5OSWk4ajZzQjRDNmQveFVyTEo1RVdCUkFsTE5GQlozVFZqQVVUUjhsdEo4ckJXTnptSW5uRTdCVWdGNHFXbkt3a05MU3JzVDc4SnFpdUVkRC9ndlpFemQwVjlPS0dYeVBGU1NXU1ZkWTZmRkVOc3VsQ29KR3BUZ3ZVUndtN0ZoS1N4d29XNXUxVzJaM1NOdW83LzBkUEYxOExXQmxLRnJvbDhVOVlsTmwxV0FUbkZiNUQ1Ly9CSUoyK240ZHhLNUhRK2xGRCtxZ3J0Ky9lTzNTUGt6OFpCazgrWnBKaTMreHpOTWJzTFhSN0ZJN1NEc0ZnckVMYjgwZnRDTGF5YkorZm13YmJMOHBBdUs3N0x0S2Fob2d1MGg5ZEdNUC9aZlZqQ0tRN1Z1d0lmRHhQRnFaaFZyZG1QYUVnNzdmZWwvWnpiNmZ6WmxLeVJBRTgiLCJtYWMiOiI5MTRhMzM2YjhlYzRkYjliYjMxYjRhMDM5ZTY2MjkyYTdmZjhmNjMxMWNiNzdlY2Q3OTYxNDIyYzFjMTQwYWE5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:27:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-342979867\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}