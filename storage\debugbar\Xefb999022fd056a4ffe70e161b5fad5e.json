{"__meta": {"id": "Xefb999022fd056a4ffe70e161b5fad5e", "datetime": "2025-06-30 14:55:56", "utime": **********.775971, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.293715, "end": **********.775985, "duration": 0.****************, "duration_str": "482ms", "measures": [{"label": "Booting", "start": **********.293715, "relative_start": 0, "end": **********.706839, "relative_end": **********.706839, "duration": 0.*****************, "duration_str": "413ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.70685, "relative_start": 0.****************, "end": **********.775987, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "69.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00341, "accumulated_duration_str": "3.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.748419, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 57.771}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.760067, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 57.771, "width_percent": 19.062}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7676709, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 76.833, "width_percent": 23.167}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=2uy8by%7C**********058%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFKdGJGdDhqSHpycko1VW1rb0Q1TEE9PSIsInZhbHVlIjoiSkZNVjhTN3Bza3pFUUhKSCtkVitGYVlMbnRmK2lSOGM5YXhUamt4bEYvbngyZi9YYStsSk9oS2RHWnlIcDY5aHFYRFZPN1g1ektRWEhlRFhDUk1IYTQybndTR01GL2ExeXhxZElCMllRMXMvV0tBemxmbTh5TW5vTDBHZU5jMWxyQk4xOXJyOG1aWnE0YkI5TFo5Sk9jWHlGL002QXRwVmlZa0x5Mk9pb29weGdUUkJpU2pSMS83MDYzTUdTY0lDSldOSlM0K3NLOVU1a20vQ0pzaFV1bSs5dGRGaHF3Nk5HMk53NUZvaEFSYkFEcGtvL0JTcDdNQ0xaOS9RazRpdVV2Rm1OWC9sdkZDS3hsaCtNckwzWTBzNHFvZ1h6RlFmK1FBU2hEaGI3aC9FZEgyZ20vaXF4YjA4alR0cldYV1I1Z2JEWXFwaW8ybXpBRUw0V3RFMVFMTmZURzJwTzlmbFQ1VGMrbW01NEJpeDR3Z3p6aWF2M3BMM29rczZrY29iQXF4bzdvbkNBZHVnbWVHOEJGZ3B4U1JqODVSR3BYWGpqZ1BMbW8wVDdFdjlWV0VYNllVbG5HQURBRjEzUkVpeks4ZVZJL1FSV1czNTdZT1FHTVY5bFVaekFES004QnBoMFA2ZTFxQlIvRC8xOUZzcVk4T29PcDN6dkJjL29CU0wiLCJtYWMiOiI3OTgxODUzMjVjM2QzMTJlMTE4ODQxYTRhZjM0NWVkZDE4YTZlYjg1M2MyM2IyNzFkYjM2M2E5ZTZkMmVmYzkyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImdQL2hJc3lORTZZMkY3MFlrUk4rY2c9PSIsInZhbHVlIjoicTZjYjRWd1BlRE9Rd2w1aXU5bE0wQnpKc0s1cGZhRTN5WDd2bVRQeWJYNWI0K0s4SU1lUHlIVHFkclVWMUpHUE9hMnl5Q01XZjJ3ZUc2bUEzU01DYXRzK1RsbGM0Rk0ycEkvNjFCQThFZGN1L0o4V3pwczl5ajMzTjU5OFgxdUcxRUdsUkM0NUJ6QUVhRWhWN0cvNGRrVWdMRmp5US9tQ0pFRU5HdEk5QnZGK2xqNllWbGNSV2ZRZi8yUEFqT2lydUQzbHdlSml0Y1VtUzd6MUtDT0hzZS9KVk1MTjM1djBQL01qUGdWVWE3NGV1WDJxTnB4dmNlRGVneVpKWnRkVzdFMGdORmhoRURwdVc5SnhHMXZxYUtEc3R5M1VsbjRvTVAwK1g5azBRRVlJNWt2NGNrSUJ2K2RxN3JNelIxUm1SV2JIMGFCMXVrZzVYblIxK3BjcXdnd1J1dzVFcmlGS1FIeWZQOE1GNG9KMVNzVUJQaFUyeGk0RmxPTG05aHBaS0V5Y1hQd2hTMDRCY0RaRkd6R0JGWUpWbmVVaVRPLzZ2M01YdDJ1blN1L2NRK2U2aTBKMVVnSmRkcDVqWk9wb1FhMUhmLzdZTlNRV3dzbXZVYXZrbnVRaitVbkZldFlCd2VBQkFUU2hyZ2ZGMzkzRXJzWUd5aVdSdVVzck53elAiLCJtYWMiOiI3YmZmMTkwMzFmNDJlNDBmODIzMGNmYThkYWQ2YjFlYjYyNWU1MzE4ZGNhM2EwNTg4NjRlM2VjNWM0YzA3NmRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1721631088 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rf3imG1lPKr2xfPRA9rNVzJMke6dPWK1Wb6HvZ8N</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721631088\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1429480011 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:55:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlQyaHl6Y1p5L0xVVmlwNjVISlM3Y1E9PSIsInZhbHVlIjoiWXZhUTRpU0pNQ3VWUUcrNkdJSnZjcWt6UUo5ZWxMb1ZJV1hKUWZFMXF4aGNvSUZhSmNSWmZ6cW1mcTRORHpHaW41Tkg5SC9XeTAveEQzZHowdTBJNHRxbk5YWjlNYXlHWkR5N2hQS0tOa1gxd2RaNTYrWjFEQ3lyck5LcDhUeVVoQzZ5TS80aUVDS0dMRW03cFlwVm93TmYyazluVkcvODJxRXNPampqYzBGekRMeXE5dEprRlhwRkNSN05YbCtWNEoxZGs0M0FxcDF0K3BJOG1FZC9JN0F3M2toZE9mNmk4aWk4WU45OG5uUnc3anB3TXM5MXBMQndneXk0UkZYVnZDZ0tkT1ZNVGI3RlZISUNJUFpxRVRTaXhyMlhUK3VhaVNRK0ZvVHJOL2NPdGpjNVoyQUV2U3pUTDVvN2JUNHIxZm5zZlZMZUg2dTR2UlhvQ3FiYStKY2xVbU1zNmJiMkk0cW5RbzdZaUJBS3NNci9aYU1kZ3FGNFB4ZGZDblB0SDF4TWszYTRmOW1hcEJjcmtuNDNwSWhVanB1SHo0ZFdSYjFCbFpacFB1c25DQWtibGFHaGFsMFlCV1RhRXZrRCsrdXl0dVFoSzRmZXBFMTdpbmlUZDk5QXRJaEcxNUtwTnRtYnhnQ1JDZklTamVoMjArNHNJdzNTN21iUjAxaDkiLCJtYWMiOiJjMGNmOTMwMzBkNjgwMDc5NDIxYWVkYzE0NDFmOTI0ZWE0OTcxYmY2YmJmZTNlNjJjMzAzMTI5MDMwYzY3MjY0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:55:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVYbEV3d2thZ3o1ZEFIVWpaVEFjYkE9PSIsInZhbHVlIjoiTnk5cnh0SHhDbGZjMWtDZVhnWlJEZy8vOFBtL2hlSC9IRlc4TkNGa1Q0RndnM2RYb1E3V0NiV3NiNGw4UDZhNzMwR00rVVo3akJnd0dHc1BtTk5PMmNYY3BCSkFGbEFQa0dqbzRucjJVd1JZODEvV3pUOC9mRHJJdVgzRjF1c09XMm1pWEZaYlNtQlpoUklyaVFzNHlXNkd0b1daYTU1a2JPRmN1Q0Vsc1NBU3pPZFF2ay9Uem83MmQweTlqY0NXRVZEU0U2bEtzRmJoWGFQQnMzV1Y3dkw1T0RWaFdKY3dIdDBSYktNQ1AzY1MyeWpzbGt6SytMaE9vSDlrVkFCalEvaWFUdG8wUFpEOWVWVjVmd01GaWRkSUlyaWtXVVN6dVk0MHVOUUFxWmdTREZ6eUhDZDJ5Rm80S0lNZCs3TUNHU1NETlYwTlNSRDRDR0lYeHRaRUo5aUpuUzQzQjFIVkNlZkpjOTFCK0g4YzF5anpyOUYxYjJHbGFyMXg4SStNQnFzSG1FTHFOMkIwRHVtY2k1WFVTcE83czV5U2FCNG1hWjFROUx4a1BLUlJTcjNBSUwvK01LRjZ2VmlXek1FWStMWjNRQ3FPRk02RGtPRmNaVHo0eEJvcHRZYlY5SFRRUzdWd3E4bHJCendzbzRtNmF3Q21vaTZiODFGeHRMTmEiLCJtYWMiOiI1MTE4NTc1YTdiZmYyOWE1OWZiNmJhOWZhMTIxMTM3MTg3YjgwNTM4OWI4OWU4NGZmYzUxZGU4OGI5MDc5OWJkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:55:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlQyaHl6Y1p5L0xVVmlwNjVISlM3Y1E9PSIsInZhbHVlIjoiWXZhUTRpU0pNQ3VWUUcrNkdJSnZjcWt6UUo5ZWxMb1ZJV1hKUWZFMXF4aGNvSUZhSmNSWmZ6cW1mcTRORHpHaW41Tkg5SC9XeTAveEQzZHowdTBJNHRxbk5YWjlNYXlHWkR5N2hQS0tOa1gxd2RaNTYrWjFEQ3lyck5LcDhUeVVoQzZ5TS80aUVDS0dMRW03cFlwVm93TmYyazluVkcvODJxRXNPampqYzBGekRMeXE5dEprRlhwRkNSN05YbCtWNEoxZGs0M0FxcDF0K3BJOG1FZC9JN0F3M2toZE9mNmk4aWk4WU45OG5uUnc3anB3TXM5MXBMQndneXk0UkZYVnZDZ0tkT1ZNVGI3RlZISUNJUFpxRVRTaXhyMlhUK3VhaVNRK0ZvVHJOL2NPdGpjNVoyQUV2U3pUTDVvN2JUNHIxZm5zZlZMZUg2dTR2UlhvQ3FiYStKY2xVbU1zNmJiMkk0cW5RbzdZaUJBS3NNci9aYU1kZ3FGNFB4ZGZDblB0SDF4TWszYTRmOW1hcEJjcmtuNDNwSWhVanB1SHo0ZFdSYjFCbFpacFB1c25DQWtibGFHaGFsMFlCV1RhRXZrRCsrdXl0dVFoSzRmZXBFMTdpbmlUZDk5QXRJaEcxNUtwTnRtYnhnQ1JDZklTamVoMjArNHNJdzNTN21iUjAxaDkiLCJtYWMiOiJjMGNmOTMwMzBkNjgwMDc5NDIxYWVkYzE0NDFmOTI0ZWE0OTcxYmY2YmJmZTNlNjJjMzAzMTI5MDMwYzY3MjY0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:55:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVYbEV3d2thZ3o1ZEFIVWpaVEFjYkE9PSIsInZhbHVlIjoiTnk5cnh0SHhDbGZjMWtDZVhnWlJEZy8vOFBtL2hlSC9IRlc4TkNGa1Q0RndnM2RYb1E3V0NiV3NiNGw4UDZhNzMwR00rVVo3akJnd0dHc1BtTk5PMmNYY3BCSkFGbEFQa0dqbzRucjJVd1JZODEvV3pUOC9mRHJJdVgzRjF1c09XMm1pWEZaYlNtQlpoUklyaVFzNHlXNkd0b1daYTU1a2JPRmN1Q0Vsc1NBU3pPZFF2ay9Uem83MmQweTlqY0NXRVZEU0U2bEtzRmJoWGFQQnMzV1Y3dkw1T0RWaFdKY3dIdDBSYktNQ1AzY1MyeWpzbGt6SytMaE9vSDlrVkFCalEvaWFUdG8wUFpEOWVWVjVmd01GaWRkSUlyaWtXVVN6dVk0MHVOUUFxWmdTREZ6eUhDZDJ5Rm80S0lNZCs3TUNHU1NETlYwTlNSRDRDR0lYeHRaRUo5aUpuUzQzQjFIVkNlZkpjOTFCK0g4YzF5anpyOUYxYjJHbGFyMXg4SStNQnFzSG1FTHFOMkIwRHVtY2k1WFVTcE83czV5U2FCNG1hWjFROUx4a1BLUlJTcjNBSUwvK01LRjZ2VmlXek1FWStMWjNRQ3FPRk02RGtPRmNaVHo0eEJvcHRZYlY5SFRRUzdWd3E4bHJCendzbzRtNmF3Q21vaTZiODFGeHRMTmEiLCJtYWMiOiI1MTE4NTc1YTdiZmYyOWE1OWZiNmJhOWZhMTIxMTM3MTg3YjgwNTM4OWI4OWU4NGZmYzUxZGU4OGI5MDc5OWJkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:55:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429480011\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-72967383 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72967383\", {\"maxDepth\":0})</script>\n"}}