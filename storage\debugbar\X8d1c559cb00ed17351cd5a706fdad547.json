{"__meta": {"id": "X8d1c559cb00ed17351cd5a706fdad547", "datetime": "2025-06-08 00:40:59", "utime": **********.473771, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749343258.809139, "end": **********.473813, "duration": 0.6646740436553955, "duration_str": "665ms", "measures": [{"label": "Booting", "start": 1749343258.809139, "relative_start": 0, "end": **********.371554, "relative_end": **********.371554, "duration": 0.5624148845672607, "duration_str": "562ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.371569, "relative_start": 0.5624299049377441, "end": **********.473818, "relative_end": 5.0067901611328125e-06, "duration": 0.1022491455078125, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45037680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02003, "accumulated_duration_str": "20.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.415198, "duration": 0.01789, "duration_str": "17.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.316}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.449178, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.316, "width_percent": 4.194}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4587638, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.51, "width_percent": 6.49}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1556105832 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1556105832\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-934150052 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-934150052\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-574412097 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574412097\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1846825212 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749343246363%7C41%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpIeTNVR0R4cjJJeThYMm8yNkphUGc9PSIsInZhbHVlIjoiSDNxSnYzWlc4UjU3YjVWTms2OER5QUc1TUtYTEwyMUp5N3dVWUY5dHRCa1lBRW11RHVRME4yd3pGdWFFV3ZFNkdTNmdxNUJsek1rL0l4YVVoUlAxNE5jb2k1MndDaE9LV2dkUzFVOTNuK2xjRUc1YWdVVndoRWxOTmZ6WE12LzVVaHZCKzVFYnAwWkl3SHQvckRMSE4zRm1ESFY1bCtNMFRxOS9pYXFsMTJISTVnWWhkNWNiSmVNZTdqWXhUNmRUbVFMb09NTHIrNm9oSFIwbDh6UUtpNWRIRTBFUXFJcCtPMEo0MDc3RWN0SXRucUpGYkkrRjVQSWRYeE5HdnBoTWxhUllBeUROK0pyZ25NSzVhTzdaWHlrM0lmczRiMkovMFRqWnE0UjNqWElPdHd2d1RkbFg5OHdWQzVITWQ3TS9maWVvTGdpaXJpTUlzR2R0YjJrY2VjdkNhb2JGRGxlK0JFUUdIcUUyZ0FwckxrMC9QVk95TFVpcURaTFdibjdMRFVCaG1ZVWhCZTlvci9LT0hYM2xhekcyU3FrSEFjMERNV0h4a0dyTS9PcDNRbFNGQk16em1tQ3RWUGEweUo5MFdoZTdhK2syY21lZGFPSHUvc05GUWZGY3Rmd243ZExBU2VDM1NOekJyMlBHSnRjUHdBbnFBOVFzYWNOb1lhV3kiLCJtYWMiOiI0NzdhZThjOTMxNTgyMDUwZjk4YjgwNGVmYTM4NWRiYmNlYTI1OWRjNWMyNDllNDdlN2RjNmVjZDA1MjBhYmYzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkE3MDZPdnBleTdYOGxnbkNIS05DWUE9PSIsInZhbHVlIjoiM0dqZnVoNy90R2Z5TVVZWXc0UGRmT01UaFFpVVFWZ05rY1hPbThWWTFKd1M2cUdVeHhDd3hHelN6OHhCLzd3RjYwZmw5bmNyM2pyQ0lxb0tYa2xyZWlGMHNJTTdHaUNOMVQ0WlE1NTdPM04yeW1sSDc4QXVGUnhLWE1IU2JrWUtJcGZEaU4wUGtjUnh5ZXJVUkVwVWF4Z1hmQjhuc3ZjTGtKaElmekRDQUJYUk1LQ3J5Sy9USnY4Q2Y1QTBHM01SYTMwbUYreWR1L1RrNFlmUHV1YlFTbEZ3djhnNENmaWdDZTlRQnc2WmFvMm5oam1hU3p6M2lGc0xsRzY1SFpjQnRia2ZSN1pHMmt1TTNoWi9wVGlNRkE0T2JWRm0zeG5GODV2U3VqUjhWWThCSDRsc3N2OVhxd1AwYmNsYThvQ1VyQUptYXpLbzRQNHlrSFpGTzdqUlo2alRSeFhsdy80QUJveTBWS2VIMnhLSnhBejAycE55ajZmMjJialEyUnFoU2RMWVRvcm5iWHJvTVZkREpSclBQcUxSaEpUL2NmSGZxT2VDWEZrUzhLdVV5c3RvYVJka0NFOE1uamdjUUhPUHRhRWVzeGJDSG5HSFJ4VVEwUFNrS2kxaVorelRCZ3QxVUxESWdvL3J2N2VWekdxbEx3NHFVc0N2c3BkNEc2WmwiLCJtYWMiOiI3MDNmMzlhZWRmNjY1YmM5MjUyZmU4M2M3MmE2MmI0ODc0M2I1NjYyNmNhYzE4YjIzZWQ4ZmUzOTNiYjIyZDhiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1846825212\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2087966090 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2087966090\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:40:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imw0d1VjUHhJWXpFL2IwQ0lmYTVUTWc9PSIsInZhbHVlIjoiT2NnbTFldjAyU203dGNiaDJ6c3hJWVVUaXY4WHduV0N4OUFxWjF5RjZBVllzYi9uOFhReUh1dVhLVENJSEFSSWJUbXBtL05od3d4cDFXSXh5OWtNamZmUGFoMEhNSGpEQjNVMWpHdTl4d0JjMVFUdDIySnF5bW1MMU51MHJHcnYzdFdTOGRHZ3lKUmtjN0kvNnlPei93M1BqeldOVU9WKzNGbmhSUy9kb2llbVViYmFuUlNCTEowZnlTVWRlZEthdnNYK0pqUTNhQXBlUWdHUGppUFI1WXpKNWgxYTI0VldtcUY4S2pJR0ZyMFFZUk9yQUxpdUxkM0VUMmkvL2xoLzdJUWgzZnhCV3JiZW5waG5mdWs3bUJoSXQzQ3Y2UmJsNWMxYXo0TUpqVWNzdHVRd0daOHM5dkpGcFRNaVVsMnN3ZFlHejJPTWtnNXZTR2RlNEx0bU4wMXFqTldmZjlXVkMyOTdmOFJmUkNpS1FwRXJmb0ZEU2VFNjZ0V2ZBcERyQ1gyYisxUE5YVzdxR3BTRExNUnRDeWtmREtDZXY2ajZxWnp3Z3B3Z3BDaGFUekxhcjcrZlNhYlFWRmYxbk43VnVUcjZ4OHhBYyszby9zYzh3UEU5TDd5MldaRWh3bmJjRStjVGV1dXZSMGtZQ0RlWmxLQWJpSGxkczVwa3lrMFoiLCJtYWMiOiJiNjE3ODhiYjFhMjAyOTJmOTgyNjJjNmNhZWVhZmVjZTJlY2E5MjkyYjg1MmMzMTYwNzgzMjRmNzUzNTYzN2MwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im5wWEVDN2VZZUxEN3YwVXVGQTR5SGc9PSIsInZhbHVlIjoiYk9YZWxxcHgyRk1YY04zMGwrcm5QT0Vva0ZWbmRESkZuR0hhV3o4TTN5QzIyUXlVYVlSQmFyaitiUFlUby9XT3MrdW13Y3RDOE13cXdROGVmVFE1bnFhczQyNnRoZEU3ZFBmc1lZRHVJQkNyeEJINm9UNnBjZXVnTG9lYjFZMWJzK3BEOTQzaU02MzZsNHpJOVA4YUd0K08wTlZzY3BQNmhyaVprb3ZHUlJtOFBHSldpeSs1SmlOYTd1Q0dRenFOZEJhNWNGaGFGNnUxaFh3UmNtZTJxZHl1OTFZNnhYNGFxekVDZENqV2pkYXN3ZXdMdklBKzFTR0gxVnR3cDRXckphem1FVFU4YkYxMnBSU1F0Z0J6b0p6MGNMVHFBdkhkY0RxeDJucnNtZHNEWlVSZW9NNmpKQUQyYnlyYkpWQytQQjJTclEzRHMzb2Z3NFlVQmlYM3o3Zzd1UVVYY1ZLYlZDdEJTUjUrVXpkNXR6RTMrbXdVcllSR2t3MGcyZ0Z3SWJ4ZnVsc0RmV1UyYlJrMUhyVXV6Tkp1eEhUUGdDRm1YOEJsL3FBRXlsSUc3YWt5QkpRZFFSUmRKRzhBNC90RzRxRTNxcWpXOGJBTktPc3ZzeUtkenVnVGY0c21nZTVmS1lWSEJZWklJcnZEaGJzbWhaTm5WMkJjS0szaWMvUWMiLCJtYWMiOiJhNTUyNzk0YmMzNWVhMzA3YTczNWJlYWRkNzQyYWYyMzQ3MThiZWQ1MmEzNjFlYzgzODUyNjUxNjI3YzYxY2U4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imw0d1VjUHhJWXpFL2IwQ0lmYTVUTWc9PSIsInZhbHVlIjoiT2NnbTFldjAyU203dGNiaDJ6c3hJWVVUaXY4WHduV0N4OUFxWjF5RjZBVllzYi9uOFhReUh1dVhLVENJSEFSSWJUbXBtL05od3d4cDFXSXh5OWtNamZmUGFoMEhNSGpEQjNVMWpHdTl4d0JjMVFUdDIySnF5bW1MMU51MHJHcnYzdFdTOGRHZ3lKUmtjN0kvNnlPei93M1BqeldOVU9WKzNGbmhSUy9kb2llbVViYmFuUlNCTEowZnlTVWRlZEthdnNYK0pqUTNhQXBlUWdHUGppUFI1WXpKNWgxYTI0VldtcUY4S2pJR0ZyMFFZUk9yQUxpdUxkM0VUMmkvL2xoLzdJUWgzZnhCV3JiZW5waG5mdWs3bUJoSXQzQ3Y2UmJsNWMxYXo0TUpqVWNzdHVRd0daOHM5dkpGcFRNaVVsMnN3ZFlHejJPTWtnNXZTR2RlNEx0bU4wMXFqTldmZjlXVkMyOTdmOFJmUkNpS1FwRXJmb0ZEU2VFNjZ0V2ZBcERyQ1gyYisxUE5YVzdxR3BTRExNUnRDeWtmREtDZXY2ajZxWnp3Z3B3Z3BDaGFUekxhcjcrZlNhYlFWRmYxbk43VnVUcjZ4OHhBYyszby9zYzh3UEU5TDd5MldaRWh3bmJjRStjVGV1dXZSMGtZQ0RlWmxLQWJpSGxkczVwa3lrMFoiLCJtYWMiOiJiNjE3ODhiYjFhMjAyOTJmOTgyNjJjNmNhZWVhZmVjZTJlY2E5MjkyYjg1MmMzMTYwNzgzMjRmNzUzNTYzN2MwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im5wWEVDN2VZZUxEN3YwVXVGQTR5SGc9PSIsInZhbHVlIjoiYk9YZWxxcHgyRk1YY04zMGwrcm5QT0Vva0ZWbmRESkZuR0hhV3o4TTN5QzIyUXlVYVlSQmFyaitiUFlUby9XT3MrdW13Y3RDOE13cXdROGVmVFE1bnFhczQyNnRoZEU3ZFBmc1lZRHVJQkNyeEJINm9UNnBjZXVnTG9lYjFZMWJzK3BEOTQzaU02MzZsNHpJOVA4YUd0K08wTlZzY3BQNmhyaVprb3ZHUlJtOFBHSldpeSs1SmlOYTd1Q0dRenFOZEJhNWNGaGFGNnUxaFh3UmNtZTJxZHl1OTFZNnhYNGFxekVDZENqV2pkYXN3ZXdMdklBKzFTR0gxVnR3cDRXckphem1FVFU4YkYxMnBSU1F0Z0J6b0p6MGNMVHFBdkhkY0RxeDJucnNtZHNEWlVSZW9NNmpKQUQyYnlyYkpWQytQQjJTclEzRHMzb2Z3NFlVQmlYM3o3Zzd1UVVYY1ZLYlZDdEJTUjUrVXpkNXR6RTMrbXdVcllSR2t3MGcyZ0Z3SWJ4ZnVsc0RmV1UyYlJrMUhyVXV6Tkp1eEhUUGdDRm1YOEJsL3FBRXlsSUc3YWt5QkpRZFFSUmRKRzhBNC90RzRxRTNxcWpXOGJBTktPc3ZzeUtkenVnVGY0c21nZTVmS1lWSEJZWklJcnZEaGJzbWhaTm5WMkJjS0szaWMvUWMiLCJtYWMiOiJhNTUyNzk0YmMzNWVhMzA3YTczNWJlYWRkNzQyYWYyMzQ3MThiZWQ1MmEzNjFlYzgzODUyNjUxNjI3YzYxY2U4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}