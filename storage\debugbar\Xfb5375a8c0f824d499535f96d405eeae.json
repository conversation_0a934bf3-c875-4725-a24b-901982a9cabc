{"__meta": {"id": "Xfb5375a8c0f824d499535f96d405eeae", "datetime": "2025-06-08 00:40:39", "utime": **********.83309, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.145653, "end": **********.833113, "duration": 0.6874599456787109, "duration_str": "687ms", "measures": [{"label": "Booting", "start": **********.145653, "relative_start": 0, "end": **********.752027, "relative_end": **********.752027, "duration": 0.6063740253448486, "duration_str": "606ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.752042, "relative_start": 0.606389045715332, "end": **********.833116, "relative_end": 3.0994415283203125e-06, "duration": 0.08107399940490723, "duration_str": "81.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45052640, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00507, "accumulated_duration_str": "5.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7907548, "duration": 0.0030499999999999998, "duration_str": "3.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 60.158}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.809026, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 60.158, "width_percent": 16.963}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.820166, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 77.12, "width_percent": 22.88}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-4541365 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-4541365\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1458453517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1458453517\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-899669324 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-899669324\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-756115690 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749343217349%7C39%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFWK2pZMFNKZ1dRY2NNZE1uOW5mQlE9PSIsInZhbHVlIjoiemlERkxNaTJpekk4cUNZdFF6ZzQ4RTkyUVBqK2FPdm8zak1oMDlTWjd4L05oZk9XbTNzN1ZvcXpRRmNxYTZYSytGd3lJcGd6SUM5SlJkdGpOdEd0N0htSFZFTFZGNFZJNDlhQS9xZDc1Y1FJV3JOd05ydUFGSXl5dnhOMU9TM1ZiQ2N6MVQ3WWZ6ZVkwdEdxY0cxVGpabWJKSTQ2WHd5MTlJcVVUNmpRbFhDRjc5ZGVsUlZoL0RJSTJ2dVdDU0QySEFkRWZuM3d0amVlaEwyWmRzTDFOL0xTZkxVUng2ZWZmNkNCUUZNajcrQktYQW01K3BKOHhyVm9icE1WbmpKS3Y3WjJ6YzFDZTFndnlYOE00eGxxVWNrVG9GVzdFMVRXekx2TXZ5WHNKcnJZYk1TWnA3dkpYL3RUd1hoTzFEYUg2R25JM29PbE00NmZJYWxPR0ZXdzJON2FDWXU4QXlaczFWRW1EcVhXaTZmWjlZYk5sMUIvQlhZR1lGbU9qNlRrL2twVXBGMzRjSVh4eFJ6MGJpQ1c4bWh2dVd6bG94eEs2enlNWGZGTG1TZHpvRGVJNEEwc1FBQWJNNHlHNlc3VGQxbjEyUkxHTmRvVzZvd1JyV1pmb3dQOHdkaE9XSS9PL0pqeFhLWklOY2NXK2xQdjQ5eTlxanFDeEZ1dFlzL2siLCJtYWMiOiJmMjQyOGM4YTVhNzllMDNmMGUyZDM1MTMyZmNlMWEwYjUyZWRlZjU4Y2RiMGMzODJmMGQ1NzIyMDI5MTg3ZTJkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlJrSDJhaU5CalR4RVFPRU9WSHpkVkE9PSIsInZhbHVlIjoiQmFlOVNmd1dOeGNRZUZHNlhZaHpRRmM4V3NRakJ5QlNMbHM5ZHdlTVNsd2ZmZW9vMit2RDF6NVNWeU5xN3VVTG4yZDg4YnFoUTFjZEJGRWNHNkxTalJMaUFLSXpSMDZtbEorbTgyZWdHaXZLKzFaL3pXNmR4bW15Z1RCdE9GWUtqV01hNVpHUkx6WFlaT3pyMi9tWC9JcmJ3dG9BeUM3eDdlWHhJMXhLUXlOQ1d3OTIzWWFRL1BpSzNZVlVMeFIzdEhyeml2Nlo3UmYrTC9MUWlhRmc1VkNBRHEvYTBlNGtDRW1YeWdvYjVYOUVDVFAyYm01UUhCOHBsbVlDN1FJQkhwRFZ6V3NZNzcySVQ1cUpWckJqZWVmb3lyYWNQOEZmUnc4UDRSdllVck5rZERSK1hEOWEyTjBYWGN0TkNHam91aHdZMXZZS2R1SmVoMldrdS8vY2swNnFxcVM5SVNuQ1NSSk5WSXRsRGlxTUdIcHd0ajVnbXl4dmRsOU1jeXpkYjN5aUxKK0JKYW02amtINDErNzFhOVlEYTIvdnZhYmNwUVU2NEVmUkNKYXRzOXVlbkR1djdvMWovTVhpbGV2OVVEM3dSdVl6TWNERzlDeVdXOXU0OVNqdVhiSjJFSVBGRlVWQ0JjTXBURjBBdVVQL2tscE50eEduKzB1OUErTG0iLCJtYWMiOiI0ODJjMGQ0MzY2M2E1MjdmM2I3NzMwMWUxYTAyZjk4MjgwNGJiZWMzMWMwYWMyYjhiOTIzYTQyMDFiNDg4ZWNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-756115690\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-659872087 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659872087\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2116357886 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:40:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdmQ2NOQVRJYUp3UmNTSDEzck5KNlE9PSIsInZhbHVlIjoiY2FOUDE0VnpnVDgvREhubEJLcFpwWWVIUkNhdU44SEZmYktSYStVOFFEYnVONHZxMlVQVlRNL2NMQnlpNzlEREFKVVkzUUI3WTlzd2VHR0xmMXd2ZFdPZWpqbGt6TFJQaElzNWZKSWJQL0NQbVlnVmNTWFZLa3B2T3V4dlY0aTRLRTU0MitGajRMdlZYcmZvOUNBcmdEUDdvYkFCVnFsR2Rsc2dWT3NOQ3NyblpOQlZnNHRQTVhxak80YUJnYnVwSjAxakhLVFNZOW1vYzQzdzhQNFJ2ZjJ4VE5OS3gwZFlZcTlaaHYyZGtKcXc1Z2FQL29jN0NnTEZNdEVpUVNQaVVhN2l2UGRkZU5WQWRJOFIyWW4zK0RPY2FCeW1lQUdMaEh6blQ2eGRIVXhvQ1Y5MXZXanRkQXBMcUczT2FtSFRSWkM3bThxTVpmc3FlNTlGZmoyNURlT1h4SW5WWUpuOE5MZElOSTl6Q3hHR3hVdzN0aVg0SXJYRG5IdUpsY0JwczZHRnlQa1RvMDA3SUtQNGNDbmI2WjRDZmxhQVdMSWlKV0FBaG5vUmJoUStpdlBHMGJpWFI2d0pCMjBmUU9icEJEVmNyNjNVemNWYnNkdlA0eGFpbklaZHR3ZjhyUlJuaUQ4NnZrZGh2bUtVYWJSbmZVS2RWZXRFUFcyS3E1bVEiLCJtYWMiOiI1MTZjNDZlMDZhMjA0MDRmMjYwZDNmZDZmN2M5ZDNmMWQ0YTI4YjU4NWYyNmFiNmM2ZDJjOGU3N2IxYTZmZDI3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlYrRmhqU2J5U2JNNWxuekhTUHdFaFE9PSIsInZhbHVlIjoicVRVbXNSM2czUHRQRmhqaGF2cUlIaHZkZmt3YThCMWpTdHVDUnI0Vng1RUZHc3IzV0d1bTExOWdzekJrWjVYc21TVE9JT09sUU1tMmdvUi9NVm1UK3kzcVhmMUF6RGhlbXRVWEY5VDhyb2ptNytVMEJhYkg0cHVsVmxRN0RqRkdmbFpDb0pWYVovTGo1OVZnZU9YaDdrcDRiYk9wY1krMnl6Y0RPVlNrZ2dodVM0ZXpoRTN2K3lNazBaZ09pdFNrTStseWF6UVIvc0cwL1hGNU1PUm9EQzBDNjRYT0JiN2U1SStvTkJ5WVNjOW5DaHZFMHhLalRWcEdHK0RndmpsMFJpa05DMEVWK0c3aWx2UTNyRklvaTlwYjVPSWorOFRkZEFHSS9UUWRJak1hRVR6N1lMNWlJYW53YzhpMlJodFNTRjJrYis3MWhzeWdRRTZkd1Nud1NwbUtIMWtSdjU4bmxEdEFkQUJYZnpsVmIwcklpODRITWRXVmN6RDNzdzlITDQyQ0JvVmlvVzdIelZmcTVTZW5rT3dzajg3bXAyVlJzeFovNWJNNWFZM3JTZXlDM1c4bk5iaTV0bHZ5c2hUNEhGUU9OWjFtRVFFdUZRZi9qSVc4N1RuNGg2WllSV0tBUWQ4Wkp0eUhCMkgraTZxWU94SG9vOUtxZXEzLzdqbk0iLCJtYWMiOiJkNjVhNmI1ZTExZDNiYjNkNmIwOTEzYjk5Y2RmYTZhYjFiYWVkMzkzMmVmMWZhN2NiYjJiNDVkYWY5OWUxNzU1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdmQ2NOQVRJYUp3UmNTSDEzck5KNlE9PSIsInZhbHVlIjoiY2FOUDE0VnpnVDgvREhubEJLcFpwWWVIUkNhdU44SEZmYktSYStVOFFEYnVONHZxMlVQVlRNL2NMQnlpNzlEREFKVVkzUUI3WTlzd2VHR0xmMXd2ZFdPZWpqbGt6TFJQaElzNWZKSWJQL0NQbVlnVmNTWFZLa3B2T3V4dlY0aTRLRTU0MitGajRMdlZYcmZvOUNBcmdEUDdvYkFCVnFsR2Rsc2dWT3NOQ3NyblpOQlZnNHRQTVhxak80YUJnYnVwSjAxakhLVFNZOW1vYzQzdzhQNFJ2ZjJ4VE5OS3gwZFlZcTlaaHYyZGtKcXc1Z2FQL29jN0NnTEZNdEVpUVNQaVVhN2l2UGRkZU5WQWRJOFIyWW4zK0RPY2FCeW1lQUdMaEh6blQ2eGRIVXhvQ1Y5MXZXanRkQXBMcUczT2FtSFRSWkM3bThxTVpmc3FlNTlGZmoyNURlT1h4SW5WWUpuOE5MZElOSTl6Q3hHR3hVdzN0aVg0SXJYRG5IdUpsY0JwczZHRnlQa1RvMDA3SUtQNGNDbmI2WjRDZmxhQVdMSWlKV0FBaG5vUmJoUStpdlBHMGJpWFI2d0pCMjBmUU9icEJEVmNyNjNVemNWYnNkdlA0eGFpbklaZHR3ZjhyUlJuaUQ4NnZrZGh2bUtVYWJSbmZVS2RWZXRFUFcyS3E1bVEiLCJtYWMiOiI1MTZjNDZlMDZhMjA0MDRmMjYwZDNmZDZmN2M5ZDNmMWQ0YTI4YjU4NWYyNmFiNmM2ZDJjOGU3N2IxYTZmZDI3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlYrRmhqU2J5U2JNNWxuekhTUHdFaFE9PSIsInZhbHVlIjoicVRVbXNSM2czUHRQRmhqaGF2cUlIaHZkZmt3YThCMWpTdHVDUnI0Vng1RUZHc3IzV0d1bTExOWdzekJrWjVYc21TVE9JT09sUU1tMmdvUi9NVm1UK3kzcVhmMUF6RGhlbXRVWEY5VDhyb2ptNytVMEJhYkg0cHVsVmxRN0RqRkdmbFpDb0pWYVovTGo1OVZnZU9YaDdrcDRiYk9wY1krMnl6Y0RPVlNrZ2dodVM0ZXpoRTN2K3lNazBaZ09pdFNrTStseWF6UVIvc0cwL1hGNU1PUm9EQzBDNjRYT0JiN2U1SStvTkJ5WVNjOW5DaHZFMHhLalRWcEdHK0RndmpsMFJpa05DMEVWK0c3aWx2UTNyRklvaTlwYjVPSWorOFRkZEFHSS9UUWRJak1hRVR6N1lMNWlJYW53YzhpMlJodFNTRjJrYis3MWhzeWdRRTZkd1Nud1NwbUtIMWtSdjU4bmxEdEFkQUJYZnpsVmIwcklpODRITWRXVmN6RDNzdzlITDQyQ0JvVmlvVzdIelZmcTVTZW5rT3dzajg3bXAyVlJzeFovNWJNNWFZM3JTZXlDM1c4bk5iaTV0bHZ5c2hUNEhGUU9OWjFtRVFFdUZRZi9qSVc4N1RuNGg2WllSV0tBUWQ4Wkp0eUhCMkgraTZxWU94SG9vOUtxZXEzLzdqbk0iLCJtYWMiOiJkNjVhNmI1ZTExZDNiYjNkNmIwOTEzYjk5Y2RmYTZhYjFiYWVkMzkzMmVmMWZhN2NiYjJiNDVkYWY5OWUxNzU1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2116357886\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1428233532 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1428233532\", {\"maxDepth\":0})</script>\n"}}