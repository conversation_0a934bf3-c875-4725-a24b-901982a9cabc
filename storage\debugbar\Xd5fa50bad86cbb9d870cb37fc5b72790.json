{"__meta": {"id": "Xd5fa50bad86cbb9d870cb37fc5b72790", "datetime": "2025-06-08 00:04:01", "utime": 1749341041.017218, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.336079, "end": 1749341041.017236, "duration": 0.681157112121582, "duration_str": "681ms", "measures": [{"label": "Booting", "start": **********.336079, "relative_start": 0, "end": **********.915724, "relative_end": **********.915724, "duration": 0.5796451568603516, "duration_str": "580ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.915739, "relative_start": 0.579660177230835, "end": 1749341041.017239, "relative_end": 3.0994415283203125e-06, "duration": 0.10150003433227539, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45054800, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02672, "accumulated_duration_str": "26.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9546142, "duration": 0.02434, "duration_str": "24.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.093}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.993032, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.093, "width_percent": 4.416}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749341041.004905, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.509, "width_percent": 4.491}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2032225645 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2032225645\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-899741070 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-899741070\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1413433101 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1413433101\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1461363437 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749341026995%7C25%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpNMkE5TGlWMk9Obm5iTzBFL2tkeFE9PSIsInZhbHVlIjoieUdVNEp0UGRDMldaSnJrSkRwWHk3QTV2bHdUK3dJbFU5L0xYZFRaUzBYcC9Dc2I0UjFRS0d6Q1FDYitNeEhmKzZYbVJ1V2YycGxYNkxaRlNzUnlvM3YxN081QXFuSUxnRmxpRFI4Q0pjZUhhRmZlWFlOTXBrWjd3L05ubWRzN2xKVEhMMW5uRTlGMnh1UU9uZTc5VkIzNWlqMlZVZGt1cGVqeFRCTnk0YnhDVVZGdTFmdHArNlViMFVHdXV4V21hbWl4Z05TTUJBcURrQjhMYUVmN1FFRmVaTkt6VWdOeFpxK25ybVlReDlEdFh2Y1pVLzJkbEkzUlVBSEJRT3l1eHhrazFzeXVlZHZocll3WnZvYTVkOUZtTUl2TmRuTEJEcklRNVB5ZHJCY2hYL3MvbHB6L0tlVi9zaXllOXRxQ2NzUlJkWk5mMEx5V0pBWjJqZW5OY1NkSlZQdW9NbkhtcU5yYjJFTnJFRzRXTVRHTDFHRzl3aEkyVXhBOTNHNzlGN3hLeDd1dkdTWklqbUNZTVNLaE1HN2tVVHFxMHlYOGphSXRWbm1ocndCT1JxeVk0REF6bTZYRGh5alVZa1RsbnNLQllhRVltSGw0UVdzRXFoeTJMRk0wK3diRE1TK1Rlc05hYVdPdEJ5VGpVTVo5VitUbGJPLzJBWmdkNFkyWEsiLCJtYWMiOiJhZDI2YWIxMWIxMzM4MTVjN2M3YTFiNjI2NjJiYWIyNjA0ZTQ1NjMzYWE1MTc3ZGY3OWEyM2MwYTcwNWMwMThlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlNhV29VQjJrcFlYb2lSU2RGWG9VQ3c9PSIsInZhbHVlIjoiS1VXY3FaV1o2akhqUVBvYStON2RpVjZvemhZNDkvdytVU2w4UVVudFlrcG9rcWQvRmYxVXhlLzBIMzB4cFNQUTFqSm8zSGI2MG9Zb2lPbW0wU2dZMlEzdERSZEpBOThWd1JyZjhiTXZFQ3JzbUJ4RHNzZXN4RU56VHlGaWgwUFFmWEkxdGpyOGF5eWlySEs0enJjWEo3VWJuQ2Vid1dTbVNUZC83d2FTaG4vT1dTTk5sWDR2KzZJQkRURk5zQXJHZEhNdjlVSEdsTUd5WStuUnVrZnFGaFZTaldZWnAweVc0WUFWMytlajBMTzFkVHkybnBvSDNpVkdPa0d2cW4zZTJOQWE4bnNCM2Z5UlE4VDdmVHd0YU8rK2FwWmhxalRoMHFGZ09jT3dJeXdEUTcwYVRBK1A5THdKcm5WSmJIUzB2SlpERVVSZkVOWGQ1bUJvS01yRFhQVXNQa2VlZ2NicUtvb0tFd0pIYnpWRWJuZ3lrbUxveHpIZ1dFOVJBMWprd2JIdkdHbGZpc1BQR1lUcEJwSzJRbFFmNVFZeTJjNExYT29qSXQyTzF0eWNlUW40V3I0TFBSKzlJSVR6aklvTzFZQkhZcXdZS2hyUm1wNWxmWUlBai8vdU95SHN3OWxGZkp2dmN5clRaUk4wZndXVUN2VzIxKzBINno1dG1sMVUiLCJtYWMiOiJkODJjNDBmNDk2NjEyYjExOTFjZGJjZGFiNzEyMmU3ZjFkMmVjYjhlYTg1OTBkOGQzNDVhODcwNThlYTE5MGU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1461363437\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1962872926 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1962872926\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-132552908 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:04:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkM0YWpzVXZ6RXpRM3VkZ1hOcWNwNVE9PSIsInZhbHVlIjoib3BjZmVYUnltclRYNXNkdDNWSEEwMFRBbktCRTJXRlpXYUMvZk1MdndZZkQ0c2M4NjZjRzBsQzZNK2VOQWZiVFphalczcXVVRHlua2VzeXJhWUZMREE1aExHNVVBTExLcVFRa0N3MjlacVR0V2hhaUlOSlJtc2pkeGt6RjNMQkd2bmFlTnIzcHlUMjJzd2YrNWhjN1Z4cnp3dHhleTJQT1VBQTZmTlRLMkdlU0xDdm9iZWw0YnFJZVllb25HR2tOTmFLQ0FBdjB3emgzZ0dheUw2b1VVT1hyQUhsSXpWOXJCQndSTGdiVHc5cUNwZ2NnL25wWFdiVjNveGN3amJHNlQ1VXNEaDBNMkRMbEdKWnJoRVZNc0VaYTVQV2NmK3pnR2c0OEpwNG8zRk1MMTE1QXYyTmpsdFRLeis1NDhseHBDZjd2OEhxNmd3Mjd2cXdSK0htUllaV05kOWE0VWJicXJWZFFZL0FMQTRzeXhSNFJYc0xQbWRGcWpCdGpnekV2VTQrMFVNQVIrRWpTZTByQWhHWkRvUC9NQlJSN3U2ZXVLeXcwMmdtNzcxTDJIYWtCRlhoV0tVSWo0SG93MWZFUW8wUU9XTFgrNmFLSjM5V1FLSENEeHdiTTBueGJLUHFYRFdFNTFJdnR1Mk9PeXo4cVhzT1ZQakJXK0FwbFF3QVoiLCJtYWMiOiI4ODE5NDg5YmFlYTFlMjZmNmEyMmI0YTAxOWI3ZTdlZjM0MmQ2NDVjYWEzOTJjYjRiMzRhNjEyMWYyZmZmNDQ5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:04:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InhTOWZqbGdpYjVtNzI5M012R2g4MVE9PSIsInZhbHVlIjoiVW5RakU1MnV4cVFEdVdzcTh1V1JqU1VEVk4xYmlRbE9QWVVnMzFTMi9Mc3U4dUo0L1NQT1ZtUkhEWTV0RHFRWEg3aWlKeFhXWm9uVzdvdXdEbm5IN3Zhdk5hYlIyMktnYncrdVEyVThRbGVmU0xSS1g4bUhaeW8yeTU4SW8rbjRvSkpZZXlQeXJmLzJ5ZUJuY2NwM3diNXJDZUF0bUNuci8zOUw3NWVYNTNCVytzZ2hlNGYrS2hxVjV5dlhDb2JDNlhDMkNsY29kV0VJU1VTZ0VIc2ozQno4MTRWa1VFUU9hVlFhYTczS1NJTHhNVHZuSTM0UDV5NEp0Yko4QkNJRnBDWHJRNTVIVEZtRmFQRFNVWHpncEpOcEorbFdCd3hUOHY3QWJtSi9uSHRYZ1VlTEVkZEJpZ1o3Uy8vMFpCSHYzU3lNMTRpRlpuTUNaNi9xdk1nZmlBMm8weXhBTHhBZlZWeGJFSTY4SDdOcU0ya1R1Q3JINEJKTHE5bDI0TWZSeTlqejBQdENXRm54VVBvMzZmampMVzhVN2VmL1d6YWFHdzNRM1FBYk8rVTlPcURoeFZGak55emVaTlpkM3VlUXUzZ2FpcWZLT21CTDdTVmtQY3ZLNVM4WTZKQmYra3JNS2VNRC9zcy9HWFBxWTZ1ODB4ZjVUQXJhWTlPNnVOZGsiLCJtYWMiOiJkZDVlYWY3YWI0ZTM2YTRmNzgwMWRlNGIwZjVhYjU5ZGIyYmIxYzNlYWZkZjIxY2RjZDhiMGEwYmU0ZmIxZThkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:04:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkM0YWpzVXZ6RXpRM3VkZ1hOcWNwNVE9PSIsInZhbHVlIjoib3BjZmVYUnltclRYNXNkdDNWSEEwMFRBbktCRTJXRlpXYUMvZk1MdndZZkQ0c2M4NjZjRzBsQzZNK2VOQWZiVFphalczcXVVRHlua2VzeXJhWUZMREE1aExHNVVBTExLcVFRa0N3MjlacVR0V2hhaUlOSlJtc2pkeGt6RjNMQkd2bmFlTnIzcHlUMjJzd2YrNWhjN1Z4cnp3dHhleTJQT1VBQTZmTlRLMkdlU0xDdm9iZWw0YnFJZVllb25HR2tOTmFLQ0FBdjB3emgzZ0dheUw2b1VVT1hyQUhsSXpWOXJCQndSTGdiVHc5cUNwZ2NnL25wWFdiVjNveGN3amJHNlQ1VXNEaDBNMkRMbEdKWnJoRVZNc0VaYTVQV2NmK3pnR2c0OEpwNG8zRk1MMTE1QXYyTmpsdFRLeis1NDhseHBDZjd2OEhxNmd3Mjd2cXdSK0htUllaV05kOWE0VWJicXJWZFFZL0FMQTRzeXhSNFJYc0xQbWRGcWpCdGpnekV2VTQrMFVNQVIrRWpTZTByQWhHWkRvUC9NQlJSN3U2ZXVLeXcwMmdtNzcxTDJIYWtCRlhoV0tVSWo0SG93MWZFUW8wUU9XTFgrNmFLSjM5V1FLSENEeHdiTTBueGJLUHFYRFdFNTFJdnR1Mk9PeXo4cVhzT1ZQakJXK0FwbFF3QVoiLCJtYWMiOiI4ODE5NDg5YmFlYTFlMjZmNmEyMmI0YTAxOWI3ZTdlZjM0MmQ2NDVjYWEzOTJjYjRiMzRhNjEyMWYyZmZmNDQ5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:04:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InhTOWZqbGdpYjVtNzI5M012R2g4MVE9PSIsInZhbHVlIjoiVW5RakU1MnV4cVFEdVdzcTh1V1JqU1VEVk4xYmlRbE9QWVVnMzFTMi9Mc3U4dUo0L1NQT1ZtUkhEWTV0RHFRWEg3aWlKeFhXWm9uVzdvdXdEbm5IN3Zhdk5hYlIyMktnYncrdVEyVThRbGVmU0xSS1g4bUhaeW8yeTU4SW8rbjRvSkpZZXlQeXJmLzJ5ZUJuY2NwM3diNXJDZUF0bUNuci8zOUw3NWVYNTNCVytzZ2hlNGYrS2hxVjV5dlhDb2JDNlhDMkNsY29kV0VJU1VTZ0VIc2ozQno4MTRWa1VFUU9hVlFhYTczS1NJTHhNVHZuSTM0UDV5NEp0Yko4QkNJRnBDWHJRNTVIVEZtRmFQRFNVWHpncEpOcEorbFdCd3hUOHY3QWJtSi9uSHRYZ1VlTEVkZEJpZ1o3Uy8vMFpCSHYzU3lNMTRpRlpuTUNaNi9xdk1nZmlBMm8weXhBTHhBZlZWeGJFSTY4SDdOcU0ya1R1Q3JINEJKTHE5bDI0TWZSeTlqejBQdENXRm54VVBvMzZmampMVzhVN2VmL1d6YWFHdzNRM1FBYk8rVTlPcURoeFZGak55emVaTlpkM3VlUXUzZ2FpcWZLT21CTDdTVmtQY3ZLNVM4WTZKQmYra3JNS2VNRC9zcy9HWFBxWTZ1ODB4ZjVUQXJhWTlPNnVOZGsiLCJtYWMiOiJkZDVlYWY3YWI0ZTM2YTRmNzgwMWRlNGIwZjVhYjU5ZGIyYmIxYzNlYWZkZjIxY2RjZDhiMGEwYmU0ZmIxZThkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:04:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132552908\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-508612697 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508612697\", {\"maxDepth\":0})</script>\n"}}