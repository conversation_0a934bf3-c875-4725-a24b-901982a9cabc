{"__meta": {"id": "X7e275e1a47b44c85f09d4c91ec12817e", "datetime": "2025-06-30 15:21:24", "utime": **********.568423, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.134318, "end": **********.568437, "duration": 0.43411898612976074, "duration_str": "434ms", "measures": [{"label": "Booting", "start": **********.134318, "relative_start": 0, "end": **********.50972, "relative_end": **********.50972, "duration": 0.37540197372436523, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.509729, "relative_start": 0.375410795211792, "end": **********.56844, "relative_end": 2.86102294921875e-06, "duration": 0.05871105194091797, "duration_str": "58.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041192, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.009059999999999999, "accumulated_duration_str": "9.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.538639, "duration": 0.00784, "duration_str": "7.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 86.534}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5553641, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 86.534, "width_percent": 5.298}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.560967, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 91.832, "width_percent": 8.168}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-529104117 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-529104117\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1524480907 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1524480907\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-931788111 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931788111\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-618845950 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=2uy8by%7C1751296699440%7C5%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkUrN2xGcGlBU2czdGloZjlNYkNzbkE9PSIsInZhbHVlIjoiakxrS1IvRWdYNURnZnJEM05xNXRhT2Mwb0lJNzgvNU5TbjBCeFFyVDV1S2wvMlFId0gvdkNQK1ZhYzNFdjFCbUZuMlIxR1lpOW91eHRKeGduZjVkTTFWL0VYYWt3aFpMTUlnaFErQ1pocEFLWFN4K25LNDRVWFFwaXpaNDQrY3JiSGRNRDkvcWlOVUdiM3hvY0JBSFB0SmhURXdKZExIZHNtQjZSaXNLQTJickRiVU8xRlc2Vk5xL3dGRDJnYlVCL1JPZnZUaVR5VFVEUm5wRWhRTXk5aHVTRGVzVXc1eFdncFZVdUt5RmNnQVljZEdjMjAvZEhuTTVEaFNSNmhGRFhSUXJFeE44K1I0ZDJQTjFmOVNDb0lxeE1raWl6SHh6dFZIcDQ4Z1UzcHZpbFNZTHF5bEo4L3kxWGE0aTV5NUdmNHZJK0JrM2E0MnBnaENjS0s4R0FDcXZRYnBadHVjQkpZRTlNZTlha2VEYlBPaTRGdkw5WGJBaURxVlQ3MDIwTWZJb2ZIZHlvcDVvSDFxRjdGNVNJaExhYXRUT2w4eUp6VzlLcmpSaXIxVDA3WFlFQnhsbDNlaytRWU4wMG1QelAxa3lVUGJwTjU2NXY1SU9naktwMnVhSjBGR3FwSkdMMnFwUDlYaEJBMjFYVDJ6aU5HbDJMY3AxNXJkQWUxbCsiLCJtYWMiOiIxMGE4Njc2Y2RmZTM5NmI4MGNiNjE1YzUyMGRiOGRlZmQyNGY1MjVlMzk1NDdlYzlkZWFiMzE4YzAzZTBjMGI4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik82cVcyZStCeWt4dEttUW5qanZNQ3c9PSIsInZhbHVlIjoiYW9YN3Y5Q3NBNFVZRHJ5aHRMbzRSQmRtSW1qZWJ3Vld3cG0xMmpOTUtFUldnT1JZRXBjVnp3Q28zdElJMVVvWWVrMEh4YVlvNFo3b05jMDVXeFAyS2pRZ0I2NnJQbzlKcDlQMXpUUEIvQkloVWJtc3BHbmIrSmtJaXg0djZpUDJGR2lTeEl5b1lrR1BRbEoraDVGNnIxM3VCcDlWZ1ZGNWxPVTJLZzNqeHNFYXlIeWR3emZtbFczbXl5UGdTYllkUDlLWmNrMTE5RzhKdXJBY1lFTm9iYjhXWlVHQXo5QWZJSGRvcFhxYlNNMzBvdytGVnNCWVhIdGZkcEdPUksxaUM3elBMMHcrQWk3RUFuTUtxTFlEcDNTQnFyN1p0ZXFLcUNZUmhvaDA5S0R0bDI1ZngrZGRkU21WS1ZvZ1ZkRXc4VHBIazdoZXI4aEZsNm9vOTdrcWxFSHF5Yml5VkQ4NEw2M3gwbmk1ZWNCSmJlOUIydHpLUEd2U2c3blh2bmNYMmxoOUh2dXJuS1VSc2Y2MGJZR2ovZEs4amtNZTVpU0VKOWZHbzZ6UG1xQk1FTEJ0dlZVck0wRUc1WnFrZ3pXWURhWVg1dThSdEswR2dlRHhCWjRnbnF2d1R2QXdYM2VOSDNqMWJ0ZTBNN1MydGNBTlRQTnp2RUVwQWxpTE5EZDYiLCJtYWMiOiJhMjA2YWRmM2FjMjA1M2U4N2QxYjRhMmJmNDlhNGYzNTRjZGUyNmQyYzE1MDgzYTVkOTVkNzg1ZmM0ZTgyYzM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618845950\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1200641486 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h9WVO03qniSiJmg5fuu4tum1lr0j0c1k18JkoyAo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1200641486\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2083417682 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:21:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJ3VWMyNVFOQ1dTeVJ3MENQdG9pc0E9PSIsInZhbHVlIjoiKzI2Yld0Zms4TzluOWhKaU0zSUpxMzc1a3pBdGdCTGszRnZ0WXNZYTQyVEtqKzY4OFhZK1BJdTBJRVJGbmwyZzFUWFRRWkNJUjMzbnZCWSs4L0JCM3VldWRzVXFtTEpPU1RFZ2wxTHBhcmxlT3pEN1IrcHJ6QjNyd3ZhV1ltU0xhYktXZzEvMVpTQ1Boa01TL2FRWDhFWU5FMFV2MVlkc2JyeWp1eHVtRVA0YXhNWElVay80VXYzbEhmWFVpRjBqK25nem1VeWF6Sys5a1QxZktXbGJZWmFWNzdzQmFiOERqUXB3WFBldFVFc3hxMDljeGV1Q1VQOHpHZGw3VjQ1dEdRWWF3VHY3anpxMEl4bXFlSzlDSEd0RGRVUGMrTFJaSE9rV3BpQ0dLQnExSXU0ZG96V0tCVUVmVURCdXovRm5yZHhDYXloNC9CaHJKYm9TaTg3RHIwaEd1TWpqT2lSb0lITGtQNDhnYzhNQnR1d0NEQ1poMnlKOXhsZWg0RHpmRHB0NG1IUWw5dFdBUk8rZzd1a0o0eGpnTjdPQUlTdThtZWhMQThybEd3OG5PSnFMMnJ3RjV2WEUrKzFtdW5KVm9vMnJUZkVQaGhROEw1ZEUvREVFdDNielF4WE9LUGZpNVZYN0JGd1RaSXBNY2kvZXpYMVp3ajFKdnBrRFRQbU4iLCJtYWMiOiJkZjMzYjliYTUzMzZkNjkxNWFmZTdkY2NkZTE2MmQxOTZkMWJkMGZhYThkODI1NDM5MjI5NjFiMGQ5OWIyYzVhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:21:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNyT1ZlZ3ExU3lJV0NjOVBwTXNMeGc9PSIsInZhbHVlIjoiQy9TVW1hM1FaMWJrY1NRSUlLMXJFQkp1UWt2eTBCaDNLTTlhRjYrQlQ1S3hwWC9MWVhLcGZOYkhnSFNtKzU3ZUo1N2RzTU51NzVoNFFxSUw4ZlMybUVFQzNHWFQwZ1NrdEJObWdqYnRBbTljWEJxVFJSRkd4V2x0YWQySnZPVFp3QnorUHVobW1YTGhmRUlmNVNDb2lGNW9neDhOcm1SQTdXWU8rMHlMUG9wUUQrOGVqTlpOZnpybnRRNkZJWXFwNFRGNTRTRGdtNmFPT0RHeGh2VW5WSjlSZ0lWS013VFdYSjlMV1ptbTNuUzFhZytBT1dpRFVnaHVOLzkvQlZoazZPQlk5U01rVWJya1B3dXh4RW1MZ3A0OS9yOW0zMXJCUGJ2aVNRQ2Z0ZW11NHlqRmFkMUl4dFlkM3RCTmYreHFPSW54alEzT1RvMjdCR1dFZUNBRHFkVVgyVkc1dERaNE5VRVpWRUF1M2d5ZGFoTmk2a3VDaC9GdlYrOE80ZjNNSHpWb0lyM3pXNW9uTE1FTDNHNVVpa29MQysxVDM0RnFXYTVrZXZMVld5c3l4MzZCdUE0TUtqaUNDV2FEU1lkaXNCbDNVYWN4YXY1STYvZ2JFU3NyNUZVK2NkR2xzaEs5bzRqRG9YeXh1NUN2MjhseHJGM09jL3RhMEVZMng3TDMiLCJtYWMiOiJhODQ3YzkwYzM3OTYwNDBkMTNhMjM4NzFmYmE0YjFmOWMxZDI5MTBmMjA2MGEwODdjNGZlNDU3MGEyOWRlOGQ2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:21:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJ3VWMyNVFOQ1dTeVJ3MENQdG9pc0E9PSIsInZhbHVlIjoiKzI2Yld0Zms4TzluOWhKaU0zSUpxMzc1a3pBdGdCTGszRnZ0WXNZYTQyVEtqKzY4OFhZK1BJdTBJRVJGbmwyZzFUWFRRWkNJUjMzbnZCWSs4L0JCM3VldWRzVXFtTEpPU1RFZ2wxTHBhcmxlT3pEN1IrcHJ6QjNyd3ZhV1ltU0xhYktXZzEvMVpTQ1Boa01TL2FRWDhFWU5FMFV2MVlkc2JyeWp1eHVtRVA0YXhNWElVay80VXYzbEhmWFVpRjBqK25nem1VeWF6Sys5a1QxZktXbGJZWmFWNzdzQmFiOERqUXB3WFBldFVFc3hxMDljeGV1Q1VQOHpHZGw3VjQ1dEdRWWF3VHY3anpxMEl4bXFlSzlDSEd0RGRVUGMrTFJaSE9rV3BpQ0dLQnExSXU0ZG96V0tCVUVmVURCdXovRm5yZHhDYXloNC9CaHJKYm9TaTg3RHIwaEd1TWpqT2lSb0lITGtQNDhnYzhNQnR1d0NEQ1poMnlKOXhsZWg0RHpmRHB0NG1IUWw5dFdBUk8rZzd1a0o0eGpnTjdPQUlTdThtZWhMQThybEd3OG5PSnFMMnJ3RjV2WEUrKzFtdW5KVm9vMnJUZkVQaGhROEw1ZEUvREVFdDNielF4WE9LUGZpNVZYN0JGd1RaSXBNY2kvZXpYMVp3ajFKdnBrRFRQbU4iLCJtYWMiOiJkZjMzYjliYTUzMzZkNjkxNWFmZTdkY2NkZTE2MmQxOTZkMWJkMGZhYThkODI1NDM5MjI5NjFiMGQ5OWIyYzVhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:21:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNyT1ZlZ3ExU3lJV0NjOVBwTXNMeGc9PSIsInZhbHVlIjoiQy9TVW1hM1FaMWJrY1NRSUlLMXJFQkp1UWt2eTBCaDNLTTlhRjYrQlQ1S3hwWC9MWVhLcGZOYkhnSFNtKzU3ZUo1N2RzTU51NzVoNFFxSUw4ZlMybUVFQzNHWFQwZ1NrdEJObWdqYnRBbTljWEJxVFJSRkd4V2x0YWQySnZPVFp3QnorUHVobW1YTGhmRUlmNVNDb2lGNW9neDhOcm1SQTdXWU8rMHlMUG9wUUQrOGVqTlpOZnpybnRRNkZJWXFwNFRGNTRTRGdtNmFPT0RHeGh2VW5WSjlSZ0lWS013VFdYSjlMV1ptbTNuUzFhZytBT1dpRFVnaHVOLzkvQlZoazZPQlk5U01rVWJya1B3dXh4RW1MZ3A0OS9yOW0zMXJCUGJ2aVNRQ2Z0ZW11NHlqRmFkMUl4dFlkM3RCTmYreHFPSW54alEzT1RvMjdCR1dFZUNBRHFkVVgyVkc1dERaNE5VRVpWRUF1M2d5ZGFoTmk2a3VDaC9GdlYrOE80ZjNNSHpWb0lyM3pXNW9uTE1FTDNHNVVpa29MQysxVDM0RnFXYTVrZXZMVld5c3l4MzZCdUE0TUtqaUNDV2FEU1lkaXNCbDNVYWN4YXY1STYvZ2JFU3NyNUZVK2NkR2xzaEs5bzRqRG9YeXh1NUN2MjhseHJGM09jL3RhMEVZMng3TDMiLCJtYWMiOiJhODQ3YzkwYzM3OTYwNDBkMTNhMjM4NzFmYmE0YjFmOWMxZDI5MTBmMjA2MGEwODdjNGZlNDU3MGEyOWRlOGQ2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:21:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2083417682\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-849221857 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849221857\", {\"maxDepth\":0})</script>\n"}}