{"__meta": {"id": "Xf5b15111e10e2b86598a9c68dfc29b0c", "datetime": "2025-06-07 23:52:19", "utime": **********.636691, "method": "GET", "uri": "/productservice/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749340338.895918, "end": **********.636714, "duration": 0.7407960891723633, "duration_str": "741ms", "measures": [{"label": "Booting", "start": 1749340338.895918, "relative_start": 0, "end": **********.441446, "relative_end": **********.441446, "duration": 0.5455281734466553, "duration_str": "546ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.44146, "relative_start": 0.5455420017242432, "end": **********.636716, "relative_end": 1.9073486328125e-06, "duration": 0.19525599479675293, "duration_str": "195ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51659072, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 10, "templates": [{"name": "1x productservice.create", "param_count": null, "params": [], "start": **********.60549, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/productservice/create.blade.phpproductservice.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fproductservice%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "productservice.create"}, {"name": "9x components.required", "param_count": null, "params": [], "start": **********.622454, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 9, "name_original": "components.required"}]}, "route": {"uri": "GET productservice/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "productservice.create", "controller": "App\\Http\\Controllers\\ProductServiceController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=53\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:53-97</a>"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.029259999999999998, "accumulated_duration_str": "29.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4866838, "duration": 0.0171, "duration_str": "17.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 58.442}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.517099, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 58.442, "width_percent": 3.281}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.5417721, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 61.722, "width_percent": 3.315}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.5459728, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 65.038, "width_percent": 2.563}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'product'", "type": "query", "params": [], "bindings": ["15", "product"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 56}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.554284, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:56", "source": "app/Http/Controllers/ProductServiceController.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=56", "ajax": false, "filename": "ProductServiceController.php", "line": "56"}, "connection": "ty", "start_percent": 67.601, "width_percent": 1.811}, {"sql": "select * from `product_service_categories` where `created_by` = 15 and `type` = 'product & service'", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 57}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.558532, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:57", "source": "app/Http/Controllers/ProductServiceController.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=57", "ajax": false, "filename": "ProductServiceController.php", "line": "57"}, "connection": "ty", "start_percent": 69.412, "width_percent": 2.973}, {"sql": "select * from `product_service_units` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 58}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.563247, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:58", "source": "app/Http/Controllers/ProductServiceController.php:58", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=58", "ajax": false, "filename": "ProductServiceController.php", "line": "58"}, "connection": "ty", "start_percent": 72.386, "width_percent": 2.358}, {"sql": "select * from `taxes` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.567655, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:59", "source": "app/Http/Controllers/ProductServiceController.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=59", "ajax": false, "filename": "ProductServiceController.php", "line": "59"}, "connection": "ty", "start_percent": 74.744, "width_percent": 2.802}, {"sql": "select CONCAT(chart_of_accounts.code, \" - \", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id from `chart_of_accounts` left join `chart_of_account_types` on `chart_of_account_types`.`id` = `chart_of_accounts`.`type` where `chart_of_account_types`.`name` = 'income' and `parent` = 0 and `chart_of_accounts`.`created_by` = 15", "type": "query", "params": [], "bindings": ["income", "0", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 64}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5725138, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:64", "source": "app/Http/Controllers/ProductServiceController.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=64", "ajax": false, "filename": "ProductServiceController.php", "line": "64"}, "connection": "ty", "start_percent": 77.546, "width_percent": 3.896}, {"sql": "select CONCAT(chart_of_accounts.code, \" - \", chart_of_accounts.name) AS code_name,chart_of_accounts.id, chart_of_accounts.code, chart_of_account_parents.account from `chart_of_accounts` left join `chart_of_account_parents` on `chart_of_accounts`.`parent` = `chart_of_account_parents`.`id` left join `chart_of_account_types` on `chart_of_account_types`.`id` = `chart_of_accounts`.`type` where `chart_of_account_types`.`name` = 'income' and `chart_of_accounts`.`parent` != 0 and `chart_of_accounts`.`created_by` = 15", "type": "query", "params": [], "bindings": ["income", "0", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 74}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.57737, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:74", "source": "app/Http/Controllers/ProductServiceController.php:74", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=74", "ajax": false, "filename": "ProductServiceController.php", "line": "74"}, "connection": "ty", "start_percent": 81.442, "width_percent": 4.99}, {"sql": "select CONCAT(chart_of_accounts.code, \" - \", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id from `chart_of_accounts` left join `chart_of_account_types` on `chart_of_account_types`.`id` = `chart_of_accounts`.`type` where `chart_of_account_types`.`name` in ('Expenses', 'Costs of Goods Sold') and `chart_of_accounts`.`created_by` = 15", "type": "query", "params": [], "bindings": ["Expenses", "Costs of Goods Sold", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 80}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.582239, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:80", "source": "app/Http/Controllers/ProductServiceController.php:80", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=80", "ajax": false, "filename": "ProductServiceController.php", "line": "80"}, "connection": "ty", "start_percent": 86.432, "width_percent": 4.409}, {"sql": "select CONCAT(chart_of_accounts.code, \" - \", chart_of_accounts.name) AS code_name,chart_of_accounts.id, chart_of_accounts.code, chart_of_account_parents.account from `chart_of_accounts` left join `chart_of_account_parents` on `chart_of_accounts`.`parent` = `chart_of_account_parents`.`id` left join `chart_of_account_types` on `chart_of_account_types`.`id` = `chart_of_accounts`.`type` where `chart_of_account_types`.`name` in ('Expenses', 'Costs of Goods Sold') and `chart_of_accounts`.`parent` != 0 and `chart_of_accounts`.`created_by` = 15", "type": "query", "params": [], "bindings": ["Expenses", "Costs of Goods Sold", "0", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 90}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.587593, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:90", "source": "app/Http/Controllers/ProductServiceController.php:90", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=90", "ajax": false, "filename": "ProductServiceController.php", "line": "90"}, "connection": "ty", "start_percent": 90.841, "width_percent": 3.486}, {"sql": "select * from `users` where `users`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 5096}, {"index": 21, "namespace": "view", "name": "productservice.create", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/productservice/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.607883, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Utility.php:5096", "source": "app/Models/Utility.php:5096", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=5096", "ajax": false, "filename": "Utility.php", "line": "5096"}, "connection": "ty", "start_percent": 94.327, "width_percent": 3.383}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 5097}, {"index": 21, "namespace": "view", "name": "productservice.create", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/productservice/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.612701, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:5097", "source": "app/Models/Utility.php:5097", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=5097", "ajax": false, "filename": "Utility.php", "line": "5097"}, "connection": "ty", "start_percent": 97.71, "width_percent": 2.29}]}, "models": {"data": {"App\\Models\\ChartOfAccount": {"value": 63, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FChartOfAccount.php&line=1", "ajax": false, "filename": "ChartOfAccount.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 69, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => create product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-237552207 data-indent-pad=\"  \"><span class=sf-dump-note>create product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">create product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-237552207\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.553033, "xdebug_link": null}]}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/productservice/create", "status_code": "<pre class=sf-dump id=sf-dump-571613607 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-571613607\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-751622167 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-751622167\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-386917657 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-386917657\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-331822792 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749340336006%7C18%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im4reGJmSUN0UFpUdHJXRFY5SkE4T2c9PSIsInZhbHVlIjoianZmbWRYbTlGeUt2bFVpblE0amxhSFhDR1JyM1k3S3oxclZsMHYyM3c5eXJoWHdFVXA1dS9rL2EvOWZGUHFUYUlQTDRhZUFrRHZ4YldmNlk0Z0hNYUdTcEl4WnNNdUpTWVhNMlRlRW1rdFF2QkJaanpiTWZxNHhZS00xeURZdGc5eHhvZS9HREdRZzhjZnZ4MGRGOHBQU1lNRGR1c0l6Y2Z2Wnp1NDRoK0FUUDg3aWg5V3NjYjBWeFdtdjFUSFVkazl2SEp1b1N5RGswU2M3T1N2dHQrcjRDaC80V0FqbmVHOUNxSHozM0s2MjlmZXZjWGFCOVlzdVVWQ2tFUDFTa01jamRsb29FaXJmem5xMzBmdkd1MVNYNWo2WTFjSEQ5TFcvaWozc05TWGovcXgzUlFGOE1FU3IyaUZSZGE0OFo5b1BCZWN6WVhYNUNPdWM1ekxzYzZqNkZpd1V4bklBUVhTUUk1akJtZjFyQjhLb3gzK3YzUXY1dGFxb3dmL2NyK2wrZlhrTlVwcEQreStrb3lKV2s1MWRQNU9ITHQvN3NkU3ZPVVRyT3loekNhaHdrclB0N2Z1d2lHVy84aHVZVEJ0SHMyOHY0a3gzUHN6RnloNzBzS20yZjFJamg5WjhNcTdiV1FwZys2SFZxY1J0Q1ZVUXBTTWJkQ3JXNVVPWWkiLCJtYWMiOiI3Y2Q2OThjN2Q4MDU2YzMyMTA0ZTU4OWZjZTU0ZDY1YTYxMzMwZjYwNzBmZDBjNmI4NThmMzI1NmZmMDFjMGE2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjE2MTFzc1FMNlcwV1I5R2d3OVhoMFE9PSIsInZhbHVlIjoiaEtHcjBBMUpQY3R6RGNxZG01T3lBQ0pjbUhLdzl4U1NCbDJHQnpMamphTGZ0UlRldk8wcjdta3dLV1YzSUpWL3RNZkd1bnJwMDNqUVVrdUhWT1drOUFaKzEzUkRJVTd1MG4yRmhLVkJPK2l4ZUIzN01kb3NpMDVCRzl3WGhYNzlqbEtkYXF2TFRtN21MUHhjQVM0T3QxMkJjeU11TjdHb2RsQzZZOVhFYUU3MjVScHJVK01yRVBTbFhXVW1GVlFJMEJXMm1BWTRLWHBZaE1SYWx2RkpxUWp2U2diVkNRTnJrM2RWemRIWkxYVUJRdlpHSHRRaWVKM1VTN3hNcVluSDBoa3YzR09PaWxoSTRIMGQyaTRNNzU2RnJ6V1FwcEJkc1MrczBtRi9hVnJvOFNBZVIvSVVwSDk3aTNMYzlqTkFvOEpLTUlFZlNXVlBwZlpxVHhBYUh4UU01dUFpV2NxOUhzTjVMT3pTNktZa0ltc2Y2WGpzOHBHZnBDTlNPb0M1NUhVRnJHc3UvZFdTck5vcy8rQWpNdmFZOTVKM25xTVUrNCtBVyswNldxV2tjbVZDQW0wb1BwWERVUXpNeUNRWjNrbWZoNnJ1WmtiZUNxNDF0Q0JuTHBmSWNXUWJHeTAyUmRsUHJrWFpnTm96NHMxMUdQRnNVREVlYUhEVUpXQXIiLCJtYWMiOiJlNDVlNjJmN2UwMzQyMzYyNjdiMDlmODk2NDZiMjQ0YTUwNThjYzFlNTU0MzcxYzZlMjNlYjZhYmI3NzE5YjEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331822792\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-764124242 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764124242\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1686443066 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:52:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1malNBUVQ2VGZtWEw0WEJFSDVQZlE9PSIsInZhbHVlIjoiWlRwMDc1RFJ4M1psb2xnbytrTmlhWS9DRWk4MmZBRGkzNi9XZGlNL1hnUkVvSEtjMVZvNHo3bzh2VDZmNE4zZ21KdEFGQzhOaHZ5V3d3VFFIQ0dIbjVlNWRvczR1NFRUNzlDQ1l4UGxaUDArQXFmVnNkV1hhWFVJL1lSQkVLN3J1NythOEs4MXZsYjhCclJTcjZnTStMRncxUjNtdjlqcTI3aU1BMi9xbmQrOGJzc2VWNHpKeDMwZkRzRktBb1NvdHFmNWQ2ZE4wN2NRYlFmbDFTVHFsRVZ1ZWhCUHZ3MDlTUUFKaHRYUFZZMkR5MmsvOHgrLzZvMk9YdXdab1hINE1oc1hKTTF2WDdBZU9xTXlqSWZNRHFxWjJnTkVlWnBWTkx3R3JZMDFFRklRNFNjOEVUR1dhL0dtV0c2QmZHWXhKWm5iWFJMOXVWdGtBNUVhbnRYR0x6MGJnaUhxdXVTcXVYQVVKL2RZWUQxbG50V2k5VFRRVXRvajhhNHNITGhZR0FxWkZSWGc0M3JiNjVhbXdFWXFOaGl3c3NLdER2Yk84TEE0aE0rQi9hd1VqUE1veHZ6WEREWXVDdEJsNCtjNUhydEMvMkE4MjIyUHdtM1JNS2FmVGg3ZGdXR0w3U0xwYVdWN3pBNEdyNS9ZSWRjYldBT25PbitjaEthK0xHaWMiLCJtYWMiOiJmM2I4OTM5MWQwYmJiMDFjZTBiODllODY4ODg1OGVjMTk4ZTMyNTc1ZjMyNGQzNzQwYTU4YmUyODVhYWUwOGU3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:52:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkVHMktyREtnWno4c2FRa2Y2bjhwcWc9PSIsInZhbHVlIjoiRitJanVrOC9XZVl3U1MzdldpNEU5Mkprc3VOdGZhZ1dsZU1nMGErL2RscDduNlVSVVhBRlhpZGRRSFI2MWZFWkZXMUVQN1Zpb1AvWG5OQmkrRkFmelNrdmRuMjd6WlJvTkJDYURDMmxyNnFKbUJ1ajBEZzlaazhXL0pzM2pyNXNjbzVTODltTEZuNmlCcnZwM2ZGQXRqNTlpLzVHSDgybzJhWTEvOC9wY21TUnVSbGMzMXRUY3k4bDA0dFA4c2EvcTRQOEhhYUhXVk5xNm45NkthQTFKUnZMZ3U0UVpEZFpZY3oyRG9KWGM1emVyems3TVVzVktFLzV4YjRQejA1cFIzc3JYMTNlVWJQcXlOMU1kcmRXYUlBM1ZpNVpxYWpvRk9zbVNjbk1RT1dkUEc5aTRCMEFSWGVpTmlUVEt2eUxGVElNajBJSzBqZVJLWmJ2N05UbjJPRXd1Z2h0aGZDQWFKcVpmYVMrSGwrN3hSRE5Qb1Iwa1FvYTNDYThac0Ntd1k3Qm4xcVczRm5PWHh3ZkNVeEt0bjVuYnZ1OXRTcFRVekZJWndHMWdDNDVtcytoVnQ3b0VpSlNPZlNsZHhCdmZVci9hbGFady9ib3IwZ05GM3JTRndnV2tmL2pPd2oyVUVtanJFZlM3akpFWVVhVkVMejN2WjlhcTVQMGdmNEQiLCJtYWMiOiIwOGI1YjIzMDQ2NGMwYjk1NjAzZWUyMjcxNzBmNjg3YjA3YWVhMWZkNzVlNDNlYjFmYTM2ZTAzMzMwNDRkMjQ3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:52:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1malNBUVQ2VGZtWEw0WEJFSDVQZlE9PSIsInZhbHVlIjoiWlRwMDc1RFJ4M1psb2xnbytrTmlhWS9DRWk4MmZBRGkzNi9XZGlNL1hnUkVvSEtjMVZvNHo3bzh2VDZmNE4zZ21KdEFGQzhOaHZ5V3d3VFFIQ0dIbjVlNWRvczR1NFRUNzlDQ1l4UGxaUDArQXFmVnNkV1hhWFVJL1lSQkVLN3J1NythOEs4MXZsYjhCclJTcjZnTStMRncxUjNtdjlqcTI3aU1BMi9xbmQrOGJzc2VWNHpKeDMwZkRzRktBb1NvdHFmNWQ2ZE4wN2NRYlFmbDFTVHFsRVZ1ZWhCUHZ3MDlTUUFKaHRYUFZZMkR5MmsvOHgrLzZvMk9YdXdab1hINE1oc1hKTTF2WDdBZU9xTXlqSWZNRHFxWjJnTkVlWnBWTkx3R3JZMDFFRklRNFNjOEVUR1dhL0dtV0c2QmZHWXhKWm5iWFJMOXVWdGtBNUVhbnRYR0x6MGJnaUhxdXVTcXVYQVVKL2RZWUQxbG50V2k5VFRRVXRvajhhNHNITGhZR0FxWkZSWGc0M3JiNjVhbXdFWXFOaGl3c3NLdER2Yk84TEE0aE0rQi9hd1VqUE1veHZ6WEREWXVDdEJsNCtjNUhydEMvMkE4MjIyUHdtM1JNS2FmVGg3ZGdXR0w3U0xwYVdWN3pBNEdyNS9ZSWRjYldBT25PbitjaEthK0xHaWMiLCJtYWMiOiJmM2I4OTM5MWQwYmJiMDFjZTBiODllODY4ODg1OGVjMTk4ZTMyNTc1ZjMyNGQzNzQwYTU4YmUyODVhYWUwOGU3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:52:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkVHMktyREtnWno4c2FRa2Y2bjhwcWc9PSIsInZhbHVlIjoiRitJanVrOC9XZVl3U1MzdldpNEU5Mkprc3VOdGZhZ1dsZU1nMGErL2RscDduNlVSVVhBRlhpZGRRSFI2MWZFWkZXMUVQN1Zpb1AvWG5OQmkrRkFmelNrdmRuMjd6WlJvTkJDYURDMmxyNnFKbUJ1ajBEZzlaazhXL0pzM2pyNXNjbzVTODltTEZuNmlCcnZwM2ZGQXRqNTlpLzVHSDgybzJhWTEvOC9wY21TUnVSbGMzMXRUY3k4bDA0dFA4c2EvcTRQOEhhYUhXVk5xNm45NkthQTFKUnZMZ3U0UVpEZFpZY3oyRG9KWGM1emVyems3TVVzVktFLzV4YjRQejA1cFIzc3JYMTNlVWJQcXlOMU1kcmRXYUlBM1ZpNVpxYWpvRk9zbVNjbk1RT1dkUEc5aTRCMEFSWGVpTmlUVEt2eUxGVElNajBJSzBqZVJLWmJ2N05UbjJPRXd1Z2h0aGZDQWFKcVpmYVMrSGwrN3hSRE5Qb1Iwa1FvYTNDYThac0Ntd1k3Qm4xcVczRm5PWHh3ZkNVeEt0bjVuYnZ1OXRTcFRVekZJWndHMWdDNDVtcytoVnQ3b0VpSlNPZlNsZHhCdmZVci9hbGFady9ib3IwZ05GM3JTRndnV2tmL2pPd2oyVUVtanJFZlM3akpFWVVhVkVMejN2WjlhcTVQMGdmNEQiLCJtYWMiOiIwOGI1YjIzMDQ2NGMwYjk1NjAzZWUyMjcxNzBmNjg3YjA3YWVhMWZkNzVlNDNlYjFmYTM2ZTAzMzMwNDRkMjQ3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:52:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1686443066\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2082109249 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2082109249\", {\"maxDepth\":0})</script>\n"}}