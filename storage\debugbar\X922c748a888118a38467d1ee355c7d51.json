{"__meta": {"id": "X922c748a888118a38467d1ee355c7d51", "datetime": "2025-06-07 22:18:40", "utime": **********.362243, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.428227, "end": **********.36228, "duration": 2.****************, "duration_str": "2.93s", "measures": [{"label": "Booting", "start": **********.428227, "relative_start": 0, "end": **********.660496, "relative_end": **********.660496, "duration": 1.***************, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.660521, "relative_start": 1.****************, "end": **********.362285, "relative_end": 5.0067901611328125e-06, "duration": 1.****************, "duration_str": "1.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "1x dashboard.account-dashboard", "param_count": null, "params": [], "start": **********.998005, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.phpdashboard.account-dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fdashboard%2Faccount-dashboard.blade.php&line=1", "ajax": false, "filename": "account-dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "dashboard.account-dashboard"}, {"name": "1x layouts.admin", "param_count": null, "params": [], "start": **********.108042, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php&line=1", "ajax": false, "filename": "admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.admin"}, {"name": "1x partials.admin.menu", "param_count": null, "params": [], "start": **********.125976, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/menu.blade.phppartials.admin.menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.menu"}, {"name": "1x partials.admin.header", "param_count": null, "params": [], "start": **********.288853, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/header.blade.phppartials.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.header"}, {"name": "1x partials.admin.footer", "param_count": null, "params": [], "start": **********.341786, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/footer.blade.phppartials.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpartials%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.footer"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.353, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}, {"name": "1x Chatify::layouts.footerLinks", "param_count": null, "params": [], "start": **********.354833, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/vendor/Chatify/layouts/footerLinks.blade.phpChatify::layouts.footerLinks", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FfooterLinks.blade.php&line=1", "ajax": false, "filename": "footerLinks.blade.php", "line": "?"}, "render_count": 1, "name_original": "Chatify::layouts.footerLinks"}]}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 159, "nb_failed_statements": 0, "accumulated_duration": 0.*****************, "accumulated_duration_str": "222ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7524211, "duration": 0.00525, "duration_str": "5.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 2.369}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.787627, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 2.369, "width_percent": 0.528}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.8386042, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 2.897, "width_percent": 0.753}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8464541, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 3.65, "width_percent": 0.614}, {"sql": "select * from `revenues` where `created_by` = 15 order by `id` desc limit 5", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 92}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.86313, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:92", "source": "app/Http/Controllers/DashboardController.php:92", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=92", "ajax": false, "filename": "DashboardController.php", "line": "92"}, "connection": "ty", "start_percent": 4.264, "width_percent": 0.772}, {"sql": "select * from `payments` where `created_by` = 15 order by `id` desc limit 5", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 93}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8730009, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:93", "source": "app/Http/Controllers/DashboardController.php:93", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=93", "ajax": false, "filename": "DashboardController.php", "line": "93"}, "connection": "ty", "start_percent": 5.035, "width_percent": 0.582}, {"sql": "select * from `product_service_categories` where `created_by` = 15 and `type` = 'income'", "type": "query", "params": [], "bindings": ["15", "income"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.884732, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:97", "source": "app/Http/Controllers/DashboardController.php:97", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=97", "ajax": false, "filename": "DashboardController.php", "line": "97"}, "connection": "ty", "start_percent": 5.617, "width_percent": 0.65}, {"sql": "select * from `product_service_categories` where `created_by` = 15 and `type` = 'expense'", "type": "query", "params": [], "bindings": ["15", "expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 113}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.892759, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:113", "source": "app/Http/Controllers/DashboardController.php:113", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=113", "ajax": false, "filename": "DashboardController.php", "line": "113"}, "connection": "ty", "start_percent": 6.267, "width_percent": 0.451}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 1 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.905388, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 6.718, "width_percent": 0.442}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 1 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "1", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.914339, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 7.16, "width_percent": 1.191}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 1 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.92467, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 8.351, "width_percent": 0.478}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 1 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "1", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9347248, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 8.83, "width_percent": 0.988}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 2 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9454641, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 9.818, "width_percent": 0.442}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 2 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "2", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9536068, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 10.26, "width_percent": 0.654}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 2 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9629788, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 10.914, "width_percent": 0.496}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 2 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "2", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.969949, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 11.41, "width_percent": 0.803}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 3 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.977841, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 12.213, "width_percent": 0.424}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 3 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.984768, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 12.638, "width_percent": 0.641}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 3 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.992198, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 13.278, "width_percent": 0.492}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 3 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.999227, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 13.77, "width_percent": 0.69}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 4 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.006917, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 14.46, "width_percent": 0.528}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 4 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "4", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.014252, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 14.988, "width_percent": 0.614}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 4 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0218718, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 15.602, "width_percent": 0.429}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 4 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "4", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0291998, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 16.03, "width_percent": 0.744}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 5 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.037247, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 16.775, "width_percent": 0.393}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 5 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "5", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.044204, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 17.167, "width_percent": 0.618}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 5 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.052284, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 17.786, "width_percent": 0.442}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 5 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "5", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0592299, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 18.228, "width_percent": 0.645}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 6 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0669088, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 18.873, "width_percent": 0.42}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 6 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "6", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.073782, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 19.293, "width_percent": 0.641}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 6 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.081654, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 19.933, "width_percent": 0.451}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 6 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "6", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0885189, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 20.384, "width_percent": 0.731}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 7 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0962398, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 21.115, "width_percent": 0.501}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 7 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "7", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.103277, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 21.616, "width_percent": 0.654}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 7 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1110458, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 22.27, "width_percent": 0.415}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 7 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "7", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1180298, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 22.685, "width_percent": 0.659}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 8 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.125727, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 23.344, "width_percent": 0.483}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 8 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.132961, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 23.827, "width_percent": 0.623}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 8 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1407878, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 24.45, "width_percent": 0.424}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 8 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1484768, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 24.874, "width_percent": 0.668}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 9 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "9"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1561449, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 25.541, "width_percent": 0.433}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 9 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "9", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.162982, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 25.975, "width_percent": 0.695}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 9 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "9"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.170789, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 26.669, "width_percent": 0.411}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 9 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "9", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.177542, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 27.08, "width_percent": 0.618}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 10 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "10"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.185351, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 27.698, "width_percent": 0.501}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 10 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "10", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1924958, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 28.199, "width_percent": 0.654}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 10 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "10"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.200243, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 28.853, "width_percent": 0.411}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 10 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "10", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2070801, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 29.264, "width_percent": 0.623}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 11 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "11"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.214637, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 29.886, "width_percent": 0.447}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 11 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "11", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.221341, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 30.333, "width_percent": 0.591}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 11 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "11"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.228651, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 30.924, "width_percent": 0.478}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 11 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "11", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.236334, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 31.402, "width_percent": 0.627}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 12 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.243826, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 32.029, "width_percent": 0.51}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 12 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "12", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.251469, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 32.539, "width_percent": 0.668}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 12 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2597258, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 33.207, "width_percent": 0.433}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 12 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "12", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.267519, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 33.64, "width_percent": 0.695}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-06-07' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-07"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.27587, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 34.335, "width_percent": 0.447}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-06-07' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-06-07", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.284248, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 34.782, "width_percent": 0.627}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-06-07' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-07"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2930381, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 35.409, "width_percent": 0.438}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-06-07' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-06-07", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.300735, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 35.846, "width_percent": 0.677}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-06-06' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-06"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.309431, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 36.523, "width_percent": 0.447}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-06-06' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-06-06", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.317602, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 36.97, "width_percent": 0.641}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-06-06' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-06"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.325891, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 37.611, "width_percent": 0.384}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-06-06' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-06-06", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.334784, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 37.994, "width_percent": 1.042}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-06-05' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-05"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.344757, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 39.036, "width_percent": 0.361}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-06-05' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-06-05", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3530102, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 39.397, "width_percent": 0.528}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-06-05' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-05"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.360876, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 39.925, "width_percent": 0.447}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-06-05' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-06-05", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.368362, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 40.372, "width_percent": 0.636}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-06-04' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-04"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.376233, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 41.008, "width_percent": 0.388}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-06-04' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-06-04", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3840652, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 41.396, "width_percent": 0.699}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-06-04' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-04"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3924649, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 42.095, "width_percent": 0.415}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-06-04' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-06-04", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.400058, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 42.51, "width_percent": 0.627}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-06-03' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-03"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4082031, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 43.138, "width_percent": 0.451}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-06-03' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-06-03", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.41629, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 43.589, "width_percent": 0.578}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-06-03' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-03"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4238968, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 44.166, "width_percent": 0.433}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-06-03' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-06-03", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.431487, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 44.599, "width_percent": 0.6}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-06-02' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-02"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.439881, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 45.199, "width_percent": 0.451}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-06-02' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-06-02", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.447063, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 45.651, "width_percent": 0.618}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-06-02' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-02"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4573622, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 46.269, "width_percent": 0.447}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-06-02' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-06-02", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.466851, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 46.715, "width_percent": 0.609}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-06-01' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.474464, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 47.324, "width_percent": 0.402}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-06-01' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-06-01", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.481587, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 47.726, "width_percent": 0.672}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-06-01' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.489556, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 48.398, "width_percent": 0.397}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-06-01' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-06-01", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.497161, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 48.795, "width_percent": 0.717}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-05-31' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-31"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.505525, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 49.513, "width_percent": 0.465}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-05-31' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-31", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.513952, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 49.977, "width_percent": 0.677}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-05-31' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-31"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5221171, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 50.654, "width_percent": 0.451}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-05-31' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-31", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.529747, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 51.105, "width_percent": 0.717}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-05-30' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.53777, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 51.823, "width_percent": 0.42}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-05-30' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-30", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.545187, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 52.242, "width_percent": 0.623}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-05-30' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.553457, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 52.865, "width_percent": 0.438}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-05-30' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-30", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5617378, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 53.303, "width_percent": 0.749}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-05-29' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-29"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.572612, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 54.052, "width_percent": 0.614}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-05-29' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-29", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.580112, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 54.665, "width_percent": 0.731}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-05-29' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-29"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.598859, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 55.396, "width_percent": 0.546}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-05-29' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-29", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.609988, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 55.942, "width_percent": 0.772}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-05-28' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-28"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6204069, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 56.714, "width_percent": 0.514}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-05-28' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-28", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.631424, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 57.228, "width_percent": 0.708}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-05-28' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-28"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.640821, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 57.936, "width_percent": 0.433}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-05-28' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-28", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.65075, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 58.369, "width_percent": 0.717}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-05-27' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-27"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.659859, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 59.087, "width_percent": 0.388}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-05-27' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-27", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6670969, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 59.475, "width_percent": 0.668}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-05-27' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-27"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.674999, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 60.143, "width_percent": 0.456}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-05-27' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-27", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.683216, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 60.598, "width_percent": 0.781}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-05-26' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-26"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.694319, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 61.379, "width_percent": 0.478}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-05-26' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-26", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.702446, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 61.857, "width_percent": 0.555}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-05-26' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-26"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.710018, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 62.412, "width_percent": 0.42}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-05-26' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-26", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.718098, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 62.832, "width_percent": 0.641}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-05-25' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-25"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.726581, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 63.472, "width_percent": 0.393}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-05-25' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-25", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.733278, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 63.865, "width_percent": 0.614}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-05-25' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-25"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7408469, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 64.478, "width_percent": 0.429}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-05-25' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-25", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.748289, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 64.907, "width_percent": 0.699}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-05-24' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-24"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7561991, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 65.606, "width_percent": 0.37}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-05-24' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-24", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7630591, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 65.976, "width_percent": 0.609}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-05-24' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-24"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.771683, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 66.585, "width_percent": 0.46}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-05-24' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-24", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.779312, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 67.046, "width_percent": 0.735}, {"sql": "select count(*) as aggregate from `taxes` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 134}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7892652, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:134", "source": "app/Http/Controllers/DashboardController.php:134", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=134", "ajax": false, "filename": "DashboardController.php", "line": "134"}, "connection": "ty", "start_percent": 67.781, "width_percent": 0.374}, {"sql": "select count(*) as aggregate from `product_service_categories` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 135}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.796026, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:135", "source": "app/Http/Controllers/DashboardController.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=135", "ajax": false, "filename": "DashboardController.php", "line": "135"}, "connection": "ty", "start_percent": 68.156, "width_percent": 0.433}, {"sql": "select count(*) as aggregate from `product_service_units` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 136}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.803394, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:136", "source": "app/Http/Controllers/DashboardController.php:136", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=136", "ajax": false, "filename": "DashboardController.php", "line": "136"}, "connection": "ty", "start_percent": 68.589, "width_percent": 0.609}, {"sql": "select count(*) as aggregate from `bank_accounts` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 137}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.811104, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:137", "source": "app/Http/Controllers/DashboardController.php:137", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=137", "ajax": false, "filename": "DashboardController.php", "line": "137"}, "connection": "ty", "start_percent": 69.198, "width_percent": 0.618}, {"sql": "select * from `bank_accounts` where `created_by` = 15 limit 5", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 139}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.819402, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:139", "source": "app/Http/Controllers/DashboardController.php:139", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=139", "ajax": false, "filename": "DashboardController.php", "line": "139"}, "connection": "ty", "start_percent": 69.816, "width_percent": 0.46}, {"sql": "select `invoices`.*, `customers`.`name` as `customer_name` from `invoices` inner join `customers` on `invoices`.`customer_id` = `customers`.`id` where `invoices`.`created_by` = 15 order by `invoices`.`id` desc limit 5", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 145}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.828452, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:145", "source": "app/Http/Controllers/DashboardController.php:145", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=145", "ajax": false, "filename": "DashboardController.php", "line": "145"}, "connection": "ty", "start_percent": 70.276, "width_percent": 0.614}, {"sql": "select `invoices`.`invoice_id` as `invoice`, sum((invoice_products.price * invoice_products.quantity) - invoice_products.discount) as price, (SELECT SUM(credit_notes.amount) FROM credit_notes WHERE credit_notes.invoice = invoices.id) as credit_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as total_tax from `invoices` left join `invoice_products` on `invoice_products`.`invoice_id` = `invoices`.`id` where `issue_date` >= '2025-05-26' and `issue_date` <= '2025-06-07' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-26", "2025-06-07", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 861}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1031}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 147}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8379698, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "User.php:861", "source": "app/Models/User.php:861", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=861", "ajax": false, "filename": "User.php", "line": "861"}, "connection": "ty", "start_percent": 70.89, "width_percent": 0.988}, {"sql": "select `invoices`.`invoice_id` as `invoice`, sum((invoice_payments.amount)) as pay_price from `invoices` left join `invoice_payments` on `invoice_payments`.`invoice_id` = `invoices`.`id` where `issue_date` >= '2025-05-26' and `issue_date` <= '2025-06-07' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-26", "2025-06-07", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 872}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1031}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 147}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.846521, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "User.php:872", "source": "app/Models/User.php:872", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=872", "ajax": false, "filename": "User.php", "line": "872"}, "connection": "ty", "start_percent": 71.878, "width_percent": 0.947}, {"sql": "select `invoices`.`invoice_id` as `invoice`, sum((invoice_products.price * invoice_products.quantity) - invoice_products.discount) as price, (SELECT SUM(credit_notes.amount) FROM credit_notes WHERE credit_notes.invoice = invoices.id) as credit_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as total_tax from `invoices` left join `invoice_products` on `invoice_products`.`invoice_id` = `invoices`.`id` where `issue_date` >= '2025-05-07' and `issue_date` <= '2025-06-07' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-07", "2025-06-07", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 861}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1044}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 148}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.855361, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "User.php:861", "source": "app/Models/User.php:861", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=861", "ajax": false, "filename": "User.php", "line": "861"}, "connection": "ty", "start_percent": 72.825, "width_percent": 0.636}, {"sql": "select `invoices`.`invoice_id` as `invoice`, sum((invoice_payments.amount)) as pay_price from `invoices` left join `invoice_payments` on `invoice_payments`.`invoice_id` = `invoices`.`id` where `issue_date` >= '2025-05-07' and `issue_date` <= '2025-06-07' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-07", "2025-06-07", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 872}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1044}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 148}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.863441, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "User.php:872", "source": "app/Models/User.php:872", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=872", "ajax": false, "filename": "User.php", "line": "872"}, "connection": "ty", "start_percent": 73.461, "width_percent": 0.578}, {"sql": "select `bills`.*, `venders`.`name` as `vender_name` from `bills` inner join `venders` on `bills`.`vender_id` = `venders`.`id` where `bills`.`created_by` = 15 order by `bills`.`id` desc limit 5", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 154}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.87411, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:154", "source": "app/Http/Controllers/DashboardController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=154", "ajax": false, "filename": "DashboardController.php", "line": "154"}, "connection": "ty", "start_percent": 74.039, "width_percent": 0.627}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_products.price * bill_products.quantity) - bill_products.discount) as price, (SELECT SUM(debit_notes.amount) FROM debit_notes\nWHERE debit_notes.bill = bills.id) as debit_price, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as total_tax from `bills` left join `bill_products` on `bill_products`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-26' and `bill_date` <= '2025-06-07' and `bills`.`created_by` = 15 and `bills`.`type` = 'Bill' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-26", "2025-06-07", "15", "Bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 920}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1078}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 156}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.882644, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "User.php:920", "source": "app/Models/User.php:920", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=920", "ajax": false, "filename": "User.php", "line": "920"}, "connection": "ty", "start_percent": 74.666, "width_percent": 1.069}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_payments.amount)) as pay_price from `bills` left join `bill_payments` on `bill_payments`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-26' and `bill_date` <= '2025-06-07' and `bills`.`created_by` = 15 and `bills`.`type` = 'Bill' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-26", "2025-06-07", "15", "Bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 932}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1078}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 156}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.89189, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "User.php:932", "source": "app/Models/User.php:932", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=932", "ajax": false, "filename": "User.php", "line": "932"}, "connection": "ty", "start_percent": 75.735, "width_percent": 0.934}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_products.price * bill_products.quantity) - bill_products.discount) as price, (SELECT SUM(debit_notes.amount) FROM debit_notes\nWHERE debit_notes.bill = bills.id) as debit_price, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as total_tax from `bills` left join `bill_products` on `bill_products`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-26' and `bill_date` <= '2025-06-07' and `bills`.`created_by` = 15 and `bills`.`type` = 'Expense' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-26", "2025-06-07", "15", "Expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 982}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1079}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 156}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.901444, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "User.php:982", "source": "app/Models/User.php:982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=982", "ajax": false, "filename": "User.php", "line": "982"}, "connection": "ty", "start_percent": 76.669, "width_percent": 0.776}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_payments.amount)) as pay_price from `bills` left join `bill_payments` on `bill_payments`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-26' and `bill_date` <= '2025-06-07' and `bills`.`created_by` = 15 and `bills`.`type` = 'Expense' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-26", "2025-06-07", "15", "Expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 994}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1079}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 156}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.910038, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "User.php:994", "source": "app/Models/User.php:994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=994", "ajax": false, "filename": "User.php", "line": "994"}, "connection": "ty", "start_percent": 77.445, "width_percent": 0.614}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_products.price * bill_products.quantity) - bill_products.discount) as price, (SELECT SUM(debit_notes.amount) FROM debit_notes\nWHERE debit_notes.bill = bills.id) as debit_price, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as total_tax from `bills` left join `bill_products` on `bill_products`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-07' and `bill_date` <= '2025-06-07' and `bills`.`created_by` = 15 and `bills`.`type` = 'Bill' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-07", "2025-06-07", "15", "Bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 920}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1091}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 157}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9186158, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "User.php:920", "source": "app/Models/User.php:920", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=920", "ajax": false, "filename": "User.php", "line": "920"}, "connection": "ty", "start_percent": 78.059, "width_percent": 0.744}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_payments.amount)) as pay_price from `bills` left join `bill_payments` on `bill_payments`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-07' and `bill_date` <= '2025-06-07' and `bills`.`created_by` = 15 and `bills`.`type` = 'Bill' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-07", "2025-06-07", "15", "Bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 932}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1091}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 157}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.926813, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "User.php:932", "source": "app/Models/User.php:932", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=932", "ajax": false, "filename": "User.php", "line": "932"}, "connection": "ty", "start_percent": 78.803, "width_percent": 0.573}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_products.price * bill_products.quantity) - bill_products.discount) as price, (SELECT SUM(debit_notes.amount) FROM debit_notes\nWHERE debit_notes.bill = bills.id) as debit_price, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as total_tax from `bills` left join `bill_products` on `bill_products`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-07' and `bill_date` <= '2025-06-07' and `bills`.`created_by` = 15 and `bills`.`type` = 'Expense' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-07", "2025-06-07", "15", "Expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 982}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1092}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 157}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9347231, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "User.php:982", "source": "app/Models/User.php:982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=982", "ajax": false, "filename": "User.php", "line": "982"}, "connection": "ty", "start_percent": 79.376, "width_percent": 0.704}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_payments.amount)) as pay_price from `bills` left join `bill_payments` on `bill_payments`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-07' and `bill_date` <= '2025-06-07' and `bills`.`created_by` = 15 and `bills`.`type` = 'Expense' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-07", "2025-06-07", "15", "Expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 994}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1092}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 157}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9426231, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "User.php:994", "source": "app/Models/User.php:994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=994", "ajax": false, "filename": "User.php", "line": "994"}, "connection": "ty", "start_percent": 80.08, "width_percent": 0.618}, {"sql": "select * from `goals` where `created_by` = 15 and `is_display` = 1", "type": "query", "params": [], "bindings": ["15", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 158}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.952078, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:158", "source": "app/Http/Controllers/DashboardController.php:158", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=158", "ajax": false, "filename": "DashboardController.php", "line": "158"}, "connection": "ty", "start_percent": 80.698, "width_percent": 0.762}, {"sql": "select * from `users` where `users`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 161}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9597569, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:161", "source": "app/Http/Controllers/DashboardController.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=161", "ajax": false, "filename": "DashboardController.php", "line": "161"}, "connection": "ty", "start_percent": 81.461, "width_percent": 0.659}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 162}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.968641, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "ty", "start_percent": 82.12, "width_percent": 0.699}, {"sql": "select count(*) as aggregate from `customers` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 394}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.005601, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "User.php:394", "source": "app/Models/User.php:394", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=394", "ajax": false, "filename": "User.php", "line": "394"}, "connection": "ty", "start_percent": 82.819, "width_percent": 0.505}, {"sql": "select count(*) as aggregate from `venders` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 399}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 274}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.0139809, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "User.php:399", "source": "app/Models/User.php:399", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=399", "ajax": false, "filename": "User.php", "line": "399"}, "connection": "ty", "start_percent": 83.324, "width_percent": 0.411}, {"sql": "select count(*) as aggregate from `invoices` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 404}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 288}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.0209248, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "User.php:404", "source": "app/Models/User.php:404", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=404", "ajax": false, "filename": "User.php", "line": "404"}, "connection": "ty", "start_percent": 83.735, "width_percent": 0.347}, {"sql": "select count(*) as aggregate from `bills` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 409}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 300}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.027407, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "User.php:409", "source": "app/Models/User.php:409", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=409", "ajax": false, "filename": "User.php", "line": "409"}, "connection": "ty", "start_percent": 84.082, "width_percent": 0.384}, {"sql": "select sum(`amount`) as aggregate from `revenues` where `created_by` = 15 and Date(date) = CURDATE() and `created_by` = 15", "type": "query", "params": [], "bindings": ["15", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 414}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 355}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.03549, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "User.php:414", "source": "app/Models/User.php:414", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=414", "ajax": false, "filename": "User.php", "line": "414"}, "connection": "ty", "start_percent": 84.466, "width_percent": 0.699}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '25-06-07' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["25-06-07", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 415}, {"index": 15, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 355}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0425699, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 85.165, "width_percent": 0.794}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `created_by` = 15 and Date(date) = CURDATE()", "type": "query", "params": [], "bindings": ["15", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 430}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 366}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.051703, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "User.php:430", "source": "app/Models/User.php:430", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=430", "ajax": false, "filename": "User.php", "line": "430"}, "connection": "ty", "start_percent": 85.959, "width_percent": 0.487}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '25-06-07' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["25-06-07", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 432}, {"index": 15, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 366}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0586221, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 86.446, "width_percent": 0.695}, {"sql": "select sum(`amount`) as aggregate from `revenues` where `created_by` = 15 and MONTH(date) = '06'", "type": "query", "params": [], "bindings": ["15", "06"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 450}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 377}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.0670068, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "User.php:450", "source": "app/Models/User.php:450", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=450", "ajax": false, "filename": "User.php", "line": "450"}, "connection": "ty", "start_percent": 87.141, "width_percent": 0.424}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where MONTH(invoices.send_date) = '06' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["06", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 602}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 451}, {"index": 15, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 377}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.076294, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "User.php:602", "source": "app/Models/User.php:602", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=602", "ajax": false, "filename": "User.php", "line": "602"}, "connection": "ty", "start_percent": 87.565, "width_percent": 0.555}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and MONTH(date) = '06'", "type": "query", "params": [], "bindings": ["15", "06"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 488}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 388}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.085278, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "User.php:488", "source": "app/Models/User.php:488", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=488", "ajax": false, "filename": "User.php", "line": "488"}, "connection": "ty", "start_percent": 88.12, "width_percent": 0.438}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where MONTH(bills.send_date) = '06' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["06", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 671}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 489}, {"index": 15, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 388}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.091686, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "User.php:671", "source": "app/Models/User.php:671", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=671", "ajax": false, "filename": "User.php", "line": "671"}, "connection": "ty", "start_percent": 88.558, "width_percent": 0.704}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/admin.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1099029, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 89.262, "width_percent": 0.722}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/admin.blade.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1183522, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 89.984, "width_percent": 0.605}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/menu.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.133958, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 90.588, "width_percent": 0.596}, {"sql": "select * from `email_templates` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\EmailTemplate.php", "line": 27}, {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/menu.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.1422, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "EmailTemplate.php:27", "source": "app/Models/EmailTemplate.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FEmailTemplate.php&line=27", "ajax": false, "filename": "EmailTemplate.php", "line": "27"}, "connection": "ty", "start_percent": 91.184, "width_percent": 0.55}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/header.blade.php", "line": 3}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2902582, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 91.734, "width_percent": 0.609}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.30057, "duration": 0.013380000000000001, "duration_str": "13.38ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 92.343, "width_percent": 6.037}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.3240201, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 98.38, "width_percent": 0.537}, {"sql": "select count(*) as aggregate from `ch_messages` where `to_id` = 15 and `seen` = 0", "type": "query", "params": [], "bindings": ["15", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/header.blade.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.3325279, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "partials.admin.header:18", "source": "view::partials.admin.header:18", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=18", "ajax": false, "filename": "header.blade.php", "line": "18"}, "connection": "ty", "start_percent": 98.917, "width_percent": 0.559}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 6212}, {"index": 15, "namespace": "view", "name": "partials.admin.footer", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/footer.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.343126, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 99.477, "width_percent": 0.523}]}, "models": {"data": {"App\\Models\\Revenue": {"value": 27, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FRevenue.php&line=1", "ajax": false, "filename": "Revenue.php", "line": "?"}}, "App\\Models\\Payment": {"value": 27, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\BankAccount": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FBankAccount.php&line=1", "ajax": false, "filename": "BankAccount.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}}, "count": 60, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 71, "messages": [{"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.860828, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.002121, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.154545, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.156714, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.157741, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.158712, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.160288, "xdebug_link": null}, {"message": "[ability => statement report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-733927451 data-indent-pad=\"  \"><span class=sf-dump-note>statement report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">statement report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-733927451\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.162083, "xdebug_link": null}, {"message": "[ability => invoice report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-140025126 data-indent-pad=\"  \"><span class=sf-dump-note>invoice report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">invoice report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-140025126\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.163498, "xdebug_link": null}, {"message": "[ability => bill report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-591836351 data-indent-pad=\"  \"><span class=sf-dump-note>bill report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bill report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-591836351\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.164867, "xdebug_link": null}, {"message": "[ability => stock report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-752889699 data-indent-pad=\"  \"><span class=sf-dump-note>stock report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">stock report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752889699\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.166176, "xdebug_link": null}, {"message": "[ability => loss & profit report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-577940635 data-indent-pad=\"  \"><span class=sf-dump-note>loss & profit report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">loss &amp; profit report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-577940635\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.167375, "xdebug_link": null}, {"message": "[ability => manage transaction, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1574625586 data-indent-pad=\"  \"><span class=sf-dump-note>manage transaction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage transaction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1574625586\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.168514, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-633281367 data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-633281367\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.170001, "xdebug_link": null}, {"message": "[ability => expense report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2045285263 data-indent-pad=\"  \"><span class=sf-dump-note>expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045285263\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.171144, "xdebug_link": null}, {"message": "[\n  ability => income vs expense report,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>income vs expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">income vs expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.172447, "xdebug_link": null}, {"message": "[ability => tax report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2041336486 data-indent-pad=\"  \"><span class=sf-dump-note>tax report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">tax report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2041336486\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.173667, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.174436, "xdebug_link": null}, {"message": "[ability => manage report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-113539515 data-indent-pad=\"  \"><span class=sf-dump-note>manage report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113539515\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.177044, "xdebug_link": null}, {"message": "[ability => show pos dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-544590915 data-indent-pad=\"  \"><span class=sf-dump-note>show pos dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show pos dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-544590915\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.177958, "xdebug_link": null}, {"message": "[ability => manage employee, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1738435951 data-indent-pad=\"  \"><span class=sf-dump-note>manage employee</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1738435951\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.181305, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-465977962 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-465977962\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.183853, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-196629586 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-196629586\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.185944, "xdebug_link": null}, {"message": "[ability => manage pay slip, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1228970309 data-indent-pad=\"  \"><span class=sf-dump-note>manage pay slip</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage pay slip</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1228970309\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.187799, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1307169508 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1307169508\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.190194, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-277102770 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277102770\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.192607, "xdebug_link": null}, {"message": "[ability => manage attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-581955949 data-indent-pad=\"  \"><span class=sf-dump-note>manage attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-581955949\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.195037, "xdebug_link": null}, {"message": "[ability => create attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1583052446 data-indent-pad=\"  \"><span class=sf-dump-note>create attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">create attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1583052446\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.197837, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-251642232 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-251642232\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.200537, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1895488585 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1895488585\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.202881, "xdebug_link": null}, {"message": "[ability => manage transfer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1857792362 data-indent-pad=\"  \"><span class=sf-dump-note>manage transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1857792362\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.205371, "xdebug_link": null}, {"message": "[ability => manage resignation, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage resignation</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage resignation</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.207667, "xdebug_link": null}, {"message": "[ability => manage travel, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1274357551 data-indent-pad=\"  \"><span class=sf-dump-note>manage travel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage travel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1274357551\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.209848, "xdebug_link": null}, {"message": "[ability => manage promotion, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1062549457 data-indent-pad=\"  \"><span class=sf-dump-note>manage promotion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage promotion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1062549457\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.211885, "xdebug_link": null}, {"message": "[ability => manage complaint, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-251597505 data-indent-pad=\"  \"><span class=sf-dump-note>manage complaint</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage complaint</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-251597505\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.214434, "xdebug_link": null}, {"message": "[ability => manage warning, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1630364809 data-indent-pad=\"  \"><span class=sf-dump-note>manage warning</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage warning</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1630364809\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.21694, "xdebug_link": null}, {"message": "[ability => manage termination, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1903346339 data-indent-pad=\"  \"><span class=sf-dump-note>manage termination</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage termination</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1903346339\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.219108, "xdebug_link": null}, {"message": "[ability => manage announcement, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-956624852 data-indent-pad=\"  \"><span class=sf-dump-note>manage announcement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage announcement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-956624852\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.22151, "xdebug_link": null}, {"message": "[ability => manage holiday, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1974121806 data-indent-pad=\"  \"><span class=sf-dump-note>manage holiday</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage holiday</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1974121806\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.223952, "xdebug_link": null}, {"message": "[ability => manage document, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1594748360 data-indent-pad=\"  \"><span class=sf-dump-note>manage document</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage document</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594748360\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.225609, "xdebug_link": null}, {"message": "[ability => manage company policy, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company policy</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage company policy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.227416, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.228645, "xdebug_link": null}, {"message": "[ability => manage bank account, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage bank account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage bank account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.229587, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.230683, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1818562014 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1818562014\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.231687, "xdebug_link": null}, {"message": "[ability => manage proposal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1720648348 data-indent-pad=\"  \"><span class=sf-dump-note>manage proposal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage proposal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1720648348\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.233059, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-268559766 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-268559766\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.234262, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.235265, "xdebug_link": null}, {"message": "[\n  ability => manage chart of account,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage chart of account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.236722, "xdebug_link": null}, {"message": "[ability => manage goal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage goal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage goal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.238618, "xdebug_link": null}, {"message": "[ability => manage constant tax, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1509494223 data-indent-pad=\"  \"><span class=sf-dump-note>manage constant tax</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage constant tax</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509494223\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.239907, "xdebug_link": null}, {"message": "[ability => manage print settings, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1090603131 data-indent-pad=\"  \"><span class=sf-dump-note>manage print settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage print settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1090603131\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.240981, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2049714906 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2049714906\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.241838, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2132103873 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132103873\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.24278, "xdebug_link": null}, {"message": "[ability => manage role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1854268225 data-indent-pad=\"  \"><span class=sf-dump-note>manage role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1854268225\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.24356, "xdebug_link": null}, {"message": "[ability => manage client, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1257399640 data-indent-pad=\"  \"><span class=sf-dump-note>manage client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1257399640\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.244939, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1972102498 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1972102498\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.245908, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1680867336 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1680867336\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.247313, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-993187740 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993187740\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.248777, "xdebug_link": null}, {"message": "[ability => show warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-977687966 data-indent-pad=\"  \"><span class=sf-dump-note>show warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">show warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-977687966\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.25291, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1891748962 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1891748962\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.256982, "xdebug_link": null}, {"message": "[ability => show pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-506457435 data-indent-pad=\"  \"><span class=sf-dump-note>show pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">show pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-506457435\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.260963, "xdebug_link": null}, {"message": "[ability => create barcode, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-31864601 data-indent-pad=\"  \"><span class=sf-dump-note>create barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31864601\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.264756, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1245495549 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1245495549\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.268166, "xdebug_link": null}, {"message": "[ability => manage warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1302959216 data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1302959216\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.27145, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1443002896 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1443002896\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.27562, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1215233972 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1215233972\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.278755, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1716789693 data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1716789693\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.282295, "xdebug_link": null}, {"message": "[\n  ability => manage company settings,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2032551347 data-indent-pad=\"  \"><span class=sf-dump-note>manage company settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage company settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2032551347\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.283236, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1028537945 data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1028537945\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.286553, "xdebug_link": null}, {"message": "[ability => manage order, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>manage order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.287772, "xdebug_link": null}]}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/support\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334714308%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdaTE9aczJKdDJSaEYvNStVVmc1cVE9PSIsInZhbHVlIjoiTWEvUlhuUGJick8rRUlIbXFRSTNpYklDMUxjUUdOdVpyQ1k0QTMyZ1dPbFNOVFkvK2ZGTWEzRXdhR3ppM2xpbWVVcmJ6QjhIV2syZWhUZ09ET0pSWE56ekl0MXR2MVJmMEYwek8zVUhwc3dqbkxNaHkxS1djZkN1US9XeWVDTWxTd3cvUFdaQytMZG96RnhxTmNtS1NDN0JCalJvKzl5aEVwRHBMY0N1YW85MWE5dkpXbjNta3NNdmJKV0t4SHZYR1NuVjZ5K1pFQkU4KzVQdlIwYWpvcHJJRHEvVUlYZUFsZlZSTDBhV2tUU3ZyVEgvYmtsVWZTbGtONFFucktlSGdoK2FPUkt2TWhkUjZWRTlvNUV4Y2RVUExLMXROMHZ1RGc4cjlIRGpZUkVYTmRNSWNudkNjeDNNZTFUcCtYbTFZSzByOE80T3VpWHo0T2hGdEsybHBSME9JazlMWFU0dTJiR3JTOWxiMVJvbnRKUFgyam9VSjNiMWRBQkxyeFRoSlEzNWU2eEdIeUhRQUlCQzFRcGIvenp5UC9XRVZEZWdYZnhrZDZOR21HNHVVbi9Ed1RVdDZLSWRjUFQ4TGNIMjlPL29TWDFiSksxaGh1bzRJV1ovRTN6eHRoTklTVUJDdytzcE1iTmdGZnEycVpjV1NuY2FmdVVzeEo4eHl6NS8iLCJtYWMiOiJjYTkyY2MwZTgxOTI1NmU0MGM0MzczYWIyMWJhYmI4YWRmMTUxZjFmOTVjNzFlNTI5ODUwZTc2YzkyYzRiNjg0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlpSVlcwRGMrRk1Jd3NKLzEyZkFkVkE9PSIsInZhbHVlIjoiVjhGdGVaRW4xb3l2VmUvREVFeGQ0Y0JaWkVmanJYNFZrKytGaTFpWUZTWGdXRmFiQ1FCc1hZa0lnK0RJSWtoTzd3MHkxS3NDQ0h2TjJwNGJCcjNSeTFzZjFrS0RaM25DYS9qcVpLdGpZcDYwSE9FakdXQ0xLWS8ydWNTbytvaVptOGpSQWxSMno4NHdveGJXUW5FdFZuODhPZkRMd0FDaWh5R1Y3bG00TEdSeEhFTjZWbENMK0VjYy9VZUJudUJtc3llQmQ1VXFTN1hFRjBhcS92N0UwQThLZDhLNURZWEdKTUF5SGcwbHkwcnlTbm5ydHV2eS9XOWNKbUpaVmNNRzZKL21kN3dmSDJBdE80Y3pIU09sWEh1a2hKcU8yTitwQldydHpvclVVcGk3djVyRklTZnpwS0JDY0dvZHQ1Vi9IdE4vSS80WjRnR2ZMUzYxL25KZnB3TTcwVmdMVjMzSUVDai9PeE51anljYkxCcExpRDVndytFcjk3M3J3VTdoWE9uYThwaTJlVXdoUHJkUHhhejFHUDllblRScm05WmdEazBSMmV1ZzVoM0xoU3J2cXVvbXBHWjd1M3RVSmhYWldhdklqL3N1dU1ySWFleGtrOEVqalBIZWt5VkYrS3MrRTMyZU5ZT2ZVbStzRWREWU9QRHBXVm1EeXY5RmZMSEMiLCJtYWMiOiI2ZjdhODkxZTI2ZmY4NDExOTkyMjI1YjUyZmQwY2E2Y2EwNzFlZTdjOGJhMjQ4ZmM5MGExNDU3ZjgzYjFmOTYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-723550280 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-723550280\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-77212595 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:18:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVqT2hiS1FzeC8xT0k0TUNMUzBKeUE9PSIsInZhbHVlIjoiZnVxZXFWeVZtUDFJZFZoMXpNSndWMG5iY3ZXK3Z5eDlFYWJMVEQzYnZKQW5HUW9MTkU1c29qRkk1QXBiYXVZNkRTcUw5NlhMcXpFbTIvTXgyOTdNZzdLWkUzS3I0WDJtSWg2cksvNzYvRDlOcEhRRGhlelFpaExkLzloY2hSRlA1QVcyejBOUkx2QW0xeDBpK0l2Y1RCRGZuN3NxTkRjYjJQYnZ5bXhhT29ZWXZnNGpTWDBVbm50ZWJhUU9KYXdpUnF5endjVWNoR0J0ZDdCYzdtWGdhNHlSbTBBdTUvNCtOSCtNTU5ySXRKY0xmOGwxV216VHJGTjkzeUFBaTFvc0hmY3NuMmlyY1ZYb05tTTRIejJaaW5rdU8wZlZSZEw1UmNZVnhTb1h1ekg3SkYzMnNUaWduTkJJYWpRRjJRYmVCb0lZMndZRnR6VUdoUzJ4Zkc5c1gvWjJXTHN0dDRWT3dqTFBqYzN6d0hQa2I3Ui80SitNU093Znl1WVpUQVZlMTVZS1RZaWlpbjFXazFQMHViVmVYWUlIWjhkSk1qa0RvMHZRV1FzOE8yOVlZOEZZei9MSEYyKzBZdmc1alJJczhxUjFBM29HUWNtUDQvblViZFY3TU5sSmtoNm9ZcEd5WUVCeU53ZFV3MFRtdzh3WU9uc3B1Z0xGVVd4NzBRNDYiLCJtYWMiOiJkNjJiYmQ5YzlkOWNmZDU2Nzc0NzI2YzYwYmEzOTZhNjllOTY3NTE1ZjlkOWU2MjA0ZjY3OGI1OGNiYzA2NGE5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ild5dUw0d012TGJlcTQ5ZVVXa1dDV0E9PSIsInZhbHVlIjoiU01scTAvR0ZwZE9MYjV4V1crZ1RiRDFBTEY0RjlMSzNOTFNXZ3ljWlZ4OXRURzlNd0NEbW1CVWdNOGUxeW5Fam9nWHVOSHRCbmEwMGJpNG9IWSt2UnhjQmtUcWVQK2o0YWFWZm9BVG41MlBoQ1ZyTTg2VWVWczJ6ZUxTMFV5N0dJUDRONi9mYVg2NzRxMndOaUxyUFluNm5lODVhaWVKeGZLMlZnVU5Fc0QzZWY4SmVtYTlnRyt0ZXdLMFU2VjRSUkpmV29yVThQQ3QrREtMWlBKVGJTRFNEdHVEQy8xVU02N3RmL3dOeG1YU1hmeE5IcmJRQ1d1SzVVY2RjakJudkJXMnhpYkhVcTRsamVjRE01NnJld256T2syeHZlK1pHZEJ1OW9QWFNrNVl2TWNiT3JRZnFEVXorcXg3cjhMcy9YZnlQUWNmdXp0QmhLNDhGN3R4YXgxd2k5WUxMZFlOUWN2Ujk0bDErV3MxSDVOdDNBU3NPMXlqYytKdzJXZ3BXTDlDZDUzdXVscDQ5QnhFZDI4aDZZWVdvZzBPQzI0VEg1RHVBZkdFdVAxWno5K0V3RVVpNnZCMER5Y1NFTDZkbWtka3BSTmtwUDI3ak0vdVo1MVAxd3BGdHY5aXRIblpKczBqMzBsYVdteFl0MVJTWHkrcmgwOSt3UVZwYitBblAiLCJtYWMiOiI1ZmRhYmE0OGZkMGRiZjNiODYwMDZlMmM3YWQ2MmE0MDAxZTM5ZDZmOTI2ZmY4Y2Q5NzZmMGE2MDc3NDVmYjI1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVqT2hiS1FzeC8xT0k0TUNMUzBKeUE9PSIsInZhbHVlIjoiZnVxZXFWeVZtUDFJZFZoMXpNSndWMG5iY3ZXK3Z5eDlFYWJMVEQzYnZKQW5HUW9MTkU1c29qRkk1QXBiYXVZNkRTcUw5NlhMcXpFbTIvTXgyOTdNZzdLWkUzS3I0WDJtSWg2cksvNzYvRDlOcEhRRGhlelFpaExkLzloY2hSRlA1QVcyejBOUkx2QW0xeDBpK0l2Y1RCRGZuN3NxTkRjYjJQYnZ5bXhhT29ZWXZnNGpTWDBVbm50ZWJhUU9KYXdpUnF5endjVWNoR0J0ZDdCYzdtWGdhNHlSbTBBdTUvNCtOSCtNTU5ySXRKY0xmOGwxV216VHJGTjkzeUFBaTFvc0hmY3NuMmlyY1ZYb05tTTRIejJaaW5rdU8wZlZSZEw1UmNZVnhTb1h1ekg3SkYzMnNUaWduTkJJYWpRRjJRYmVCb0lZMndZRnR6VUdoUzJ4Zkc5c1gvWjJXTHN0dDRWT3dqTFBqYzN6d0hQa2I3Ui80SitNU093Znl1WVpUQVZlMTVZS1RZaWlpbjFXazFQMHViVmVYWUlIWjhkSk1qa0RvMHZRV1FzOE8yOVlZOEZZei9MSEYyKzBZdmc1alJJczhxUjFBM29HUWNtUDQvblViZFY3TU5sSmtoNm9ZcEd5WUVCeU53ZFV3MFRtdzh3WU9uc3B1Z0xGVVd4NzBRNDYiLCJtYWMiOiJkNjJiYmQ5YzlkOWNmZDU2Nzc0NzI2YzYwYmEzOTZhNjllOTY3NTE1ZjlkOWU2MjA0ZjY3OGI1OGNiYzA2NGE5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ild5dUw0d012TGJlcTQ5ZVVXa1dDV0E9PSIsInZhbHVlIjoiU01scTAvR0ZwZE9MYjV4V1crZ1RiRDFBTEY0RjlMSzNOTFNXZ3ljWlZ4OXRURzlNd0NEbW1CVWdNOGUxeW5Fam9nWHVOSHRCbmEwMGJpNG9IWSt2UnhjQmtUcWVQK2o0YWFWZm9BVG41MlBoQ1ZyTTg2VWVWczJ6ZUxTMFV5N0dJUDRONi9mYVg2NzRxMndOaUxyUFluNm5lODVhaWVKeGZLMlZnVU5Fc0QzZWY4SmVtYTlnRyt0ZXdLMFU2VjRSUkpmV29yVThQQ3QrREtMWlBKVGJTRFNEdHVEQy8xVU02N3RmL3dOeG1YU1hmeE5IcmJRQ1d1SzVVY2RjakJudkJXMnhpYkhVcTRsamVjRE01NnJld256T2syeHZlK1pHZEJ1OW9QWFNrNVl2TWNiT3JRZnFEVXorcXg3cjhMcy9YZnlQUWNmdXp0QmhLNDhGN3R4YXgxd2k5WUxMZFlOUWN2Ujk0bDErV3MxSDVOdDNBU3NPMXlqYytKdzJXZ3BXTDlDZDUzdXVscDQ5QnhFZDI4aDZZWVdvZzBPQzI0VEg1RHVBZkdFdVAxWno5K0V3RVVpNnZCMER5Y1NFTDZkbWtka3BSTmtwUDI3ak0vdVo1MVAxd3BGdHY5aXRIblpKczBqMzBsYVdteFl0MVJTWHkrcmgwOSt3UVZwYitBblAiLCJtYWMiOiI1ZmRhYmE0OGZkMGRiZjNiODYwMDZlMmM3YWQ2MmE0MDAxZTM5ZDZmOTI2ZmY4Y2Q5NzZmMGE2MDc3NDVmYjI1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77212595\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2063954740 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2063954740\", {\"maxDepth\":0})</script>\n"}}