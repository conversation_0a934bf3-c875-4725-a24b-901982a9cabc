<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Adsense\Resource;

use Google\Service\Adsense\ListPaymentsResponse;

/**
 * The "payments" collection of methods.
 * Typical usage is:
 *  <code>
 *   $adsenseService = new Google\Service\Adsense(...);
 *   $payments = $adsenseService->accounts_payments;
 *  </code>
 */
class AccountsPayments extends \Google\Service\Resource
{
  /**
   * Lists all the payments available for an account.
   * (payments.listAccountsPayments)
   *
   * @param string $parent Required. The account which owns the collection of
   * payments. Format: accounts/{account}
   * @param array $optParams Optional parameters.
   * @return ListPaymentsResponse
   * @throws \Google\Service\Exception
   */
  public function listAccountsPayments($parent, $optParams = [])
  {
    $params = ['parent' => $parent];
    $params = array_merge($params, $optParams);
    return $this->call('list', [$params], ListPaymentsResponse::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AccountsPayments::class, 'Google_Service_Adsense_Resource_AccountsPayments');
