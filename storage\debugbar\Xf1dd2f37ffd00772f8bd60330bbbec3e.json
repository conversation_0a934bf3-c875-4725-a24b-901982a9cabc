{"__meta": {"id": "Xf1dd2f37ffd00772f8bd60330bbbec3e", "datetime": "2025-06-08 00:28:40", "utime": **********.465915, "method": "POST", "uri": "/empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342519.556141, "end": **********.46594, "duration": 0.9097990989685059, "duration_str": "910ms", "measures": [{"label": "Booting", "start": 1749342519.556141, "relative_start": 0, "end": **********.312449, "relative_end": **********.312449, "duration": 0.7563080787658691, "duration_str": "756ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.312469, "relative_start": 0.7563281059265137, "end": **********.465942, "relative_end": 1.9073486328125e-06, "duration": 0.153472900390625, "duration_str": "153ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47349592, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@emptyCart", "namespace": null, "prefix": "", "where": [], "as": "empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1655\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1655-1669</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01134, "accumulated_duration_str": "11.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.380852, "duration": 0.006719999999999999, "duration_str": "6.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 59.259}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.407539, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 59.259, "width_percent": 8.466}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.436465, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 67.725, "width_percent": 16.931}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.442555, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.656, "width_percent": 15.344}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-71910714 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-71910714\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.451281, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Cart is empty!", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-198600255 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-198600255\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1984121782 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1984121782\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2082396357 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2082396357\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-706176294 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndEQ3dZak15eHZUTGd5Ti9MY1FpQ3c9PSIsInZhbHVlIjoibnlTVnVWSXFMR2V0OEFJNmsyRFBBRjNwMjhOeG9FTStQSnZTcWg0REoyYndjelhLdzhMRlZselZMbWR4UXUzV3pDQk9uWWVoaFZEdFgwVlZaaTVoTkFNbVpVTXBnVlZ2WmRUUFNPN01Wa3JoTTIwdnFTb1M2cmdFd0hNWVptZEd3S0dSbUR1T09UMWtjb2prMzd3YjdmTlllaVEvdE5VR0lob1VEZDN5QVVyS09mYlVndWhNeGFVWjc3U1Z4ajF4MmZDYytxK01rM3hoSktSMVpOTnRKcW9wMnI2bDZJZHRQL2JTRzh3V3FoVnZ1eDh3dWRzWWpRVVVJWEU1NFlyNlZuTEUrdWY5UFVjM2NrMVFLKytKN0lRZlRneVlid1M2OERpN3VUUERkZ1d6NTgyY0RlNUUrUmlYV3FMSW42ZUYxMSt2b1hwL2JZSGgzTEx4dk9TM3NaT0VLTlR3Zi80TmRqRWo4b0NYaGVKMGRZNC9vV0phNnAvSSszQ1kwZXJQS3h5LzN4QlZMMHFia1RTZ2J4ZEphcnd1QXJiNWFEQzRIcWluemRrTzdWYVFacGd2QVNNT0hPLzVIWUlCRFA5eS9KSU1ybXpKNlpXNVZheG56ZXhqMitWWnJNVnRHRXAxVWxyQlRsL1M5QzRwbjhFT0k3akQxa09Sdm0vemFoaGQiLCJtYWMiOiIyZDJhNjI4ODE1MGYyOTkxOTliZTJkMWNlZDlhY2Q4Y2FlMGNhOWQ2NjU4NmVkNjdlNGUxMTVkOTdjZDliZjZlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InZORTNNd0prcnFtejMyVGw3d2hLZFE9PSIsInZhbHVlIjoicmhRL3lUbTVmUkw0dmQrSUQvWHBSbmF4WkZGcWN0VkVUMGYwcEx5MWxUaFRMQ1hwRTgyMXlwT0oxQjlWUEN5QXRCWDNhYmRhUGhIaEk0bHRIdHpTcnZmRisvWXMyM01lR0pvTWlYb1FPMmt1SFV5WHlRdDgxbHIzR29pN0ZKVzRJYnI0b1U1K1YwNENzK1crWnI0Z1grRnBFdDliUGkzMmV3dzZMSkZMMXZnRWtSL01JMVpCTGQrVzM5T1lFRXpBdDBMU3NCL3F0NktBMG53OW5YNFdTSTF5a0NObUxvK29QNHlJQitlNDBzNUxheHVibzhlc1JLZnNZdFdYRzhPTUtLWTB5VFFDVXo2QTZjTlJ1bmEvY05od1lwVDVBTHRhV2xhZmFDOUxzYi9Md0V2L0lwS0dlYXg2VDAzWTdWRHJmL091ZVpaK3JpZXNWS3JKVmxNa25RQUJ6dHRvSVZiVDRRZW5RNXVGVDh2aW1rNVYrb3ZPQzN0UFE5ZDJmYTg2dUd3VTBCQ3lTcWFwanR4Q0VXbVpqajJwK2dwdHdZdHplRkt3bVBUcW02RFZCUFRud2h6OFMvZFlnRUNoUFBZaHlMZCtUck5ETXlZY1pXQUM5em93bFVBcHlmYm1QOFpFU2p1cE1JTHhVRDk2bWFwSGoxb3JDM1oyYjJ1VGNRaFQiLCJtYWMiOiI2NzYyYTkwZGQ3MjFhNjhjM2EzOGUwZTBmZmQ4MDhmYjNkMjEwOThiZWMwOWJjMmQ2NDI5MWViMWQwYzM0ZDYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-706176294\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1887512066 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1887512066\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1373432658 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:28:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNSbU5DTlY5ejdXWkdMa2N3TWxHTFE9PSIsInZhbHVlIjoiU1ZuYk12MldTVkZ1b3IyclZ4R1pjNVkrTDEzeDhKcFhMY295bzBuT25tL2g3cVdPU1c5NDJBMzRobFFWU1BIdTUxM0E4cjh1WEFmRzROZGRxMWpMTVc5azdQaS9KVUhQcDFqajh4bVlFS0JxSjFMd3BYNWtVUW1SU1J6aUI3M21UUTc0ODVQNmZLU3F1Ti9KMndXTm9iTTlOMDlFS2Irek5EMHBGU2xaY0F3YTBURDRMZXRHbTNNL3ZobGVnZGZ0UGlqbWdnRFhUZ1piTXhYUllVaXFpdVlJNWRjNi9kb3NJaU9SbWZEalQ4S3h3bUtSdmlRa3pNZ2QyMVVLclQ1c2hLdWx6V2NUTlNWamM4b2dLSXBhSDBKcm9BMDFVV2tnMTBSU21oYlM4VXF4dGwvY1VpTVl6ajZLcVYrdHhRenRDb1ArNTlmVGVEQXZrL3FvWUhMU0QzdmNTM3c5aVlCa1h3clI1cXRZcWVQS3llR1llUXVzdW9Rdk1xRjF1Q25zUjFud2paRVB6Q25CaGdJNXFzTWpoeXEzM1NPTUJrOUxXdVZLVTFnQ0tTc0xxOGtFa3FqbkwwVU1ocmVDNmF0dUk5UGFFSkxiVHF2ek9DRC9rcjhnSGxSOEFMZTZGY01ZR2krdk1wQmJCRTgzaXF3MHFjTGUxbjFGU09yR09yV3YiLCJtYWMiOiI5YzRhZDg1Mzc1NzIxZTMzMzQ0OTAwOTMyYWVkZmNjMjA3NDczYjdmNjk2Yzk5MWQ3ZGI1OWMzMGU1MTMzOTgzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNnR2hsMEZFYXhDSVg3UHdieWNrRmc9PSIsInZhbHVlIjoiRkFsdDVPMk1raTZFUUNnQ2IwWVd3cEM5bjVsT0NlRzJNSGVaN2NsUmdyaUpiaFcwdjQrNGt0R1p5aXdkSVI2TkllTGpkSHpQdWQ2ODQ1VlIrN25ENzlwd1RzblZGYVljdTg2OU9FQkxXVFZOZG9Qc1FvMWZsNDhkTy9mVmRCdjJWZVlLd1Jxek5yMTduYk41bGs1a0N5azZJaFNleGxnTUJNS2FQQjBsMjRtc1NTUUt3cklaWmNHWFdiLzFjQnFKaml2UDhYbTd4Nmp5cTdVSXJqVDFvZ24xSnpvQjFGWENQTi9GSy9VSWZpZUxvbmdHNXcwQjlncW05ajVaZHdac28waWhwR09rU0lVaWJCN3dVaUVjNDFWeVhRRXV6Rk9paDU1K3Z3LzZ0Z3UybE5yV204Yjk1eGd5YjhGVTVnKzRZR2VBSlppb2daTTZXc0tiY3Z2NXBwMGRVeHppTXY5Zno5eHlieGxUUm9YVUkvdkw4TVhucmlBSllPcDZsUForenJucEpXUmsvS0dDaDF5NzQrdGRrQ05CMjFGNmloWlMxelJ5TGUzWitXTnJTRlZPNkdFcnFPRFJNSndFTmZnQmJ3M256bEptRk5uWGsya2FwTTVmYXpsS1pzVW52cm1vclplNDNNUDFqMFpWeXdoRTF3N3V6TzUzWDBLUngxN1YiLCJtYWMiOiIxOWI1ZjliYjVhYTk2YTUzOTc5YWUzZGY5ZTliMDg4NGY2ZDE5ZjU1YzliNDgwNjdhMWEzOWYwYTk2ZDYzNTU0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNSbU5DTlY5ejdXWkdMa2N3TWxHTFE9PSIsInZhbHVlIjoiU1ZuYk12MldTVkZ1b3IyclZ4R1pjNVkrTDEzeDhKcFhMY295bzBuT25tL2g3cVdPU1c5NDJBMzRobFFWU1BIdTUxM0E4cjh1WEFmRzROZGRxMWpMTVc5azdQaS9KVUhQcDFqajh4bVlFS0JxSjFMd3BYNWtVUW1SU1J6aUI3M21UUTc0ODVQNmZLU3F1Ti9KMndXTm9iTTlOMDlFS2Irek5EMHBGU2xaY0F3YTBURDRMZXRHbTNNL3ZobGVnZGZ0UGlqbWdnRFhUZ1piTXhYUllVaXFpdVlJNWRjNi9kb3NJaU9SbWZEalQ4S3h3bUtSdmlRa3pNZ2QyMVVLclQ1c2hLdWx6V2NUTlNWamM4b2dLSXBhSDBKcm9BMDFVV2tnMTBSU21oYlM4VXF4dGwvY1VpTVl6ajZLcVYrdHhRenRDb1ArNTlmVGVEQXZrL3FvWUhMU0QzdmNTM3c5aVlCa1h3clI1cXRZcWVQS3llR1llUXVzdW9Rdk1xRjF1Q25zUjFud2paRVB6Q25CaGdJNXFzTWpoeXEzM1NPTUJrOUxXdVZLVTFnQ0tTc0xxOGtFa3FqbkwwVU1ocmVDNmF0dUk5UGFFSkxiVHF2ek9DRC9rcjhnSGxSOEFMZTZGY01ZR2krdk1wQmJCRTgzaXF3MHFjTGUxbjFGU09yR09yV3YiLCJtYWMiOiI5YzRhZDg1Mzc1NzIxZTMzMzQ0OTAwOTMyYWVkZmNjMjA3NDczYjdmNjk2Yzk5MWQ3ZGI1OWMzMGU1MTMzOTgzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNnR2hsMEZFYXhDSVg3UHdieWNrRmc9PSIsInZhbHVlIjoiRkFsdDVPMk1raTZFUUNnQ2IwWVd3cEM5bjVsT0NlRzJNSGVaN2NsUmdyaUpiaFcwdjQrNGt0R1p5aXdkSVI2TkllTGpkSHpQdWQ2ODQ1VlIrN25ENzlwd1RzblZGYVljdTg2OU9FQkxXVFZOZG9Qc1FvMWZsNDhkTy9mVmRCdjJWZVlLd1Jxek5yMTduYk41bGs1a0N5azZJaFNleGxnTUJNS2FQQjBsMjRtc1NTUUt3cklaWmNHWFdiLzFjQnFKaml2UDhYbTd4Nmp5cTdVSXJqVDFvZ24xSnpvQjFGWENQTi9GSy9VSWZpZUxvbmdHNXcwQjlncW05ajVaZHdac28waWhwR09rU0lVaWJCN3dVaUVjNDFWeVhRRXV6Rk9paDU1K3Z3LzZ0Z3UybE5yV204Yjk1eGd5YjhGVTVnKzRZR2VBSlppb2daTTZXc0tiY3Z2NXBwMGRVeHppTXY5Zno5eHlieGxUUm9YVUkvdkw4TVhucmlBSllPcDZsUForenJucEpXUmsvS0dDaDF5NzQrdGRrQ05CMjFGNmloWlMxelJ5TGUzWitXTnJTRlZPNkdFcnFPRFJNSndFTmZnQmJ3M256bEptRk5uWGsya2FwTTVmYXpsS1pzVW52cm1vclplNDNNUDFqMFpWeXdoRTF3N3V6TzUzWDBLUngxN1YiLCJtYWMiOiIxOWI1ZjliYjVhYTk2YTUzOTc5YWUzZGY5ZTliMDg4NGY2ZDE5ZjU1YzliNDgwNjdhMWEzOWYwYTk2ZDYzNTU0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1373432658\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1068399750 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Cart is empty!</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1068399750\", {\"maxDepth\":0})</script>\n"}}