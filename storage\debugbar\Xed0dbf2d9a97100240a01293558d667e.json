{"__meta": {"id": "Xed0dbf2d9a97100240a01293558d667e", "datetime": "2025-06-07 23:13:24", "utime": **********.53337, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749338003.509199, "end": **********.533398, "duration": 1.0241990089416504, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": 1749338003.509199, "relative_start": 0, "end": **********.406427, "relative_end": **********.406427, "duration": 0.8972280025482178, "duration_str": "897ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.406445, "relative_start": 0.8972461223602295, "end": **********.533401, "relative_end": 3.0994415283203125e-06, "duration": 0.12695598602294922, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45039936, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0062699999999999995, "accumulated_duration_str": "6.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.473915, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 66.188}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5002139, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 66.188, "width_percent": 14.354}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.514584, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 80.542, "width_percent": 19.458}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1640162622 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1640162622\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-612495299 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-612495299\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1656130072 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1656130072\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1421955099 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749337992403%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImVmZDhtVkQ3WWxQblM3L2pERjZmaVE9PSIsInZhbHVlIjoicFdpRGtkMGZHa0h0UmFGdGpCT2IrK0dCa1NTdmdwOXJ5QVdOVW9qNG50SzVOMVlOQllTalZCaVNJT290N25NUm9jdUpDcHJKTVVvbHpvZnVQRzNZRUVmSjdvOHF6aUxoZ1JBUUVHM0VoRHlQQXk1WnpQanhDZUNnR003TGZ0VXF5bGJMd3RwM0wrZVlTcERHbTJrbldGQzZ4d0g2ZHVFajZBeE4rQ0U5TzBWUVZDemNndGRvZGtEV0xlUXpINThtSzYrYVMrRC9LRDFxWjc0K2dnRzgrdWEvdFFzT29HeWhVelNxdjg0TThqdzMwNWhlZFF1b29VOW1nMXJROXRLYTZINGl4djBaMGtvNFQrb1BZRDVteEk3aU9jQ3lKcWNBZVpRVytodkRVMmtPOVJYTlhGbGpERkpET3JZN0QxYklzSHZSZjRFQm1DZVJjTndtSkhxSGlwbDdMTERZSi9rUEw0eEZtcm5qMlhxSWFqeVdEa2wvcnQ3bjRoR05ESjRPRW8yajRTcEorYTA2eHB5Q0VlcnNZT1gveng3WkZwa0JPRGxXL0lWK1M4enZFTlZMakdiZEFWbEVJQkx5TFk0MUYzWnY2aG9tUndobzJTSjJKSG9uSnRhMkdLN3VIdnIrN3A5YzZKMnhFQ2tIVFZ5TGNndGFEeHZzVWtCeVMwS3ciLCJtYWMiOiI2NDc1NzQ2Mzk2MDBhMmU3NzI0MzMwZDIzNWMwNmQyYmRhOTY5YmQ2NjAyOGVjYjczMTQ3N2ZmYjgyOTNjNTlmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkxVaXIxUElwSlZhQUJBZFlsZXk5dXc9PSIsInZhbHVlIjoiTTUyL1hQMVlsYWgvSkh3OTdlZDlUQSs4NTlsV1BTenRjNGJmU3QrL1NuREtreitsYWRZMHJoZlJNbWdYVWp5aWZGSGVOdnFYY0J1b25ScXlYaXFLRFNGL0kvUmVuV3RXbHk2dXN2T2lWU1BxaUN1Ti9XSldEMU9WK2E3L0hpRVNzckF4anBjUE5ZYWdKazRmbEs0UWhmVGE0d1hlNU1ORXZQaERXUUVvUUFjSzhMM0VtUzBjYTEyeHNidnBsUjQycmZGbjcxcU9PTnA0bFUrMmdFU0h1aFFXdEpRTEdsbHBMR3E2T2VmSXA3dmxTa0V2YkZyVUtBb2V2L2FYaENJMzRGVmlLVXlEbmVLUlBlZjdUUDdzNFBWNk9IWFU3KzZvUm9uSm40NE0yblF4TGx5MlAvc1FzMFM2STdBL0ZWd0RHNnBEczZRQnU1aWY4dUo3YjVuYjRQNmkwS21lTmhBVXRIYklHci9OdnZQa0Jyd04vOFJGb3JiWThKbCtpUi9JT1FsZVNpMTUvS0JtUElLTytEdUdzS3RpN0ZjWkNBL3lTbzE1WDFaazgwM2RJYWVhY0ZoVzFER1FyM3pWd0FzMVBSSGpjbG9MRVFTNEMwWjFDbkx0WnlwSHhqWTlCeFE4Q1RzOG5Sa3h2QnFtb2tNWWdNK28vQnNLUDB2WFdSWk4iLCJtYWMiOiJlZTU5NzNiM2I4ZThkZDQxYzdhMTBhZWQ4MTM0MWQ5ZjUxMjI4NGRjYmYwNjkzM2NjNGQyMThlYjIyZTBmMDFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1421955099\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1218150864 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218150864\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-182139986 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:13:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlIvMlJhTzJyUUxFS21vZHFVMExYQVE9PSIsInZhbHVlIjoiVEJKOFovbENVbEpudVRZUkdwc0srQTNVZ3RmS3pLNW5hR2NYYmYySkpPYlpwMVUrdGZtSnY5MnFCRGpiWkNuOC9pSUpHdEJjSTl0R3ZKdk42eGdKRFRoVVN3UENGQmVQamM3cUM4V2lqWmt5R0h3eU81azZIUWRBMlFhMGI4Z3FaYVgrdHI5aG02S0VMckl1R0w4VDFnNDRTdUV6bDFrV3kwMlh4ajRydFg0MTY3YVBnbEp3QmJQUEVORklwNzNPNzVsemU1eVNvTmRPTFptRGlNNHIzTUx4QlNlYndyT2VLZ1lrMzZjT0YySnF4Q3BXTi9xSnE5RXVORW92MStxZEhERmVta01YVXArcUkxTjRKQWRWYjdWNnFTODBZWng3OGxvaXArQXBraklmRmZoZ3FVQU5hVFZJSmhLZkQ1VjhuZThNSC9ONklRLzZ4T0dRZnpEVis1eHluSEU5Y1owV1JseUd1QjhNQWo1MmVJbVhSREw3U2x3MXNSd2h4Qk01eDZyVnBPMlBSMjdoTEg0bDcrUjNhWkQwTUwva05wRGxWZEkxaTAxRStXRzN6M21JbkhEa1g5VWhjcUxkVGZDV3c0YXZFZ3NOektKcmxPWitFcTRIeHZWcko5RUVZclNiSWIrOHE1Zm5MZlplV29veEdKd3BxM1Bxa0NGVVN6U1MiLCJtYWMiOiJhZTA1ODQwZDQ0N2VkNDE5YmFmMDI1YTUyOTEzYjc0NzA5NzY5YjVlZjg2NmE3NTlhNTg5ZjgzMjNiNjYzYmIxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:13:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImcrQnpKM05zWGdBRDlad09WUkd1emc9PSIsInZhbHVlIjoid25UTFhoVjBqZ0QzMzhSQ3VUZGlwdmZ1UEczUldIUUN5RFB1UTVZQXRwNHlCWkxUSE1CNUFIRUQvQXZ6R1FqSXFCVnF1cXdCNkZvOWszL1dkOHk5aHRVYVdEYkZXOG1Zc3JPYXl4UWh4VDBMNVdORWk1OENUenRDKzhrZG5tTnpkSlFVYTlMcjBUMXlKQmhwdjM5RE9zSDVTVXVNRGs1SXdVcTFXajhjeVExK0FWZzdFbjJKQlEwQUdPeTNKcWtJVFNjZHVLQWZpVlQ4anArendJclUzTWk4azVBMjhBRU54clphMzFRL3dsZ3BBNnNHZ0RFS0gvaVJiKy9iQ1pscW55TTFBa2FSR0RsWW5ZbWwwY0EyZ1c4Q2hiVjJQNDRDcFhPNVRrZFo0OHZkTEJXVjFQMy9KcWozMFFjNWZocXFjMkU3UFh1Lzd1aFhtUmJKQkl3V0RZYTZNYldRaU9HTjBXWkMvZjhLODNYejlvcUF3NHgycCtsNXNxeUw2ZlVNTFZYUXdsNnllQUVSeEhSTlpHT0JnYVNzTCtselZQUTc4WktqWXFxbzdERUNTb2NFVmZIRTlYTXd6cUk1YlVnRmNiejIyTFJHWEZSa1FwTnNFTVpwRVk4aHpmQitVeTlZZVVHWFJ5M0VlVnNtb1QyYS9Eb21xM2Y1OVBFQ0M2ek0iLCJtYWMiOiI0NTNiMjZmYjM2ZGQ5YTNmMTU1ZTkyMWRiZDNjZjE2ODdmYTI4ODI1OWUzNTRmYThjYzJmMzE3NDljNmY3OWY2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:13:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlIvMlJhTzJyUUxFS21vZHFVMExYQVE9PSIsInZhbHVlIjoiVEJKOFovbENVbEpudVRZUkdwc0srQTNVZ3RmS3pLNW5hR2NYYmYySkpPYlpwMVUrdGZtSnY5MnFCRGpiWkNuOC9pSUpHdEJjSTl0R3ZKdk42eGdKRFRoVVN3UENGQmVQamM3cUM4V2lqWmt5R0h3eU81azZIUWRBMlFhMGI4Z3FaYVgrdHI5aG02S0VMckl1R0w4VDFnNDRTdUV6bDFrV3kwMlh4ajRydFg0MTY3YVBnbEp3QmJQUEVORklwNzNPNzVsemU1eVNvTmRPTFptRGlNNHIzTUx4QlNlYndyT2VLZ1lrMzZjT0YySnF4Q3BXTi9xSnE5RXVORW92MStxZEhERmVta01YVXArcUkxTjRKQWRWYjdWNnFTODBZWng3OGxvaXArQXBraklmRmZoZ3FVQU5hVFZJSmhLZkQ1VjhuZThNSC9ONklRLzZ4T0dRZnpEVis1eHluSEU5Y1owV1JseUd1QjhNQWo1MmVJbVhSREw3U2x3MXNSd2h4Qk01eDZyVnBPMlBSMjdoTEg0bDcrUjNhWkQwTUwva05wRGxWZEkxaTAxRStXRzN6M21JbkhEa1g5VWhjcUxkVGZDV3c0YXZFZ3NOektKcmxPWitFcTRIeHZWcko5RUVZclNiSWIrOHE1Zm5MZlplV29veEdKd3BxM1Bxa0NGVVN6U1MiLCJtYWMiOiJhZTA1ODQwZDQ0N2VkNDE5YmFmMDI1YTUyOTEzYjc0NzA5NzY5YjVlZjg2NmE3NTlhNTg5ZjgzMjNiNjYzYmIxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:13:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImcrQnpKM05zWGdBRDlad09WUkd1emc9PSIsInZhbHVlIjoid25UTFhoVjBqZ0QzMzhSQ3VUZGlwdmZ1UEczUldIUUN5RFB1UTVZQXRwNHlCWkxUSE1CNUFIRUQvQXZ6R1FqSXFCVnF1cXdCNkZvOWszL1dkOHk5aHRVYVdEYkZXOG1Zc3JPYXl4UWh4VDBMNVdORWk1OENUenRDKzhrZG5tTnpkSlFVYTlMcjBUMXlKQmhwdjM5RE9zSDVTVXVNRGs1SXdVcTFXajhjeVExK0FWZzdFbjJKQlEwQUdPeTNKcWtJVFNjZHVLQWZpVlQ4anArendJclUzTWk4azVBMjhBRU54clphMzFRL3dsZ3BBNnNHZ0RFS0gvaVJiKy9iQ1pscW55TTFBa2FSR0RsWW5ZbWwwY0EyZ1c4Q2hiVjJQNDRDcFhPNVRrZFo0OHZkTEJXVjFQMy9KcWozMFFjNWZocXFjMkU3UFh1Lzd1aFhtUmJKQkl3V0RZYTZNYldRaU9HTjBXWkMvZjhLODNYejlvcUF3NHgycCtsNXNxeUw2ZlVNTFZYUXdsNnllQUVSeEhSTlpHT0JnYVNzTCtselZQUTc4WktqWXFxbzdERUNTb2NFVmZIRTlYTXd6cUk1YlVnRmNiejIyTFJHWEZSa1FwTnNFTVpwRVk4aHpmQitVeTlZZVVHWFJ5M0VlVnNtb1QyYS9Eb21xM2Y1OVBFQ0M2ek0iLCJtYWMiOiI0NTNiMjZmYjM2ZGQ5YTNmMTU1ZTkyMWRiZDNjZjE2ODdmYTI4ODI1OWUzNTRmYThjYzJmMzE3NDljNmY3OWY2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:13:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-182139986\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2147254085 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2147254085\", {\"maxDepth\":0})</script>\n"}}