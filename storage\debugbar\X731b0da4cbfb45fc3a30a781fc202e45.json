{"__meta": {"id": "X731b0da4cbfb45fc3a30a781fc202e45", "datetime": "2025-06-30 15:34:34", "utime": **********.973193, "method": "GET", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.271194, "end": **********.973209, "duration": 0.7020149230957031, "duration_str": "702ms", "measures": [{"label": "Booting", "start": **********.271194, "relative_start": 0, "end": **********.649398, "relative_end": **********.649398, "duration": 0.3782041072845459, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.649411, "relative_start": 0.3782169818878174, "end": **********.973211, "relative_end": 2.1457672119140625e-06, "duration": 0.32380008697509766, "duration_str": "324ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53329160, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "1x pos.financial_record.index", "param_count": null, "params": [], "start": **********.743558, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/financial_record/index.blade.phppos.financial_record.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.index"}, {"name": "1x layouts.admin", "param_count": null, "params": [], "start": **********.92223, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php&line=1", "ajax": false, "filename": "admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.admin"}, {"name": "1x partials.admin.menu", "param_count": null, "params": [], "start": **********.926444, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.phppartials.admin.menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.menu"}, {"name": "1x partials.admin.header", "param_count": null, "params": [], "start": **********.952175, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.phppartials.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.header"}, {"name": "1x partials.admin.footer", "param_count": null, "params": [], "start": **********.963334, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/partials/admin/footer.blade.phppartials.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.footer"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.965749, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}, {"name": "1x Chatify::layouts.footerLinks", "param_count": null, "params": [], "start": **********.966138, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/vendor/Chatify/layouts/footerLinks.blade.phpChatify::layouts.footerLinks", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FfooterLinks.blade.php&line=1", "ajax": false, "filename": "footerLinks.blade.php", "line": "?"}, "render_count": 1, "name_original": "Chatify::layouts.footerLinks"}]}, "route": {"uri": "GET pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@index", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=33\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:33-84</a>"}, "queries": {"nb_statements": 21, "nb_failed_statements": 0, "accumulated_duration": 0.016539999999999996, "accumulated_duration_str": "16.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.688075, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 11.306}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.698623, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 11.306, "width_percent": 3.023}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 9 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7018921, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:37", "source": "app/Http/Controllers/FinancialRecordController.php:37", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=37", "ajax": false, "filename": "FinancialRecordController.php", "line": "37"}, "connection": "kdmkjkqknb", "start_percent": 14.329, "width_percent": 2.721}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.715759, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 17.05, "width_percent": 3.083}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.717565, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 20.133, "width_percent": 1.632}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 9 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 138}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.722892, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:138", "source": "app/Services/FinancialRecordService.php:138", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=138", "ajax": false, "filename": "FinancialRecordService.php", "line": "138"}, "connection": "kdmkjkqknb", "start_percent": 21.765, "width_percent": 1.753}, {"sql": "select * from `financial_records` where `shift_id` = 44 and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["44"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 148}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.724839, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:148", "source": "app/Services/FinancialRecordService.php:148", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=148", "ajax": false, "filename": "FinancialRecordService.php", "line": "148"}, "connection": "kdmkjkqknb", "start_percent": 23.519, "width_percent": 2.056}, {"sql": "select * from `shifts` where `shifts`.`id` in (44) and `shifts`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 148}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.727152, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:148", "source": "app/Services/FinancialRecordService.php:148", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=148", "ajax": false, "filename": "FinancialRecordService.php", "line": "148"}, "connection": "kdmkjkqknb", "start_percent": 25.574, "width_percent": 1.693}, {"sql": "select * from `voucher_receipts` where `created_by` = 22 and `shift_id` = 44", "type": "query", "params": [], "bindings": ["22", "44"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.729409, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:59", "source": "app/Http/Controllers/FinancialRecordController.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=59", "ajax": false, "filename": "FinancialRecordController.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 27.267, "width_percent": 15.175}, {"sql": "select * from `voucher_payments` where `created_by` = 22 and `shift_id` = 44", "type": "query", "params": [], "bindings": ["22", "44"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 60}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.73367, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:60", "source": "app/Http/Controllers/FinancialRecordController.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=60", "ajax": false, "filename": "FinancialRecordController.php", "line": "60"}, "connection": "kdmkjkqknb", "start_percent": 42.443, "width_percent": 12.817}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9227571, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 55.26, "width_percent": 2.66}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.92463, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 57.92, "width_percent": 2.116}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9293811, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 60.036, "width_percent": 2.66}, {"sql": "select * from `email_templates` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\EmailTemplate.php", "line": 27}, {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.9312801, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "EmailTemplate.php:27", "source": "app/Models/EmailTemplate.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=27", "ajax": false, "filename": "EmailTemplate.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 62.696, "width_percent": 1.693}, {"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\User.php", "line": 1300}, {"index": 17, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 13}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.932798, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "User.php:1300", "source": "app/Models/User.php:1300", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1300", "ajax": false, "filename": "User.php", "line": "1300"}, "connection": "kdmkjkqknb", "start_percent": 64.389, "width_percent": 2.237}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.934784, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "kdmkjkqknb", "start_percent": 66.626, "width_percent": 2.418}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 3}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.952627, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 69.045, "width_percent": 3.628}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.954509, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 72.672, "width_percent": 20.193}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.9593468, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 92.866, "width_percent": 1.572}, {"sql": "select count(*) as aggregate from `ch_messages` where `to_id` = 22 and `seen` = 0", "type": "query", "params": [], "bindings": ["22", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.961239, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "partials.admin.header:18", "source": "view::partials.admin.header:18", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=18", "ajax": false, "filename": "header.blade.php", "line": "18"}, "connection": "kdmkjkqknb", "start_percent": 94.438, "width_percent": 2.781}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 6214}, {"index": 15, "namespace": "view", "name": "partials.admin.footer", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/footer.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.963742, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 97.219, "width_percent": 2.781}]}, "models": {"data": {"App\\Models\\Shift": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\FinancialRecord": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FFinancialRecord.php&line=1", "ajax": false, "filename": "FinancialRecord.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 41, "messages": [{"message": "[ability => show financial record, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1875265699 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1875265699\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.720832, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-195465693 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-195465693\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.721943, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-498205532 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-498205532\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.722733, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.93678, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => null,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.93702, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.937217, "xdebug_link": null}, {"message": "[ability => manage report, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1630639593 data-indent-pad=\"  \"><span class=sf-dump-note>manage report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1630639593\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.937918, "xdebug_link": null}, {"message": "[ability => show pos dashboard, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1470261638 data-indent-pad=\"  \"><span class=sf-dump-note>show pos dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show pos dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1470261638\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.938081, "xdebug_link": null}, {"message": "[ability => manage employee, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-784869161 data-indent-pad=\"  \"><span class=sf-dump-note>manage employee</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-784869161\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.938659, "xdebug_link": null}, {"message": "[ability => manage setsalary, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1888071630 data-indent-pad=\"  \"><span class=sf-dump-note>manage setsalary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage setsalary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1888071630\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.939576, "xdebug_link": null}, {"message": "[ability => manage customer, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-143948828 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-143948828\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.939829, "xdebug_link": null}, {"message": "[ability => manage vender, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-582731980 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582731980\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.940045, "xdebug_link": null}, {"message": "[ability => manage customer, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1296106394 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1296106394\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.940256, "xdebug_link": null}, {"message": "[ability => manage vender, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-775660049 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-775660049\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.940486, "xdebug_link": null}, {"message": "[ability => manage proposal, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage proposal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage proposal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.940797, "xdebug_link": null}, {"message": "[ability => manage bank account, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage bank account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage bank account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.941045, "xdebug_link": null}, {"message": "[ability => manage bank transfer, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage bank transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">manage bank transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.941279, "xdebug_link": null}, {"message": "[ability => manage invoice, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.941462, "xdebug_link": null}, {"message": "[ability => manage revenue, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2013646006 data-indent-pad=\"  \"><span class=sf-dump-note>manage revenue</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage revenue</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2013646006\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.941688, "xdebug_link": null}, {"message": "[ability => manage credit note, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-107993238 data-indent-pad=\"  \"><span class=sf-dump-note>manage credit note</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage credit note</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-107993238\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.941948, "xdebug_link": null}, {"message": "[ability => manage bill, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1550062788 data-indent-pad=\"  \"><span class=sf-dump-note>manage bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1550062788\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.942182, "xdebug_link": null}, {"message": "[ability => manage payment, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-424313150 data-indent-pad=\"  \"><span class=sf-dump-note>manage payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-424313150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.942413, "xdebug_link": null}, {"message": "[ability => manage debit note, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage debit note</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage debit note</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.942668, "xdebug_link": null}, {"message": "[\n  ability => manage chart of account,\n  result => null,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage chart of account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.942955, "xdebug_link": null}, {"message": "[ability => manage journal entry, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage journal entry</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">manage journal entry</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.943249, "xdebug_link": null}, {"message": "[ability => balance sheet report, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1415569258 data-indent-pad=\"  \"><span class=sf-dump-note>balance sheet report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">balance sheet report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1415569258\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.943558, "xdebug_link": null}, {"message": "[ability => ledger report, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1867941811 data-indent-pad=\"  \"><span class=sf-dump-note>ledger report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">ledger report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1867941811\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.943862, "xdebug_link": null}, {"message": "[ability => trial balance report, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2014801356 data-indent-pad=\"  \"><span class=sf-dump-note>trial balance report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">trial balance report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014801356\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.944167, "xdebug_link": null}, {"message": "[ability => manage user, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-423636663 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-423636663\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.944378, "xdebug_link": null}, {"message": "[ability => manage role, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1966994598 data-indent-pad=\"  \"><span class=sf-dump-note>manage role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1966994598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.944566, "xdebug_link": null}, {"message": "[ability => manage client, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1179467215 data-indent-pad=\"  \"><span class=sf-dump-note>manage client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1179467215\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.945031, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-170839763 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-170839763\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.945641, "xdebug_link": null}, {"message": "[ability => show warehouse, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1824454731 data-indent-pad=\"  \"><span class=sf-dump-note>show warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">show warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1824454731\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.946437, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1796903742 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1796903742\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.947206, "xdebug_link": null}, {"message": "[ability => show pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-792865589 data-indent-pad=\"  \"><span class=sf-dump-note>show pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">show pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-792865589\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.948014, "xdebug_link": null}, {"message": "[ability => create barcode, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1775580470 data-indent-pad=\"  \"><span class=sf-dump-note>create barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1775580470\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.948957, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1670160758 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1670160758\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.949762, "xdebug_link": null}, {"message": "[ability => manage warehouse, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.950848, "xdebug_link": null}, {"message": "[ability => manage company plan, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1348515844 data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1348515844\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.951562, "xdebug_link": null}, {"message": "[ability => manage order, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.951818, "xdebug_link": null}, {"message": "[\n  ability => manage company settings,\n  result => null,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage company settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.951985, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-473235210 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-473235210\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-236439493 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-236439493\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-112276697 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C**********054%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilg5bUlKOXhmN0xRUlc2bG5lVGkxMEE9PSIsInZhbHVlIjoidm5ndWxaU1JrTjZocWdROWo5dXRWdWl3dm4xOHFrWUg1cy8wb0FWVlNDcDRuT3NyaDJyZzlvSERSZ3U0QlRGTllIT05iZjBBZlJud05neWF6c2xUeHpJQk8vL2hDQ0RUQWhIQmphMDIzNDZaRHJBR2dVQ00vQVN4SFQvb1hKdHpxNHF1Uk8yVlozRXJTRlZVVVk1bmhIb2FLQmg5MkhBWHRVdGJPYS8xVkdtei9BM0toM2xna2NuSWtPMTErYUJycUJnL2tTVjJDcFlhaVNGVW01aFlERjNYdWpiUzU2S2swTWU4MzNtQi9yZnY5aDFML2pHMExKeE5wMENxWTBrZGlLV2dveHljNlNCaVJ1OExraWdDbkEvZUt2WmtWZ211MUIrVkhvVzAvTkpxMFRacEI3eVplQTh3UkJBcExMRERuTDZIZld2T0NsdEptWVVIN3diMm1rMFVoTE9YNFQ3R0hCQXR4eERRSEZPeHU0OW9IQWtkN1dPOFFmV0E1QnljYjNORWptc1E4dGgvRHR6VWZZRDdZOWZmdTdoZlA3aUxYRHRjRWFqdEsxS2I0alF4L0p3TTByd3MvVi9oVEtFamJDL0F1WlVPUWUwWTFOK1puU3o5Z1ZGR2dFdVdMdTI5c3VjUWd3eEtnZURZQlZuQWtvY3ZZRTlTUXAyZEFvZ1AiLCJtYWMiOiI1YzliNDgwNzk2YjVmZGJmYzlhMjA2NWMyMGFkZjBjNmRhZGNlMjZkZDA5YTUyM2EyMDI2MWFkOTE2ZTNlNjYyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im9jeCtlWGdJWkpZdWNrdDhlS2tacXc9PSIsInZhbHVlIjoicjZ0NldGRC9EOVdBVlZueFpIOGFaRFR2Z2V6WHg2RFZOWmZES0tSOTVFZzZlSEU4WXBhd0ovZlJMZnVUdWJEeEFEZlA3a29zdGlLSzl4azJkUGtYeUVLL2dWdklaWk12NndVM1pvYlRqbzlVR1c0WnM3dCtzOVNlVEttdWRoWTB3em1jYlBvb0xYY1lZOGljYzQ5aFhJR0NzeUI1dTVmN3dnanhKdld3eU9XV2Y3aFpkS0QzYnFIUldldnZXNUdHOTFMZjNRbHNTay9SSXo3dk0wWk8wUEFHQU9ZRlFueVBZcXFPUm1JTU5nSEJYTm9HMDVhZmpyVnhmcURNZ1R4ekJUUVEvaWo3dUZtQjFpeVV1WVR5d3lGSE9aQjhtTmJaaVdOV1Y3eHY4bWx4ajQ0Y3JpbENxbjJscVdVTDRLYmFUeXJsT05YMkdUY2t2cVVhUzFSS1hyeW9NZDdueE9oUng0RHpzdnY3ck9QdWJsM1BCa2ZXeHFMSk4xeG5NSDJLblAya3BOTjFxbVFNT0xQYXNnQkJLOThITzI2N1E1cTRQeWhpa0kvVVFWTmZsdmlLNm14WUllTnZaNVY2dFB6cHFaSHpGZ0s3UUNzMHZUSTh6KzJmeGxKN1pNRVZIemYxckhLQmdMV3phdGYzNlJ4QTlEUVVlTFhMOVUrSGQ0d3MiLCJtYWMiOiIyMzRlMWQ5MDhkNGIxZWZiNjU1ZTc1M2Y4ZDQ3N2QzYTA0MWRiM2FjZjFjNWQ5YmI3NDYxODYxNTJmNTliNDMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-112276697\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1680559097 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1680559097\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1365646105 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhaNHFUTXk4WGZZR0VGNWlmR0FiQkE9PSIsInZhbHVlIjoiaFR5R1lTdm40elNEVHVEeDY3VFNnTVdUQnppVTRTWVJydENOY1I2M0lIcW4xNFkrTU5rUVVpNFdPdFhPNCtiRE1PVTZJUkU3REtQTzl6Mk8xVFRuRkw2MjdFYitRTlMvWWJ1R3NxRUtOZmdpb1c2REgvVjA0QmVmMTVnVnBTQ2l1VDVqYmF5N0ZLLy9ZYko2UVljN01mamprUUV6L0RVb29tbzhFQXg0bk9vdThtUE1BTlg3RFJBdlZIcXEzS21PRHIva01ZL09Scm9UOU1uTDJnYU5yQXN6b1ZJWTJrVTlMMWJ2dCsvZ0dWbVorRWVoWXJJbTQxRTVtekcxa0d3eWx2cGNvK0xvVjZ4M21tWStBMkIvcTBzQzNPMkpGemsxTDljNEV0aTVGRnVaSWEvY0ZpaW5SM2NYVlEzQW41OWxwMVBaMUZOQnhncm1FS200b2Nzd2JWOFN3QW0yNHg1NUZUOWJoelFCaUhEdVd0VWRCQ0htbU1WTDAxQWFoMnJLeFNLbXB1UnhBdlZIZ0M3REd5VUM5QXlYbndpWEJTVjQ5d3ZOSkJJQ01oZEEzcVEwNjJsYUxEZkdMVzlsUFNwOTNDVnRxaFpGbWxBZ2dxTWxVRzlBbjlJcndvdmtVY3Q1S2hHc2REaDFybHV4Rk5JVHVETzJZcjZMMFBMOHVWSWsiLCJtYWMiOiI5YTI1ZDlhZjY5NWY0ZjllMzM2MzJhMzA2MDc0ZjhiZGM2MzZmOThlMWE0NGQ4YWU1YjUyYTk2ZGEwYTJmYWI2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjA4czU1YkVBU0lNYWRDODNlSERqbFE9PSIsInZhbHVlIjoidnNIWDMwUGlTaURuYW4xY1F4NUUzdFlUc2pQVVd2dzArQVlOajI2bFRHRHgyQ3I1ZW1jbW1vZ09GRUFOUDBZdmI5NzJ0aGNVQTVBSlg2US9TYkZUNUVaWU9OWHRJOGd2VkJIL3lnV3FHTlAwbGpTb2o2dzBrV1pkb3JLYUFMWGg1eGFDL0w1cHJaTERia3ZrL2piN2VuUUxFaFhnYm55OGxZR0NGMHFzZTlGeUJ4b3h2MzRBOVAvZUlTMXdrejErZzZFZ2s0RTc1aTB5VXlCVVcxRmYwV2lTVUFiekxhOWVROE9xbi9mMVM5UHlPVGJnK0dJcWJTMGVoWHhEQ09EakZrN2F3eXpwOGtMc21DRkZJWTd2Y1Jvc1RsZzU1bk9sbDlZOVAzWnpKVEYxZHByYUVBY1loTHNJaTZvQ0lMSWt3dndZQXlLdERlcU9Ya2tLaGRSdVJPMG1OeFQzOS9IN2hhSVdSMVNyNEdEV2NGNytqSFdISno5eUpPRXRPVmRQbUR3NzBxTmJvOFhNZ0ZMTmlUNEliME9GcjBucmZ2cUdXSzFiRm9iZ0JRYUUzeTZQZkdrTW56a2lIZkZiTzl5NDFzcit6VERYQnBIbGJ4SEZxWlRjczI3STZsUzdZZDV5RC9vU04yZk9rQUR0Qkp5L0NhNmh4OXlsUUYzcnFUTCsiLCJtYWMiOiIxMzMxNWNmODJjZjk0OGQ5YTZjODNkM2Q5MzIyMWU2MDEzMmU4ZDI3N2EzMmEyZDc1MGFiNTk0NDcyZWY3YTdmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhaNHFUTXk4WGZZR0VGNWlmR0FiQkE9PSIsInZhbHVlIjoiaFR5R1lTdm40elNEVHVEeDY3VFNnTVdUQnppVTRTWVJydENOY1I2M0lIcW4xNFkrTU5rUVVpNFdPdFhPNCtiRE1PVTZJUkU3REtQTzl6Mk8xVFRuRkw2MjdFYitRTlMvWWJ1R3NxRUtOZmdpb1c2REgvVjA0QmVmMTVnVnBTQ2l1VDVqYmF5N0ZLLy9ZYko2UVljN01mamprUUV6L0RVb29tbzhFQXg0bk9vdThtUE1BTlg3RFJBdlZIcXEzS21PRHIva01ZL09Scm9UOU1uTDJnYU5yQXN6b1ZJWTJrVTlMMWJ2dCsvZ0dWbVorRWVoWXJJbTQxRTVtekcxa0d3eWx2cGNvK0xvVjZ4M21tWStBMkIvcTBzQzNPMkpGemsxTDljNEV0aTVGRnVaSWEvY0ZpaW5SM2NYVlEzQW41OWxwMVBaMUZOQnhncm1FS200b2Nzd2JWOFN3QW0yNHg1NUZUOWJoelFCaUhEdVd0VWRCQ0htbU1WTDAxQWFoMnJLeFNLbXB1UnhBdlZIZ0M3REd5VUM5QXlYbndpWEJTVjQ5d3ZOSkJJQ01oZEEzcVEwNjJsYUxEZkdMVzlsUFNwOTNDVnRxaFpGbWxBZ2dxTWxVRzlBbjlJcndvdmtVY3Q1S2hHc2REaDFybHV4Rk5JVHVETzJZcjZMMFBMOHVWSWsiLCJtYWMiOiI5YTI1ZDlhZjY5NWY0ZjllMzM2MzJhMzA2MDc0ZjhiZGM2MzZmOThlMWE0NGQ4YWU1YjUyYTk2ZGEwYTJmYWI2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjA4czU1YkVBU0lNYWRDODNlSERqbFE9PSIsInZhbHVlIjoidnNIWDMwUGlTaURuYW4xY1F4NUUzdFlUc2pQVVd2dzArQVlOajI2bFRHRHgyQ3I1ZW1jbW1vZ09GRUFOUDBZdmI5NzJ0aGNVQTVBSlg2US9TYkZUNUVaWU9OWHRJOGd2VkJIL3lnV3FHTlAwbGpTb2o2dzBrV1pkb3JLYUFMWGg1eGFDL0w1cHJaTERia3ZrL2piN2VuUUxFaFhnYm55OGxZR0NGMHFzZTlGeUJ4b3h2MzRBOVAvZUlTMXdrejErZzZFZ2s0RTc1aTB5VXlCVVcxRmYwV2lTVUFiekxhOWVROE9xbi9mMVM5UHlPVGJnK0dJcWJTMGVoWHhEQ09EakZrN2F3eXpwOGtMc21DRkZJWTd2Y1Jvc1RsZzU1bk9sbDlZOVAzWnpKVEYxZHByYUVBY1loTHNJaTZvQ0lMSWt3dndZQXlLdERlcU9Ya2tLaGRSdVJPMG1OeFQzOS9IN2hhSVdSMVNyNEdEV2NGNytqSFdISno5eUpPRXRPVmRQbUR3NzBxTmJvOFhNZ0ZMTmlUNEliME9GcjBucmZ2cUdXSzFiRm9iZ0JRYUUzeTZQZkdrTW56a2lIZkZiTzl5NDFzcit6VERYQnBIbGJ4SEZxWlRjczI3STZsUzdZZDV5RC9vU04yZk9rQUR0Qkp5L0NhNmh4OXlsUUYzcnFUTCsiLCJtYWMiOiIxMzMxNWNmODJjZjk0OGQ5YTZjODNkM2Q5MzIyMWU2MDEzMmU4ZDI3N2EzMmEyZDc1MGFiNTk0NDcyZWY3YTdmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365646105\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1192039746 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1192039746\", {\"maxDepth\":0})</script>\n"}}