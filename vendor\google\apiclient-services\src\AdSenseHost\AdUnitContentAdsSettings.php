<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\AdSenseHost;

class AdUnitContentAdsSettings extends \Google\Model
{
  protected $backupOptionType = AdUnitContentAdsSettingsBackupOption::class;
  protected $backupOptionDataType = '';
  /**
   * @var string
   */
  public $size;
  /**
   * @var string
   */
  public $type;

  /**
   * @param AdUnitContentAdsSettingsBackupOption
   */
  public function setBackupOption(AdUnitContentAdsSettingsBackupOption $backupOption)
  {
    $this->backupOption = $backupOption;
  }
  /**
   * @return AdUnitContentAdsSettingsBackupOption
   */
  public function getBackupOption()
  {
    return $this->backupOption;
  }
  /**
   * @param string
   */
  public function setSize($size)
  {
    $this->size = $size;
  }
  /**
   * @return string
   */
  public function getSize()
  {
    return $this->size;
  }
  /**
   * @param string
   */
  public function setType($type)
  {
    $this->type = $type;
  }
  /**
   * @return string
   */
  public function getType()
  {
    return $this->type;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AdUnitContentAdsSettings::class, 'Google_Service_AdSenseHost_AdUnitContentAdsSettings');
