{"__meta": {"id": "Xf6b55d8d58caf22a380a93a2e8f05323", "datetime": "2025-06-07 23:23:19", "utime": **********.178044, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749338598.296967, "end": **********.17807, "duration": 0.8811030387878418, "duration_str": "881ms", "measures": [{"label": "Booting", "start": 1749338598.296967, "relative_start": 0, "end": **********.072906, "relative_end": **********.072906, "duration": 0.7759389877319336, "duration_str": "776ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.072924, "relative_start": 0.7759568691253662, "end": **********.178073, "relative_end": 2.86102294921875e-06, "duration": 0.1051490306854248, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041912, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.005529999999999999, "accumulated_duration_str": "5.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.128581, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.45}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.149483, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.45, "width_percent": 16.637}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.162498, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.087, "width_percent": 15.913}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1322360281 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1322360281\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-544727167 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-544727167\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1686417508 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1686417508\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-707981543 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749338536800%7C7%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVJay9pUUFPdENpWFgyU3QvREpPeUE9PSIsInZhbHVlIjoiQ1dSaTkwWG5RcUpJRHRXUnJnL1dXMjZJQmcwRjczckZkU3R4K0xBZm1GN1pDTi9nYmhjREk3MnI1NFk2MlhyS0wxZGRQdXpveHdXclR6UUtuYkNmOExCTUFjMkYyVEFEM21kc3llR2NnNEEvYUtabmcrMFJhYzFnOGxUU0RRcm9SMXNLRWtwSG1Cb1d6WkhqSEpZcHI0S3R6c1NyNWxiVHJwY2l5aEJXMWpaaDQyWDJpRkgzblZRZGdQNnVWS2x4WUdDWkpsZ2Q4NlZMWTNEYzN0Yll3NVU5QXUzSlNHamMvR2ozaHpBZ3pNd3paTEVNRlREMXArT3FFRHZuYzZGcW15ZFNLamJLaGIzNnRsYlUycG52c1k4V05pK3JVQVF3T1J5MWZ5QzBzdHA5VDVkczUzR3NHc2dIVTAwVkZDYnZnd1kyN1ppVVVrbzl2QjdLZ0NSL0xmTi83TkFZRlhYOE1FZUNVR3NIMWxKRUY2VlBUb2gzdURJa2h5Rlpvc0RHbHk2TlFiS3B6aWo3WElyK0RpN2RxWHZvT1VnaE5lcGZHU3VlZisyMi9LYkNVMVo1UGxLUnFrL2JUTUJMN2lSUHY4MG5MNFJlems5RytKVmU0NW80T2h1NmlRSEU4M2lUN05oT29WWTNvK2lzaFJqRGQzZ2laWWFZRzdYS0plMHEiLCJtYWMiOiIwZGFhYTM3NDA5NTUxNjE0YjE1NjE2OGQ0NDkyNTNkOGNmNDNhMjFiMTMxOGY1NDg1ZTc2OWY5MTYxMTYxN2I0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlVJTVJSd1NLSXNPWFdlVFBnTVJlWEE9PSIsInZhbHVlIjoiUDQ2UXZuMXpuU0YrdHgyNXpqR3RRdzlMamRIV0pBT0JHcEpFencveUdWLzh3b2NXankwRmlaT3VsQkZNVEZWUDlLY3QwelNza29WaTNzc0IwY1hLSS9oZSsxTVlyZno2S0lCSCsyTGcvb3Q4dHYxbUd3cGxQZEwreVpsZjh2MU9TT2dwVVRCMEdIS2xqTnJEaTFua0tlT0dOU0w3Ym5nRjk1VW5ZcmFKNWlSU3l3bFh6MUlERy9FYzVQMzQxd1V3T3JQSDNuQVhocytGVG44Q2xyUzRMeXh3SnExMk9pVHJTS2k0aGVTMzNwcGJFMkM0L3JoM244VFZsb0NpOEZ1ZytvNGJVV3M3Tkt6dGw2WGVWWWdPcXY2dnhMa1hjT0dPbFQ2bHFsOHMwM3R1bG9UL3ZKL1dsK0hBcUN3b25kVHlLd1IrVWJKTlFxWlhvOU15RFBHSFlGVzJPN3YwRUs5Q3JvNnRxSkJJemtpdkxGbXhDM0s2aE15ZzFiWlI5VklFSGRRbmxJNExQTEl2ZTNZS3BROE9uNUNwWGd3Z3drTDNGZGFGRXhkU3BYMVVPdXgwSW1IV1JHWUt6d3RVUE54bVdPOFUvellxSExFcDBGd3RzUURuTDJXSDNRTzlLVlg1eHRDNlhtdjloZWJja0h3bnphQzZMNnRzcC9KdEp0VE4iLCJtYWMiOiIyZTlmZTBhNjhjY2FjN2NkMTMwODM5ZjE1NjgxMGZhNGRkNDI5NWRjMmQxNjI4YmNlZjkxMjcxMzQwY2U3YzUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-707981543\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-515395296 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-515395296\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-52050921 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:23:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRiVXRFSG4vQjJ2aTA3emtDWEFiUWc9PSIsInZhbHVlIjoiRGFWNnlLMzBjUFh1aVI2eWlHRGtHTmRxcDhmR255dk5MbXFsbUt4TlZvQU5YNi9uYWYxeVdTUWdmZmFMOGNGZ0R5eVk5N0ZJZFQ4R2JaT2RjV3dKR0hPTktKNlNzd0ZQdmREZTZITHRVWU9uM3cweld3YnVhS1kyYkRMTk94UXl0MjBwUVlyQmJSSGVpZnJadmNQcS9RY1ZVMGtGQTA4N21mUlhZTnRnR3pOUzh2aDVLcXZVZWpBbUVaMGRpcUorZEZBZXcwdXd5T2hWU0JCVGprVldKSUwrTjRRS0JiQlJkQ1FpUElxL0VGa2JiMEVxZ0N5Wnl6aDZpRkdNTW5GSFNCeVNpSTBncHV0ZGZzUXZyQkIyc1VJSHA4V0RLc295WVVVdmNRTnhEcm9MQjM3QXhydDdEVXFjRnhnNkpJakwvRjIyZkp4andoT29OMEtDODRYY1l4ZWJiUDZ6UDRJdVR1Q1AxOXpPWVMwL3J3M2FUQTZYTHVMTWtHWklBTkpGcnNDaWw5eGdjMk84TVJCa2Z0V2t0bUFvKzBTQXg3VlZrYXVJK3FGNzhydDRsMjVsTFVlVWpMZ21KYytGS0FBaVU2RndmL2YwRERNYUVmTHVadXhnNDZwQVFlcEVhRlVSSGxDS1FYRlNTUUlDZXpwbklmRWRmRGZtbENXenNsUEYiLCJtYWMiOiJmYjVmMWU2NzFhZjliY2FmOTg3ODA0OGVkMWZhNTdmMmU2ZDhlNGIxOGYzZTY5NGZmOTIxMzg1NjkzMDFhNGEyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:23:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InFRb0RFZG45YVZIRU5HcTA1NS85cmc9PSIsInZhbHVlIjoiWHBRckRrckVVWkc4OHgyL1VvbUdLTHdtUU9RV09lVVFUZTZ5QjhjZGg1bWpMdEJZMG1ZK2xKVjhlTlY1M25UZW00ZE1BZ1VIN0hYUmtZcG9QdzlONnJWWmF2QkNnREhNa2xGdzY1eHdxT1J0TCtxVHlvVnJqVW5ObUFzMUVDNzdJU1lReGNhdXZYZnlpUU1SdE5aZ3F3YmVpdjIwWW9rbThXNHN6TzNxVyt2MkM2ajFGS0o5bDRURG1ZbmNjbWZxbFlTRU1yZVFibERka0w5c3lVVFZOZFhKMkF0T0QvZVJ2K3NnOGJIV1pWUlJaNFFFeVovMVZHZXFaT2M5eFcwRkdaZVNleHJrZjE0TEhVZXZHSUtNTDlYNVZWOFRDUDI0TVNzQ1VlczdmRDZlSDhRUnpFeWJjYVYrNHFUVkpFSkh5RGVBaXJLd0ZtZkc4QlI0Y0RQeDg0V20wT2lFZGVXcjFacVpROGNqZ2NmdDM1bjhycGhnL0lWZTlFYjRBZlp6OXZIMDRkRTE4ckxxWFNSZnVHRHY2ZDBrcytHcFZUSE84TVdSeUFzbnV0K2pBd3NRZVpyUVBkVVM1YjFjcm1mNTMyV21MSWpxbC8wL0ZsRVlvMTcxT3RZbVpIS2lyRVAwekhKcS9EMGdTZmdCWm5NZDVNZkYwZmVqZlg4MDVlMDYiLCJtYWMiOiIxYjQ1NTQ1Zjc2M2FhZmY1ZTcxZjM5ZTRkMDQyNjI2YzgzNjUyNzQ2OTU1MDI4NzBjMmFmODNhZmQ1YjcyMjdkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:23:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRiVXRFSG4vQjJ2aTA3emtDWEFiUWc9PSIsInZhbHVlIjoiRGFWNnlLMzBjUFh1aVI2eWlHRGtHTmRxcDhmR255dk5MbXFsbUt4TlZvQU5YNi9uYWYxeVdTUWdmZmFMOGNGZ0R5eVk5N0ZJZFQ4R2JaT2RjV3dKR0hPTktKNlNzd0ZQdmREZTZITHRVWU9uM3cweld3YnVhS1kyYkRMTk94UXl0MjBwUVlyQmJSSGVpZnJadmNQcS9RY1ZVMGtGQTA4N21mUlhZTnRnR3pOUzh2aDVLcXZVZWpBbUVaMGRpcUorZEZBZXcwdXd5T2hWU0JCVGprVldKSUwrTjRRS0JiQlJkQ1FpUElxL0VGa2JiMEVxZ0N5Wnl6aDZpRkdNTW5GSFNCeVNpSTBncHV0ZGZzUXZyQkIyc1VJSHA4V0RLc295WVVVdmNRTnhEcm9MQjM3QXhydDdEVXFjRnhnNkpJakwvRjIyZkp4andoT29OMEtDODRYY1l4ZWJiUDZ6UDRJdVR1Q1AxOXpPWVMwL3J3M2FUQTZYTHVMTWtHWklBTkpGcnNDaWw5eGdjMk84TVJCa2Z0V2t0bUFvKzBTQXg3VlZrYXVJK3FGNzhydDRsMjVsTFVlVWpMZ21KYytGS0FBaVU2RndmL2YwRERNYUVmTHVadXhnNDZwQVFlcEVhRlVSSGxDS1FYRlNTUUlDZXpwbklmRWRmRGZtbENXenNsUEYiLCJtYWMiOiJmYjVmMWU2NzFhZjliY2FmOTg3ODA0OGVkMWZhNTdmMmU2ZDhlNGIxOGYzZTY5NGZmOTIxMzg1NjkzMDFhNGEyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:23:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InFRb0RFZG45YVZIRU5HcTA1NS85cmc9PSIsInZhbHVlIjoiWHBRckRrckVVWkc4OHgyL1VvbUdLTHdtUU9RV09lVVFUZTZ5QjhjZGg1bWpMdEJZMG1ZK2xKVjhlTlY1M25UZW00ZE1BZ1VIN0hYUmtZcG9QdzlONnJWWmF2QkNnREhNa2xGdzY1eHdxT1J0TCtxVHlvVnJqVW5ObUFzMUVDNzdJU1lReGNhdXZYZnlpUU1SdE5aZ3F3YmVpdjIwWW9rbThXNHN6TzNxVyt2MkM2ajFGS0o5bDRURG1ZbmNjbWZxbFlTRU1yZVFibERka0w5c3lVVFZOZFhKMkF0T0QvZVJ2K3NnOGJIV1pWUlJaNFFFeVovMVZHZXFaT2M5eFcwRkdaZVNleHJrZjE0TEhVZXZHSUtNTDlYNVZWOFRDUDI0TVNzQ1VlczdmRDZlSDhRUnpFeWJjYVYrNHFUVkpFSkh5RGVBaXJLd0ZtZkc4QlI0Y0RQeDg0V20wT2lFZGVXcjFacVpROGNqZ2NmdDM1bjhycGhnL0lWZTlFYjRBZlp6OXZIMDRkRTE4ckxxWFNSZnVHRHY2ZDBrcytHcFZUSE84TVdSeUFzbnV0K2pBd3NRZVpyUVBkVVM1YjFjcm1mNTMyV21MSWpxbC8wL0ZsRVlvMTcxT3RZbVpIS2lyRVAwekhKcS9EMGdTZmdCWm5NZDVNZkYwZmVqZlg4MDVlMDYiLCJtYWMiOiIxYjQ1NTQ1Zjc2M2FhZmY1ZTcxZjM5ZTRkMDQyNjI2YzgzNjUyNzQ2OTU1MDI4NzBjMmFmODNhZmQ1YjcyMjdkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:23:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-52050921\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1208975575 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1208975575\", {\"maxDepth\":0})</script>\n"}}