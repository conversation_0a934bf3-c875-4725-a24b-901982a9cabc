{"__meta": {"id": "Xa871cf4e2ea99d0e3cf9e2c71ca69f59", "datetime": "2025-06-08 16:18:04", "utime": **********.913019, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.339713, "end": **********.913038, "duration": 0.5733249187469482, "duration_str": "573ms", "measures": [{"label": "Booting", "start": **********.339713, "relative_start": 0, "end": **********.818204, "relative_end": **********.818204, "duration": 0.47849082946777344, "duration_str": "478ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.81823, "relative_start": 0.4785168170928955, "end": **********.91304, "relative_end": 1.9073486328125e-06, "duration": 0.09481000900268555, "duration_str": "94.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45678600, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.014950000000000001, "accumulated_duration_str": "14.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.862129, "duration": 0.012960000000000001, "duration_str": "12.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.689}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8902671, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.689, "width_percent": 5.619}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.899517, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.308, "width_percent": 7.692}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-484248230 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-484248230\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1870057032 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1870057032\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-740554715 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-740554715\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-20827317 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=u1db3a%7C1749399420195%7C1%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhEOEd4UWFXVENUK0dHdjVhN2h1M3c9PSIsInZhbHVlIjoieFE5WXd3M1p0aDVLbXFlVmlzcE1OaENQTkdscVhieit1UjNacmU5ZjFLVUR2c2FkWmZnWENrYVFWdmNLL3hLQVNubVlZdEVGY0gzdjJoVVNWaTBLamd0WXBscXhLbThucW9wbVU2Zjg5dis0ZHp3ODJ0Z0c3QzhsRGNNeW0zcldOSk8zUld6T09GcTdnWHJsQy9UVVU4UjZlUVlYTTV1b2pYODVtTjI5VWlMYXFDazBDYkQ2bFRnVmNIN0Y2eE1wMlcyMzg5cXR4SFFCMlB5ZU9hRmtuelJ6eGdTUUJHN0tHNHBxU0cyUDducEFTZzE3TFd2V0tBOEVYNmJYNWJaT01IVnQxUVdyM0Q0dWZYSEk2QlRoTSt6aHN1YWlzTU9IaGlkR0FwTGtjalNEa2VNbkhKRHhrRGpjNTd5UXJjM2w4NjZkU2hmcVVuNmg4U2RaOVB4QUwySTY0L2d1a080L1NBcnUzVHhMczhsQ0JJbXJ2WXV0eVEyWG5SdHFUY05iTmNmQnc0bkpvRlltcVJOZHZlaGZNNGVUWEs4cEI1Y2FBSVNneHdSeFZiSFRLWFpYaTd3K2UvNVV5QkxkYUJwRERqd2NDZmpkSnVBYW9jSm15NDZ2U3VnS1JKUXdDZVJMdXBXQk5jbzEzZXFHRko1TnZQWm93WFh3eG5FdmVaMDAiLCJtYWMiOiJmN2ExZTk0ZDFhNTE0YmViNDY2YzUwZTYyNGRkZDJmMDNiN2E2MjdhNTZjYWY1ZWU1YjcyNzQ0ZGQzYzk0ZmYwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlJWWS9jT0ZGWk9LWHhEVEMrL0V2b3c9PSIsInZhbHVlIjoiOEI0VWZ4N0NqZUFXQXFqeTVLdVIvaHpleDNvQys1VWhMY0oyc2xwYVhjTXNVQk5YcTdGSHdtQXNXWXdIaGJ5aUhoOWFwdWxEYkdxckNhcVJHdVRWZzJYZ3loa3RuU25iM0d1Um1BUFRZNlBFODhEUUYwN1BhMDlxR25sRFdhcFArVGJ2UDZBZG5kR1Z4OTJMbmgzdnhXVXUvaXRjelprZ2toQnB5RE1PdkpLQlQ5cjR6ZFl3VXNVeUUvemRoa0czd2J4Vzc0QmhzTXY5TEF1M1psRjVqa1dnS1RMQ3V5bEJJZ2RsNzFDSnFRNENmWmRyZnJwQW5UbDhaS1J4NGJmLzYyRnZ0S0lyYWhic01sUmx6WjB2WjBXKzI5RkNrTHJvbzg1bHZlVitmNjNhZmkxdmhiZFlXTVJHZXRUYklOeVVSOXhwWWN1cGFVUmZ6d3BKaVV4SGQxN3hRcXdMVEwva045RGZJQmNqSXN1eEVsemVhK0NxYlVhdWJKcWRzSW1nY25IM3FYUm1IRkFmcC9BRzR3RmlhdlZ6Z215Nit5b0NvbjJndkxvRDRGNWlkTkpScTViN1c4dlkrZUFObW5FMG9BVzFUTW5KWkZGc2RlVExwSFBiQU9DN2I4VGUzUm1rRmRuWC9UUGw5WjNDMFArb2JBS3JhR0xnMmlSQ1NYUjYiLCJtYWMiOiJiNzMyYjJlZmNkNWU5N2EwMDZhNjQwYzU4OTE4Yjk3MzdjMzdmOTU0ZWJlODE3M2NjYjQ4MDk3NGEyZWFkMjViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-20827317\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-478357624 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-478357624\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-911692089 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:18:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdCQjVwTDVaWTZxM3FncFYzTCtBdXc9PSIsInZhbHVlIjoiR3A1OUovVTdrYlh0dWFiL1NwR3hFb3NWYnBkcUROdjVLVXlSVjJzRFZpTzBRK2FTb0Y4TEtUS2Ruc1MvMFBlWXM5bW1EcjAzNDlvMUk0c3FGeWt2TG4wd1QxZWVRV0tzVXluTk5FSmt6VHBDRUF3elYrMUNvWUxDaWFiUy9OdDc4eHd1TWZXT04rWjNuT2FYVkp0UGI3c29RVEl5ZWF5eTdBQlFQVVZFdElnTFF0aHhHM1VKV0hENnFaV28xbVFaUmxKelFwVmNtdEhXdy96UmZ6REo3SkEvd0toWTFGTy8zZ1RLSzBlVlNtV2w5endVRlRudFNwVU5xcHdOMkNQSlh2dEE2bTdYdXp1ODl2RkNQVGRPL05lTldzdGdKR1BWR2Mxc3lJbEJBb2VRMnRIL25sUFBJZ05pVXJJalJhRVdwb2h6NE5kVHdFbWRhMXBYcHZUd3FCY2h4dldHT245RVNTYUhMVlp4a0xoVlpoMUhidXd4c3J0MjFZampmYTFOWmRXQVJUK0R6SVlLU05jV2NiRitkSGVjbnI5REJlRlloT2Uyc2JnTmlla0xHYitSczBENytSUFhRcDRKclZRWDJLQnVvc2ttTml4WEIrMlA2OEZ0a0JZbXBUM2tqazNuZ3NXalhCSkhLZEZpNTVwUllzSHdJaGVkTWZCV2Rma08iLCJtYWMiOiI5MjU4NDU0MzYyODU0Mjk5YzNmMWMzZWNjZjU4ZjQ3YjFlMzIyNDE1YTdmOWY5ZDE4NTBiZjdiYjdmYjVhNjcyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:18:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkI1VmVQek5IVE1jQUYwdW1saHM5R3c9PSIsInZhbHVlIjoiY2pkTS9WMHlUZkRkekNMVFNaRENqcndVNTJtQWViUk41TjlzV0l0alkvaUduZzlPVlQ5MEYrc1kzOUlVZDVrSU8rbU9kVnpkR1pvTEtOR1lMOGtiQ3lDdjJqY3JmNDNXQ2VLNG9KNTRTUzAxZFJBRnNIQXUvZ0NqUm94OHE3Y1VjQ1czSFl1TWRwNzdRZU95TmFVNkJMdVJhRWhEY0xaU09xdGxlWlVVZlRheSsvN3VBclJSQ0svVUtibEdGYWJJNHhMbW1ZTlNMS3Fmb29ZQ0pDTmZDSDQxRzY2S1lvaWduM3ZKdzdzVUg2N2ZHQlpTdktZV0pBSWo3SEs1aDMxbytXY0JqckF5ZFpaeXlRRU1KVzNHNDFsdjNsRGovQUhqQ3BnWlNleFQ2RFROYXErRHRUV2JFWDVmVWk5RzhZbUYvQUZvNTZDa2c3dlJ6R21wYUZIVHhuMlNuSnEreUlWNjdxbkEvblczditpOGRRUGY4anRiMnVqZGZQdUdoVGtmN0Zoc1FEdlpGT094NDNBYUJKbE5PNHdZcStudmhEV2FRcncyT1JLQmYwUmI2UDdlZU9BeGtra2FiUlFtc2I5OHp2Z0M1aUpZMzRTTW8xdTk3NjcvWmlyZ2dIaDVEUkdOU1BIOCt3K2VuaVhkT0Q3SHpTL3J4U0ZvUDl4QlpDTXoiLCJtYWMiOiI4NTJjZjYxNmM2ZTk3OWYwMTkzZTQyOGU2NTNhYzc0NWE4ZjRmM2NkZjFmZWI1MjZhZmMxYmQ2MTI0MGVlYTBlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:18:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdCQjVwTDVaWTZxM3FncFYzTCtBdXc9PSIsInZhbHVlIjoiR3A1OUovVTdrYlh0dWFiL1NwR3hFb3NWYnBkcUROdjVLVXlSVjJzRFZpTzBRK2FTb0Y4TEtUS2Ruc1MvMFBlWXM5bW1EcjAzNDlvMUk0c3FGeWt2TG4wd1QxZWVRV0tzVXluTk5FSmt6VHBDRUF3elYrMUNvWUxDaWFiUy9OdDc4eHd1TWZXT04rWjNuT2FYVkp0UGI3c29RVEl5ZWF5eTdBQlFQVVZFdElnTFF0aHhHM1VKV0hENnFaV28xbVFaUmxKelFwVmNtdEhXdy96UmZ6REo3SkEvd0toWTFGTy8zZ1RLSzBlVlNtV2w5endVRlRudFNwVU5xcHdOMkNQSlh2dEE2bTdYdXp1ODl2RkNQVGRPL05lTldzdGdKR1BWR2Mxc3lJbEJBb2VRMnRIL25sUFBJZ05pVXJJalJhRVdwb2h6NE5kVHdFbWRhMXBYcHZUd3FCY2h4dldHT245RVNTYUhMVlp4a0xoVlpoMUhidXd4c3J0MjFZampmYTFOWmRXQVJUK0R6SVlLU05jV2NiRitkSGVjbnI5REJlRlloT2Uyc2JnTmlla0xHYitSczBENytSUFhRcDRKclZRWDJLQnVvc2ttTml4WEIrMlA2OEZ0a0JZbXBUM2tqazNuZ3NXalhCSkhLZEZpNTVwUllzSHdJaGVkTWZCV2Rma08iLCJtYWMiOiI5MjU4NDU0MzYyODU0Mjk5YzNmMWMzZWNjZjU4ZjQ3YjFlMzIyNDE1YTdmOWY5ZDE4NTBiZjdiYjdmYjVhNjcyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:18:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkI1VmVQek5IVE1jQUYwdW1saHM5R3c9PSIsInZhbHVlIjoiY2pkTS9WMHlUZkRkekNMVFNaRENqcndVNTJtQWViUk41TjlzV0l0alkvaUduZzlPVlQ5MEYrc1kzOUlVZDVrSU8rbU9kVnpkR1pvTEtOR1lMOGtiQ3lDdjJqY3JmNDNXQ2VLNG9KNTRTUzAxZFJBRnNIQXUvZ0NqUm94OHE3Y1VjQ1czSFl1TWRwNzdRZU95TmFVNkJMdVJhRWhEY0xaU09xdGxlWlVVZlRheSsvN3VBclJSQ0svVUtibEdGYWJJNHhMbW1ZTlNMS3Fmb29ZQ0pDTmZDSDQxRzY2S1lvaWduM3ZKdzdzVUg2N2ZHQlpTdktZV0pBSWo3SEs1aDMxbytXY0JqckF5ZFpaeXlRRU1KVzNHNDFsdjNsRGovQUhqQ3BnWlNleFQ2RFROYXErRHRUV2JFWDVmVWk5RzhZbUYvQUZvNTZDa2c3dlJ6R21wYUZIVHhuMlNuSnEreUlWNjdxbkEvblczditpOGRRUGY4anRiMnVqZGZQdUdoVGtmN0Zoc1FEdlpGT094NDNBYUJKbE5PNHdZcStudmhEV2FRcncyT1JLQmYwUmI2UDdlZU9BeGtra2FiUlFtc2I5OHp2Z0M1aUpZMzRTTW8xdTk3NjcvWmlyZ2dIaDVEUkdOU1BIOCt3K2VuaVhkT0Q3SHpTL3J4U0ZvUDl4QlpDTXoiLCJtYWMiOiI4NTJjZjYxNmM2ZTk3OWYwMTkzZTQyOGU2NTNhYzc0NWE4ZjRmM2NkZjFmZWI1MjZhZmMxYmQ2MTI0MGVlYTBlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:18:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911692089\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1934656984 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1934656984\", {\"maxDepth\":0})</script>\n"}}