{"__meta": {"id": "Xe08f611c53b317bdfa21f8684f3580a7", "datetime": "2025-06-07 23:14:14", "utime": **********.787421, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749338053.90716, "end": **********.787458, "duration": 0.8802978992462158, "duration_str": "880ms", "measures": [{"label": "Booting", "start": 1749338053.90716, "relative_start": 0, "end": **********.68146, "relative_end": **********.68146, "duration": 0.7742998600006104, "duration_str": "774ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.681477, "relative_start": 0.7743170261383057, "end": **********.787461, "relative_end": 3.0994415283203125e-06, "duration": 0.10598397254943848, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45037776, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00554, "accumulated_duration_str": "5.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.73614, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 68.412}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.756845, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 68.412, "width_percent": 14.801}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.77105, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.213, "width_percent": 16.787}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1444012035 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1444012035\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1091393322 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1091393322\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-917858938 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-917858938\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1010095571 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749338003995%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBMZ0lrOHVyUWptNzBRSXlLQVF4MlE9PSIsInZhbHVlIjoidHZZZnZnRDlOTnpmTk90TVBVbG1ZTHZUSFcvanY4L3ljZXdiVTlrMjQweEduZTlpcS9adWtXbXJyaEdUaVlkTW4wWW0rVGI1dEpTL2Z1Q21wMTBGVGMvWUpQRkVCakNyQ0FqS2RHYWh6dENIdHNGL0FlZWtndnlkYXlFd2ZWMHNycHV6Y1cySVFkRmc3STcwb0ZxcFpzc1kwNmpqNU1mby8wN0c2WXZ1dyt2SVBiTm9mbEtsM1Z2ZnZiTGdqTmJTTjZPK3orS3ZRUFBtTlVRMnY1UVgrNXN4RlBpeGZiNGJhenlrcmNDNXp4cW1XR3EwbERHUE0wWTU1NXdrcGJiU3VxVGh4bU85NTd2eUg5akVWeFhWdy9WditkUUpyYm5DZ3BKa3hNWHlPMHEvQ1VFeXpIRWhYY09MS1hSVUxqRVJIaUZNaEp1KzlFVjlVUkJhVGVFL004aWFLTU1wbGk1cWljR3NNVjR4c085TE5mOExaT3o0WmNMeXFoTnVWbFc5SnBpVlpaYmloYkI2S3EzWFNUSFdyNVVkSjlCbTVsY01qRjB0RVYyNENPVHVzVDVGNWdsSjJxeGlOdkNQcXlkOTlFcW1zakY1bXJPOERBSnUrdjR6S1hiQUh6Mng3UG40OStVcEpJRjBRSllVeG1vZUJ3VllwMVo5TnJqNWd2TGEiLCJtYWMiOiI1MDQwMmY1NmVlMWNkMzI1NWY2MGI5NmEzODlhN2MwODJhZjQwY2VjOWJlOWM4OWQ2ZDRhNmI1ZmVjMWIyYTk3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlZWRGZpazVUekVnUTdreTR2ak5lQmc9PSIsInZhbHVlIjoiOC9MZklaR2M5NzZCU1lxalFtY1Awa2IySUVESkVjVnpja3hoNWtnMFNYM3ByTHRrSlNqeWVqNkxMRDVZTjlJMVZvVGh6anNwVjlaVFBWbGxWbU5XMWdsVG81cGp6Nk5ORVRJa0RHNmZpYUtGTHY4VEtDeFEveTFVZC9zamF0VlRwTzFmRnQxcml1VWpOTkRFWG1RU2Y2VUwvRHd3bUVMbVpialpETFJaTTFCOXBXU1BkNXlhRjRxbjh4emdrMDlMSlVzM3BmVnc3NVkrSzUvbUo3MXFURk9Qb0ZZNDRnL2hyYmV1eStTUEhLUDcrMS9MV3N5QmhqSVJMN3pFdlZsR2l5Z2xXUE9kVWtpWWJkSzRKRXovZlZUbjE0RTFGSnRUUHB5WitoRmhwN0FlZ3ZFQ3JsR2hONEhlY2lCQlZNcXlCVUk3S3A0U01vcGhRRUNucHlqYm01L0VJbm5HYTdpM0pJYW4xWCtma0hRVGtCcHBBQ2g0Z2Q2NVIydzVtK0N4aXpCc05UNXRDSFA5eWk3MnJIbnJMZmxrcTRaU09MSWtMMDdUQlM3eUNpRnFOQWI3Z3lrU3Q1eDNiRy82Z2grcWpPeXZkMzJBRVQ3SzdGMkxRTXdIZkZBQzFKa3IwV3BZV2JYMGlIM3hOczZTRzM0Q2s0T0plcEk2VHE4aTZNbEIiLCJtYWMiOiJmYzRlZmYxZjBmNzEwMzAzMTk4MTUyMjU5ZDU1YjdiNmM0NTM5MjlkZmYxMzM3OGJiZWUyNDE2ZDI0ODQ4N2IzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010095571\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-641429270 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-641429270\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-639924357 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:14:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im0zOWY2a28veWxLWFhtKysyZXdVUlE9PSIsInZhbHVlIjoialRlL0FiNnIxKzlQYWRVY0Q5bWpjWlhkeHAyZUs4bjNST0tzVE55ZGpHbnNHVmp4dWUxLy9yU3dLN09BbnNsWE8wbFgxOHlnOGt2Z3JseENPNnJEUlJZR1haZ3V1UkhXMHc4Q3MybE9hNDZjZ1ZtRUVLclAzaXFpOTJSSURmemZGVVBSNVBmQXFSQ2IrOVhyYTE3T0l0Kzg1czdBelRUL0o3dGZIOFplN1NCS1A0TWc0V3ArTTU4OU15ZVJBV0M4bXlOM2lhblVvQ3d5YmZaMW5adzZkWjE4b0k3ejJYeFJlSWFSQU5JdVlXdHZvN0ZvTWtrbDg3dVllWkZlSXZBb2g5cWxFbGpOaE1icGlMWGdFUVljNEN3Z1AwODllbkFjWTFKa0xNN3hSQWNacVBkNXYzNURSRnVWaGMwMHdVZ3dmMFBqZE5KQUVPUVNrdTltbE9rc01iMnN4d3lYZlE5QWgxYW9qQ2NKUW9sYWhlTVE2TktNM05IRjhOUVJ6MnQrSnBReDVac3BDZ1p5ekRTbzhOWFgyYlFUcmE5TzNVS2ExOTNGK0hWR0k4SGxnVUtjKzJUZDB4YlpDNHltUGVkMTdCRHV4R1FBZmZrdlp4NkVpTlNROUhvL1lhNzBPK3FpNFpqV3h4Unk3dUwxZ1I3NnhaOTlSaHVCQUloK1VoaGgiLCJtYWMiOiJhMzIyY2E2MTdmYmVhNDg2Yjg2MGRiMjFlOGZmNTM2NjgzODg3YTRjMmViYjU1YmVkODQwNDIzYjkyNmJiOTdhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:14:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFQcVRhV05NR2NhVEJ3SGp1Y2l0NGc9PSIsInZhbHVlIjoiYnJLaHR5KzJZL0ltem84MUFhdWtKNldpaVRCSkJmbGppTTZKMExtdGxib1puSGVrRm16SnJwMWwzWmhQZHF3ajIrZ0oyaFgybGZCTzFqVi90anBDK2FwZjE3UDNUdFVXaC9qbWpXRXlsSVZxZjlvdUFNdnJYd1IvMHI4ak41Y0dXZytsS3lJc1lEY0dJc05IMUd0QWFlcHBFU0pSTjVVT0ljd0p3cUp1NkFnd3pPOHlLYWkzYmY3MDZkSmRsbWtrZkMvQUZ2UzlHQXp6bFNCNUNielN1Y2hDanZoTlFCMlRRMEwzSkVKR04yZnA3Qnh6dnpBc3hZenRJak5LRVdQUFIvQ0xIUktFYXdYUldUQmhCN21tQk9PVkpxRmMyd0h0UER4WXpjOG1hdFhZQWpLcVpUNWc1aGJjTXlIbGM1S0xZZlM0Z0Zsc3J5djJ5dVdTZDRlVTNSWi8zRm5xeDVHMFVwK0NQNW9TWmRZWUFmL0VvL0hkSWNGS3E3cWlOMWpmUCtxblRFcGIwWGordjEyWDV6Q3FtZjFvdjIxNkEzMG1tem5Vc3JRZG9WWUVFcHQwZXpVZTVPekhBTkJQYVJEaWNlTE5RQkFqcnJqL25QY1FJN2ZEMnJvVkpBSnZaMC9VcEVhUHNkT3dNTVdldjVsUEUxeTNlQlY4dS9GME0yTHQiLCJtYWMiOiI2YmYxMGE1Yzk1Njc3YzEwMjBlZjJkZjczMTliM2JmODNmOWY5ZTA4MDJjNTVlN2M1NjQxM2RlZjQ1MTdlYzJkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:14:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im0zOWY2a28veWxLWFhtKysyZXdVUlE9PSIsInZhbHVlIjoialRlL0FiNnIxKzlQYWRVY0Q5bWpjWlhkeHAyZUs4bjNST0tzVE55ZGpHbnNHVmp4dWUxLy9yU3dLN09BbnNsWE8wbFgxOHlnOGt2Z3JseENPNnJEUlJZR1haZ3V1UkhXMHc4Q3MybE9hNDZjZ1ZtRUVLclAzaXFpOTJSSURmemZGVVBSNVBmQXFSQ2IrOVhyYTE3T0l0Kzg1czdBelRUL0o3dGZIOFplN1NCS1A0TWc0V3ArTTU4OU15ZVJBV0M4bXlOM2lhblVvQ3d5YmZaMW5adzZkWjE4b0k3ejJYeFJlSWFSQU5JdVlXdHZvN0ZvTWtrbDg3dVllWkZlSXZBb2g5cWxFbGpOaE1icGlMWGdFUVljNEN3Z1AwODllbkFjWTFKa0xNN3hSQWNacVBkNXYzNURSRnVWaGMwMHdVZ3dmMFBqZE5KQUVPUVNrdTltbE9rc01iMnN4d3lYZlE5QWgxYW9qQ2NKUW9sYWhlTVE2TktNM05IRjhOUVJ6MnQrSnBReDVac3BDZ1p5ekRTbzhOWFgyYlFUcmE5TzNVS2ExOTNGK0hWR0k4SGxnVUtjKzJUZDB4YlpDNHltUGVkMTdCRHV4R1FBZmZrdlp4NkVpTlNROUhvL1lhNzBPK3FpNFpqV3h4Unk3dUwxZ1I3NnhaOTlSaHVCQUloK1VoaGgiLCJtYWMiOiJhMzIyY2E2MTdmYmVhNDg2Yjg2MGRiMjFlOGZmNTM2NjgzODg3YTRjMmViYjU1YmVkODQwNDIzYjkyNmJiOTdhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:14:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFQcVRhV05NR2NhVEJ3SGp1Y2l0NGc9PSIsInZhbHVlIjoiYnJLaHR5KzJZL0ltem84MUFhdWtKNldpaVRCSkJmbGppTTZKMExtdGxib1puSGVrRm16SnJwMWwzWmhQZHF3ajIrZ0oyaFgybGZCTzFqVi90anBDK2FwZjE3UDNUdFVXaC9qbWpXRXlsSVZxZjlvdUFNdnJYd1IvMHI4ak41Y0dXZytsS3lJc1lEY0dJc05IMUd0QWFlcHBFU0pSTjVVT0ljd0p3cUp1NkFnd3pPOHlLYWkzYmY3MDZkSmRsbWtrZkMvQUZ2UzlHQXp6bFNCNUNielN1Y2hDanZoTlFCMlRRMEwzSkVKR04yZnA3Qnh6dnpBc3hZenRJak5LRVdQUFIvQ0xIUktFYXdYUldUQmhCN21tQk9PVkpxRmMyd0h0UER4WXpjOG1hdFhZQWpLcVpUNWc1aGJjTXlIbGM1S0xZZlM0Z0Zsc3J5djJ5dVdTZDRlVTNSWi8zRm5xeDVHMFVwK0NQNW9TWmRZWUFmL0VvL0hkSWNGS3E3cWlOMWpmUCtxblRFcGIwWGordjEyWDV6Q3FtZjFvdjIxNkEzMG1tem5Vc3JRZG9WWUVFcHQwZXpVZTVPekhBTkJQYVJEaWNlTE5RQkFqcnJqL25QY1FJN2ZEMnJvVkpBSnZaMC9VcEVhUHNkT3dNTVdldjVsUEUxeTNlQlY4dS9GME0yTHQiLCJtYWMiOiI2YmYxMGE1Yzk1Njc3YzEwMjBlZjJkZjczMTliM2JmODNmOWY5ZTA4MDJjNTVlN2M1NjQxM2RlZjQ1MTdlYzJkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:14:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-639924357\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1137260907 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1137260907\", {\"maxDepth\":0})</script>\n"}}