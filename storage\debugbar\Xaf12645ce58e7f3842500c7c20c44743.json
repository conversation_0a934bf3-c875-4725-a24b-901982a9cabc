{"__meta": {"id": "Xaf12645ce58e7f3842500c7c20c44743", "datetime": "2025-06-30 15:34:40", "utime": **********.384337, "method": "GET", "uri": "/pos-financial-record/opening-balance", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[15:34:40] LOG.info: Opening Balance Request Started {\n    \"user_id\": 22,\n    \"warehouse_id\": 9,\n    \"is_sale_session_new\": 1,\n    \"has_manage_pos_permission\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.354297, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:40] LOG.info: Returning opening balance view", "message_html": null, "is_string": false, "label": "info", "time": **********.355264, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751297679.944661, "end": **********.384356, "duration": 0.4396951198577881, "duration_str": "440ms", "measures": [{"label": "Booting", "start": 1751297679.944661, "relative_start": 0, "end": **********.272124, "relative_end": **********.272124, "duration": 0.32746315002441406, "duration_str": "327ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.272133, "relative_start": 0.3274722099304199, "end": **********.384358, "relative_end": 1.9073486328125e-06, "duration": 0.11222481727600098, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52151528, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.financial_record.opening-balance", "param_count": null, "params": [], "start": **********.360794, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/financial_record/opening-balance.blade.phppos.financial_record.opening-balance", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Fopening-balance.blade.php&line=1", "ajax": false, "filename": "opening-balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.opening-balance"}]}, "route": {"uri": "GET pos-financial-record/opening-balance", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@opinningBalace", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=262\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:262-323</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.024179999999999997, "accumulated_duration_str": "24.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3031769, "duration": 0.02286, "duration_str": "22.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.541}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3355591, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.541, "width_percent": 1.82}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.348786, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 96.361, "width_percent": 2.109}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.350519, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.47, "width_percent": 1.53}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2113717036 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2113717036\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.353697, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1137765122 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1137765122\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.355178, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/pos-financial-record/opening-balance", "status_code": "<pre class=sf-dump id=sf-dump-110480770 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-110480770\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-501358552 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-501358552\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-787965778 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-787965778\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297678216%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkowSzdxTGNXR3lXWEptK2kzb2doZUE9PSIsInZhbHVlIjoid3FvVGZ4RUFRRG9ud1NaaXBrM0w2bGg5eVRxZ1QySGFTWXNkNkh5eThHaTVSdUI4NWl0Zmk5S0FHK3JBamd4MmkwSTc3S1BtSUpLZkVpTnhweGZPUDJwQVoraHo5UGt1YU9ucmdOVmNrVnlIaGdnQUtUTjFQOHorZWdNTmdUZUp0VjhsdnZvdjAvekphRjVUc0Q3bzJHbDV4WU9aWld6dUpVejBrc0dOdWV4aGRzOWw5QnlLYzFORlNkYklteVMyNEhOWC9KSHQwY3k5NXAwVjFNK1JkTnlDSFRGTUY1WHdzTW1qRldpU3ZlU1dVNjZLVmJKeUk1TWQvT3NZb0NOZEppYmFIRUpYYkw4bXdUWU1uUkRqaXVWRmtPU0tCdHpza3FzTnUrS21ZZzc4cDBpU3JpdUZEVlBqQTVkeXdwZk1vcTVwVEozVUVObVRzSXYrYXB2RGhUTW5ZbzVRWFJjZGJDd1FLaWJtWjlJTWFqb1pIL1k1WlgrTkRnTmZtR2xtQ1BZWW56Y0wwdElFbVd5d29qaTRSMWp6L2VWUXI0b3gwMFdDclgwRE5QMVMweGh5d2JhLzVTUDA1UzFCWlpkN2hJUVBFWWlOckVWVkV0TTdCQWVRZDhOclhFZHRad29RQjRvY0NlU1hNd2JTL3A1QURjTk8vU0djWE9iblBCWjEiLCJtYWMiOiI2MzZmZGIyMTY1Yzc4YjVkYzNkMTBlOGNjNTE1OGNkNzI1N2U4ODBhNWI4MGNiMjM4OWQzZGY4MDYxODQ5YjI1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Iml1aXVzRFBLbVhKaHN0cEFsL0dENVE9PSIsInZhbHVlIjoiYkRmT2tYb3BHQ1I2NXpRanR1V2xUTzF3RTBKeW1NRFBoNlVTV2k0MzZ6NkNhVVNHYXVGOVpNcG9oeXdEcFA0UTRHbTdmK1BrbkNhL3NXYlJUd2hRQytzbFlxT0ZqVEF1U1Y4NkVJTit5NkpoWDNveDVObUNJZ0RSQlZOdWtsT29xellsY3Uxa3ZBbDU3M01DT3JnV1gwTXUzZDNJMzVNYkxUd0Uzck1MaVNBUWFVN0NITG5NbzRURnRUZi9pOE56SHVCZjVCVXdnQ2NSL3BtOTYzU0d1WXRMeDUzMkREa2RnZzFJQU1SVW9zbWIwcHZYOGdsTE1KQTVBMEFVbzlwT0pMNE9IODR0TU5GREtWUWFzeVg4RXFSNkR5MUt3RXFPZ0liQTRFNS9vOUdDRytnWFRIczc1U1ZtWEtuY1MzZjVaOUMyUVh2ckFnOElWMUV1dVo1dFlXdkZ4anlmMW56bXNncUNlR3RUQXhlaVBjMFFjMGhZVlV4bFN2a0ZzUWFDZmYzSW1YTSs4OWJWY0VGZXhOMjR1T2xCdHkzVGtlMWRiWE5iTWNYeFVzdlZwV2pOY21RS1RxamRTcFEwZDRWTElwekJHdHIzRTQ1M0NXK04zMWJmWHA5NEJGT2d2aDJNSXJQUUhUdzJ1bVE0U21Heis0WmVxZllYbXBEczNYSlUiLCJtYWMiOiI5MjNiYWUwYjNkMTI2ODE3ODE2M2NlNWIzY2MzOTVmMjZjYzNjNjdiZGQyMmI3MWU1YjRlNWNkZDJlOTc0NWE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1263825440 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263825440\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1065420239 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9mNkNsblkzSXMzdzFRdnJsbmxIc1E9PSIsInZhbHVlIjoiQ3REVjlkOUgzVVdtSGxJUXhsNlU0QTdSY0FGbVRXSDRJWHlOS0IvQzZxS3JrR1JQak1scUVzS2lsYXZaVGJoZHRTNi9nN3Z4dzByU0xFblZQMDhESVF0aSt2b3c5Mk1zN0c2WjVhS3NEUUNXSWdWQjRCTlpoZ09yM3NYbmhtaUxsa3ZReDVsTFM5bkk1c0V0OUc2ZE5nU2RISWxRL29EaER4eG9vSG50ekhjWmRaWjdqVjBoQ1JPdFd1RWcrMFU1WXljY1Vaait0NU1kbDF3bEpVd1RXOTMvQXlBb3VXcElPRlNLTW44RzVFQzNoSFJzY0xCOS9lL1Y4RC9leUhDd3d6TGJBSGpQUWw3bjMxVlVMN080aDAvaEdrYkNzQ3Y0OEd2d2psMkJ2TS8rTjRPSVRtUnRYS1pub3VGMzcwVjJCckNoTmFvdFZ6ekZpOHk5dHZ5a1VRampKQW52Vk1JZ1g1ellCR1pnVytlZ2YycFcrWkxSQjhVVHhybHZ2aWVkeWYyRWRKVFlQamF6aEx1cUNsSGxRdS9UY3ErVTUvSHczcVhvRjB3ampMaU9PMngyYm5saVc2c0tYdGNqNms4dmQ3K2wzamhmdTZDOEJDL2N0Undac3dhbnpTQXlKV2Y2ekdid0NWOWZFSzZiWG5mRVJKSlU4OTFTNk1MV0cwcWMiLCJtYWMiOiI0ZDk4OGY4ODFjNTcyMmNjNDI2Y2Y2NTRhNTNlYzVkYjQ3NTQ1NGM1Nzc0YTI3Mzg5MGFmOTVjZjViMjU1YmNkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJXcTRDUy9JdEZhQmdmNTFEOWI3emc9PSIsInZhbHVlIjoiQnBqaGt6dVB4L3g2RG1XOEhnMmc5My8zUXJiWm55WE5XNzhtWFU3SEkzUnFHeWJNNXYydDhLdU9nWU5TRXVmZS9zYkRUVWo3UW9PcG9Dc20yYjQvOFNJR2dseVZnd3dvWTJXakZZQ2dmYXF0bVZlaFlTZHl5bUlwQkpwL1dJWTMrcnlRTWpiYitvc21WeVJ2QXBZcWtRc1RMd0JTakxMWXBBRG9CRitjSE9CUjZOZWJJSlNyNmdXY0cwcjlXNisrZ3h3T1drMGVtZFowMlBQOTJqeXZDWnE2SVluT2JEaWhxb2pwbUdpb1BSYVY3aVlHcUU2dm0xV3Q4Y3RoVTUzL1pIN0hCMWFTNVdCVHV1dXZwaFdUZFV6UElRUzZEWWdpcDJTWllocVNTZ2VVVnlmTlJlSmVmdWs0WTFkUlFCWDVyd09xNHp2ZTdoTllYTTRYdDZ0RmN5VkI2YStXUE15YUEwVGd0QVl6YnhpeElsV3lUZ0xPdTlYdW1nbHg0MEFKRjBKQVptQkd3MDN6Z0svQUtyUzUvZHQ0bDRES3krcWpBYXdlQ1VXRTh1YUlpbVVqNkg3Q3hNNHhyOG5sOEk0a25uR1BRZnFiTTBPSmFlakE3OWplU0dSZURRaitCdFY5RjQvdjluTjJBSjBxT3p6K0pnSmxmMnNQUW52dXNPdnAiLCJtYWMiOiJlMDQyOTNhZGNiZGJlZjA3ZWEzNDE3NmE3Njk0OTI1N2Q3ZWU2NDA0NWQyMDM2N2IwZmEzMDYxYmQyNjg0OGY1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9mNkNsblkzSXMzdzFRdnJsbmxIc1E9PSIsInZhbHVlIjoiQ3REVjlkOUgzVVdtSGxJUXhsNlU0QTdSY0FGbVRXSDRJWHlOS0IvQzZxS3JrR1JQak1scUVzS2lsYXZaVGJoZHRTNi9nN3Z4dzByU0xFblZQMDhESVF0aSt2b3c5Mk1zN0c2WjVhS3NEUUNXSWdWQjRCTlpoZ09yM3NYbmhtaUxsa3ZReDVsTFM5bkk1c0V0OUc2ZE5nU2RISWxRL29EaER4eG9vSG50ekhjWmRaWjdqVjBoQ1JPdFd1RWcrMFU1WXljY1Vaait0NU1kbDF3bEpVd1RXOTMvQXlBb3VXcElPRlNLTW44RzVFQzNoSFJzY0xCOS9lL1Y4RC9leUhDd3d6TGJBSGpQUWw3bjMxVlVMN080aDAvaEdrYkNzQ3Y0OEd2d2psMkJ2TS8rTjRPSVRtUnRYS1pub3VGMzcwVjJCckNoTmFvdFZ6ekZpOHk5dHZ5a1VRampKQW52Vk1JZ1g1ellCR1pnVytlZ2YycFcrWkxSQjhVVHhybHZ2aWVkeWYyRWRKVFlQamF6aEx1cUNsSGxRdS9UY3ErVTUvSHczcVhvRjB3ampMaU9PMngyYm5saVc2c0tYdGNqNms4dmQ3K2wzamhmdTZDOEJDL2N0Undac3dhbnpTQXlKV2Y2ekdid0NWOWZFSzZiWG5mRVJKSlU4OTFTNk1MV0cwcWMiLCJtYWMiOiI0ZDk4OGY4ODFjNTcyMmNjNDI2Y2Y2NTRhNTNlYzVkYjQ3NTQ1NGM1Nzc0YTI3Mzg5MGFmOTVjZjViMjU1YmNkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJXcTRDUy9JdEZhQmdmNTFEOWI3emc9PSIsInZhbHVlIjoiQnBqaGt6dVB4L3g2RG1XOEhnMmc5My8zUXJiWm55WE5XNzhtWFU3SEkzUnFHeWJNNXYydDhLdU9nWU5TRXVmZS9zYkRUVWo3UW9PcG9Dc20yYjQvOFNJR2dseVZnd3dvWTJXakZZQ2dmYXF0bVZlaFlTZHl5bUlwQkpwL1dJWTMrcnlRTWpiYitvc21WeVJ2QXBZcWtRc1RMd0JTakxMWXBBRG9CRitjSE9CUjZOZWJJSlNyNmdXY0cwcjlXNisrZ3h3T1drMGVtZFowMlBQOTJqeXZDWnE2SVluT2JEaWhxb2pwbUdpb1BSYVY3aVlHcUU2dm0xV3Q4Y3RoVTUzL1pIN0hCMWFTNVdCVHV1dXZwaFdUZFV6UElRUzZEWWdpcDJTWllocVNTZ2VVVnlmTlJlSmVmdWs0WTFkUlFCWDVyd09xNHp2ZTdoTllYTTRYdDZ0RmN5VkI2YStXUE15YUEwVGd0QVl6YnhpeElsV3lUZ0xPdTlYdW1nbHg0MEFKRjBKQVptQkd3MDN6Z0svQUtyUzUvZHQ0bDRES3krcWpBYXdlQ1VXRTh1YUlpbVVqNkg3Q3hNNHhyOG5sOEk0a25uR1BRZnFiTTBPSmFlakE3OWplU0dSZURRaitCdFY5RjQvdjluTjJBSjBxT3p6K0pnSmxmMnNQUW52dXNPdnAiLCJtYWMiOiJlMDQyOTNhZGNiZGJlZjA3ZWEzNDE3NmE3Njk0OTI1N2Q3ZWU2NDA0NWQyMDM2N2IwZmEzMDYxYmQyNjg0OGY1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065420239\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1551784080 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1551784080\", {\"maxDepth\":0})</script>\n"}}