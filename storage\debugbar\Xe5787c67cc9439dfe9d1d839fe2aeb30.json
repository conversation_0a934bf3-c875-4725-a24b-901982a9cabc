{"__meta": {"id": "Xe5787c67cc9439dfe9d1d839fe2aeb30", "datetime": "2025-06-30 14:57:19", "utime": **********.532102, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751295438.943534, "end": **********.53212, "duration": 0.5885860919952393, "duration_str": "589ms", "measures": [{"label": "Booting", "start": 1751295438.943534, "relative_start": 0, "end": **********.446135, "relative_end": **********.446135, "duration": 0.502601146697998, "duration_str": "503ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.446148, "relative_start": 0.5026140213012695, "end": **********.532122, "relative_end": 1.9073486328125e-06, "duration": 0.08597397804260254, "duration_str": "85.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45553736, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.004229999999999999, "accumulated_duration_str": "4.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.494624, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 74.941}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.513584, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 74.941, "width_percent": 13.712}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.520801, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.652, "width_percent": 11.348}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-470056421 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-470056421\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-177785890 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-177785890\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-344976840 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-344976840\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-263768722 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; _clsk=hcmbe9%7C1751295430794%7C1%7C1%7Co.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjdrUnF0UzVBZkovQ3RnSE1zZ1lXeUE9PSIsInZhbHVlIjoiRk04RWpRT1JNa2VXWmdqYVhKemplUnlIUDlXREpHVjFpWnYyVUdkTE14MzU5dkc1K2ZueWtGWnczb0NscHNvVVZ1Q1NRMDhkWklMTkZ5Zm5UZWVzYnpzUFRLaE9ia2RBR1RyNUxZSVM2eTNZSmNkMGszRThVbWJITWRPNXl3Ymg3b0pmREQ0WFM0MFo2NVdMSEV5aFYvdFZuWmRsVW1BWlpGQmZrQWZ0NituWEFhTlFNR1RhdXRWcVJBbmowQngyYlhLaFh4UmR4eHMvWWh0SDJBL25RTUt2S2F2UVk0YVBqYzlNbkhVTTdvNndwUGRycTV2UHlyd0d0ck9tNWpkOGs0c1ptd1R0QWZTRjk0VlN3dEdRbGF4ZGkxdThlSnd6SVpyVkdTc2pOTXhSeC9HckZnWEpoMFpBd09iS3hiZm1SSzY1N1VYcXhhVVYzRE1hY205c2pqZEQveEhaUWpZYVYvU1FqQjR5NDUxbnp4MSswbjZyeCs1MWFMbUQ2bVp1akttZTBsZ0I5bjk0dndTL0lwZHQrNnBieWlrM0JpSHlSKy8yVUlQeHFaK0Y3M0ZLUytaQkdmTldka3dmTmhETFFBMGp3SExTZkI0MHN4K252MzhVL1JmZ2toS0N1cnRHRW1iUGNIMEdmYmdhUWRCUHR1cjBYaHNtcG05M2M0Z2MiLCJtYWMiOiIxNTgxMjI0OTlkOWNhZTAyODY2NTMzNDBhYjA3MjY5NDViY2Y2ZmM3YzJhMDU1ZDU0MjgzZGNkNDBmNmE5NmIzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IndiODJHTGhmUFdFTVI0L1ZyVWpPOHc9PSIsInZhbHVlIjoiWk1JRFIwcUhOcU1YamxnSFI0Z3RCRjcrNlV1WHR0eXVUVVp5c2hwaFJyamFlMXN2cldVdXp3anVoK2dOaU9LZ042ZHlEdXdhcnpBOFBjTzNaeG1rZ3pOVXlydlVXWVd2LzJuWjBQSFJXaGZoN2NRNlVIZGZHK1NLdVJiZktQcnNjM2daRkc3RFhmZ29UQUl3R2VvVTE5bSsrN2hvSUFxVUxua2F2ei96QXVYcit0SnVDaHRoaEF2eU1EaFp5M3c2ZU5Gem1uTkEwR1dDcXhRQjdoSUJtc3puVHVLTUhDVGpiSStJZTNaQlZNVWtpZkc5UWdPd2tpalVqRThZeXNsV2NsekVzYlZhWEFQdDRubTVMWHFSRFd0NnJyUDhONndQSUlsWVZERFZDWEhKamxyWDJsblFxRWxqdXVXUS8xbkhwaWxUSWIrTnlHZmRjMm1TbUhWYjM0SGx4YkJyR0JocUpWRVVDNlQ2SWZFUFd3ZjZhZ3RFN2tic3Z1cXZEVzlvT2RQSFpOSGNjZG1TbjJvYzJXT3JWNmRXdkhvbmNzajN3SjZLRHVsLy95RmNkSi9pcGJ0ZFVQRndPczNTeVg0UllTTWFmd0pqNVdtQWg3VUJaWjZhYTUzQ2VWRjZ1dm9qRmF3bnoyVXVrTzRJNU51MVBRenppNnpoNHFleHhTYWwiLCJtYWMiOiI4NTU0YzZmNmQ1M2QwYjA3ZGE5YjcwOTE5ZDc0YjM2YmEyM2EzZTU4MDk1ZmY0NDJkZWQwZjNhNzNlZjRhZTRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-263768722\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-253174289 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-253174289\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1818461068 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:57:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpsTWNYMXl0NGZ5WGhXYS9rQ1RoWXc9PSIsInZhbHVlIjoieWF4Zk1aQzN1MkNqVEM2dHJTcXVNeS9KMGRVcUhLcnhVNGFUaW9CWVFEM1pNNll3ZXNaZDY1UVBQYUprdXhTZENqdU83LzNjSVIzVEpWWCtZZFB6bUVjQ3lVVVkrS1FwdFdBT0pYeDNPMTJja3hydXpINVFJckY0N1NneFVrODBJMHY3cWRnUU1WdHphb3F5RFhWMVlzWnY3aW45QXE1cGhsR2krbnp1bzBRb3RGUmFHS3BOenB2Y2dkM2ZBditveG9TN1dia01WVXp1eXMzelZkZVpIYjFSZ3pvSUkzVEFIT09MeHhvOGhidkhVOUVMM3JTOGNPTmp4Nm8zelQvbGxVL0ZKckhVcHNQaWpBT0ZrOGk2dlJscmZiVnBxcmFPR0NBc3FEM1kyZ1hTeHRnKzZyVUN1cjBPWEtTZ2tvdEJMVWdNeXNpRlJlVktZTDNjVlpyNFd4K29VMTh1NmZ3YWVRUiswcjI5ZnV2U3U2VnNWT3VvUkdoM0N6SGtLcXlPOGhHVm5OVjBRWG5LVFdaRURRSldYNDVDQ2FZakZ1Vmg3aTVUV3dnb3dFaUlMMDJveEY1NEZSQnJTVkJ2Y0RkVWxNL0YwL3pPR0ZrT2lzejFsSUttcEROY1FYd1diTUJoVHFJS0lycHZJRU9FVmlVZ2xWTXFKZnE2NmRZODNQN3EiLCJtYWMiOiIzMmUyOTU3ZjZhNWVlZTcwODkyNDViYTc3NzY2NGE3NzViYzlhYjQzOTRhMzg4ZDRiMjQwMmI5NDM0YzQ4YzczIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:57:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Inh2WnRuSUc1MlJiMC9TbHJtSDYwTlE9PSIsInZhbHVlIjoiamZkd3JicnFTaW9vL293eDVXN25MMjNobDdxeDcrMHN5b2ZoWC9EUHBUWFRCT056MGtadWJPbzAvSGM4TnhIVzY4aERnVkZUWkZDY2dIL0NWSHpnbnZDTnBqTWo0RFRQd3JqYm4rTjh3VlNvWmV1WEp6V29zNS9XY1IrdU1oaGs0eGxPdStBSm5qYlJsY25sUS8zY0Nzb2FEb0YvWDlWcVNzYlV6MTdqcEtxbUJ5amRUL0ZMOFFvZ0Z1ZTFGVzRpWC9SRGc3dGNZN0VzTjEwb0thV2JKa2I2dC9jSDhqQzBtS1VJZ0J5eDRMODNhTGx6VldEd2w3QytJTkFpempsWVZzeWxIaUNseDVQcURxNW84WS9qWXFidWYzMXVwR0dnOG4rQ3NKdW9rRk5GYXZ3Z0hUTU91MFB3RWRQM2w5SUIzcU9xbGZzWS9zdVNEUWttRFJaU2RRd0gzbUxiUDZEbTdTN05LK1ZoZUo0SHU0OTBXV240RjRLdzJ3SU5pZXZBRjFQVjF5bkI0QUVNbDhFcHlxODRCdFU2bWdIanVUS2NidUQyelNEZVhLZ2pVMEE5ckwzQ0ZOUDNqNWtub09OQ2RPSXpJTm95SlgzNlBqVDhac1NvWGMwMnk4NVoyUDJpb04wRktZMU1tTnJQLzZmWmxVV0RsbW0vWmlkT3J2SmMiLCJtYWMiOiI5MWUyYzZkZDJkZDZmN2NmZmM5ZjMzYWI3ODQ5ODAxZmEyNWU5N2M0MTQ3YjUxMzczNjMwNTIzYmE5MjEyODYwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:57:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpsTWNYMXl0NGZ5WGhXYS9rQ1RoWXc9PSIsInZhbHVlIjoieWF4Zk1aQzN1MkNqVEM2dHJTcXVNeS9KMGRVcUhLcnhVNGFUaW9CWVFEM1pNNll3ZXNaZDY1UVBQYUprdXhTZENqdU83LzNjSVIzVEpWWCtZZFB6bUVjQ3lVVVkrS1FwdFdBT0pYeDNPMTJja3hydXpINVFJckY0N1NneFVrODBJMHY3cWRnUU1WdHphb3F5RFhWMVlzWnY3aW45QXE1cGhsR2krbnp1bzBRb3RGUmFHS3BOenB2Y2dkM2ZBditveG9TN1dia01WVXp1eXMzelZkZVpIYjFSZ3pvSUkzVEFIT09MeHhvOGhidkhVOUVMM3JTOGNPTmp4Nm8zelQvbGxVL0ZKckhVcHNQaWpBT0ZrOGk2dlJscmZiVnBxcmFPR0NBc3FEM1kyZ1hTeHRnKzZyVUN1cjBPWEtTZ2tvdEJMVWdNeXNpRlJlVktZTDNjVlpyNFd4K29VMTh1NmZ3YWVRUiswcjI5ZnV2U3U2VnNWT3VvUkdoM0N6SGtLcXlPOGhHVm5OVjBRWG5LVFdaRURRSldYNDVDQ2FZakZ1Vmg3aTVUV3dnb3dFaUlMMDJveEY1NEZSQnJTVkJ2Y0RkVWxNL0YwL3pPR0ZrT2lzejFsSUttcEROY1FYd1diTUJoVHFJS0lycHZJRU9FVmlVZ2xWTXFKZnE2NmRZODNQN3EiLCJtYWMiOiIzMmUyOTU3ZjZhNWVlZTcwODkyNDViYTc3NzY2NGE3NzViYzlhYjQzOTRhMzg4ZDRiMjQwMmI5NDM0YzQ4YzczIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:57:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Inh2WnRuSUc1MlJiMC9TbHJtSDYwTlE9PSIsInZhbHVlIjoiamZkd3JicnFTaW9vL293eDVXN25MMjNobDdxeDcrMHN5b2ZoWC9EUHBUWFRCT056MGtadWJPbzAvSGM4TnhIVzY4aERnVkZUWkZDY2dIL0NWSHpnbnZDTnBqTWo0RFRQd3JqYm4rTjh3VlNvWmV1WEp6V29zNS9XY1IrdU1oaGs0eGxPdStBSm5qYlJsY25sUS8zY0Nzb2FEb0YvWDlWcVNzYlV6MTdqcEtxbUJ5amRUL0ZMOFFvZ0Z1ZTFGVzRpWC9SRGc3dGNZN0VzTjEwb0thV2JKa2I2dC9jSDhqQzBtS1VJZ0J5eDRMODNhTGx6VldEd2w3QytJTkFpempsWVZzeWxIaUNseDVQcURxNW84WS9qWXFidWYzMXVwR0dnOG4rQ3NKdW9rRk5GYXZ3Z0hUTU91MFB3RWRQM2w5SUIzcU9xbGZzWS9zdVNEUWttRFJaU2RRd0gzbUxiUDZEbTdTN05LK1ZoZUo0SHU0OTBXV240RjRLdzJ3SU5pZXZBRjFQVjF5bkI0QUVNbDhFcHlxODRCdFU2bWdIanVUS2NidUQyelNEZVhLZ2pVMEE5ckwzQ0ZOUDNqNWtub09OQ2RPSXpJTm95SlgzNlBqVDhac1NvWGMwMnk4NVoyUDJpb04wRktZMU1tTnJQLzZmWmxVV0RsbW0vWmlkT3J2SmMiLCJtYWMiOiI5MWUyYzZkZDJkZDZmN2NmZmM5ZjMzYWI3ODQ5ODAxZmEyNWU5N2M0MTQ3YjUxMzczNjMwNTIzYmE5MjEyODYwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:57:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1818461068\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1883718816 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1883718816\", {\"maxDepth\":0})</script>\n"}}