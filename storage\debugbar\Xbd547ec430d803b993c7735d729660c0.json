{"__meta": {"id": "Xbd547ec430d803b993c7735d729660c0", "datetime": "2025-06-08 00:30:04", "utime": **********.423959, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342603.510545, "end": **********.423985, "duration": 0.9134399890899658, "duration_str": "913ms", "measures": [{"label": "Booting", "start": 1749342603.510545, "relative_start": 0, "end": **********.264967, "relative_end": **********.264967, "duration": 0.7544219493865967, "duration_str": "754ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.264982, "relative_start": 0.7544369697570801, "end": **********.423988, "relative_end": 3.0994415283203125e-06, "duration": 0.15900611877441406, "duration_str": "159ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48114848, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.009909999999999999, "accumulated_duration_str": "9.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.334879, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 32.694}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3545399, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 32.694, "width_percent": 11.302}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.3842869, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 43.996, "width_percent": 8.981}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.388207, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 52.977, "width_percent": 7.164}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3985412, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 60.141, "width_percent": 27.649}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4078841, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 87.79, "width_percent": 12.21}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-498405899 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-498405899\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.395811, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-826534981 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-826534981\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1549866415 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1549866415\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1204893910 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1204893910\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-894096387 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749342593005%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik8xa2dBQ0hkdnloTVpGMjZsNEI2T2c9PSIsInZhbHVlIjoiOEdPNXV5TVl4K20yUnhlZWhRVjZEOXVESDhkdW9pdFJoSTVSNUl4KzVjWlNuam5uRXZwYUtCaW1zUXVCZXJhV0xsdXRYNkRrdW1JVXBDVkVKNktCUGdYWXFTdzFQNjN6Tm1pVDFLZHNmczF2WHIxSklnWXpsb3dYTG1KenBUNXRWYkVhbnhXZFMvL3ptY2sySmphL3kxYU4vQyt5dHBTT3Y5YnZPVzB3MGl5OTlKalVkOWZoRGhUNVpQMFNQV1B6UjBGbFMvY1ppQjdQVFhoRHE5STNtTDc2Z25GUVpUSlBLTTR3ZE5NUzAyVk4zZGJVUEZ2N2piN0FPYVNuaVIrODRDemwzSk9HaTFiS29zamZRMFZWVVVSeHBCWFdxd2pMWXJRU2ZHdFMvazZBYytab0dpbXptSkVqTXduSVlrcHNHNzJLc2k2VFFUUjNPZXNMSjBzbG9qNDdyTFk3dGxQSmNkVFVsTVQwaDl0dGpob09jTTFYaThIa1RBekJMSC9QN285S1FqUmxtR2JLa1FpdGhia29WbTA2Z1FKZlFoc0hEaGZQc2JtTFFGTUtKOXdTcFVBdURQamI5NDJpN05ONCtsdDhBRkR4aGJHOFpFcW82Q0ZiR09qOFQ2czd0eXBBUFd0Wjg3NXFIS0RvRSthQjNSTHUrUXVXQmpINzhWeXgiLCJtYWMiOiJhZjEzMmVhMjE0MDgzYzE4MzdiODVlYWU1MGI5ZjQ2ZTk4NmVlOTRkM2ZhMTE3OTgzYWQxMjVlODM1NjAzYjkyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImJnd0tHQ1dnK2Q0ekNNM1F2OFZtYUE9PSIsInZhbHVlIjoibW1UVm9NekR6dHBubFhxcDBWcTRXWHdJUmhQa08zVldZQWlORlloNjA0aXltOGVSNWNyVDArUDhOMkthZjZsRXY0OU9SWGkycUh0WnhyYXJ0cXN1UVVwMG5Rb0tNd1ZIMHVHNnNoZjRZMUFvcFpIVmNNU3paTk50cUx6am43V3ZzSURjUHpTZTJkT3RKbVAwY1ErYTZLK2dnYkhLVnBCbVJCRXZLaGxXbXVGOFRNL1hpTUZvb25ZOG96RWQwYjBJQXVNY3FKTGt5Ymo5VlEzcnJNdmtoemR5YjN3MVNtdGE3TGpna3hYUlE4cXlpTUd5RWEzZWJkaGIxTUl5Q0RnOXIxRW9HWjg2amhZNDZlcUoySStmN1ZneFFwcjRoOTNRWW1CTjh4TUVNWklZNTVhT25qaW8yRjN6SlkyUzh3S2xjZlhMQXBBcUpMclNmekdGZ3QrK0ozQ3lWU0J5NjkxRUJUTS9GdnpqVUtYcXNQNG5aMEdlVi9RbTA5Yk5lMGJzV2RST2Ura2ZENE8ybDVWY3pld0hjVlVSK25WYUdSUEt0aXp0KzZZTVdybzk2OUxBSUlTTTA2dTJWbnZneHh6SEt2VVBLSWFCc2tNeGh0Z041TFJpUUQyd25Jd2w4ZndBYWs5alg3U3orUllUWHRIQXJBbHZ5WDkrdWV0WUpCSnIiLCJtYWMiOiI1OTcxZTc4OTg3MzViYzU2ZGMzZjdmODJhZTAwOWViY2E4ZGUzYTFlZmI4ZjQzMmZkYTY0ZmM2ZTg1YjUzYzBmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-894096387\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-801957126 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-801957126\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-531447745 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:30:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZBcTVZaCtQaTEvempJdDgxWHJZZmc9PSIsInZhbHVlIjoiandWSzFsRUVMZGEyWU93MUU5L1NBclc0aEx2eGt4MS82R0pDNWxBNXR3cE9NeDMvNGFZc21PUzI5SWFMRjFSb1VkdEVHeTZUSldVWHVBVHFoUzB0OVBnUVRxb3B4cW9jOVcwOU4va3UyVEZUc29nSmxNRmRYeFM0RGhWVitzVnpEQ1ZjSWk1Qms0NWJPVHlnQlV4d3A5Q09YNTl0UlhQWEloU3lqZVp3UzhDMkJSbmtmaDNIUllzeTFzcHl3L2pWVGFvOU5hYzNiTXZtYzZQMXBzUy9TQlA2YlExOEEzTjdqekhCUnRKaXpaUUJMMzRyaEhoZ0p1QWp1aWcvMkY0dTA1STV1SEhvd2p6Y3RMVk83L2JMcnZCUlhJc3QvZ1JVeDZKMnQ2Sm8xMWNIck1WZlNNc1V1eklvT0V0djFCcm9sVjdaOERqZGlHY3VSMCtZcWVkWUZiZk5CVW5FYVNWUEZVSVhSUTloTWViTDlaaWNkZkhBakpiNDRpUnBWeGx5RjJpVUtoc0hqU2JkTDQ4RGxLRXAyY2xZR25oZVZxR2FwUDdrTlB1TjZyWG9JcEdPWTY1cXg2NmVJT01EaVZkSytmZE1ZTlhLQVZaM0o5cFkrZW1NMEFNcUpwOHJGaERBdXhNcDBXSklWQUZtN0h5SU0xVy9ZTmVaandPUnVBVVEiLCJtYWMiOiJkMTQ2NTExNjEyY2M4NzU0YWUxNzliN2MwNTVlMDZkMzZkMDVmNTY5YzA3NjQ3YjBmMTc1NmJlNmVlOGVlNDgwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlR6azFockM5UVpQdzJ6LzdpS1RwVGc9PSIsInZhbHVlIjoiZEZwa0ptNlpDL2Rpd2NuMzFhSWtmQ3ZQV1lCR28wZlVYMXZsTjFZOWRSbi9Iek5TcmM2dDhQS0tyaXc5VkJONWR0K1h3MWw5QUVSVExlZ1RnOUlnL2FETE44WmMrTXRGanlmd3M4SkgwbmR6NkR3THdMYTI2alE2M2tTeUhrS2hCa28wczlHcm1xdTFYQVlPODhVWnZHTmFwdjdTaE5vZDJZL1hxcDZGc09ubWtZOEsweWNmQjg1dzRHd0VHbi85SmJwL2hEcytOeHNxaUJZU1VFSnpEKzRvSDd1MFB1cktvNXE2R29vZXRld3dveExObjVLTUNRN1FucXdtYVl1TE1yQUZoalhqRWxFOVBsSkJaNXczdnFsSkN5RkxZbFB5MHFuZjNuakNKRVFnM3U2MEQ4U1NRS090LzlyekVSbWt1NEdsa2lnTkYxWEFweFlJUGZUQTlNVFpRcktlSWpzemhpZzNQcHFITngwa3lmdmpNZWVKYWFETzd5UVp2aEhYRFFMVHVmZlZ3TkRoSHlkZkdQbUYzVFFxZ014alZuQ1d1ZUkyVElLSWJQYzhLV0sxWmdpSWhBY1RNU0UzRkd3WDdpSFpGb2RLRVYvQ1kvVGg3VnozMy9yWU1ERDZsSlNKa2FEQk5LK0NLSnllTFJOaXgyOUpvNHpHWThQaVJLUWgiLCJtYWMiOiIzNjQxM2MwYTc5NDUwNDFlOGMxMTdkM2U4Y2FlZjgwMmI4MGEzYTdmOGRhY2QyYTFmYTVlOGVhNmU3NGYxMjQwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZBcTVZaCtQaTEvempJdDgxWHJZZmc9PSIsInZhbHVlIjoiandWSzFsRUVMZGEyWU93MUU5L1NBclc0aEx2eGt4MS82R0pDNWxBNXR3cE9NeDMvNGFZc21PUzI5SWFMRjFSb1VkdEVHeTZUSldVWHVBVHFoUzB0OVBnUVRxb3B4cW9jOVcwOU4va3UyVEZUc29nSmxNRmRYeFM0RGhWVitzVnpEQ1ZjSWk1Qms0NWJPVHlnQlV4d3A5Q09YNTl0UlhQWEloU3lqZVp3UzhDMkJSbmtmaDNIUllzeTFzcHl3L2pWVGFvOU5hYzNiTXZtYzZQMXBzUy9TQlA2YlExOEEzTjdqekhCUnRKaXpaUUJMMzRyaEhoZ0p1QWp1aWcvMkY0dTA1STV1SEhvd2p6Y3RMVk83L2JMcnZCUlhJc3QvZ1JVeDZKMnQ2Sm8xMWNIck1WZlNNc1V1eklvT0V0djFCcm9sVjdaOERqZGlHY3VSMCtZcWVkWUZiZk5CVW5FYVNWUEZVSVhSUTloTWViTDlaaWNkZkhBakpiNDRpUnBWeGx5RjJpVUtoc0hqU2JkTDQ4RGxLRXAyY2xZR25oZVZxR2FwUDdrTlB1TjZyWG9JcEdPWTY1cXg2NmVJT01EaVZkSytmZE1ZTlhLQVZaM0o5cFkrZW1NMEFNcUpwOHJGaERBdXhNcDBXSklWQUZtN0h5SU0xVy9ZTmVaandPUnVBVVEiLCJtYWMiOiJkMTQ2NTExNjEyY2M4NzU0YWUxNzliN2MwNTVlMDZkMzZkMDVmNTY5YzA3NjQ3YjBmMTc1NmJlNmVlOGVlNDgwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlR6azFockM5UVpQdzJ6LzdpS1RwVGc9PSIsInZhbHVlIjoiZEZwa0ptNlpDL2Rpd2NuMzFhSWtmQ3ZQV1lCR28wZlVYMXZsTjFZOWRSbi9Iek5TcmM2dDhQS0tyaXc5VkJONWR0K1h3MWw5QUVSVExlZ1RnOUlnL2FETE44WmMrTXRGanlmd3M4SkgwbmR6NkR3THdMYTI2alE2M2tTeUhrS2hCa28wczlHcm1xdTFYQVlPODhVWnZHTmFwdjdTaE5vZDJZL1hxcDZGc09ubWtZOEsweWNmQjg1dzRHd0VHbi85SmJwL2hEcytOeHNxaUJZU1VFSnpEKzRvSDd1MFB1cktvNXE2R29vZXRld3dveExObjVLTUNRN1FucXdtYVl1TE1yQUZoalhqRWxFOVBsSkJaNXczdnFsSkN5RkxZbFB5MHFuZjNuakNKRVFnM3U2MEQ4U1NRS090LzlyekVSbWt1NEdsa2lnTkYxWEFweFlJUGZUQTlNVFpRcktlSWpzemhpZzNQcHFITngwa3lmdmpNZWVKYWFETzd5UVp2aEhYRFFMVHVmZlZ3TkRoSHlkZkdQbUYzVFFxZ014alZuQ1d1ZUkyVElLSWJQYzhLV0sxWmdpSWhBY1RNU0UzRkd3WDdpSFpGb2RLRVYvQ1kvVGg3VnozMy9yWU1ERDZsSlNKa2FEQk5LK0NLSnllTFJOaXgyOUpvNHpHWThQaVJLUWgiLCJtYWMiOiIzNjQxM2MwYTc5NDUwNDFlOGMxMTdkM2U4Y2FlZjgwMmI4MGEzYTdmOGRhY2QyYTFmYTVlOGVhNmU3NGYxMjQwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-531447745\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1163106124 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163106124\", {\"maxDepth\":0})</script>\n"}}