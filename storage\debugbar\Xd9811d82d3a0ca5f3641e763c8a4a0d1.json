{"__meta": {"id": "Xd9811d82d3a0ca5f3641e763c8a4a0d1", "datetime": "2025-06-07 22:18:43", "utime": **********.402681, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334722.088309, "end": **********.402716, "duration": 1.3144068717956543, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1749334722.088309, "relative_start": 0, "end": **********.23501, "relative_end": **********.23501, "duration": 1.1467008590698242, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.235033, "relative_start": 1.146723985671997, "end": **********.402721, "relative_end": 5.0067901611328125e-06, "duration": 0.16768789291381836, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45054800, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00732, "accumulated_duration_str": "7.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.323763, "duration": 0.00491, "duration_str": "4.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.077}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.356018, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.077, "width_percent": 16.94}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.37838, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.016, "width_percent": 15.984}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1983795776 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1983795776\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-271423373 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-271423373\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1988594085 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1988594085\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-686833372 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334714308%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJiWEQ3UnlpZ2k1blJBSDVnWVN4Ymc9PSIsInZhbHVlIjoiRkRLQlQxSnIrcG16d3hMMklKVHZEL3ptNzl4czJ5SmZ0Q1lhaDN1ZkhUSVBRa1k4MG4yQjVlMlZRN0lXd1dlWHlPRXdRUmxxeG5qSlM3Z0tYMUNjZUE2YkdzRHp6OTg1Wk1PUGJwMVRlelpPMGdmQ2VjZ1ZIRFdRS2tvYkVNdm1TUW5vNkhXZ0ZpUjIwdGMrYXI5aW9ISDltQWI1RFVRejRJOWN6NWUremdmRExBcUswZllDU05rRGoycEdua3RnNktpR2NxeUMvcTU1VW1yOE5SankrQ0JFbUMySDVQUkRoUjVyMjlTZnVEeXJmUWw5b1pvczN5YzZyMVRPQWt2Q0ZCeUhVc3RFY0xxVmFNVUd3TGhMUTI5bHFvUkJleGR6RWxhSWM5eFhZa3ZHaHpFUU5sZlk4d2o3RDZYVXBEK2xxT01mdmlINUR4OXFNSDI3dVd1eVovWnVmb1hPc1p1V25lTlpMUUZKR1o5ajNTb1l2ekR0U0NUT2p2QjVvM1lQVkEyU3o3NkFaWWdHaFNaMzZjSzlXWEVldklDZkREK3BIOHJBTEhTREtZV04wOGZSb1J1dWRWaE1aSFNyUmY2TERRdkxrN0ZDSFJ1alNFK0ZDZW8xb01mOHErZVYzbkVUYjNiQUhYcmVTeFhoQnZ3MWFnN09OR21sVlBmbzNrZFoiLCJtYWMiOiIyNjI0YTkzYzlkMDdiMGMxMmZiNWQ4MzEwYjQ0MGY0NWM1YzA4NzU2ODQ0ZmNlNjljZWJlODk3ZGYxMDM2YzhjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpCYnB1WFVqN1ErZzZUczZNbjc3MHc9PSIsInZhbHVlIjoiWUxkMkxDbVBwcmFzNWR1Wm95Um1LbmFPcWtUblA0S1N5LzlrOGRLTS9KalVRSGkwYzhmbm94S0puNWV5UUFxdmk0bnZ5NkhHRU15djB1aTBvVWkxenNLTXBhUjRzdFJKUGZoUHNEN3BWU0JqOFQ1WkNrZysyQXl5cEFoVnFkMjZUN0N6WkEza2RrS3Q3VjBoR2YwOHZPb0x0ZHhLcEd1cVV3a3RwMEZIMkNIdXIzY2tZT3V5c3FvdHhiYmZkbk5MV0Q2MlhBK0lTYlN1dHdHYmNIbVlSakhhK2d3aHpOTFhTYWZiT3NHMEZNNHgrNDkwR3MrVHVBaFVkNmJaZHRoS0FXZWZoVEpGOGlzbW9lSGpCMFRPYXdGMFFLSVprcGg5WFFwNXhVbzE0N05TTnpjTGFFQmd1aXhSS21icXdyVDR4YlBWN3RCS0RYakpHbFEyMllaaXVQNGRNbE50bSt4UGFYWHBCS0krZWVORVp6L25meCtPcjlFNmNqc1pId0RhY1dsa0FTUmdJaGhaWlA2WGxsYTFIRTJ4di9aWncwcXluclJ5c1JuZUk0am1IOVBqMk95S3NQOHJINytqVHBGSWdFcHJWWDA0a2FtbHR5YXkvOFIzeXd2NXRNeUdWUDQydWVRaU9aMTR5UlhHbENiOWtidk5XMmpad1VxVCt4VFAiLCJtYWMiOiI0YTNmMDEyYTBjODFmNTE2MmQ2NDY3MGZlZDVkNDdjNGZkZGY4ODEzN2Q2MDFlNDM0MDQ5MWNiZDA4ODA2NGY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-686833372\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1662425265 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1662425265\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1374602329 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:18:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjQvcElxRFN2WGtxZHNFa3N2MCtuY1E9PSIsInZhbHVlIjoiVmNKWWRYWlhqUzNaQUhQNHFTYWx0NC9TN0hnYWFERm1TcFAzTjlxSG1kT1pVQnJZSjViN2pVT25rVEJSOTh5TjNOYlVEckcxZzNMdGlPZS9EM01MdXZBNkRHVGt3NDZLOXlnYVBBREZmUTFoUWxpQ2ZHajFQUldDOE00c1VMR1luRlBITnNqd241Qk9HZUd6bHhzTkkvNldhaXYxb0J3UEpiaGZTTDk2RjVUZjFKY01vYUxOR3lXdmZ1Mnk5N2dpaElmeU05VytqNUFCOUVKZnJ5bjRzOURTVVhvdnNtNHpGN1R5aFVDaHVOQUQ4WkJwMmpseUJLM2kzNSs4TE1lSS9pcDJiSFZZR2phWWo4TExDa3dTa0F3dGRCNnl6TkJ0alkzTE1XV1RhVUdid1dOenUyNHJqOVduU0hWVndOSTdkUjhwb091TzRSaWhQWnRRc2doRkgveUxzUGhDZ3IwZ2dDU2pPYmVXR2VyMWV0MCtsenFHU0NieEtUN0x0NWlMS0FhZDZLQmwwcFJnc0tuUEErbkl0dUd5dGtOYk1kb21wSmtMN3lmejc5QnEvN1A0Z3I0dFRrQ3lac1B1VUFJU1RPU1lwTnlTeVRGTE1VQjV5cnVRcStvWUZOZWlOMDEzQnNnelljN1o3eEVyYzE4MmpJcksyRHJ4Y0RNeGNvMmMiLCJtYWMiOiIzZDUwY2VmZjE0NzUxN2VjMDljMWZmMTExYjEyMzliZTQ1OTYwOTc3ZDE2MWI2ODg3ZTZiYmJmZjEzNzBhMTJhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkgyQ1pTVFVTa05TYVBGRDBmY08xM3c9PSIsInZhbHVlIjoiVXlzOGlUck9qRFQyZWpMalBOZy9YbW1qS1A3VTNFYjlMWnZwb2dUYncwMnBzYmtxa3ROSm1RMUZQbktocWd4ZEV0RVlRQXpsaHdLckY2OTIzOForT090bU5vTndyUHI0blhlR1g1c253M3JsVzlFTEU3aWIxSnVudDZxblYxc2M2QXZiRVptQkpMZlh5d0dkZkJwYVozcEFQSHRZR242dVZ6YjJQYWpGalJiZUVXTmszSE5JTkRna0hZd3V0Q3Z0dkhuNUxudzhONktlWXRyTVE5eXhsOXhFSjNEbGEzVVNqc2FHS09oZVZVbC93amdLN3FPcHdhRnErL2t2QXhRNU82NG9HeDkwM016dFdQNUNDYmFaWldFZk5hVHl6R1hxaEFkKzZBSHhQR082NjJ2ZjVXSk9oZ1BaSFJ2M1BTSFpHSDdKQW1jVnpmKzdKQjEzdVZwTHlqNG1Ob2gwVmpZejNVT3kvN0ZFSGFoa081TDNQcDg3MnUyVUNlblliVnJqaWFTS3B0RS9yVXV4VUxrWndTQzd1MC9RTGpaWEZ4SzQ0UTlMK3VlbE1WUnY4N0dLMkF1RmlPckNUblhhenpCdFlWOWZMRmxxUTM2aFdmS0dIZmlsRzdzUkozT1IwbnUyaWxObDhyb1IvaVh6THgrVHEwZ290WlZBbHpjYUY5Z0kiLCJtYWMiOiI1Y2I5NjJiNmRmMWVmYzE4ZGExZjg0YzA4ODUyMWYyZTYzZWE5ODFkOGQyM2U0MzMwMjcyY2U3YjMwMjU4NTFhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjQvcElxRFN2WGtxZHNFa3N2MCtuY1E9PSIsInZhbHVlIjoiVmNKWWRYWlhqUzNaQUhQNHFTYWx0NC9TN0hnYWFERm1TcFAzTjlxSG1kT1pVQnJZSjViN2pVT25rVEJSOTh5TjNOYlVEckcxZzNMdGlPZS9EM01MdXZBNkRHVGt3NDZLOXlnYVBBREZmUTFoUWxpQ2ZHajFQUldDOE00c1VMR1luRlBITnNqd241Qk9HZUd6bHhzTkkvNldhaXYxb0J3UEpiaGZTTDk2RjVUZjFKY01vYUxOR3lXdmZ1Mnk5N2dpaElmeU05VytqNUFCOUVKZnJ5bjRzOURTVVhvdnNtNHpGN1R5aFVDaHVOQUQ4WkJwMmpseUJLM2kzNSs4TE1lSS9pcDJiSFZZR2phWWo4TExDa3dTa0F3dGRCNnl6TkJ0alkzTE1XV1RhVUdid1dOenUyNHJqOVduU0hWVndOSTdkUjhwb091TzRSaWhQWnRRc2doRkgveUxzUGhDZ3IwZ2dDU2pPYmVXR2VyMWV0MCtsenFHU0NieEtUN0x0NWlMS0FhZDZLQmwwcFJnc0tuUEErbkl0dUd5dGtOYk1kb21wSmtMN3lmejc5QnEvN1A0Z3I0dFRrQ3lac1B1VUFJU1RPU1lwTnlTeVRGTE1VQjV5cnVRcStvWUZOZWlOMDEzQnNnelljN1o3eEVyYzE4MmpJcksyRHJ4Y0RNeGNvMmMiLCJtYWMiOiIzZDUwY2VmZjE0NzUxN2VjMDljMWZmMTExYjEyMzliZTQ1OTYwOTc3ZDE2MWI2ODg3ZTZiYmJmZjEzNzBhMTJhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkgyQ1pTVFVTa05TYVBGRDBmY08xM3c9PSIsInZhbHVlIjoiVXlzOGlUck9qRFQyZWpMalBOZy9YbW1qS1A3VTNFYjlMWnZwb2dUYncwMnBzYmtxa3ROSm1RMUZQbktocWd4ZEV0RVlRQXpsaHdLckY2OTIzOForT090bU5vTndyUHI0blhlR1g1c253M3JsVzlFTEU3aWIxSnVudDZxblYxc2M2QXZiRVptQkpMZlh5d0dkZkJwYVozcEFQSHRZR242dVZ6YjJQYWpGalJiZUVXTmszSE5JTkRna0hZd3V0Q3Z0dkhuNUxudzhONktlWXRyTVE5eXhsOXhFSjNEbGEzVVNqc2FHS09oZVZVbC93amdLN3FPcHdhRnErL2t2QXhRNU82NG9HeDkwM016dFdQNUNDYmFaWldFZk5hVHl6R1hxaEFkKzZBSHhQR082NjJ2ZjVXSk9oZ1BaSFJ2M1BTSFpHSDdKQW1jVnpmKzdKQjEzdVZwTHlqNG1Ob2gwVmpZejNVT3kvN0ZFSGFoa081TDNQcDg3MnUyVUNlblliVnJqaWFTS3B0RS9yVXV4VUxrWndTQzd1MC9RTGpaWEZ4SzQ0UTlMK3VlbE1WUnY4N0dLMkF1RmlPckNUblhhenpCdFlWOWZMRmxxUTM2aFdmS0dIZmlsRzdzUkozT1IwbnUyaWxObDhyb1IvaVh6THgrVHEwZ290WlZBbHpjYUY5Z0kiLCJtYWMiOiI1Y2I5NjJiNmRmMWVmYzE4ZGExZjg0YzA4ODUyMWYyZTYzZWE5ODFkOGQyM2U0MzMwMjcyY2U3YjMwMjU4NTFhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1374602329\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-900846729 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-900846729\", {\"maxDepth\":0})</script>\n"}}