{"__meta": {"id": "X9e7f686845f17183835eb1156d731876", "datetime": "2025-06-08 00:03:43", "utime": **********.80529, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.167709, "end": **********.80531, "duration": 0.6376008987426758, "duration_str": "638ms", "measures": [{"label": "Booting", "start": **********.167709, "relative_start": 0, "end": **********.717192, "relative_end": **********.717192, "duration": 0.5494828224182129, "duration_str": "549ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.717204, "relative_start": 0.5494949817657471, "end": **********.805313, "relative_end": 3.0994415283203125e-06, "duration": 0.08810901641845703, "duration_str": "88.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45144232, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01799, "accumulated_duration_str": "17.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 26}], "start": **********.763667, "duration": 0.01732, "duration_str": "17.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.276}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.793351, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 96.276, "width_percent": 3.724}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-363309027 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-363309027\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-776794533 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-776794533\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1590444142 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1590444142\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1210818538 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; XSRF-TOKEN=eyJpdiI6IjkxVGtIVzlVOU91eUlGY1ZzOW5zRkE9PSIsInZhbHVlIjoibXAzRTI5SHFiZitRYWhQL2dIeVIyLytIOXk1OHZHTEs3cUVTY2VSRG1sTEdpVTJUZkhoS1FjQng4RlUvcEJNaWZ4ZlQwaEV4R09jV21rUFQ1WFAxcjdxckJtcEFRR3NUTDFOems5S1NBS1JwZjBmVVEwY2NMWUN1RDNvNkFMQTBwNlpYd0dxQ0VZalNLSFpzSWdMN1lKV21hNHdrVU13TjFqQzkvYWc1aEg5TjJFNXh2OHNiaU0wa2E3OEVYMWVrclBxSGJUYTJpeFdYa0wrNEU2L1RtTlY1RnZRYml0Zk9tQmE1RVM5RmNIMHNva0NCUFl6U1lZcC9oM0Q3Wmw5RVVpOXpOVXFDTThlUnhvRW1xY2ZxSHR5U3loRkFBclVxUmw3MDdzZlh5Y255blFvR0J6TlVORWR6eGo5anhTQlNzcldMaWpjeUFyYis0c0VXZlJoL1FmQ0Z1UEpkeEd2azdDWWYrcWtIVFNsZkVVdTR2aGs1WFB4ajZsM1BEM2RRZ0Ntd2FLNy9kaEorY3laVlRLNEYyNU9kSWxFaVJpdHozN0RjZXp5TEtCUEIwNXV2Y0djL3dyeW9RYmFaeHQwM0JXTkdsOHVoK2tlZnd3bDZBTmJPWnR3SnA4M051Z0xoODJ2MDY3eWxsK0YxV1FPNzhyVUF6dzdBSEFOQ09YUEsiLCJtYWMiOiIxNzBiYzczODA1NzZiOThiMjZiZmEzNzRlYWE2MTQ3MzE3NzZiNTgzMWIwMjg4NDY2MmE4Mjg0MjFhYjMyMDVjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImpIbmhSVWZxMG1wd2lSeEhuOWROT0E9PSIsInZhbHVlIjoiQkdjajF6UmV1a29VK3hOSHZ3ODYwVHdSUCttUWY4YkdXN0VBVEJZU3I1TkVYemFVTlFsaTJkNmpra0QzTEY3Q1NwU3lteTl6bjBvTWdEeU1tMVRZQ3NubVdpTkVoTEFVSVBwN3M2LzNjTG42djNWNnU4UEx6VFRhTllSaWRpY09sMURkeHQ2UnUwazQvd09aNzZ2ZXgvYUhxRDRseG4zT0JrYTgzdVZXSHBKY2l0OExxY3JOY2IyQ3pPOFp4MDhxdjRxNWw2MlpsR0VzRFZUbXVBbkZMRnRNcjJvMWNCVGptSjhUampLQ1U1NFdxNXpnR2xVV0tGMXlBSXB3cWdKdExCdzl2cGFHaFVvcytvSDlyMkFJOTdad2VRR1h0RmtGSzR5ZTBSalJDcHVTMjJZcDZaM2Z5eDdBVU5mbmUyUHBGYkxJcG91bzZFYlk5SnpuTDI3akc3Y1czNENiVldnc2NtRlA1dEZxRjduRXZ0ZWR4aEFNNDZDR2FIcUlWb0lrK01YUk1PMnJ3UEtOYXYvUGxRUHBteVRkTWsvelp2YSt2VmFuTythT3poQWhsQ0kvczJqTnBIdThXRUZVV2FVMXFZNkx5clRsM1l6cjBxS2QwM2NlelhGOUFCVnRvbmwyclFBbkZjSkhMaS9ZWXJkYU9DcUlFSGdoU04ydmc4cXAiLCJtYWMiOiI3MTdkMTE3ZmM3NGM2ZWVhOGY3N2IzYWQyZDI1ZWYxNWFmY2RmZjBjN2UzYzJiMzgxOGRmODhhZTg2ZTQ4YTVhIiwidGFnIjoiIn0%3D; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749341009015%7C24%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1210818538\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-18315239 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18315239\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:03:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldIQ3FhVkRuMTRQMlRPOHhaRCtIN1E9PSIsInZhbHVlIjoiWUVQRS9Eek5jRlVMTWpKeko3NCtiMlZGejlHMTJVdXJkY01qN3lXcnlmSWVSSkprSnVMdnN1UExrR0g3bS80WVI5YzhjaTF3aWF2Zk93Zi9FT2trYkZVNlVPMkwvTHpFM0hoSm1BNjJ0YkNGWU5wd0R3K2d1U3RnaGt2RDZIaitrbUNrSk45ZWRBb1JmUVlPR2hSd1FmNkRPL0VFUS9OWERsZUZpV3gxaHNTQm5nU3FCeENQaC9TUlZiVm5lSW51ZTkvemZ1bUFCUXpWaE5oMEExSW9QUGJNb1BPSHVGbC9hSENuaVR0LzdNb3dWaG5XVWdwSUJ0R1JIQW9veXFBcFJBSmRaR0VWblFoSklWSjZ5WHZyV3F0SThKNFR1U0llMnV3TEFjNWxuRnhTOHNnSElQY3lHTXhQY0wvYW9LTTc3WTJMR2grQjF2NlE3WW01Tm16YTZnbVdxeGVkRWtkOVBETVpzc0U3akQ2dUp2YmgwbU9CcEtGZHNRV0NoWDM4KzFJVXRZb3ZDWWx1a1FOMHdXdTZNQldWK2FnY3FRWU1pUVJnU2xtNjRoRURZb3AzVmxBQzRaQnpZbzV6d0FLWUV4Sm5CUm9FM2RGSE4wVlZwV3hld2I5dGpCQ3dWQ0FMS2gxTTJoSVcwaXgvOG5GTFFIS25xaDZFS3lmcFpucHAiLCJtYWMiOiJiODJmNTIzMTc5Zjc3Y2U0YTM2ODgxMzYyMDY5YTY4YWE3ZmY3MTMwMTE2N2ZjYTU2YjllNTM1MzgwNDVmMGRlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:03:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InVteW5TTFNFN0lidVFOZEQ4RFd1cFE9PSIsInZhbHVlIjoiMkYxK011NlpVdGFLeFdsRlk0OGxVa245NGovNWl0NU5ENnZCN2pPM2cvNE1DK3VJMkVDTXdXbVlBWEl0Q0NsZFVpRHZvQW5DVlpLQTVqQmVUVjZEdUpDNHZ6anNWd0l5YVNVUGpIZXNjSmxBYjR0NDVORU9jYzJvOTdQdHZXSzNuYjg1OUZTd0pzOUd1Umo3NUx3NjNZZWtSMWJFNFdTaXd0UjdqeWMrelp6aVFKTkNUM2I1SW92MVlKMC9Rem5rYW9McDZoVXFad2U1M2pQaGRCRmwzc1E4QW1EWHlOSWsxLzU0ckppQm5kV0JBelhxN2V3eVFnVGtiMWRZemxJSWNxcmlubGVQU0FnOXB1RW9RUXYrZStwR3Q4eVJyT09lRkt0U3RkeUFDK29kVUs0TlB0dEpHOVczNHB3d1VpNjJRUWxGWEp5OGlvN280ZGZvMUU2WlR2eDZIL3Y3Skl4S3pvcDU5RnZEOWdpOUw0RGJHaFc5disvTEtQWm1sdTg2eUErRU1lZ1A0S000ZEpucVhZM3FxRndxdndTVTQ0T2YrV2dhVHRneUFqRDA5UUEvL2JsQmJpbStTcnBJZC9FdkVFT2pCYXFxTG5KeGNlNVJ5NnVxRThhZWRrdGZXVUlsclRmenZQdjMza3Z0N3pSU1YzZTdJcUtDeDZsUlU0dVciLCJtYWMiOiJhMjM1M2JmYjMxNWZjMWI2ODVkOGIzZTU1NThiZDc0MWNjMDgzODY0OTFjNjFhOTZlM2ZmOTczZTc3NjUzMWFhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:03:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldIQ3FhVkRuMTRQMlRPOHhaRCtIN1E9PSIsInZhbHVlIjoiWUVQRS9Eek5jRlVMTWpKeko3NCtiMlZGejlHMTJVdXJkY01qN3lXcnlmSWVSSkprSnVMdnN1UExrR0g3bS80WVI5YzhjaTF3aWF2Zk93Zi9FT2trYkZVNlVPMkwvTHpFM0hoSm1BNjJ0YkNGWU5wd0R3K2d1U3RnaGt2RDZIaitrbUNrSk45ZWRBb1JmUVlPR2hSd1FmNkRPL0VFUS9OWERsZUZpV3gxaHNTQm5nU3FCeENQaC9TUlZiVm5lSW51ZTkvemZ1bUFCUXpWaE5oMEExSW9QUGJNb1BPSHVGbC9hSENuaVR0LzdNb3dWaG5XVWdwSUJ0R1JIQW9veXFBcFJBSmRaR0VWblFoSklWSjZ5WHZyV3F0SThKNFR1U0llMnV3TEFjNWxuRnhTOHNnSElQY3lHTXhQY0wvYW9LTTc3WTJMR2grQjF2NlE3WW01Tm16YTZnbVdxeGVkRWtkOVBETVpzc0U3akQ2dUp2YmgwbU9CcEtGZHNRV0NoWDM4KzFJVXRZb3ZDWWx1a1FOMHdXdTZNQldWK2FnY3FRWU1pUVJnU2xtNjRoRURZb3AzVmxBQzRaQnpZbzV6d0FLWUV4Sm5CUm9FM2RGSE4wVlZwV3hld2I5dGpCQ3dWQ0FMS2gxTTJoSVcwaXgvOG5GTFFIS25xaDZFS3lmcFpucHAiLCJtYWMiOiJiODJmNTIzMTc5Zjc3Y2U0YTM2ODgxMzYyMDY5YTY4YWE3ZmY3MTMwMTE2N2ZjYTU2YjllNTM1MzgwNDVmMGRlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:03:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InVteW5TTFNFN0lidVFOZEQ4RFd1cFE9PSIsInZhbHVlIjoiMkYxK011NlpVdGFLeFdsRlk0OGxVa245NGovNWl0NU5ENnZCN2pPM2cvNE1DK3VJMkVDTXdXbVlBWEl0Q0NsZFVpRHZvQW5DVlpLQTVqQmVUVjZEdUpDNHZ6anNWd0l5YVNVUGpIZXNjSmxBYjR0NDVORU9jYzJvOTdQdHZXSzNuYjg1OUZTd0pzOUd1Umo3NUx3NjNZZWtSMWJFNFdTaXd0UjdqeWMrelp6aVFKTkNUM2I1SW92MVlKMC9Rem5rYW9McDZoVXFad2U1M2pQaGRCRmwzc1E4QW1EWHlOSWsxLzU0ckppQm5kV0JBelhxN2V3eVFnVGtiMWRZemxJSWNxcmlubGVQU0FnOXB1RW9RUXYrZStwR3Q4eVJyT09lRkt0U3RkeUFDK29kVUs0TlB0dEpHOVczNHB3d1VpNjJRUWxGWEp5OGlvN280ZGZvMUU2WlR2eDZIL3Y3Skl4S3pvcDU5RnZEOWdpOUw0RGJHaFc5disvTEtQWm1sdTg2eUErRU1lZ1A0S000ZEpucVhZM3FxRndxdndTVTQ0T2YrV2dhVHRneUFqRDA5UUEvL2JsQmJpbStTcnBJZC9FdkVFT2pCYXFxTG5KeGNlNVJ5NnVxRThhZWRrdGZXVUlsclRmenZQdjMza3Z0N3pSU1YzZTdJcUtDeDZsUlU0dVciLCJtYWMiOiJhMjM1M2JmYjMxNWZjMWI2ODVkOGIzZTU1NThiZDc0MWNjMDgzODY0OTFjNjFhOTZlM2ZmOTczZTc3NjUzMWFhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:03:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}