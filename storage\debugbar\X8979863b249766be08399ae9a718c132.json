{"__meta": {"id": "X8979863b249766be08399ae9a718c132", "datetime": "2025-06-07 23:06:16", "utime": **********.818652, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749337575.781111, "end": **********.818709, "duration": 1.037597894668579, "duration_str": "1.04s", "measures": [{"label": "Booting", "start": 1749337575.781111, "relative_start": 0, "end": **********.670192, "relative_end": **********.670192, "duration": 0.8890810012817383, "duration_str": "889ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.670213, "relative_start": 0.8891019821166992, "end": **********.81872, "relative_end": 1.1205673217773438e-05, "duration": 0.14850711822509766, "duration_str": "149ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45582608, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0079, "accumulated_duration_str": "7.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.742904, "duration": 0.00534, "duration_str": "5.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.595}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.772641, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.595, "width_percent": 15.443}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.79129, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.038, "width_percent": 16.962}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1843054701 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1843054701\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2080894138 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2080894138\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1542994710 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1542994710\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1780132663 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2811 characters\">_clck=erumib%7C2%7Cfwk%7C0%7C1984; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; pos_system_session=eyJpdiI6ImVNL2JmZ05iY3huemxuNWZOWkJqeHc9PSIsInZhbHVlIjoia2taNWtSajhLZmtWVmhqZzM2a1VRQ1ZpNk8zdVZaUHlpUGlZVkRwREY2dy80cVN5Rk55RGZldFJIempxdmFVSGdtRDVQRkxyMEtpNU84THFWSlpJQWVjL1p0MzZnSFBQbTFFanNPcytGU1R1cjV0bDAyQkl1RURud1JKeHBNVjZRcUN6WGF5cDhZYlV0UVBGVXY4dURZYTdIY0dZcHdUb2JPcHM0V3FuSFNHblJLSTVwTlZJazU1SG1pZzl6aGZMbHpkOFRTMnRGWDNKSVVrdWNIa29wcGlUMTFtWTI1bTducU92LzZIcXNFamFDZUVlN1FTVHB3bXJSWHh5MFVEVmYzZ3ZRL0EvNHg4bjVNUWhjTFJPTWlGL05GeDNUbUt4d3NJY29rVXVOVGxka2Fkc1cxR0Q4UGxpWGVGVTVMUnU0UTQ1bXBXcmE4ZGIzTnpuWjNtMnQ0ZEdsbjIzOERhTWpFaHNtbHFLRkZQcW1ZQW1zeXdINmNmMERIWDcyZFhTenUrWklDQllEa0k3TUJkM3Uxd3E5UWhZRU1vT283ODNXU3VSVVBvTFZGK04zVDE0MU5YdXNqZVdjS0VJVi9POVJRRU80dkxsYmRpZWlkYXlwV0g1cEZvTys5ckhNUUZFTThmc2VrQWp5RmhWMTUzdlNYY01henM1aXFRUFZFRHoiLCJtYWMiOiI3ZmVhMjFjNGMwODdjZWJkNDZjNzY1NGVkZDYxODkxZTUwOWExNWIzYTE4YmVmMDhiMzQ4YjkzNjM1NDE3YzY5IiwidGFnIjoiIn0%3D; _clsk=vcxus7%7C1749337562427%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhyK29TazA2dEg1RUdwMXU3WGpWZUE9PSIsInZhbHVlIjoiMnlRMnVvOTl3V0JpSEljaFZRY2Zvb0JnckZyb0lveldERXN3Tk52TnByL1hJVGVab1dnVEhkRVpwWUNUMG1YaDlTQkgvL1pQNnJmcXVSc2tCVWdjM1JyOUhUSThRNHpkOTNtMXJuSURML29tbExMUC93RXFqSllPZUI0N1RqL1laQ0RvQzhDbHRSeWhFRVFCSWpCYzgvSnJPOS9TRzVGbituKzNuNFRBQzhVdmRHRmo2QlBJREthWHYxSy9MM2tJZXN6YldVVE14MG5nRVdZSUd3bnd0d0tQYlJja1BKMXh2RVhhZloyM1hNcUNYYUJEeEhwU1l4QmZKQWtXRFljblVBTDc4bitJZU8yNjlOc2lmWEhYMVpEQ3grRWtqTWFBU0RkVjVla0RwR0YrNkZUZHNQaTVuY0xFUnp4SzJtNDBmUytSRFhZMzg1Q25vNEY1RWVyMjQzRHZrRm01VlZhb1lBb0EzaEJib1Q4RlQwNjJpblY2YmFMMnhhbkhqd0ppS2tTYUh0OVlseHU1d2p1UEZXRzRseVFkSEtZTjdvRTNpVG5BU2I1c0h6SDNtN3M3MExaSXhQeVN4N2N4aVhvYkhOTjJRUkJaZG8wQnkwNmpaejBsVmt6bk91NGhSWVZOZktvVjFGaC9LeklIN2lzZUl2eFU3TGdzQWtpMDN6YXgiLCJtYWMiOiJiYjg3NTFmYzJmNjI4MDE5MjFkNjhlODNjYmQyYTExOTdkODJlMTI1NDQ4ZTBmMjdjNWNlMWQyMjdiZTJjNThhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjNTOEpKV1BleDNZbVhoQ2prVnAzbUE9PSIsInZhbHVlIjoiNVl0bEIvYk1ZQzB0Vmd4Q3ljcDhXVzNzTXEwOXVWbFI4TmF1NWJjdnBoVW0vM25NUVB2dHZ0YVRaNDdzOWpEWko0aFdUQmZST3MwZmJjWXoyRjZaOFF0bjJlVjRpbUJSZmRxRTVObVFDMU4yWVUzZDlpLy9ONGIyMXU4MWRwS09SOTZHd1hRMmkzWVZsc3FybDVlSGZKTENFSFovMFhCRDRZS0NYQjFpNUJ5dm1vWm5sMSswVUJKRDVNV0pQKy9NMkFaVU9McmF0dE8xU0ZtVVp0UXFLSCtVZlR3YVY2eWlMR3NrK2l5N1BhT05yb1F2TFdsQytkdG1sS2lMMnRWUUtHTnc0NStKalN3VGc3NnhzMS96M2tkUWFWZ3ZjNCtJbTZOZ0Mxd0NnRGVidzFubW52R3FzbmR2dGtPU3BCQ3NxUVdCVHREdHlFcno3UG1HdmJsN1J1RmFKNDZWdEVTV3FtaXV6MFQ5ay9Zc2VqcCtQZDVaNHYwdWptZU9kWlQxMzlCSjF5cVNmNDZHNzhVdG1hWkdoRTMvL1d1TVNaaTkxL0FMaGlER1BmNnFKOUUwbElKdXo4YUdiMWcrSjh3ejBRaUsvSjJWTEZWa0VobTY0OHRocWREdTg5bHBYdGJNQXpGVlJSTE84bEQ1NjRwaTk2elN5T1hPQi9GVEdmUGgiLCJtYWMiOiJhNWQxYzI1M2YxOTZiNGQ4YzdjZGUwZGM3YzJhZWYyM2EyZDNkMWU4ZjkxMzM1Y2E5NzNhNjNiN2FiMGU5MDNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1780132663\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pos_system_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k8SQqyhwBkgpW9lptEbMoTN8iNDNeZ6DzX94m50O</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1457748978 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:06:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRVWS8rZVRCWXpwNXptc21wY1pObUE9PSIsInZhbHVlIjoiUjNYTitmTmtDc0RTUFlaWGVWVnN0YmRMckVhMlZoKzd6dXcxR2Nyb1JsRVIxQ3BOa1RkSjBBK0JEdmZOYnExRFVBQUV0ZXNKQjFaM3JwMlBTMmt5dUhLN1Q3OE5RZjNleC92a0tiTy84OTdDbzI5U0lISmxTdXZxbVppdmxQNmFFTTNORHNxd3haNTBzaVhBN0pxNU9tMkVTeDYrWWNzSXNGTzFxZlgxMzk1RDZON1A5Y01JV3d1ZUd5QjNSL2ZUeWlwcHBYS3R4aiszNzBhK3NaNjdNKzM3OFdJdHVnUzc0WmxwZVlqNGF2M2Z0NmlYVHpiMUFLN0hodERyaVMrMVY4ejZnNmpwRXBlSGR3VVhmb0YyQ0lJOFhtdkp2R2VEVDdNV24vc3ArcENuSjRTZmd4Rmp1U1JCdUUrbjU1clZVRmwwVVlPTmpZd3o0UFZuVHJBU3N0YkFoSlZmZjJYSUNYaGx1Zlk1VTFQL1VyV3M4NjdxQTFZZ01mV1FGT2l3elFYRDRxRWlnQ0xZL1hjUzB3NHZmTndDLzlFMUtyd25MRWdUTGhyV09nRTVGYmpObzVZWFN1R2VYNTJXTk1KK1hCZDJ0Sy9xTDZtbzUzSXUvRC9jVWRjZkFVOER6Y3o0S3JBQm12TzRQeVZqUWhUVHVxa2M4YmpVOU1nTE1rU0QiLCJtYWMiOiJmYWZhZjRjOTkwOWIwMzgwNGRjN2Y4MWZiMmZhM2I0MDAyZTVlYzRlNGY1YTI2MmJiNDEzMmZkMWQ2MTBiNmE0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:06:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkVzNGR1MzRXbDJBMDNTSGZsN2tMbUE9PSIsInZhbHVlIjoiQVduRDFzY0NIbCtPOWlWUnhhNXhHRUpTQ2NsN2FDYitRUmx2bmdXa2QrVlNsYWdPVjRkdDd3dUhZWm9LaC9PeVMvZUNEQ3c0bElIQkJJejZMaGljdDdleERjQ094dHMySFRQU3pPU05SUTh6WmNNWElST2lNZ1VUKzMrM0Rkb3Yxa3FjaTViOTJCb25qZytnTG10djkxWVRFSGt3UjRCN1Y0U1BBbVdyTHJIT2lycEZneUMzZVA3cGRGTldJenlXWGZVVjB2WVVzaTMxZkZLM1VBNmpOZGNYblpKZW1IYzFpSWhmVzRFM3BZSmNObjgrOXFuU2FsMzRoVEZmK0tPamlsOExFMWF1eE5XMTdGeXd0dC9wemlONGFIVEcwSjl2M0plWjAxeSswQ2NxL3RRcUs4VEpTbDhEZ29tdStMNHNxdWtOL0h0ZldjckFONHQ5RlZoZytibmJhYUF4WW41VnIyT1Awb0JTZGY4WnRBbWo0aG94cXhJL3J6ejkyOXpaWllXVkhZQWkyZ2tiQXlnQSt2cUE0dXkwWVdGcytlMmpOVGNpOW5GbkZBM05DNDZNMytQdXd5dEJGYXA2Vm1ZWEVMUllYWG1UeWNNY2lmMk92UXpNV0lUdTBKTTRRczVWeGFsNEZhWjcvQXRQWXRIdW5lSjBWaGUxWVpTMDBPRkQiLCJtYWMiOiJhNDAxNDBjN2Y0YTllMTFlMTZmZGU4YzFjN2NlNjM4ODk2MjFlOTI0YjNmZTQzYjM2ZmU5MGMyZGI1Y2ZjZmRlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:06:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRVWS8rZVRCWXpwNXptc21wY1pObUE9PSIsInZhbHVlIjoiUjNYTitmTmtDc0RTUFlaWGVWVnN0YmRMckVhMlZoKzd6dXcxR2Nyb1JsRVIxQ3BOa1RkSjBBK0JEdmZOYnExRFVBQUV0ZXNKQjFaM3JwMlBTMmt5dUhLN1Q3OE5RZjNleC92a0tiTy84OTdDbzI5U0lISmxTdXZxbVppdmxQNmFFTTNORHNxd3haNTBzaVhBN0pxNU9tMkVTeDYrWWNzSXNGTzFxZlgxMzk1RDZON1A5Y01JV3d1ZUd5QjNSL2ZUeWlwcHBYS3R4aiszNzBhK3NaNjdNKzM3OFdJdHVnUzc0WmxwZVlqNGF2M2Z0NmlYVHpiMUFLN0hodERyaVMrMVY4ejZnNmpwRXBlSGR3VVhmb0YyQ0lJOFhtdkp2R2VEVDdNV24vc3ArcENuSjRTZmd4Rmp1U1JCdUUrbjU1clZVRmwwVVlPTmpZd3o0UFZuVHJBU3N0YkFoSlZmZjJYSUNYaGx1Zlk1VTFQL1VyV3M4NjdxQTFZZ01mV1FGT2l3elFYRDRxRWlnQ0xZL1hjUzB3NHZmTndDLzlFMUtyd25MRWdUTGhyV09nRTVGYmpObzVZWFN1R2VYNTJXTk1KK1hCZDJ0Sy9xTDZtbzUzSXUvRC9jVWRjZkFVOER6Y3o0S3JBQm12TzRQeVZqUWhUVHVxa2M4YmpVOU1nTE1rU0QiLCJtYWMiOiJmYWZhZjRjOTkwOWIwMzgwNGRjN2Y4MWZiMmZhM2I0MDAyZTVlYzRlNGY1YTI2MmJiNDEzMmZkMWQ2MTBiNmE0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:06:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkVzNGR1MzRXbDJBMDNTSGZsN2tMbUE9PSIsInZhbHVlIjoiQVduRDFzY0NIbCtPOWlWUnhhNXhHRUpTQ2NsN2FDYitRUmx2bmdXa2QrVlNsYWdPVjRkdDd3dUhZWm9LaC9PeVMvZUNEQ3c0bElIQkJJejZMaGljdDdleERjQ094dHMySFRQU3pPU05SUTh6WmNNWElST2lNZ1VUKzMrM0Rkb3Yxa3FjaTViOTJCb25qZytnTG10djkxWVRFSGt3UjRCN1Y0U1BBbVdyTHJIT2lycEZneUMzZVA3cGRGTldJenlXWGZVVjB2WVVzaTMxZkZLM1VBNmpOZGNYblpKZW1IYzFpSWhmVzRFM3BZSmNObjgrOXFuU2FsMzRoVEZmK0tPamlsOExFMWF1eE5XMTdGeXd0dC9wemlONGFIVEcwSjl2M0plWjAxeSswQ2NxL3RRcUs4VEpTbDhEZ29tdStMNHNxdWtOL0h0ZldjckFONHQ5RlZoZytibmJhYUF4WW41VnIyT1Awb0JTZGY4WnRBbWo0aG94cXhJL3J6ejkyOXpaWllXVkhZQWkyZ2tiQXlnQSt2cUE0dXkwWVdGcytlMmpOVGNpOW5GbkZBM05DNDZNMytQdXd5dEJGYXA2Vm1ZWEVMUllYWG1UeWNNY2lmMk92UXpNV0lUdTBKTTRRczVWeGFsNEZhWjcvQXRQWXRIdW5lSjBWaGUxWVpTMDBPRkQiLCJtYWMiOiJhNDAxNDBjN2Y0YTllMTFlMTZmZGU4YzFjN2NlNjM4ODk2MjFlOTI0YjNmZTQzYjM2ZmU5MGMyZGI1Y2ZjZmRlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:06:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1457748978\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-612697545 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-612697545\", {\"maxDepth\":0})</script>\n"}}