{"__meta": {"id": "X9dfaa39d51649a565e6bf45b83e27424", "datetime": "2025-06-30 15:34:35", "utime": **********.53985, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.110233, "end": **********.539865, "duration": 0.42963194847106934, "duration_str": "430ms", "measures": [{"label": "Booting", "start": **********.110233, "relative_start": 0, "end": **********.46595, "relative_end": **********.46595, "duration": 0.3557169437408447, "duration_str": "356ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.465957, "relative_start": 0.35572385787963867, "end": **********.539866, "relative_end": 9.5367431640625e-07, "duration": 0.07390904426574707, "duration_str": "73.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45571088, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02473, "accumulated_duration_str": "24.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.492237, "duration": 0.02354, "duration_str": "23.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.188}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.524306, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.188, "width_percent": 2.75}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.531263, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.938, "width_percent": 2.062}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-853262156 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-853262156\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1110150695 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1110150695\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-480607464 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-480607464\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-600916055 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297674054%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhaNHFUTXk4WGZZR0VGNWlmR0FiQkE9PSIsInZhbHVlIjoiaFR5R1lTdm40elNEVHVEeDY3VFNnTVdUQnppVTRTWVJydENOY1I2M0lIcW4xNFkrTU5rUVVpNFdPdFhPNCtiRE1PVTZJUkU3REtQTzl6Mk8xVFRuRkw2MjdFYitRTlMvWWJ1R3NxRUtOZmdpb1c2REgvVjA0QmVmMTVnVnBTQ2l1VDVqYmF5N0ZLLy9ZYko2UVljN01mamprUUV6L0RVb29tbzhFQXg0bk9vdThtUE1BTlg3RFJBdlZIcXEzS21PRHIva01ZL09Scm9UOU1uTDJnYU5yQXN6b1ZJWTJrVTlMMWJ2dCsvZ0dWbVorRWVoWXJJbTQxRTVtekcxa0d3eWx2cGNvK0xvVjZ4M21tWStBMkIvcTBzQzNPMkpGemsxTDljNEV0aTVGRnVaSWEvY0ZpaW5SM2NYVlEzQW41OWxwMVBaMUZOQnhncm1FS200b2Nzd2JWOFN3QW0yNHg1NUZUOWJoelFCaUhEdVd0VWRCQ0htbU1WTDAxQWFoMnJLeFNLbXB1UnhBdlZIZ0M3REd5VUM5QXlYbndpWEJTVjQ5d3ZOSkJJQ01oZEEzcVEwNjJsYUxEZkdMVzlsUFNwOTNDVnRxaFpGbWxBZ2dxTWxVRzlBbjlJcndvdmtVY3Q1S2hHc2REaDFybHV4Rk5JVHVETzJZcjZMMFBMOHVWSWsiLCJtYWMiOiI5YTI1ZDlhZjY5NWY0ZjllMzM2MzJhMzA2MDc0ZjhiZGM2MzZmOThlMWE0NGQ4YWU1YjUyYTk2ZGEwYTJmYWI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjA4czU1YkVBU0lNYWRDODNlSERqbFE9PSIsInZhbHVlIjoidnNIWDMwUGlTaURuYW4xY1F4NUUzdFlUc2pQVVd2dzArQVlOajI2bFRHRHgyQ3I1ZW1jbW1vZ09GRUFOUDBZdmI5NzJ0aGNVQTVBSlg2US9TYkZUNUVaWU9OWHRJOGd2VkJIL3lnV3FHTlAwbGpTb2o2dzBrV1pkb3JLYUFMWGg1eGFDL0w1cHJaTERia3ZrL2piN2VuUUxFaFhnYm55OGxZR0NGMHFzZTlGeUJ4b3h2MzRBOVAvZUlTMXdrejErZzZFZ2s0RTc1aTB5VXlCVVcxRmYwV2lTVUFiekxhOWVROE9xbi9mMVM5UHlPVGJnK0dJcWJTMGVoWHhEQ09EakZrN2F3eXpwOGtMc21DRkZJWTd2Y1Jvc1RsZzU1bk9sbDlZOVAzWnpKVEYxZHByYUVBY1loTHNJaTZvQ0lMSWt3dndZQXlLdERlcU9Ya2tLaGRSdVJPMG1OeFQzOS9IN2hhSVdSMVNyNEdEV2NGNytqSFdISno5eUpPRXRPVmRQbUR3NzBxTmJvOFhNZ0ZMTmlUNEliME9GcjBucmZ2cUdXSzFiRm9iZ0JRYUUzeTZQZkdrTW56a2lIZkZiTzl5NDFzcit6VERYQnBIbGJ4SEZxWlRjczI3STZsUzdZZDV5RC9vU04yZk9rQUR0Qkp5L0NhNmh4OXlsUUYzcnFUTCsiLCJtYWMiOiIxMzMxNWNmODJjZjk0OGQ5YTZjODNkM2Q5MzIyMWU2MDEzMmU4ZDI3N2EzMmEyZDc1MGFiNTk0NDcyZWY3YTdmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-600916055\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-803592303 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-803592303\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-330917365 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJ0bWZuM0ZZZUJ0d05USzdWd2xONXc9PSIsInZhbHVlIjoiT0VxVk56NDBreENqSUdlNGx6MXFscS81SzZKamxVK0dJRzVyRVE2dWx4QVV1VDFDbzVNZTA4WWM4SXBaek5weFpHU0NRcyt3OTJBS2RtdEZoV3NCaWtsL0lDWTFXb0V3Ykt5YWRiN2IrNzdLMjVQVzB5dUJMUVYzd1FTbzh0VnlhZUJIb2xPUllsRC9zSTNyeGtnV0J3eXNNbWQ5cHQ5UDdpZUc3YVQ5bGNidWgyd013SHE5RWdTSitJZzJlVGJWYnNnVndNSWRhL2IxYnhKdDVWcTN0VnUxcXJNWS9kbi9ObTMyL0NnM1RmNEFIbVRwNE8vdkpKRExmTllGTjY5b1ZxUXRTL1ZZOXJiSUs2K2pNV0FWaHdWZWhaK0V0ZmloTmlUK3hJU0Z6RGN4eHNkNHM3anM5emFzdmViU0poczhEeThkU09Edyt1dmphd1Z3NGwyeWNpOE14MHpNYkFjcUFSK1VEZUY4TlNzZmNMREx0ZEZqOVJHSjZySUxGT2o2Rjlwc2xnK2xVUUlWQVdSLy84MXVGSnVIMzFvQVROVDVMWjBFVjdYN2E3V1R0U0JQVmo4SG5vemc3OTk0d1R4R0pDRXZXTXB0UHZMZUt3OEYraWs1OWVmdVBKemY5K0J2L283MU96bGJvY3c4L3lyQVI0LzFuUG1zS213bFJCSTEiLCJtYWMiOiI3YzYwZTdlYTQyOWQ5MGRkMGMyYWFiYTVmZjJjNTQ1ODg5NmUwZDk4N2U3NDQwZTk2MDBlMDJkNGFhNTI4MDk4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ijl1Y2J4Zjd2RTJNVzBoZlV6Um8zdlE9PSIsInZhbHVlIjoiMUhIQzl2aUtJWGV3MUVLSkN3dDd1OWJMNi9leGVFYmdHdmxyRkt3c3FrcDI3MDYrQTNMaXlqdnBMckdjajhTM2hKRkoxUGcxSjhSNEVyamtOS0g0UEx2eE85WG9Uby9WMFR3cEIrTXRvNmY3Y05lQ25nZU9lR3pOMm5oSlV2bHVJcE9USU43VGtBZXdpOWJYWHJNYzNJVG5WdTFQR2w0aUxleFVIUTJINndUR1lNYzJ2Sjg2VTQ0VFR1cDN3Zk1ST01TMkpObWlIam9kVmdscXVodVhTVjZwcDJsK1FmWUpIY2huSzQ1V0RSS1ErMnJuTDArSmRObTQwdXh5SEZScFNpYThmaTNTcXhmWldhbUdiZ0hjNE1wNXFna3ZDS0p6Y3NSN2dGY015MXgwTVVQNnVEMTFCVVpLOFBXSWdpTDEyUHd1TlpMQWlXaWRucmo5SFZoUVQrNzdjTHFZWVFNaWNNSWpONWpOUktUTTZSSS9EVUlrb0M3bHc5SmRucWw1MHVGOVJzSG1QQ0EwYzJXcDZNOWI3U2JURDdtYW44V1BRS2xuTUF4MFdJWTRxKy94VUVTSDNoejVUTVhqWU9sMUVhcnpKcGwrbXRWOG8yQlhNV05tNGxpdzRLUkluN25MbnJVNjBKRUVLM1dmbGNmdHZPT2JuajUreHl2YWM4RFkiLCJtYWMiOiIzYjg4ZTcwYzE0OTA2NWQ2NmVlZmY0YmM2ZDI5MjExYmE3ODc5M2I2NDdlMTU0NTNhODhlNzkyNWY4NjIyN2NiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJ0bWZuM0ZZZUJ0d05USzdWd2xONXc9PSIsInZhbHVlIjoiT0VxVk56NDBreENqSUdlNGx6MXFscS81SzZKamxVK0dJRzVyRVE2dWx4QVV1VDFDbzVNZTA4WWM4SXBaek5weFpHU0NRcyt3OTJBS2RtdEZoV3NCaWtsL0lDWTFXb0V3Ykt5YWRiN2IrNzdLMjVQVzB5dUJMUVYzd1FTbzh0VnlhZUJIb2xPUllsRC9zSTNyeGtnV0J3eXNNbWQ5cHQ5UDdpZUc3YVQ5bGNidWgyd013SHE5RWdTSitJZzJlVGJWYnNnVndNSWRhL2IxYnhKdDVWcTN0VnUxcXJNWS9kbi9ObTMyL0NnM1RmNEFIbVRwNE8vdkpKRExmTllGTjY5b1ZxUXRTL1ZZOXJiSUs2K2pNV0FWaHdWZWhaK0V0ZmloTmlUK3hJU0Z6RGN4eHNkNHM3anM5emFzdmViU0poczhEeThkU09Edyt1dmphd1Z3NGwyeWNpOE14MHpNYkFjcUFSK1VEZUY4TlNzZmNMREx0ZEZqOVJHSjZySUxGT2o2Rjlwc2xnK2xVUUlWQVdSLy84MXVGSnVIMzFvQVROVDVMWjBFVjdYN2E3V1R0U0JQVmo4SG5vemc3OTk0d1R4R0pDRXZXTXB0UHZMZUt3OEYraWs1OWVmdVBKemY5K0J2L283MU96bGJvY3c4L3lyQVI0LzFuUG1zS213bFJCSTEiLCJtYWMiOiI3YzYwZTdlYTQyOWQ5MGRkMGMyYWFiYTVmZjJjNTQ1ODg5NmUwZDk4N2U3NDQwZTk2MDBlMDJkNGFhNTI4MDk4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ijl1Y2J4Zjd2RTJNVzBoZlV6Um8zdlE9PSIsInZhbHVlIjoiMUhIQzl2aUtJWGV3MUVLSkN3dDd1OWJMNi9leGVFYmdHdmxyRkt3c3FrcDI3MDYrQTNMaXlqdnBMckdjajhTM2hKRkoxUGcxSjhSNEVyamtOS0g0UEx2eE85WG9Uby9WMFR3cEIrTXRvNmY3Y05lQ25nZU9lR3pOMm5oSlV2bHVJcE9USU43VGtBZXdpOWJYWHJNYzNJVG5WdTFQR2w0aUxleFVIUTJINndUR1lNYzJ2Sjg2VTQ0VFR1cDN3Zk1ST01TMkpObWlIam9kVmdscXVodVhTVjZwcDJsK1FmWUpIY2huSzQ1V0RSS1ErMnJuTDArSmRObTQwdXh5SEZScFNpYThmaTNTcXhmWldhbUdiZ0hjNE1wNXFna3ZDS0p6Y3NSN2dGY015MXgwTVVQNnVEMTFCVVpLOFBXSWdpTDEyUHd1TlpMQWlXaWRucmo5SFZoUVQrNzdjTHFZWVFNaWNNSWpONWpOUktUTTZSSS9EVUlrb0M3bHc5SmRucWw1MHVGOVJzSG1QQ0EwYzJXcDZNOWI3U2JURDdtYW44V1BRS2xuTUF4MFdJWTRxKy94VUVTSDNoejVUTVhqWU9sMUVhcnpKcGwrbXRWOG8yQlhNV05tNGxpdzRLUkluN25MbnJVNjBKRUVLM1dmbGNmdHZPT2JuajUreHl2YWM4RFkiLCJtYWMiOiIzYjg4ZTcwYzE0OTA2NWQ2NmVlZmY0YmM2ZDI5MjExYmE3ODc5M2I2NDdlMTU0NTNhODhlNzkyNWY4NjIyN2NiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-330917365\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1732002674 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1732002674\", {\"maxDepth\":0})</script>\n"}}