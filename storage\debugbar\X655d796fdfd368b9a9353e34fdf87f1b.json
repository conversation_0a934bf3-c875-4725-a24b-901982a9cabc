{"__meta": {"id": "X655d796fdfd368b9a9353e34fdf87f1b", "datetime": "2025-06-30 15:34:13", "utime": **********.885552, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.474387, "end": **********.885567, "duration": 0.4111800193786621, "duration_str": "411ms", "measures": [{"label": "Booting", "start": **********.474387, "relative_start": 0, "end": **********.823813, "relative_end": **********.823813, "duration": 0.3494260311126709, "duration_str": "349ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.823823, "relative_start": 0.34943604469299316, "end": **********.885568, "relative_end": 9.5367431640625e-07, "duration": 0.06174492835998535, "duration_str": "61.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139384, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0189, "accumulated_duration_str": "18.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8509479, "duration": 0.01812, "duration_str": "18.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.873}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8770108, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.873, "width_percent": 2.116}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.879507, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 97.989, "width_percent": 2.011}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  1839 => array:9 [\n    \"name\" => \"ميداليات شكل محتلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"1839\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1142534390 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1142534390\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2005767060 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im5NNVdXOUx2VXNwQk5zQkIwZzB5Tnc9PSIsInZhbHVlIjoiZG5KMlh3dG01OXdYYTZJQSsrbFpTakNBVWN5MDZhcHRtSi9neUJwUWlwT2dqR2FLWVRldGdnMzhyYXRQTmFDcmVDTVNydGRYSlJXdlk5Z0lPUFMyTCtBTU5ITSs3RmlFOVd6UGV6NnZsc2U5bzVaMi9IamdtYWpmR0kzMXVaYjBEMSs2Rzc1RU85ZWpsaUR4TjlOYXNmaTBjQ254SzZweUxRSGVDUmh1UWZscW44bGlrNzJFRFJZM2hiSUN6Z0h1azBxSnhpWjhraEUyUjZWUU1nVVN5N2xXbnVTK2xBOXpvRkdNaWNWdTVPeFZUQU14bEZJejl3QXRXVU1CZ3plQnVocm0xT2s0b0cxODVOeHEySVR2S2ZJL3hNT0hsUmgyUVZNVE50TUJyRS8wNmwwTGpyL3IzUGNCaXkzeHBXbnY4S25ZU0VVOU55Q2I1SmREdVpxTXJHVHVHOGFxV3g3TUtoUjRGdDIwQUxlUDB4UDdIaVlpTmhGUjR4Wm5hVHRoYjlhU3dtejJoK0lBSVZVQnp4Q2VVeDd2U20xRkNnN0pzbGxkUWpFaXAzT2J2eEFoK29wdVFHZ0VuSENlRnIrbi9vWGRPT0JuTHVHd0E5ZjFwQXFxTmJ6REZtU0lWaGdnRWp3c0p6WkUvWTlXdWNweEhNemtONHhWRllRblJ5eVQiLCJtYWMiOiJhMzI0ZGI2MzRlMjMxY2FkNzM1YjJlNDRhNDY5YTcyZDBlN2I3Y2I5ZWQxNzk4ODEyMjFkNGRlMmM2MjY3Nzk5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlRpeXYrby8zdkpsakN6MmQ2RDVaTWc9PSIsInZhbHVlIjoibTBpcURHWlBHWm1JaXI3Y01NQXFDc2hPZVljOVAwbUVSd2FQejl4YmxNaU5nVzM0c2Y0WnpKME5hWXZZWkhMWDg4Y1Q4cE1OejZpVjA4bGp4eU9oVUoyaytleEZSU1crc0dDWVFmcGdXZWhoZnlwVkVjZUoyVHY3TlZKOUMvU2o3eGFNZnZmYjNwZTJxTzZ1WDRqUzA5RFd0U1AybVl6ZmVVdVExYWdacHN6TFExV1IxQkpGVnZOOERYSEFieFU1M3h3RTI0V2wrRzdpcEpscUxEZFBNZXl0VTk1Mkd1RXZCVXN5SEtDb29BQWd1d08vZlVpU3lBQnhOWVA4ZjZRUnN4ZEdoVndKd29EK2ppSDFCRjVtdXpKN1lRR3QzdkFwSUdnYzBrcVMrWWV0Z2lxdnVZZ1YvS1krdzJ5bkxMVXNqZkd0Z084SU02b2pPbm1xQXU2VDBadXdpdUtFU0ZVYTdaUGpBemdNSDBybVNGUGEydHJwVHUvM3ZwMzN6aUc4dWlMWjVNUjgwVzEvaEhaNFNabjVQdFQrbGNGUjNISEJWR2MwZ2FpNFdNUEVWbnhxWEpjUzAzT05FaXpTS3ZWZ3V2cE5oUlQ1em9WSVFuYkdsb0c2R1lObGJUZVp5MkZ1QnlvOFpremVRaW9rSWxCTGQzcGRkaEltK3hSSlJQNnMiLCJtYWMiOiI3MWZhMDJjMjdlNDkxMWI2MjEwOGFiNmFiYzg0ZmNiMmYzZGJiY2YzYmY3ZDhlZDBmNTY5NDIyYTA2MTRmOGM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005767060\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-57036208 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57036208\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1294263489 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndaaFZTTGtZd25LT05lMUQ1eklVY3c9PSIsInZhbHVlIjoiaUFrODJpK2t2ZU9ubFhYUnExT1dic1Q3ZkQzRmJBeC9VQkJ6cW1FazVhTittTFhSd0U0NXkvR2J6VVZlRysvYm5GQS9RaGJkUzlUWk9yN3JDVVpyR3g0SEdJMmNEdnN6ZXBtSXN0R01LNmZ6RUpuM2ZWN2hUd2RaajZkOWo2UERFSWQ1OUVQRWlNTGJHSk5uZnpKeWZMdnExcU5BVWJYLzlYMHh5NEJYYjBVRHN5SDQzR29HY0Erdm0rcnQwc0NUejArN2V6ZWlhYldhVjZsUVF1VnBYb0VsRVFPZWFYNmRnVG5iaDNmaUk0N2V6aEtLdnRUd2tVOHBlOU5RWTdtL2Y5anFQL21qVVlIYlFlWGFaVDhHeWptdlJUcmp5WkJkLzdKUjMwbm5qVTN1MkVqaDIzc29jY1JEb2t3bm9KRExIeUllWm9iNU10UHFWYklmbEZFdmJ0MlZ4TVo5clN0ME4rcEp5a3I4MlMrUk9xamFPUFN3K1FwaUFBeVcvWmozdllPZUFhQTlLWGVLMzhLZ2VrQkxZbldhY1A1ZHE4WEF5dzdCQlIrOHlML0NFZzZ3Z3VKeVo5S2tlaEhram9NbTN4UEVHMlRsTmxPcU9yakJhL3hKaGRzUUNaZ21WcU5vaGRlekt5S0Ric0xQU1A1aVhiek9LNkVaeStQNjNvUE8iLCJtYWMiOiJkOWNiMmM0MDZkMWE5Zjk5YjkyNzhlYmRhM2FiMWY2MzViMTgwZTg3YTNhNGM3YTUwY2Q1MTc0ODgzZDc4YWIwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkF3TksrK0lRQnhBRXhaVS9jUk1NaHc9PSIsInZhbHVlIjoiWmFteDVSUmx3REpRZXc4Z2FHTjZxQThpQzdYd3ZvTUN1RlVva1U1cDdlUjVkaDIvQlhheTM5QTRUc3U4UDhWQTVaODg5T2lQbjZlRnVLN1pnanA2MWI1UmpiS2VoSGhnMVVkS1dzRCtUU2tVbWY2bllCWTBIVlBkTDlrTzIwcUgxVkM1NUR2ZzNsUitzMk1zSm92NStJdzU5WGVVN2F0NjFab211cDhCWWlxU0ZGYW1uaG93bFIwejVQcDRISEhRcy9ObHRueWVQdWVhZlJndTAzMm9FTUhCVk5FcGJmWjJ0NmFsbE9ocTZJUnZnZVBxa3JiZ2thQVJnckFocEFmQ0VJVkNidUU3T3hZbW55endlUU45TmdiWnd6YmhOSFcwSHh2cTBkcW1NUkgxV3R4NUx0ZHBiV0Y5YUY3NzNoMlNWbk45d08zeXdxQVczRmhCQUJudGIvK2pCRXAzKzArUHdYNzQxTjVyTUp6Q0JwYlh5UU0zMFJIcGhLRmlhTU5JcWYrTnNQM2RSN1JETER2Znp0WkdZUHhqSm44VG0weUNwelIxVlk1KzVQUmZwZkk0RktRc0ZLUE11ekFKdlVZbUdtZTNjaS8wb3U1UG9GdEh3Qm1CN3dORisralBsR0Y1QVlHMGRLdDd2cmhSNSthVVI2V2MySXZ4eDg0ZjdCZ1YiLCJtYWMiOiJlNjdjMjBlYzM2MDc5ZTE2ZTgzMDM1MzM3M2U2YTJkNmU2YzkyMmM1MjkyNTFjZjhhZTEwNDExZTdhNDE3Mjg2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndaaFZTTGtZd25LT05lMUQ1eklVY3c9PSIsInZhbHVlIjoiaUFrODJpK2t2ZU9ubFhYUnExT1dic1Q3ZkQzRmJBeC9VQkJ6cW1FazVhTittTFhSd0U0NXkvR2J6VVZlRysvYm5GQS9RaGJkUzlUWk9yN3JDVVpyR3g0SEdJMmNEdnN6ZXBtSXN0R01LNmZ6RUpuM2ZWN2hUd2RaajZkOWo2UERFSWQ1OUVQRWlNTGJHSk5uZnpKeWZMdnExcU5BVWJYLzlYMHh5NEJYYjBVRHN5SDQzR29HY0Erdm0rcnQwc0NUejArN2V6ZWlhYldhVjZsUVF1VnBYb0VsRVFPZWFYNmRnVG5iaDNmaUk0N2V6aEtLdnRUd2tVOHBlOU5RWTdtL2Y5anFQL21qVVlIYlFlWGFaVDhHeWptdlJUcmp5WkJkLzdKUjMwbm5qVTN1MkVqaDIzc29jY1JEb2t3bm9KRExIeUllWm9iNU10UHFWYklmbEZFdmJ0MlZ4TVo5clN0ME4rcEp5a3I4MlMrUk9xamFPUFN3K1FwaUFBeVcvWmozdllPZUFhQTlLWGVLMzhLZ2VrQkxZbldhY1A1ZHE4WEF5dzdCQlIrOHlML0NFZzZ3Z3VKeVo5S2tlaEhram9NbTN4UEVHMlRsTmxPcU9yakJhL3hKaGRzUUNaZ21WcU5vaGRlekt5S0Ric0xQU1A1aVhiek9LNkVaeStQNjNvUE8iLCJtYWMiOiJkOWNiMmM0MDZkMWE5Zjk5YjkyNzhlYmRhM2FiMWY2MzViMTgwZTg3YTNhNGM3YTUwY2Q1MTc0ODgzZDc4YWIwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkF3TksrK0lRQnhBRXhaVS9jUk1NaHc9PSIsInZhbHVlIjoiWmFteDVSUmx3REpRZXc4Z2FHTjZxQThpQzdYd3ZvTUN1RlVva1U1cDdlUjVkaDIvQlhheTM5QTRUc3U4UDhWQTVaODg5T2lQbjZlRnVLN1pnanA2MWI1UmpiS2VoSGhnMVVkS1dzRCtUU2tVbWY2bllCWTBIVlBkTDlrTzIwcUgxVkM1NUR2ZzNsUitzMk1zSm92NStJdzU5WGVVN2F0NjFab211cDhCWWlxU0ZGYW1uaG93bFIwejVQcDRISEhRcy9ObHRueWVQdWVhZlJndTAzMm9FTUhCVk5FcGJmWjJ0NmFsbE9ocTZJUnZnZVBxa3JiZ2thQVJnckFocEFmQ0VJVkNidUU3T3hZbW55endlUU45TmdiWnd6YmhOSFcwSHh2cTBkcW1NUkgxV3R4NUx0ZHBiV0Y5YUY3NzNoMlNWbk45d08zeXdxQVczRmhCQUJudGIvK2pCRXAzKzArUHdYNzQxTjVyTUp6Q0JwYlh5UU0zMFJIcGhLRmlhTU5JcWYrTnNQM2RSN1JETER2Znp0WkdZUHhqSm44VG0weUNwelIxVlk1KzVQUmZwZkk0RktRc0ZLUE11ekFKdlVZbUdtZTNjaS8wb3U1UG9GdEh3Qm1CN3dORisralBsR0Y1QVlHMGRLdDd2cmhSNSthVVI2V2MySXZ4eDg0ZjdCZ1YiLCJtYWMiOiJlNjdjMjBlYzM2MDc5ZTE2ZTgzMDM1MzM3M2U2YTJkNmU2YzkyMmM1MjkyNTFjZjhhZTEwNDExZTdhNDE3Mjg2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1294263489\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2117324619 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1839</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1605;&#1610;&#1583;&#1575;&#1604;&#1610;&#1575;&#1578; &#1588;&#1603;&#1604; &#1605;&#1581;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1839</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2117324619\", {\"maxDepth\":0})</script>\n"}}