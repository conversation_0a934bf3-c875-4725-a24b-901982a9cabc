{"__meta": {"id": "X25470eb2b7a32862b70ec748e64d1837", "datetime": "2025-06-30 15:31:20", "utime": **********.667684, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.227029, "end": **********.667702, "duration": 0.4406728744506836, "duration_str": "441ms", "measures": [{"label": "Booting", "start": **********.227029, "relative_start": 0, "end": **********.576726, "relative_end": **********.576726, "duration": 0.3496968746185303, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.576735, "relative_start": 0.34970593452453613, "end": **********.667704, "relative_end": 2.1457672119140625e-06, "duration": 0.09096908569335938, "duration_str": "90.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48095280, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00915, "accumulated_duration_str": "9.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.612355, "duration": 0.00276, "duration_str": "2.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 30.164}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6267998, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 30.164, "width_percent": 8.634}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.646095, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 38.798, "width_percent": 10.82}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.6486819, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 49.617, "width_percent": 5.792}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.653643, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 55.41, "width_percent": 29.617}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.658751, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 85.027, "width_percent": 14.973}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1508923927 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508923927\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.652521, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1617698926 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1617698926\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1196220579 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1196220579\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1409393845 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1409393845\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1258093465 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFtOW14MHFWMXQxM0dtWVB6a01MaGc9PSIsInZhbHVlIjoieGdhUEtlN2JmQlBJQkFVWk1WdWZYSDNaQ0hHaEoybEFFTnlqSTBVTFp0UlNjSmhmY3p1VTZhK1hmajZyTFEvRit4Wkg2UlgzWWRwM3Q3TDUvS2xpdXNpU3NkemhZNU1KS0dLL2NOQ1VvNURIR3IwYkJlZE5NWlc3M2lOQUM4QVNESnFjNHk1ZjQzZFlhNGc2eTVxcXhEeHI2c25QcW5UOHlwQlpQTnhMS0NCdmFJRVlQdDd4MEFlZytrNHk1UDdEajk1WWMrOWszVEFIck83VklnWSt1SU4yaXJoYW1mVkdUTThOWWFreEZ5VllxS3hoeU5aK2F3anNJRjF6dS9mZXhsalZXc3NwUFNCcWtuNUd6VmdVSTk3OWZYYncyRkV3QlFOcFExNFVhYnZDdEN4UXVnYlJZdFVzVGhxa1dCWWNzeUxqQUs1VlB6Smo1SS9rZTBhUEhpYVFYVXNjcStDT3VZd1IwSWVlMGxoSHlpdStoTlRVUzY0SXhuOGdxVGMrWXZvUHdBSFBTcnhXWDhySExZQlpjQXJlcVdQbktIZDc2M0VSWGlhVE91K3IxMzZ4S2pOcm9lV1BVNVE1RzFTc2MvNnZBK2NSZGVNbWxUSWZaZE05QWFtMm9SamxvYVU2MzB0VWNOcXl2THAweTh6WkVnZzRoQXQ5blhRMVUxNkciLCJtYWMiOiJlODEwOGRmOWUxNDY0NGRiMmQxNTJhYmEzNmVmMzY0ODg4MTliZDRhMDhkZTA0MTU0ODU4N2I0Y2NjM2FhZTA4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IllIQTBqamRJN0pkRlRIY1RweHlqZGc9PSIsInZhbHVlIjoiTHVtMDlEdG5sNHFleitaQ2thamN1WjZvc0VjN3pLTjlzUzh0eE1mdjNtSUJFTDViOEJsNHRwUlQrcUsyMGlSbE1BVHR1a3Y3UzZ5dlcrZ1hKeUsvbDNrdVQyeDl2RUo5WmxxTitqR1N4RitXaFFBSndkNldaWk1PRjZwdU1oUFhuaGxvc3MvRGxSWVlVd0grTzRmWXVaMWlpK2VoM2FsMXc3TXZZaC9ra2dWNi9jaWY4UkxlYm1IK0VQRVZpVm9kTU5UQmtseksvQ1ZQS0E3Z3NZU0JQZ1RrdWFRNkR6VkJRdmx0VjIxcHdQRXlNU2J2M3pCRzlSMVJlZ29KeXRBRDdzZlplM0FZUGora2pjcWNjbTZZNk1BYVFzRlZqU2xzdUxyT2dHdXY3ZEwxVTVFekxpa0dydUtkZ21Rd1IrMU4yaGlBeDJHdjk1TDlDYnI4OVY5L3A0YjQ1dC9GTU9jZk9MSHZsNDNkSnNTSm1UWlQ0cFI4eUtqSkFmMThLaXllYTB6ZHdab0JyUWFiY0JuTUhrSm81MFNIR2ZRT2Z0dmovMDFDZXJhODc1amZ6OG91VmxEL3M2WkVJRE1vTXBjQTVsajJwLzM2alFoSmxGS2J3V2JGbmovc1I5bStRSmw5dDN3MVUrenFoMEtXZy84QndnbzZEUjYycEJHcFN3TkciLCJtYWMiOiIzNTBlYmY1YjFmNWNiNGI4ZTk5MmEyMmM5YTkzNThlMzA1M2U2ODJjY2RlOWRlMjE4ZTAyYTEzNGIxMTBhZjQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1258093465\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1828887999 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1828887999\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-995990873 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:31:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9wcmRRMitmc0NGekd0SGtTaG9SUEE9PSIsInZhbHVlIjoiOE82by9PYlF4eXhvMFdsUEptZ3dKdEx4RzZlNWYycFo3cERISmhLZkFsQ1BxdmVNNGZDQWo0OGdYakF5akZYVjZqbGNJenl0eVI5UE1lc1hnNEVVZkVpQ0pWTjUxWWNGTWVER3JNZFc4bkVzRzE2UFdhNEo2QytkYlptdE5SLyt5TGtUTkttTXJPRjNpSEVUbEdPaDBnclB2b0tnTHo2d2VPYkRjcjF4YzNUSnJSM0QvVjlXb2t2RTl0MUZlb3RlWTJiNFdOMDRnaTFwQzZqS1h5MjBYL0d3RGdaSHNkUmVBVmRpcVRzWWNyMXFBb2hQQkZZRGdSZDhId0tHbFczYlBEb1NWWXV3NHNvRGxDcXB5NENST2Q0SGRkK2lJVno3bGE2WTNuODlzNDNnWFc0bEROcWFNWHk5Ujd4MXZ4KzViZUtJNDdJNzJib3U0OGFubUtleWhnQlAzT1VjTEt3MHVSc1JkTU56SEYrQi9Ra2Y4ZEp2WnJDY2lldmRibVVqNWRYNW9wUU9wQUtmRjFQRHB2UC94aWZNWE1jMlo0YXZIQUlxbmdhNjYzbnlRSWlkTkwvanpWZXRDVFJYNEpCbjJCYUNKb0R4Q0lGVUtYUXJWWWZTYWJWYkZhcG5uZEcranZTVlQ4cE5EbUZiRHpiaHF0UnM5YnBMd1JFNStoUUciLCJtYWMiOiJkZWNlZTExMTM0NjJmYzNiY2Y5MmIxYjBiOWIzNDg5MzA2OGNmMWQ1ZTVmMGM3NzYyMmVmN2RhMDE4ZTI2YzkyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:31:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlFrRDh5bVRrWCsrWHpvMG5BK1F0ZHc9PSIsInZhbHVlIjoiVGRCc0JtK2c2YlF0SGdXZWtaanQvRm5HdWFwWTUxdjl4eFYrRGQ0bCttTHQ3ZjJ4czd4NjB6amw3ekFrOGQ1YjlGclljaGRYbUVkL0FhUGNFVkxvbElmSEhDbk1KV0RPV0RaYThIUlMvTC9BdXFPTlBORmRrbDZ4WjdhekJibzVVQloyZGFvLzVWMno5VWVSTFZRcWdiY2pGS3MxSVNtdGlHV1BmeFlSOUVUZlJ0dGsvakNxZm1pMjI5QjZxRTNYbUdmUFY1SjU1UGM5VDNXbWlFckhGb2VRaWhmNU1EL1Excjd5eUNZdHBYd1FuWFg0dldEUi9lcEJtcStRdWdZSHVSYnlQT0l4OWorbkw2cW9zeGtjeUdNK2kxdlRtZ1lJY2kvYTB5NkpPaCtaczIvVHZsMXpkQm9CdzhBWDVubWRtV1VPcWZFNFZqblBaNVFIdUw5T2V6VFdyNm82UVFVbnhBM0IvaWM4MkYzdUxtcXl2cWplZjhkWVJlZW12UzlZSFlFQk9lVU1VUjJLb3J6TUtlTDN2NVNEbWhqYytHdWpBRFcrL2RzUzEzN3FjYWowVkNka1VrWHYrR1Q4dnFvRmlkV2tkZEYyRnNIRExJcDBoblJGSStlbjhOVWdXSlNLYzhab3M0S2FxRUg2TndJS3BLdi80Y0FTL0dSckVPc0ciLCJtYWMiOiI2MTBiZmIzYjM1YjljMDc0ZjdmMDg3ODY5OWMwM2ZiN2VkZjRiMDE4NWRhMmM0NzRhZGUxMmJjNjQ3ZDljOGI5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:31:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9wcmRRMitmc0NGekd0SGtTaG9SUEE9PSIsInZhbHVlIjoiOE82by9PYlF4eXhvMFdsUEptZ3dKdEx4RzZlNWYycFo3cERISmhLZkFsQ1BxdmVNNGZDQWo0OGdYakF5akZYVjZqbGNJenl0eVI5UE1lc1hnNEVVZkVpQ0pWTjUxWWNGTWVER3JNZFc4bkVzRzE2UFdhNEo2QytkYlptdE5SLyt5TGtUTkttTXJPRjNpSEVUbEdPaDBnclB2b0tnTHo2d2VPYkRjcjF4YzNUSnJSM0QvVjlXb2t2RTl0MUZlb3RlWTJiNFdOMDRnaTFwQzZqS1h5MjBYL0d3RGdaSHNkUmVBVmRpcVRzWWNyMXFBb2hQQkZZRGdSZDhId0tHbFczYlBEb1NWWXV3NHNvRGxDcXB5NENST2Q0SGRkK2lJVno3bGE2WTNuODlzNDNnWFc0bEROcWFNWHk5Ujd4MXZ4KzViZUtJNDdJNzJib3U0OGFubUtleWhnQlAzT1VjTEt3MHVSc1JkTU56SEYrQi9Ra2Y4ZEp2WnJDY2lldmRibVVqNWRYNW9wUU9wQUtmRjFQRHB2UC94aWZNWE1jMlo0YXZIQUlxbmdhNjYzbnlRSWlkTkwvanpWZXRDVFJYNEpCbjJCYUNKb0R4Q0lGVUtYUXJWWWZTYWJWYkZhcG5uZEcranZTVlQ4cE5EbUZiRHpiaHF0UnM5YnBMd1JFNStoUUciLCJtYWMiOiJkZWNlZTExMTM0NjJmYzNiY2Y5MmIxYjBiOWIzNDg5MzA2OGNmMWQ1ZTVmMGM3NzYyMmVmN2RhMDE4ZTI2YzkyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:31:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlFrRDh5bVRrWCsrWHpvMG5BK1F0ZHc9PSIsInZhbHVlIjoiVGRCc0JtK2c2YlF0SGdXZWtaanQvRm5HdWFwWTUxdjl4eFYrRGQ0bCttTHQ3ZjJ4czd4NjB6amw3ekFrOGQ1YjlGclljaGRYbUVkL0FhUGNFVkxvbElmSEhDbk1KV0RPV0RaYThIUlMvTC9BdXFPTlBORmRrbDZ4WjdhekJibzVVQloyZGFvLzVWMno5VWVSTFZRcWdiY2pGS3MxSVNtdGlHV1BmeFlSOUVUZlJ0dGsvakNxZm1pMjI5QjZxRTNYbUdmUFY1SjU1UGM5VDNXbWlFckhGb2VRaWhmNU1EL1Excjd5eUNZdHBYd1FuWFg0dldEUi9lcEJtcStRdWdZSHVSYnlQT0l4OWorbkw2cW9zeGtjeUdNK2kxdlRtZ1lJY2kvYTB5NkpPaCtaczIvVHZsMXpkQm9CdzhBWDVubWRtV1VPcWZFNFZqblBaNVFIdUw5T2V6VFdyNm82UVFVbnhBM0IvaWM4MkYzdUxtcXl2cWplZjhkWVJlZW12UzlZSFlFQk9lVU1VUjJLb3J6TUtlTDN2NVNEbWhqYytHdWpBRFcrL2RzUzEzN3FjYWowVkNka1VrWHYrR1Q4dnFvRmlkV2tkZEYyRnNIRExJcDBoblJGSStlbjhOVWdXSlNLYzhab3M0S2FxRUg2TndJS3BLdi80Y0FTL0dSckVPc0ciLCJtYWMiOiI2MTBiZmIzYjM1YjljMDc0ZjdmMDg3ODY5OWMwM2ZiN2VkZjRiMDE4NWRhMmM0NzRhZGUxMmJjNjQ3ZDljOGI5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:31:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995990873\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-751649123 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-751649123\", {\"maxDepth\":0})</script>\n"}}