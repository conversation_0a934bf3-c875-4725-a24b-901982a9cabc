{"__meta": {"id": "Xd3f753cf0475408850af954e91a063f9", "datetime": "2025-06-08 15:42:37", "utime": **********.555958, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397356.803156, "end": **********.555989, "duration": 0.7528331279754639, "duration_str": "753ms", "measures": [{"label": "Booting", "start": 1749397356.803156, "relative_start": 0, "end": **********.462984, "relative_end": **********.462984, "duration": 0.6598281860351562, "duration_str": "660ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.462994, "relative_start": 0.6598381996154785, "end": **********.555992, "relative_end": 2.86102294921875e-06, "duration": 0.09299778938293457, "duration_str": "93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152176, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0188, "accumulated_duration_str": "18.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5011182, "duration": 0.01677, "duration_str": "16.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.202}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.532578, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.202, "width_percent": 3.883}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.543372, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.085, "width_percent": 6.915}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1987718140 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397219377%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJlbHh5YmZXbHU5am5mVUdzR1RyNUE9PSIsInZhbHVlIjoiNjBnK09TNERYZ1hqVFBpNytMbG01WVFBSnNJQXZBSFpCVHBkQmhSeFArNThZS0JXVXd1eFM5V2JuOGFZcW5IWDEreFc2dCtsT25wdExFdi9PRTdzY3RjU2VIQmlYZFc0RGp2TTVDTFBDZGs3NHlESVB1eG1aOVlZS0hoR083UDFKY0JaOUs2YmVzQ0N6UloyNGVpQWZQQ08vR0hkUG1BSkkrZlNlclZNK1FBRW0ydTU3YkZyL3FIMXR1OEg0Y1o2Mjc3cFh3WmdCQTBZQVNDZW1KdFdJZHdPS2Uva2FBa2JPanNaRWdFeW5nKzNZK1UzeWFXZi9BaVM0VTR6N0VmNVFVcXZqVHFqQTNvTXhKT0k3NmEya3g0QkxBMzVubE1uSzFDVXJDUFBXa3lzbU1aZU9IVjczcXZRbVI3ZVZNc1dUcWViSGJLbjlNeFVPMTI0OGNTckJEV09tdDJETW04RG5ScnJaeG44K3Mvc2RGNXJVczQ4QXNmUjlnUGM1VnZ2b3VqK3RFQmNHeCtXdnc1RkppQkRBT1A1bjd0TTRJQ093TUFKQ1cwWDFMaDlkSEdkMHpyMTgrakxFN0tOcDdpSHducm1FaW1sYWZRYW1xS3BhSTRlZXdqTHB6eXkxSUFtd3cxSVR0UUdsTktyVVZ3a3BCU29yMzdLMGU5a1Z4SEMiLCJtYWMiOiIzYmU4NTcyOWZhMjZiYjFmNGI3N2YxYzNiMjExYmE4MTI0ODMxYTIzYTc1M2QwNmM1OWY0ZmQxYTBjMmQzYmJkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImI1ejlZdTA5R2U0VWxJZ2ZtQWUyWHc9PSIsInZhbHVlIjoiVUVKM3Q3c1AvMnpBUHVxaTFmMDRteUJKdmh2UFdnWU91cFB6eUlNbGdzMUpPRHoxQWZ4dU9IbW1qVms4WUxLdG9kYzhHSDUvNWR6bXZzekdCQ3FjZVJaWTUyUmZkSmtVQjFBZkMxRkhjU1Nwa1p5WjZjM0N1bkpJQk84ZXRBZU83cnhxc0hQbDZXcE56cmZxbFplbkhaM3dHcGQzd01oV21KK29jRXlHUE44N3lWVW9aUm5JaG11R2ExYm4vME1PNkhsSG9VaHBIdVAzeXFMVWtqTWtWb2gvaTlZZUdwNkVTdS9rSEo5UmNTUWs5MjhsOHlmclJ6cjlkc3JCYnRRNS9OMlRwcFg1bE56RTFicTZ5b0tka2FTcGRqUnZCSDB0S01jQ0JQYmI2ZlN5U1VZUXdYYnYvWFY0RUdmR2pzUU5qeHljN0FKRXEyOSt3dDVrc3RRbEFJSmFPcVE4Ly9LeTdndmhuYkdvZ1JOWEhCblY5OHlvN3p0QU1vd3YxbE9wSDRnR3JsTHAzdkNoTFhLcmxsdlZqWEZiQW1oTmgvOExlRit2dG1UTGVWeHQ3RHZSbVQzclRWbmZHVUVuOVJ0TkN4WEpvczYyQjVoS29XY2tyNXVMOFdKVjNBdStpQ2YxN01aR2dWcFZlNUhNMG9HdE5CZng2MUhjUThHREpVOVUiLCJtYWMiOiI0ZTEyNDMxNzFjMTA5ODU3MmNhZDVlNDcyMjVmOTc0OTVlM2U0M2JjODE2MWJhZjM3NjQ2OTUwNDhhM2Y4NmJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1987718140\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-496621966 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-496621966\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-868164757 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:42:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRBdmlvN201SW5WTjRDUWVnY2hLV1E9PSIsInZhbHVlIjoiS09lQ0QyRnI4NVFRMEswdXFVZHBvM3htYTBmSkFNMEMwUjZ5NjdYODhJUlJtZlo3dmNsb2pYZ0Y4bDY0MHBMZUJER2NTWndnMmJadlUzQ3hkbHM2cDJSaHQ5aldOR0YzRkQyVWFsdk1wVVF1ckFkKzVhZ0NNU2wyczV2cy9aeEZCeGIvbENKdzVpcmYvMEdEZDRITzYxSUZ6L1FnbzFJSmFLMll4aUhXS2YrdkwyRjJ6amNmb2d2NGZTNlFDNTRUN2ErNnJtaFVjbHAxRkRLTzVhYnQ2b2I4TzVWM0hIUmVDYllJK3FXWFZHVlMzQ2JXTWxXeExOMitqZ0RWYzNnN1B3RURCRW1WOHhudndPai85REh0VSt3N2VZVzgyV2ptdllGcXk3QkxvYjM1WUdRZ0J3RzFYTkIwTDgwT3BieHlIWVhjaEJpcVZYdVJMTk1oVTAxWURWMWozeEk2bFR1NlVTVXNkTENiT21QbHZhaVBzejNVS1VLT1VFaDBiaURRYVBMSHN1cE5WcFBFRnFyZC9XOXBlWjFmZ3NKUkFjYlRMbFJERUp1UUVOdVNQMllkZi9tZGxqRjZ4U240UDlSUGJEMzF3L2w1L0J2SFBhOWF0Q0RLclJ5alJKWjY4czFFWUpCSVY5ZkExZGtDUm5mTlpiNXhmc0habWJYNTN2QmQiLCJtYWMiOiI3OGZhOTg1Y2IzNjIxYjA4ZWVjY2FmM2YzM2E2MzU1YTE1YzNjNjFiODE2NmI5ZjkwNzMzOGMyZjUzMmI4M2U5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImxKNEJSNDNPaHkxKzdtVHBFenJPaHc9PSIsInZhbHVlIjoiV29FMUZkM0JoQ0xsYXJmWFV4TnQzSTVNdkZBTWtnMXo3MG5GZVp6N1NhcW1JakdHTlN3WTVzK2hHTzd4N3QzTlpFenhkdUxYQWRPS3dxcStmSHg5Nkt1anBPU3pRZFBkc0xEaEMzeHBoN2ozSkx1bFR4aW4vRkI0MEh6UVVuNWQvcmIrQ2JvSVpNL1ErbCs3dCsrTXNVMDlOMDNvVnR1bWs4bXNTZkZlSU56WnMxS3RkeFA0cXZEL1lLQWtkK3FwZm5PdXZGTnNGTExkTDhHQ2EvNiszMi9jcmxtQWNRb011VW5XRUlCMFBES0pHc1V2K05NY2Vjc1pwZTdCR0R4MW5OeS8vZFhRVzVIMVUrR2ZmVEdSVnFSd1c3dWZYRXo5MU5IZjUxaWxTTXpxVGZoaitrSlh3d0JRbTNnL3cwekFxbGtnQVdhZHVORE9paFF4SVNMdXFtQ0ZLRGlkSkxmWEk1TnMxdHdWT082UjNHNDRzY2dLME0rSXFVcm41MEFqM28rSjdVUUNrQXNWZ2xXazZ1TmhnOVBCbUtnMWtTQzVQSVZ1UWpBNHhnUzBZQ05BSU1nOUZvNm1yTDlNTDB4bTZtVW94TDRWZWVISXYycmF2ZEQ4eVdOa1pzSzlTOFRvdTBtajlhZERsMVNEYXE2M2lSYStZTkVOdTMxVWZ6emoiLCJtYWMiOiJiOTAyZGYzZjY2ZjA1NTE4OTk3NDU5YzRhYzc2YTk3M2ZjOTM0NTBhOWQyMDg2MDEyM2M5ZmZmZmNiZWJkMDc4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRBdmlvN201SW5WTjRDUWVnY2hLV1E9PSIsInZhbHVlIjoiS09lQ0QyRnI4NVFRMEswdXFVZHBvM3htYTBmSkFNMEMwUjZ5NjdYODhJUlJtZlo3dmNsb2pYZ0Y4bDY0MHBMZUJER2NTWndnMmJadlUzQ3hkbHM2cDJSaHQ5aldOR0YzRkQyVWFsdk1wVVF1ckFkKzVhZ0NNU2wyczV2cy9aeEZCeGIvbENKdzVpcmYvMEdEZDRITzYxSUZ6L1FnbzFJSmFLMll4aUhXS2YrdkwyRjJ6amNmb2d2NGZTNlFDNTRUN2ErNnJtaFVjbHAxRkRLTzVhYnQ2b2I4TzVWM0hIUmVDYllJK3FXWFZHVlMzQ2JXTWxXeExOMitqZ0RWYzNnN1B3RURCRW1WOHhudndPai85REh0VSt3N2VZVzgyV2ptdllGcXk3QkxvYjM1WUdRZ0J3RzFYTkIwTDgwT3BieHlIWVhjaEJpcVZYdVJMTk1oVTAxWURWMWozeEk2bFR1NlVTVXNkTENiT21QbHZhaVBzejNVS1VLT1VFaDBiaURRYVBMSHN1cE5WcFBFRnFyZC9XOXBlWjFmZ3NKUkFjYlRMbFJERUp1UUVOdVNQMllkZi9tZGxqRjZ4U240UDlSUGJEMzF3L2w1L0J2SFBhOWF0Q0RLclJ5alJKWjY4czFFWUpCSVY5ZkExZGtDUm5mTlpiNXhmc0habWJYNTN2QmQiLCJtYWMiOiI3OGZhOTg1Y2IzNjIxYjA4ZWVjY2FmM2YzM2E2MzU1YTE1YzNjNjFiODE2NmI5ZjkwNzMzOGMyZjUzMmI4M2U5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImxKNEJSNDNPaHkxKzdtVHBFenJPaHc9PSIsInZhbHVlIjoiV29FMUZkM0JoQ0xsYXJmWFV4TnQzSTVNdkZBTWtnMXo3MG5GZVp6N1NhcW1JakdHTlN3WTVzK2hHTzd4N3QzTlpFenhkdUxYQWRPS3dxcStmSHg5Nkt1anBPU3pRZFBkc0xEaEMzeHBoN2ozSkx1bFR4aW4vRkI0MEh6UVVuNWQvcmIrQ2JvSVpNL1ErbCs3dCsrTXNVMDlOMDNvVnR1bWs4bXNTZkZlSU56WnMxS3RkeFA0cXZEL1lLQWtkK3FwZm5PdXZGTnNGTExkTDhHQ2EvNiszMi9jcmxtQWNRb011VW5XRUlCMFBES0pHc1V2K05NY2Vjc1pwZTdCR0R4MW5OeS8vZFhRVzVIMVUrR2ZmVEdSVnFSd1c3dWZYRXo5MU5IZjUxaWxTTXpxVGZoaitrSlh3d0JRbTNnL3cwekFxbGtnQVdhZHVORE9paFF4SVNMdXFtQ0ZLRGlkSkxmWEk1TnMxdHdWT082UjNHNDRzY2dLME0rSXFVcm41MEFqM28rSjdVUUNrQXNWZ2xXazZ1TmhnOVBCbUtnMWtTQzVQSVZ1UWpBNHhnUzBZQ05BSU1nOUZvNm1yTDlNTDB4bTZtVW94TDRWZWVISXYycmF2ZEQ4eVdOa1pzSzlTOFRvdTBtajlhZERsMVNEYXE2M2lSYStZTkVOdTMxVWZ6emoiLCJtYWMiOiJiOTAyZGYzZjY2ZjA1NTE4OTk3NDU5YzRhYzc2YTk3M2ZjOTM0NTBhOWQyMDg2MDEyM2M5ZmZmZmNiZWJkMDc4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868164757\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-850841 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-850841\", {\"maxDepth\":0})</script>\n"}}