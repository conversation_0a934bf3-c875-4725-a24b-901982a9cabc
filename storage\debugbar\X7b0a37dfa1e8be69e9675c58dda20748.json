{"__meta": {"id": "X7b0a37dfa1e8be69e9675c58dda20748", "datetime": "2025-06-07 23:27:43", "utime": **********.215008, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749338862.354324, "end": **********.215039, "duration": 0.8607149124145508, "duration_str": "861ms", "measures": [{"label": "Booting", "start": 1749338862.354324, "relative_start": 0, "end": **********.108045, "relative_end": **********.108045, "duration": 0.7537209987640381, "duration_str": "754ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.108062, "relative_start": 0.7537379264831543, "end": **********.215043, "relative_end": 4.0531158447265625e-06, "duration": 0.10698103904724121, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043568, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00591, "accumulated_duration_str": "5.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.165075, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.805}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.185965, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.805, "width_percent": 13.706}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.199516, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 78.511, "width_percent": 21.489}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-787512014 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-787512014\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1178346290 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1178346290\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-915748682 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-915748682\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1204831178 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749338822528%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5LSkNYSno2UTFZM0VvNUllc0hqL1E9PSIsInZhbHVlIjoiK1BPVXhTNUo1VFV6VjRtYUdEMHlUQTRhMlY3eXJmeUc0OThET2U3ZW5reUxQVjhBMVpMSmNRVWxzY0dETmx3R1RDRlhCVlliS3NOQ0E5ZzdEbjVwVWJ1MkpreDRrZDlkb0lMTUpNWmdrSzBzT1JLczVsaVFzdjRUUmRMd2VpMmN6MXVBZmZIMWU0UjE5d3dNbU03WTJXb25EQlBGcHViR3JLeW1GeURoK3R0dU0xL0hJa1JGaTJhOHQxcGdGcUZidjNLOXhuRVZjQ3ZySVZnQ2VmSWE5blp2c29UY3ZRaU9CWnhzdU0reE1RcE1QQWs5TkcrRkwrQUZnZjhWL1UwUENrMm42YWUxTmd5ZWlFMUNmS0t2N04yaGhidm9LR2VZbkNxZTdXS21sUHB5alR0bkI2M2tFMUV6eGxkc1VnRDlEczRsZ3dQRDFOOCtiblhjTWt6RWZybWlaeXF5eHcreTVUOHUvWnBQWE05cWZheFhxNitxTDBITlpQQ3p4b3MzMUVYS3c3NFpoc2dOYU5PK1RyVzNKRmRkL3V4ckt5VTlCQjg3bzZwbzFSbmtJbE9BdWk5MEVwb01EaGlkMTZOdTJ0QnNtc0tSR2NWU2JEUXY5VWtHUHN3QlVpWGxFN2NoTEhLVHB5RmxQUGlKQUloMDVRN3F0K2lVeDN1SVJIaFoiLCJtYWMiOiIyYzU1ZjVmYzE4YmExODBjNjc5YzE3ODUxNTk0N2I5ZWU0ZDUzNTY2N2Q5YWE5MmFlNWEyNmM0MGYxMjEzMjZlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjQwMm1kNmRXLytlVDlIcWJVRUJLcHc9PSIsInZhbHVlIjoidXYvZ09MeS9rTDh0WnhCb3RvMUNacXlGSllGL0ZTZGpLejgrZUNUdFRHT3BpeVMyNXB5WmJVbFMyblpKU3ZnZlFrbGFXeDZYUmVzQWd5ZFN6WkUrcUxzQUV2a29IWGdSbWE4MEtuZ2k5d0o1dkxEeTZOTVF0S1QrZUxzRVdhOWlOalJEUEdhSldDdTZpdFhrd2JjQnNhZVIvSGhMTENKSWc2OGw3dWQ3dklkMHFFRDBxUG9vVXlmMkxPb0k4L3kvQWdBeGx4S1BFSFNXREJ4S3pRbnlUQXNlKzZWY1NIb0NsMzNzS2NWbFFKK2s2Y1BicWpWTlVaZExaODl3SUxka2UxdnZWUUlFajg1b2lkT0FnT0JTZ3hmbXRIQ003MGVieUtBR0lmSVJvNDczMFhreGpLYzAyamVNTGRnOVVnT0hMeW16eUxiMldNMkVaeS9xOHh4cExWS09zSTB6OTd1NHBYcTBCQ3hCYSs1ZWNoTGxKWEZkV3dKOFBVdmw3T0FEQmhaVGxwNE40TUJEK20vT0RlRk1lMXc3dnIzYXpjcFhwUG8rM3JjQ1czbFh6TjAyNlI2ZE1lTksvZTBPbUpoSG82dTA5c0RRYzNBQUZFcWF1ZGxPVTByek1SNURidmZ1Z3owSS9lL2d3dEI3ZEZZZkw2YndKUm96K0hlcjFwa04iLCJtYWMiOiIxODIyODJlMGZjZjMxMmRhNmQ2ZDEwZTg1NjExNTg3MGVhZjE4YmZmZWNhNmQ1NjU4YTdjZGI3M2YxYmY2Zjc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1204831178\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-588198958 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-588198958\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-836011718 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:27:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZxZ2psK1JlSG8xWHQ5S0VOVURZamc9PSIsInZhbHVlIjoibVVWQW5RYmRCQnZtTGV0Q2k5YW0xSEdtTVJKSFNsU3l4cG9LdnhIM2UrMHluK2RURGVYQ0MxNDhQYnZFeTZvb1drdnpSTTFrcHRMN1hvbFpLWGxBdHl2eE4vVXpDNmszRnNjT3NidGZmczJyM1ZEbFFtdEl3eVlBbzlKVExVZFowK0hyRFd5RnNtL0MyUTZ6MnR2aFhxSFRwUmlmTE5ITlYzRThLdUE5ZUpVSUV3ZmdHNTlXOEd4d0hsMUZ6ZzR0amtvYm8zQmgwWFFROFgvT1dITWI3bjBZSk5OZGJmOXFpcVNHUHlhMXQ2RnNQUEVXc1d4NDlURlBnVlZ4RUl2a3llWElET0RhUXh3SmI3d1ZUY3puNkpRUGRuTStkNXlwQWp2VUY1QU1FQVR4Q3VPVXpQYVpKajdjSTBVSWQ5U0NBUnNvS04ralpZTDl0QlBINXFPS2FOQTlZdjVaaTBiMnVXOUJNS043MWxqdDg5UFNKS2N5Y0t3ZEFUTmh6LzZZdGFTZ3lPcTY5RG5NdDAzQ3lkNTdEMldmNHJUN2hGK2RYWUJ0dlA4aURHV0xFQVlkcmR2SkN4by9DSDkxOWZpRXljSkJlYTJRakU4TkRnSGF6WVBFM2xET0pwL0FQWHRzbmxVN3gxenFGTUpFRmhNeGw2TkhiMWc1OXM2RDUxMmoiLCJtYWMiOiJhZDZkOGYzYWM3Y2YxYjM3YThiZTcwN2U0OGY1MDg3ZWQwOWM2NmZkYjNmZDRjMjVlNjdkMDNkZDliYWFmODE0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:27:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikdjc0VRWjhKQlpxV2NkTURkZ1FDeHc9PSIsInZhbHVlIjoibzU5dkFmSnM3cjRERnNDaGl0SjdZczU0T0EzMUVLZ3VDeFo4NEVhYzlzNGIwaTBvVS8rY3hzQmhnbzU5VXN4bW5uTUtrM1NaM09kNDBqdlNQK1NOUFlCZVVZVmdRL2VRV0xQRzcwQUc5RnIxcjAycy9vQTQ0L0dOV0M2S1ZkVFczaE1La1FWVm4xYTRUcTkyVEYram8zUFlPN2lHVnFFdU1XN0gzK3IzOGlrNGFWRVNFOFBaa0NTR3hmNXR0aUlhN29xdXNRWlg3KzVRa2pTa0ZUVGRtYUJqYmhPUm41Lzl1d0pvcWRCSE9JRUh5M0Rza2kxSm96aEtNM3pDZ2hCVmJtVWJUbEFydEdpMzdkWFlCUGkwRUxlMUNxZnpOWXZaamxzUkdabDJqQmc2b3Y0R0xIMS9tTVNNN1RrU0xPRzdjQkgwemJKcnFRd0JtQlFwdG83c1gwVkIreFY3UUwrU0I3VlVpa3kwMDlCTzdreFZ3VUgraEFuVytkSzZNTURJbkdPaCtGWDBnOUpFN1BvZmV0dG14Ylg5VXRkYnBFaGI1ZkxIdDZtVDViajhNUEMzUmtDQzM0SmJLOW9ydUdzcmlPVUIya3p6eTgvcHVCQnFGOG82TFhuRVZLb1Z1SlpWMVZQeFEyZ0E2NkE3SnhneERiVFh3Z0tCcjA5RkdQKzMiLCJtYWMiOiJhNTNhMjJlNWM0ZWIyMDc2NGQ2ZjdmODQ0M2VkZTI5NjE4OTA5N2IyNzBmOWU4ZmZjOGI4MTg0YjJmOWFlMjhjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:27:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZxZ2psK1JlSG8xWHQ5S0VOVURZamc9PSIsInZhbHVlIjoibVVWQW5RYmRCQnZtTGV0Q2k5YW0xSEdtTVJKSFNsU3l4cG9LdnhIM2UrMHluK2RURGVYQ0MxNDhQYnZFeTZvb1drdnpSTTFrcHRMN1hvbFpLWGxBdHl2eE4vVXpDNmszRnNjT3NidGZmczJyM1ZEbFFtdEl3eVlBbzlKVExVZFowK0hyRFd5RnNtL0MyUTZ6MnR2aFhxSFRwUmlmTE5ITlYzRThLdUE5ZUpVSUV3ZmdHNTlXOEd4d0hsMUZ6ZzR0amtvYm8zQmgwWFFROFgvT1dITWI3bjBZSk5OZGJmOXFpcVNHUHlhMXQ2RnNQUEVXc1d4NDlURlBnVlZ4RUl2a3llWElET0RhUXh3SmI3d1ZUY3puNkpRUGRuTStkNXlwQWp2VUY1QU1FQVR4Q3VPVXpQYVpKajdjSTBVSWQ5U0NBUnNvS04ralpZTDl0QlBINXFPS2FOQTlZdjVaaTBiMnVXOUJNS043MWxqdDg5UFNKS2N5Y0t3ZEFUTmh6LzZZdGFTZ3lPcTY5RG5NdDAzQ3lkNTdEMldmNHJUN2hGK2RYWUJ0dlA4aURHV0xFQVlkcmR2SkN4by9DSDkxOWZpRXljSkJlYTJRakU4TkRnSGF6WVBFM2xET0pwL0FQWHRzbmxVN3gxenFGTUpFRmhNeGw2TkhiMWc1OXM2RDUxMmoiLCJtYWMiOiJhZDZkOGYzYWM3Y2YxYjM3YThiZTcwN2U0OGY1MDg3ZWQwOWM2NmZkYjNmZDRjMjVlNjdkMDNkZDliYWFmODE0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:27:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikdjc0VRWjhKQlpxV2NkTURkZ1FDeHc9PSIsInZhbHVlIjoibzU5dkFmSnM3cjRERnNDaGl0SjdZczU0T0EzMUVLZ3VDeFo4NEVhYzlzNGIwaTBvVS8rY3hzQmhnbzU5VXN4bW5uTUtrM1NaM09kNDBqdlNQK1NOUFlCZVVZVmdRL2VRV0xQRzcwQUc5RnIxcjAycy9vQTQ0L0dOV0M2S1ZkVFczaE1La1FWVm4xYTRUcTkyVEYram8zUFlPN2lHVnFFdU1XN0gzK3IzOGlrNGFWRVNFOFBaa0NTR3hmNXR0aUlhN29xdXNRWlg3KzVRa2pTa0ZUVGRtYUJqYmhPUm41Lzl1d0pvcWRCSE9JRUh5M0Rza2kxSm96aEtNM3pDZ2hCVmJtVWJUbEFydEdpMzdkWFlCUGkwRUxlMUNxZnpOWXZaamxzUkdabDJqQmc2b3Y0R0xIMS9tTVNNN1RrU0xPRzdjQkgwemJKcnFRd0JtQlFwdG83c1gwVkIreFY3UUwrU0I3VlVpa3kwMDlCTzdreFZ3VUgraEFuVytkSzZNTURJbkdPaCtGWDBnOUpFN1BvZmV0dG14Ylg5VXRkYnBFaGI1ZkxIdDZtVDViajhNUEMzUmtDQzM0SmJLOW9ydUdzcmlPVUIya3p6eTgvcHVCQnFGOG82TFhuRVZLb1Z1SlpWMVZQeFEyZ0E2NkE3SnhneERiVFh3Z0tCcjA5RkdQKzMiLCJtYWMiOiJhNTNhMjJlNWM0ZWIyMDc2NGQ2ZjdmODQ0M2VkZTI5NjE4OTA5N2IyNzBmOWU4ZmZjOGI4MTg0YjJmOWFlMjhjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:27:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-836011718\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2009975171 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009975171\", {\"maxDepth\":0})</script>\n"}}