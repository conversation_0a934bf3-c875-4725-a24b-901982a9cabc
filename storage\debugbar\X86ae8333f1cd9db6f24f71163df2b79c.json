{"__meta": {"id": "X86ae8333f1cd9db6f24f71163df2b79c", "datetime": "2025-06-08 00:30:54", "utime": **********.396848, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342653.130621, "end": **********.396893, "duration": 1.2662720680236816, "duration_str": "1.27s", "measures": [{"label": "Booting", "start": 1749342653.130621, "relative_start": 0, "end": **********.224867, "relative_end": **********.224867, "duration": 1.0942461490631104, "duration_str": "1.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.224891, "relative_start": 1.0942699909210205, "end": **********.396898, "relative_end": 5.0067901611328125e-06, "duration": 0.17200708389282227, "duration_str": "172ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45578400, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0083, "accumulated_duration_str": "8.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.322633, "duration": 0.0058200000000000005, "duration_str": "5.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.12}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3515859, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.12, "width_percent": 15.301}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.369646, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.422, "width_percent": 14.578}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-973866751 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749342593005%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii90bHdPUWZvaTlDdXNnRWw3OVpSUUE9PSIsInZhbHVlIjoiaTR3L2o2bkdKUFQ2RGJOY2VwNC9jRzZkeC9CZGJrdlJmNlR1ei9oN1lVR3ZjWER1VDV4eHVyNDRjbE9oSWhwNFlEWXJiN3NPQkptSUJLYlBlaC81VjJubEZ2VDFxUE5YM0pFMU9QQkpRYVJVenZTb3pMNlE0MlJCRlVPT2pTUVZoZWdlNVAwWmw1aWplQTIycStsMVMwb3lGNWZHbVdhc1h1MDY1dVR1MmZYOVNNVHVwTlZCY012dlYwSllDcHpvSkdhM3gzL2VNeFlwZGdxYnhKdk5mNmt6WG5oMFIxSlBhOW9lYmxFaTFsSVlrb2IyME5WVWJGZk9QOGJsQzZBY3JrMnFtdWlEVUs3bVZFeXFJM0R5KzRBcXpodUVDY2ZKSDNqMEdxOU1La2hFRklBaUsxNjhzeVZYL1RBR0RINGV1NEFsMk1KcUNVaktMd3hZdlphU25qRllDNHR2MDBmV3FWYzV4VytMdVp2MzhBQUg5QXRNdHFlcDV6ejJiNFZEeUo1UC9GSmZUd1VrZDZFbngwaVg3dkYzbll6NGtQU0ozdVZ2c2hoOXY4VlJvWVF2RThtWlBoOTFpbHQwTTVOWWZZWFFhSDE5R1o5WWNld0ZjK29WZERnMXUzcktDcElHZEpnUXA5M1orMUNCRWNBaUVNSTl4SUNXWUFCK2NtMVkiLCJtYWMiOiI4NjNjMzUzZDFlZTM5OWViNDIzMDk4ZjkwYTQ2MzIyYjIyYzIyMTY5YjY3NGE5YTQ3OWYyMTM3OTkzNzczM2MyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikc1bmI0c1VER1FoNCtVMUVwWm1hb2c9PSIsInZhbHVlIjoiWXZ0ekY0Rm5nSytlTDFYR0tPeDJWODUvKzZGUk1WdDRnTmplSTNISkxpdnFOYkVBZXRMK3NRZ2RuR1N3cVRJbG95YkRrL0ZDcXFjcGpDWVdzRnlra0xoMHRoeUttYnhZSmZBT2pJSkRkZ3VMSVhpNFRVc3BrQWkyUmdZYW03VS83RjJjRGVIWlJRYzZjQXBuWFhmRG0xaTZTalloU2xncVJxWEc2ZkVOd21HUVU1NU43WWl6OFBZNng0YmJhdGVyOXJTbkp2eU5mZXJDY3hERkJUcEpTcUxXSTNDU0ZRNVQ3a1pDU1RyNmRXYTNwR2g5eW9iSVlhcWJFalI1bWZPbkQxTlFpYVd6Mm5yYmV6aG9ERW9jR1RMdVYybjR2eDA1UEpSR2NQR0tlSkk4TTQwOUZDbXJ6dEVCMXB5V0c1L0xDTzMyREkwY2xiTC9xWWQwdUx1dCtSbi9JSlVackpzVzhITXdPb3NFdWlGRVpscGp2QzhvZTZzOTBhUXZYOWNOZXc3cVI2WTM4bzlFMWovZ0VmbDhRVlAva3RhV2RWcGdxSU9qdDBwRW01d2dhRzVsWTRpL2dLWWpTd1BDRkpSTjA3U0ZxU1p4bE1GaHk2OU50UGVUcmxETWlWVHFXYjBrcE1aVzVwd3FqY3h5bysvdUZXU1NDVHU0QTJPTzc4UXoiLCJtYWMiOiI5ZWIxNmFiZGVmYmMzNzJlOGE1MzRlYmI5ODMzODU4NjgwYTFkNGEwZjI4MDkzNDU0OWRhMWIzZGE0M2M5MzA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-973866751\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1855606599 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1855606599\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1354698110 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:30:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9LbnM1YkdScWpvb3U1OGpWaTdpaFE9PSIsInZhbHVlIjoieHFOMVNPUjJjMFpsUkJiL1N0MFN6U3RTVU5idElGQldML3FLbEQvMkljQWUxYVpWblQ2cC91SDJzbmRmZ0hlNk5pcGM5Y3FnbjRwd0k3cTVrYUtBMjJoWWFNU3FRaHhFQmlFOWt3VWxhdXM3MEF5ZVlsVlduaEdyZERQeWRQOHlwZ1djK0txdFA0MEdzRVJ6bldLOUp6ZXVLdXdLU21qMmgydzhQZFhlU09iclQydnQrYWZUendsQ0JnbUYwb25neXY4SHIwenJ2R3FWYzVERVZ6R1JxY1lldTRZYjhwOEN6SmRjVTRMb3UyRU1US2FvTzhESWIwRzFtVERYVW55WDZiNDdCaVpPeUZQdE5RZ1lnNkkwaWdkR2o1R1FVZGdBU2Q1d1VuS29rQjZnbmtLdzd0WXRGaDJJSUI3ak42cjkyaTA0U3l4MTN5MDUxTlRyY2tpMDRuZzdMeXdHL00ybzFEVEd1dXBFUTJsdnphdFRlTkh3UjJGOTNaWUQ4ZHhTV2gwOEQ1L055bHZqZ3F1WWFZemJUdXlBMU1nSXZTWURsS0RKdVBuMS9mb09Ca0NzRnNueFovYVE4d3kvUFVTRTNSRDZyODFJMk9EM1BTS1d4U3JIU29weXpjQUhHbTA2Y2Q5ZTFZMmV0SjhhUkYzM3ZTQ1R2aVJDamZBbUJpNXIiLCJtYWMiOiIyOGQyMTQwNDVjMGY4ODUxMzkwZDU4MTdhYjE4MmUyZTIxYTFiZDk5YmI1YzRjYTViMTQwN2RlMjVkZGM3MmJkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkVNZzhUd3lacTV4RWs1VGhEc1gzSHc9PSIsInZhbHVlIjoid3BlSjlHOCtMVllGZzZTcm5rMkVYSGNuc2d6U2hnK0NadStsR2lqRXFrTUszRU4vcStRckQzZzJONkF3aDJYd2pKS1lPdWh2bGxXKzZXZGszYjFGV0tjUS83ODBpRVZ3ZHdZc29rbXVOdzROdjlFZnRNTFJlbi9LdWY4eUlVT2FTWDlQekJuYjZNTXFXV3BTQkYreW0yRmUwMFMyV1JvbW05a2xJZTVrUVNrQy93eldrc2N6QVlDTnBHSGhTVW90US9XSlIzb3B3NG9pWGdhMDVaVHRraGRMYk0veVZtekc1bWlzR2xLN2lCd0lLRmxWNE5DN2xqMGJXdEIyMGliS3FOZXdDaWFhWjRubTJreVlpRTIvb1IvY0RPeGRaUHR0eVcrb2VkUVpVVUcybHdlZ2MycUV5M2tuT1ZwVW4wamtHOWd0cFhlS2ZmRzEyb25saE85TlhpdnJ0R01mZXUrZ0tOUDd2dlQ5Q2x5bDBYZURmL2srdDFoSEMreWJ3dEJ5M2tSTVgzNCtPTVgwWjBpUnkrbkkvZ3R6QU9ydC9FbFJkbE0wUDFpT0dZUStGSis3cWNyZ0pyZytHZ0hkbUk5SVljemRGVWphaHdPaDcvY2dXQVB5N2lDQnYrakFkR3dpalQxcnJ1SVZuQk9tbGZYYUVjb3RuZVhkTktJb0xiQ2QiLCJtYWMiOiIyOWE1MmQ2ODkyODcxYTRjNzVmNzAwYzVlODE0NjdhOGE0ZjIxMjEwNDQzYWZmN2Q3Mzg5MTBhMDk1NWMyMzQ4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9LbnM1YkdScWpvb3U1OGpWaTdpaFE9PSIsInZhbHVlIjoieHFOMVNPUjJjMFpsUkJiL1N0MFN6U3RTVU5idElGQldML3FLbEQvMkljQWUxYVpWblQ2cC91SDJzbmRmZ0hlNk5pcGM5Y3FnbjRwd0k3cTVrYUtBMjJoWWFNU3FRaHhFQmlFOWt3VWxhdXM3MEF5ZVlsVlduaEdyZERQeWRQOHlwZ1djK0txdFA0MEdzRVJ6bldLOUp6ZXVLdXdLU21qMmgydzhQZFhlU09iclQydnQrYWZUendsQ0JnbUYwb25neXY4SHIwenJ2R3FWYzVERVZ6R1JxY1lldTRZYjhwOEN6SmRjVTRMb3UyRU1US2FvTzhESWIwRzFtVERYVW55WDZiNDdCaVpPeUZQdE5RZ1lnNkkwaWdkR2o1R1FVZGdBU2Q1d1VuS29rQjZnbmtLdzd0WXRGaDJJSUI3ak42cjkyaTA0U3l4MTN5MDUxTlRyY2tpMDRuZzdMeXdHL00ybzFEVEd1dXBFUTJsdnphdFRlTkh3UjJGOTNaWUQ4ZHhTV2gwOEQ1L055bHZqZ3F1WWFZemJUdXlBMU1nSXZTWURsS0RKdVBuMS9mb09Ca0NzRnNueFovYVE4d3kvUFVTRTNSRDZyODFJMk9EM1BTS1d4U3JIU29weXpjQUhHbTA2Y2Q5ZTFZMmV0SjhhUkYzM3ZTQ1R2aVJDamZBbUJpNXIiLCJtYWMiOiIyOGQyMTQwNDVjMGY4ODUxMzkwZDU4MTdhYjE4MmUyZTIxYTFiZDk5YmI1YzRjYTViMTQwN2RlMjVkZGM3MmJkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkVNZzhUd3lacTV4RWs1VGhEc1gzSHc9PSIsInZhbHVlIjoid3BlSjlHOCtMVllGZzZTcm5rMkVYSGNuc2d6U2hnK0NadStsR2lqRXFrTUszRU4vcStRckQzZzJONkF3aDJYd2pKS1lPdWh2bGxXKzZXZGszYjFGV0tjUS83ODBpRVZ3ZHdZc29rbXVOdzROdjlFZnRNTFJlbi9LdWY4eUlVT2FTWDlQekJuYjZNTXFXV3BTQkYreW0yRmUwMFMyV1JvbW05a2xJZTVrUVNrQy93eldrc2N6QVlDTnBHSGhTVW90US9XSlIzb3B3NG9pWGdhMDVaVHRraGRMYk0veVZtekc1bWlzR2xLN2lCd0lLRmxWNE5DN2xqMGJXdEIyMGliS3FOZXdDaWFhWjRubTJreVlpRTIvb1IvY0RPeGRaUHR0eVcrb2VkUVpVVUcybHdlZ2MycUV5M2tuT1ZwVW4wamtHOWd0cFhlS2ZmRzEyb25saE85TlhpdnJ0R01mZXUrZ0tOUDd2dlQ5Q2x5bDBYZURmL2srdDFoSEMreWJ3dEJ5M2tSTVgzNCtPTVgwWjBpUnkrbkkvZ3R6QU9ydC9FbFJkbE0wUDFpT0dZUStGSis3cWNyZ0pyZytHZ0hkbUk5SVljemRGVWphaHdPaDcvY2dXQVB5N2lDQnYrakFkR3dpalQxcnJ1SVZuQk9tbGZYYUVjb3RuZVhkTktJb0xiQ2QiLCJtYWMiOiIyOWE1MmQ2ODkyODcxYTRjNzVmNzAwYzVlODE0NjdhOGE0ZjIxMjEwNDQzYWZmN2Q3Mzg5MTBhMDk1NWMyMzQ4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1354698110\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-9******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9********\", {\"maxDepth\":0})</script>\n"}}