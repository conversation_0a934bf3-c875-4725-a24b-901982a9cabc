{"__meta": {"id": "Xdc9238caa9e12d38f14d93da065e054c", "datetime": "2025-06-08 15:29:04", "utime": **********.235871, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396543.511214, "end": **********.235896, "duration": 0.724682092666626, "duration_str": "725ms", "measures": [{"label": "Booting", "start": 1749396543.511214, "relative_start": 0, "end": **********.140854, "relative_end": **********.140854, "duration": 0.6296398639678955, "duration_str": "630ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.140876, "relative_start": 0.629662036895752, "end": **********.235899, "relative_end": 2.86102294921875e-06, "duration": 0.09502291679382324, "duration_str": "95.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43901368, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02237, "accumulated_duration_str": "22.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1911871, "duration": 0.02135, "duration_str": "21.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.44}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.217751, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 95.44, "width_percent": 4.56}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-1119262757 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1119262757\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1516819482 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1516819482\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396538091%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdUSlpJNUNtNmtSNFNGZXc5dnpBclE9PSIsInZhbHVlIjoiVDlMN3A0V2RlVTh6SGdxcTBmcG1vQzVVTTdiQlh6U000S0FjQzd0Y0drcXJKRjB1cXFkQlM3Y016MEFOQ3ZQRlVnT2VLem1RMmVYK1RoR05TWmxRN2oxMTNXelltVnNOR1ZuMFhLbXN3WG9MNXZtTzhWU1hlZEZKOENNclFhWFRzbjArbjhPczhWL29scjRKaDhCQVZpbEdXVklwMFdpQmh2U1pJVzBWTk94SUNoVWRsNGNycXR6Ykc0aEVXV3lsZktQM0NzUEw1dHhQTTVSV3JqTE81anJ6QnJqVmg1SlM2TGVVVEZPY0k3VEZqdjJWUzdnR0dETW5BMU9Vd2ZidHNaUjBpK1FWVG5ZSkgwNHZ5VWdsWkR1VHdnVStxelpsTW1ORU5iVC90WVV1N2tTUFVrVEp6cE1ZelZRZ1A3dlEyOTRiMU1LWWtWaTVqNGdFcC9iNVVPZ1dRbTFoN3NFMjJ6UElHeHhKZzQzdlZ3dU4vZUg1ZXA3YmVEd2RHVXdYNFNJU2RxemNnajJGRWs3T29VZUtQL3phWEtpdFVITGxmK2J2N0hOb1M1ZzlzSitpbHNaK0pxa0xxbDB1akR5a1k1STJXMmhFaElMNHRPM0Rwb0NXTVN5QUQxZkJQR3R3UlpRY0lRMTR6b3ZLd3FaaGNpOEVPb2psaWFyNnliQ0wiLCJtYWMiOiJlYmE3NjQyYmJjNjY3MGZiYjFiZmJiMGVmNjNhY2FhYjYxYTU0YTU1ZGM0ZjM4YTk5YTMzMjU1N2M1NDUwZTIyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InVacytxbG1YQjBVRFViTEpqSHdTV2c9PSIsInZhbHVlIjoiMDRaRmNmdFlQRTBKV28rakJGN2FQWTZxV09kQW5KaHB4Nkp4T2ltYkxMVVBjbUpoNG82ZUxOUHZFZHNteUE3eDFNdWUvWlFUcytzWG95MzdmSC84VmxERFlhQkxvUW9XOElDQXc5S3dZTXozaWVhb01rT2R4RlJIOHhZSXhweWhrK051ek53Rllaa1VycXdkcEgyMU1lQ0o5REFhdkZvUy9HdWtqb3UzbzQ3eDRtbUsxeUhjaENtWlZrZHZvNXhqdHc4bnQvUmMxeEJPc1NxOHRNQUlRc2lWTXpqbmFZODE5c3VuN3FtRHE4U21Dbkt2MzFERE5EWHVqNTAyM3pLUWthY3dRekhtWm9HTnM5VW1QZXpoWkVicWJOSklwYXVCMmMzRFhmWHJlTU04aGk3NWxUaU9MYTZ3Zy9DRUdOSkU5WVpHcjRxUVpQUmR5U0FpeGcydXRBdlFidHNIZkl1dGVhMzQ1eXBuVFZYSGpESXFrQnd4dkwyNEU2ejhyT3ErQ3N3Z25WZ1lqQmI0eXUrOUlNMjRjMVBUMnhSelczWDRpdHZIbm5DSXUvWFJhSkJoYmVRbllJRGRzVG84Uzl2ZTBnVkJaZmN4V3Q4aVVzY0tWa1lhYnRxWVpnTURITVo2K0tEUmRVUDJMRlFuSUd3dDV5azU1T01wandyR2Y5OHQiLCJtYWMiOiIxYTFlMzU2NTRhODdiOWMxODhjMjhiYTQ3NzA3MjViOWQ1MDhjZGFlY2U4ZGEyOWQ1ZGI4MzhiYzAxNzljNDAzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6s1OaNA8h4DHGuLO0qhsUHg7TnRJSWwbuakqNtUI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-446790366 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:29:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVhZXo2ejExdHlVczFyYVgwRXNPSUE9PSIsInZhbHVlIjoiVFhSMCtqNnhSUExxWWNVZ25YWW1ZdGxJcFZ0T1RCekFWOE43Q0kxdlNqS3VhQjdRL2hLUjY5RU5GMXdqcjJEUFpxYkFPeDZXN3FkTFNKUFd4N1MrK0RmaGh0dWFvTGNSSUNiZHB4ZnRzd0lwQzhCSkFSdmFETXBPY0VSQVBrMmc2RzFmVGoxajFycWRIWTNFb3drRDd6dk5vakZSaGljcTVJclliQ3BhS2c0ajFjRjVwamdjekRQU1N1SlBmc2dYWFdxUEQ5MklKNFdxcUNkMEkxSFNPd3I4OEswWEhad3VZUjgxK09PZkk4Ujlmd3NkdXp6bUJZOER2WUdicjFVK0QwYytzaXhYOWhVS25UU0hMUXNIdGZpQ2VwWVlDTk42YWM5UGNKV3hrWUdzbUtUV0RNYnppQnVqSmgyZjZKQkhZbUpUOENFT2Q0MWdTN3dseXNVMEhkYmhNS0tqZTJaWmdRbmdpcXUzUzlQdEVFVG5SWTFPOVU2d21ydVlNbXU4di9hWC9wbTdpMmQwclFIYzkvQUEvVEZab3pHYlJiaGZCZWNBRTZLbW1CZFNxdEMvd212MUtjTlNBTExlWUM0NHdBWEt1VXd5WDF4YlgvUVBHaENxWDdteHNRUjJXY3RWMEJ3Z2gwTWl6aHJUb1BZMkxLTHVSM3VGNFNFZ0pPVUoiLCJtYWMiOiI2MDUwNmQxYzUwNmExYmVlMTY5YTJmNjgxMTE1ZmZjZjBiZTE4NWQxYjYyZTQzMGFkMjJkMGFiYzkwN2RiYjkzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlYxTDhXanBEOWpmTFkxN3VXdzBTYVE9PSIsInZhbHVlIjoiaWRtQk9lZUpYdFl1ZXoyd3Y0SWJOQXEzbW5MeTgrdEpWdnBGWWxDSG9LcWVGQi9jQi9tbUdnd0Uyd3JScWJ5L2NoL3dHK0ZiL044cGEwOS9OdEY1TkdmY1JGWTRncW5TRk5Pb0E5N2FNS3VRTW9id0dPbVY0bkU5c1Mwb21ZOUp1VVJ2aDdyQW8wUkNyL3NJKytMTkd0ZTRIaUZaeEIzc3J0c0VDTzZFdzhXUEo3RDdZTG1WZDlOM29xc0s5L3lpenRFM2xDUjFXblFDZXNsQkJud0cxbk12cHVJVk1vVlV6MDBlNzJoL0VVRWhvUGU0Tlo5NE1KQ2ZydWxIckZ2eHp4c0gyL2NGTEhiSm15OEFod1FrdXlaQ0JUeElnVmpNMU9NVE1Ha1g1eis2VFdKZWVjWi9xdTJoWmFUUEFKMUNVYmNkYnJMN1oyakxmNjJLZ1J1a3VucmJzZDFrUXdmNVZqNGZZemlCZzI1R0kvdHAyWlZVVGRsazl0UDMvY05jeFIzTUJUdWRjTFJ1UVB2bVlNK2pKRktIV3F0MW5XVks2ZDU4WE1JZWRldzVCeitCdW9pSHdTYjhWU0Q2Wnkwcm5nYk9JSnFsbU96WjdLQlhGdWxsU0ZEcnZGVkxXN3ByVk5FR3NoMGVHMG1vVURtS1hsMVo5cjFjbVhFbHRySDMiLCJtYWMiOiIyZmMwNTU2ZTM2NmQ2OTU5NzU5ODc0MmQxN2U0ZTJhODE0MmFiNGJhMTc4MzZiN2FmNGUzM2UzODQ0YTFhNTUzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVhZXo2ejExdHlVczFyYVgwRXNPSUE9PSIsInZhbHVlIjoiVFhSMCtqNnhSUExxWWNVZ25YWW1ZdGxJcFZ0T1RCekFWOE43Q0kxdlNqS3VhQjdRL2hLUjY5RU5GMXdqcjJEUFpxYkFPeDZXN3FkTFNKUFd4N1MrK0RmaGh0dWFvTGNSSUNiZHB4ZnRzd0lwQzhCSkFSdmFETXBPY0VSQVBrMmc2RzFmVGoxajFycWRIWTNFb3drRDd6dk5vakZSaGljcTVJclliQ3BhS2c0ajFjRjVwamdjekRQU1N1SlBmc2dYWFdxUEQ5MklKNFdxcUNkMEkxSFNPd3I4OEswWEhad3VZUjgxK09PZkk4Ujlmd3NkdXp6bUJZOER2WUdicjFVK0QwYytzaXhYOWhVS25UU0hMUXNIdGZpQ2VwWVlDTk42YWM5UGNKV3hrWUdzbUtUV0RNYnppQnVqSmgyZjZKQkhZbUpUOENFT2Q0MWdTN3dseXNVMEhkYmhNS0tqZTJaWmdRbmdpcXUzUzlQdEVFVG5SWTFPOVU2d21ydVlNbXU4di9hWC9wbTdpMmQwclFIYzkvQUEvVEZab3pHYlJiaGZCZWNBRTZLbW1CZFNxdEMvd212MUtjTlNBTExlWUM0NHdBWEt1VXd5WDF4YlgvUVBHaENxWDdteHNRUjJXY3RWMEJ3Z2gwTWl6aHJUb1BZMkxLTHVSM3VGNFNFZ0pPVUoiLCJtYWMiOiI2MDUwNmQxYzUwNmExYmVlMTY5YTJmNjgxMTE1ZmZjZjBiZTE4NWQxYjYyZTQzMGFkMjJkMGFiYzkwN2RiYjkzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlYxTDhXanBEOWpmTFkxN3VXdzBTYVE9PSIsInZhbHVlIjoiaWRtQk9lZUpYdFl1ZXoyd3Y0SWJOQXEzbW5MeTgrdEpWdnBGWWxDSG9LcWVGQi9jQi9tbUdnd0Uyd3JScWJ5L2NoL3dHK0ZiL044cGEwOS9OdEY1TkdmY1JGWTRncW5TRk5Pb0E5N2FNS3VRTW9id0dPbVY0bkU5c1Mwb21ZOUp1VVJ2aDdyQW8wUkNyL3NJKytMTkd0ZTRIaUZaeEIzc3J0c0VDTzZFdzhXUEo3RDdZTG1WZDlOM29xc0s5L3lpenRFM2xDUjFXblFDZXNsQkJud0cxbk12cHVJVk1vVlV6MDBlNzJoL0VVRWhvUGU0Tlo5NE1KQ2ZydWxIckZ2eHp4c0gyL2NGTEhiSm15OEFod1FrdXlaQ0JUeElnVmpNMU9NVE1Ha1g1eis2VFdKZWVjWi9xdTJoWmFUUEFKMUNVYmNkYnJMN1oyakxmNjJLZ1J1a3VucmJzZDFrUXdmNVZqNGZZemlCZzI1R0kvdHAyWlZVVGRsazl0UDMvY05jeFIzTUJUdWRjTFJ1UVB2bVlNK2pKRktIV3F0MW5XVks2ZDU4WE1JZWRldzVCeitCdW9pSHdTYjhWU0Q2Wnkwcm5nYk9JSnFsbU96WjdLQlhGdWxsU0ZEcnZGVkxXN3ByVk5FR3NoMGVHMG1vVURtS1hsMVo5cjFjbVhFbHRySDMiLCJtYWMiOiIyZmMwNTU2ZTM2NmQ2OTU5NzU5ODc0MmQxN2U0ZTJhODE0MmFiNGJhMTc4MzZiN2FmNGUzM2UzODQ0YTFhNTUzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-446790366\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1641719930 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1641719930\", {\"maxDepth\":0})</script>\n"}}