{"__meta": {"id": "Xb11ee7197cfaaf43252d43de094fc85d", "datetime": "2025-06-30 14:56:49", "utime": **********.039957, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.579212, "end": **********.039973, "duration": 0.*****************, "duration_str": "461ms", "measures": [{"label": "Booting", "start": **********.579212, "relative_start": 0, "end": **********.97451, "relative_end": **********.97451, "duration": 0.****************, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.974522, "relative_start": 0.****************, "end": **********.039975, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "65.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0031300000000000004, "accumulated_duration_str": "3.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.012365, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.744}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.022605, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.744, "width_percent": 17.891}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.031828, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 77.636, "width_percent": 22.364}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; XSRF-TOKEN=eyJpdiI6IjBoRUoyNWJyK0FUYnljakllTjRzOVE9PSIsInZhbHVlIjoic3BtUUlNc1lUamJ1S0I4bTltQ2xSWnJlSFlPSU12ZU9veldta3k5YjU5VlZBbUFZdUVZa2RTMW5jZjg3dnVTQjREN0NjaXRuM25BZkJ4ZzJxMTNSRWZxVzl6UDlFZTMwSzlYenZranpXb0JtYmVJcnl2eGIwdUdMcEw4YTl1NUMrd25DR0R3ZC84YzVBdFlmeFVNZzhjYlMycUN6TjlZUno0RnRNd0ZOMC9lU0dkS1lZSTNZWnlsZUJzK1lLSW1rVXFycUtmUWhZL0lLdFlzUGVRajhEZzZRbkNFNzhGUFdIcm5oTDZ4VTZUMTdsNUNGb3BOS1ZmVDRVRlA5WXNzYWlGK2RFQXNEeEp6V3g1OTc2VGlsdDZPZjdTUzRuWTNsNjdnUzBIQVB1ZUhmOUliZ1pIck91bnBFcHpxZTZOd0pLUi9OcitBRHRPMnRtRzA0eHNDdHdEZkg4UG8zdE5KT1hTSjRVVTJLQjdsU3c2b1lMVTVzRk1XUUh3d0xFdXBBVFFQV2JORGdxeGdWQk9ObDJpZzRTYUxZdE5EN21lUXNrZWM1Zk1Pemgvb1lLVzFVd2J4Rks5MzFsR0lwNFdHSGh4UjNwQnNxNkFZU0R0MFgxMmhKUHYvQ3lQTGFnRFhMVXgzL05uNEFSMjZmRFlVQ0FNUERReWV6NHdBNEdZT2giLCJtYWMiOiJmYTVmYzY4ZDkxNTZhNTNhM2JiODEzYzAyNjU4YjY1YzA4OGEwYjk5MTI3NmQ2NDEwOWZhNDEzNTA2ZmU5YTlmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik1hU1B6WDQzWDlPc1U2Skg4Z3RBeFE9PSIsInZhbHVlIjoiOHhWQWhRdVpaYS9oSTZUVEpXbzFJSU8zaDRiUUxTNlZLditNcVdMRVhzQzVDZXd5eXF0TWxWQTIzVjdPZUxjRkV2RnkyZHlId0ZlZWRacnVSakVVVXV6ZmxrWHIwMDdaeU54dEJHakw0TlNmRVhVMVV0RzRGSnd4MmdrVGttSUlGNVBKc2ZUUTlxc2hsb0d1dExaYjIrMk1NQVJBU29zRHcxZEQ4NDRNaE9oU01SdnFFN0VrWGdBbkU4Y3I2dEd3dmQxS3dMY1A5RVBObDZDeTlCSDNhcTRtOTZkZHhZcG5rTjhORDdwS2VGb2lCUVpTQkQ2eHFIWTQ3SmpGMllhY1lNS2lWZS9XUmV2amNxVmNTTGpIMTVhK041b1dNMXF3a01qUjZrQWYwTHJ2aS9ZRTVpWDRlNG5NQXFDRGZCNm5ydTFvZ2NyNXR0cXpJMGRWY1V4NWRjVllFU3lvN0NsTnNLOUpWVEk0d0wyK0d2V005SVBGVVFFU2dpYXM5bTJIS3N2UHF3cnVtUUJxVGsvYlE0aGYxL2RsSzMyK1hlMTZPYTQ1NW1Nbyt5WFl3TFFTenhlN21sSnY1K0tWZDVuOTJPKzJCaEZIbzJSZ3RDQ3VyNG41eXJydktBamt6SzEvaGNSOW1BcDQ0VERURDYrWFM5Zk15Mi9rTGNJU0FSNUoiLCJtYWMiOiI0NDVkNTA1ODkxZTdlZGE1NGRjYTFiMDNkYjA0MmU2NTQ0MDRmODE2NjUxMzc4OTk5YmI0ZWNmYmRiYTc1ZDJjIiwidGFnIjoiIn0%3D; _clsk=2uy8by%7C**********578%7C3%7C1%7Co.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rf3imG1lPKr2xfPRA9rNVzJMke6dPWK1Wb6HvZ8N</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:56:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpaM1MyMTFVTURIbzhRamgwa29TQnc9PSIsInZhbHVlIjoiLzI4em5GN1owdnc0MmZPMXBCOTBOeEk1ZGQralN0NmFKd0VPOVY1YVp2eE5CVTd4S2RhOG10dENiKy8rSTZHOTY0Tm5EckRpajJlbUFzZkxXZVpoVHBGK3EwMmROMXNROFhJUEpjMGlMTXBJMDZKYnk4bnhMMVQ1ejBRVmdLQWJ3STFrWFdvS0hWcDZDOFFOMTF5aHY5Y1dMNU8xUEVTdjl0ckQ1MzYyMnRoUDFpTFBodnRsSkFBcnlhMUZUZWw2UklRUGpqRW9UMFNDbzloaktDT2xnMVRDbXRCQmExZmhkRXFkYmFQaUFLcVp5NFAwdWI3NU8xSHp3YlNwMERsNzVBTUVvd054Wm0wckNENFZ6Wm1GRll1bzJuZ2crcmM1ZlFVL2k1Nk10WFNuWUtVU3F2c3VDUzFvcTZ6azcrL2x1MDI2NEEza0I2SU5hVVVhYjIrM3pwajJsWi9yM1N0Y01tcUEydjlTZTZwZy9yeEdXK2pZakRXYUJsS1hsa3hmdUZGYVZpSUVsaVVvOVMrMUVJVlFIekgvMWkwNFFqZW5GK05xZVQvbXBHdUVnUmhiRzQ2aDk4K2ZLZXFGOVJkUU5UQkV3emVUSmd2NjV4bVp5bW1qbnc0dEE4RDBrcmZ4eG5PQmdRckt6N3lya0hNaUhHVmpFcGpoa2hUdTNEQVUiLCJtYWMiOiI3YWEyMzc2YjI1NDczMzdiNTcwYzU5ZmZhMGJlNTQzOTFmOWE1NjA2M2FmYTBmYjE0ZTVlZWNlYzhjOTkzZTljIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:56:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBaQUkya3ZsUE42aXVtRmVSOEx2a2c9PSIsInZhbHVlIjoiMFgxcmdlR1Bta2tLT0JPcG5iaDhPRkxiMFQ5Vm8wdVN3ZFFQN1lpc090dHQ3SzJnU0p5bEFPYjN6UWhXZGZsRXNyWW9pQkNMSnJNRllGNHFzYk9DQ283eG5ySHpYWWJXbjlielhiZWpMTDQ5bnJTdkVuZlBpK0FFZWNZMHFtUU93RzVmR0pDS3pBVnFOcWp4dmkrWFphdVZYK3gxWjV2a2pmRk5qQTlyN2lRUEdNQitnM2p1MVJrcmhEcFhSemZJc1c2ekNWWHVzc0pVcnJnTHVBdFdHUkhOdlJJNjEvWGdMeUtqRVZ3L0NXdC9KZnRLaWFZYmE0aW1ST2dEK2tXc2I1Z3kySDFuaFNxNDFqMjBlbGVRMzU2YzBzeXJyOWlHVlcrV0RselVwVTVmY09aaDdDZFlLVEVDK0pIczZtTi9oeXNHbnk2am9GMjdvb3N6cUtVUHd3eWZaZnJjNVdOeDByenBqYU1rTyt1RUlOMmNCTDFkRzg3UzlTZUFSS1Rnb243WndzK3YrVUN4UE0zbUwrb1h5NHhlc3lHb1diemR4RnBMVld2eHUwSExXNkd2aE1XZTd6bWJQYXM4ZmE0MzlQZ2ZCckdsd3o3UFVQM05BMTc0azVjeDRGbDF0T2txTkFsUmRCOTQ1ZXo3bEhQV21GQ1h2eFhoSmdiRGlvVjMiLCJtYWMiOiI5N2U3Yjg2OWRhOTllYTc5MWY4MzI5NTA0YWE4NWM5ZDRlMThiODY3YzM3NzRiZGM1MmNmMmU1YWM1NTc1YjY1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:56:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpaM1MyMTFVTURIbzhRamgwa29TQnc9PSIsInZhbHVlIjoiLzI4em5GN1owdnc0MmZPMXBCOTBOeEk1ZGQralN0NmFKd0VPOVY1YVp2eE5CVTd4S2RhOG10dENiKy8rSTZHOTY0Tm5EckRpajJlbUFzZkxXZVpoVHBGK3EwMmROMXNROFhJUEpjMGlMTXBJMDZKYnk4bnhMMVQ1ejBRVmdLQWJ3STFrWFdvS0hWcDZDOFFOMTF5aHY5Y1dMNU8xUEVTdjl0ckQ1MzYyMnRoUDFpTFBodnRsSkFBcnlhMUZUZWw2UklRUGpqRW9UMFNDbzloaktDT2xnMVRDbXRCQmExZmhkRXFkYmFQaUFLcVp5NFAwdWI3NU8xSHp3YlNwMERsNzVBTUVvd054Wm0wckNENFZ6Wm1GRll1bzJuZ2crcmM1ZlFVL2k1Nk10WFNuWUtVU3F2c3VDUzFvcTZ6azcrL2x1MDI2NEEza0I2SU5hVVVhYjIrM3pwajJsWi9yM1N0Y01tcUEydjlTZTZwZy9yeEdXK2pZakRXYUJsS1hsa3hmdUZGYVZpSUVsaVVvOVMrMUVJVlFIekgvMWkwNFFqZW5GK05xZVQvbXBHdUVnUmhiRzQ2aDk4K2ZLZXFGOVJkUU5UQkV3emVUSmd2NjV4bVp5bW1qbnc0dEE4RDBrcmZ4eG5PQmdRckt6N3lya0hNaUhHVmpFcGpoa2hUdTNEQVUiLCJtYWMiOiI3YWEyMzc2YjI1NDczMzdiNTcwYzU5ZmZhMGJlNTQzOTFmOWE1NjA2M2FmYTBmYjE0ZTVlZWNlYzhjOTkzZTljIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:56:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBaQUkya3ZsUE42aXVtRmVSOEx2a2c9PSIsInZhbHVlIjoiMFgxcmdlR1Bta2tLT0JPcG5iaDhPRkxiMFQ5Vm8wdVN3ZFFQN1lpc090dHQ3SzJnU0p5bEFPYjN6UWhXZGZsRXNyWW9pQkNMSnJNRllGNHFzYk9DQ283eG5ySHpYWWJXbjlielhiZWpMTDQ5bnJTdkVuZlBpK0FFZWNZMHFtUU93RzVmR0pDS3pBVnFOcWp4dmkrWFphdVZYK3gxWjV2a2pmRk5qQTlyN2lRUEdNQitnM2p1MVJrcmhEcFhSemZJc1c2ekNWWHVzc0pVcnJnTHVBdFdHUkhOdlJJNjEvWGdMeUtqRVZ3L0NXdC9KZnRLaWFZYmE0aW1ST2dEK2tXc2I1Z3kySDFuaFNxNDFqMjBlbGVRMzU2YzBzeXJyOWlHVlcrV0RselVwVTVmY09aaDdDZFlLVEVDK0pIczZtTi9oeXNHbnk2am9GMjdvb3N6cUtVUHd3eWZaZnJjNVdOeDByenBqYU1rTyt1RUlOMmNCTDFkRzg3UzlTZUFSS1Rnb243WndzK3YrVUN4UE0zbUwrb1h5NHhlc3lHb1diemR4RnBMVld2eHUwSExXNkd2aE1XZTd6bWJQYXM4ZmE0MzlQZ2ZCckdsd3o3UFVQM05BMTc0azVjeDRGbDF0T2txTkFsUmRCOTQ1ZXo3bEhQV21GQ1h2eFhoSmdiRGlvVjMiLCJtYWMiOiI5N2U3Yjg2OWRhOTllYTc5MWY4MzI5NTA0YWE4NWM5ZDRlMThiODY3YzM3NzRiZGM1MmNmMmU1YWM1NTc1YjY1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:56:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}