{"__meta": {"id": "Xc3847f0a6dc40f2bf4f5ba3b0c41bc39", "datetime": "2025-06-07 23:58:32", "utime": **********.648504, "method": "GET", "uri": "/productservice/create", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749340711.558898, "end": **********.64853, "duration": 1.0896320343017578, "duration_str": "1.09s", "measures": [{"label": "Booting", "start": 1749340711.558898, "relative_start": 0, "end": **********.37059, "relative_end": **********.37059, "duration": 0.8116919994354248, "duration_str": "812ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.370612, "relative_start": 0.8117139339447021, "end": **********.648532, "relative_end": 1.9073486328125e-06, "duration": 0.2779200077056885, "duration_str": "278ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51672272, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 10, "templates": [{"name": "1x productservice.create", "param_count": null, "params": [], "start": **********.598978, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/productservice/create.blade.phpproductservice.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fproductservice%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "productservice.create"}, {"name": "9x components.required", "param_count": null, "params": [], "start": **********.632287, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 9, "name_original": "components.required"}]}, "route": {"uri": "GET productservice/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "productservice.create", "controller": "App\\Http\\Controllers\\ProductServiceController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=53\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:53-97</a>"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.020739999999999998, "accumulated_duration_str": "20.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.440108, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 18.756}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.466251, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 18.756, "width_percent": 5.159}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.506303, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 23.915, "width_percent": 6.654}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.511955, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 30.569, "width_percent": 5.786}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'product'", "type": "query", "params": [], "bindings": ["15", "product"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 56}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.52316, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:56", "source": "app/Http/Controllers/ProductServiceController.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=56", "ajax": false, "filename": "ProductServiceController.php", "line": "56"}, "connection": "ty", "start_percent": 36.355, "width_percent": 5.4}, {"sql": "select * from `product_service_categories` where `created_by` = 15 and `type` = 'product & service'", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 57}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.530411, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:57", "source": "app/Http/Controllers/ProductServiceController.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=57", "ajax": false, "filename": "ProductServiceController.php", "line": "57"}, "connection": "ty", "start_percent": 41.755, "width_percent": 4.436}, {"sql": "select * from `product_service_units` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 58}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.537847, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:58", "source": "app/Http/Controllers/ProductServiceController.php:58", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=58", "ajax": false, "filename": "ProductServiceController.php", "line": "58"}, "connection": "ty", "start_percent": 46.191, "width_percent": 5.834}, {"sql": "select * from `taxes` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.54532, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:59", "source": "app/Http/Controllers/ProductServiceController.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=59", "ajax": false, "filename": "ProductServiceController.php", "line": "59"}, "connection": "ty", "start_percent": 52.025, "width_percent": 4.147}, {"sql": "select CONCAT(chart_of_accounts.code, \" - \", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id from `chart_of_accounts` left join `chart_of_account_types` on `chart_of_account_types`.`id` = `chart_of_accounts`.`type` where `chart_of_account_types`.`name` = 'income' and `parent` = 0 and `chart_of_accounts`.`created_by` = 15", "type": "query", "params": [], "bindings": ["income", "0", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 64}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.552706, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:64", "source": "app/Http/Controllers/ProductServiceController.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=64", "ajax": false, "filename": "ProductServiceController.php", "line": "64"}, "connection": "ty", "start_percent": 56.172, "width_percent": 11.283}, {"sql": "select CONCAT(chart_of_accounts.code, \" - \", chart_of_accounts.name) AS code_name,chart_of_accounts.id, chart_of_accounts.code, chart_of_account_parents.account from `chart_of_accounts` left join `chart_of_account_parents` on `chart_of_accounts`.`parent` = `chart_of_account_parents`.`id` left join `chart_of_account_types` on `chart_of_account_types`.`id` = `chart_of_accounts`.`type` where `chart_of_account_types`.`name` = 'income' and `chart_of_accounts`.`parent` != 0 and `chart_of_accounts`.`created_by` = 15", "type": "query", "params": [], "bindings": ["income", "0", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 74}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.560569, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:74", "source": "app/Http/Controllers/ProductServiceController.php:74", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=74", "ajax": false, "filename": "ProductServiceController.php", "line": "74"}, "connection": "ty", "start_percent": 67.454, "width_percent": 8.293}, {"sql": "select CONCAT(chart_of_accounts.code, \" - \", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id from `chart_of_accounts` left join `chart_of_account_types` on `chart_of_account_types`.`id` = `chart_of_accounts`.`type` where `chart_of_account_types`.`name` in ('Expenses', 'Costs of Goods Sold') and `chart_of_accounts`.`created_by` = 15", "type": "query", "params": [], "bindings": ["Expenses", "Costs of Goods Sold", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 80}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.568377, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:80", "source": "app/Http/Controllers/ProductServiceController.php:80", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=80", "ajax": false, "filename": "ProductServiceController.php", "line": "80"}, "connection": "ty", "start_percent": 75.747, "width_percent": 7.377}, {"sql": "select CONCAT(chart_of_accounts.code, \" - \", chart_of_accounts.name) AS code_name,chart_of_accounts.id, chart_of_accounts.code, chart_of_account_parents.account from `chart_of_accounts` left join `chart_of_account_parents` on `chart_of_accounts`.`parent` = `chart_of_account_parents`.`id` left join `chart_of_account_types` on `chart_of_account_types`.`id` = `chart_of_accounts`.`type` where `chart_of_account_types`.`name` in ('Expenses', 'Costs of Goods Sold') and `chart_of_accounts`.`parent` != 0 and `chart_of_accounts`.`created_by` = 15", "type": "query", "params": [], "bindings": ["Expenses", "Costs of Goods Sold", "0", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 90}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.576497, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:90", "source": "app/Http/Controllers/ProductServiceController.php:90", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=90", "ajax": false, "filename": "ProductServiceController.php", "line": "90"}, "connection": "ty", "start_percent": 83.124, "width_percent": 6.557}, {"sql": "select * from `users` where `users`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 5096}, {"index": 21, "namespace": "view", "name": "productservice.create", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/productservice/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.61698, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:5096", "source": "app/Models/Utility.php:5096", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=5096", "ajax": false, "filename": "Utility.php", "line": "5096"}, "connection": "ty", "start_percent": 89.682, "width_percent": 4.05}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 5097}, {"index": 21, "namespace": "view", "name": "productservice.create", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/productservice/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.621832, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5097", "source": "app/Models/Utility.php:5097", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=5097", "ajax": false, "filename": "Utility.php", "line": "5097"}, "connection": "ty", "start_percent": 93.732, "width_percent": 6.268}]}, "models": {"data": {"App\\Models\\ChartOfAccount": {"value": 63, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FChartOfAccount.php&line=1", "ajax": false, "filename": "ChartOfAccount.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 69, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => create product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-84114766 data-indent-pad=\"  \"><span class=sf-dump-note>create product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">create product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-84114766\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.520749, "xdebug_link": null}]}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/productservice/create", "status_code": "<pre class=sf-dump id=sf-dump-1733798137 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1733798137\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-715643912 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-715643912\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-240327382 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-240327382\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1123539857 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749340706378%7C22%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndLU3poN0l4YllmVUlLZ0Ivak9jclE9PSIsInZhbHVlIjoibFBGTWI3dUZWenE4UGhicDRGNUxEajByTUFLV2dkdStMQ2JuRnNKaExPanZaRGg5TmU4aTM4dFVRZ0ZmeVg2dlphRmcvMDNhdGRVYXFIMlpiN2xmOU9qUmg1T1VOWkxtOGs1dWNNRi9sTy9uS0cwM01kQTlGNzI3TDBGZWNrYWUzOXNITk5jZjVjblgxQTl1NTJDZ1JsNkRpRExlYUNieGR5SEw1a3pteEhHdDlnVjNyMVJNSmgydnFrNjdmWlZybnRWZFpmMmdCd0VsTzRVOVNTbnQvakgyeEltN2FoU1JPUWJEaVFEa0tIaHo0TU9mWHZPLythMmRWSUtsSGR0MWJqYnM0Uk1UQ2NScGt0RjBLWi9XRWxOK1ZzY0ZEMERkSzYxUU03eENaakF3emt3STFlMmRIYVRpMnY5UlhLNTVxQ1V1MWx2UU5QVllDWFBOTkR2ZUFSbHFjSzR3Vk5wUk01cCtOM2tYTzZBSTVvRVZyMmoxa09EUjg3NDZmV1NBa1Nsc2VKYkxLcGt1LzdiZ2czVXBFeWQvWkZzZVlkTjZYYm5WRnhjUlVlYmNVRFFQMFNKMUJWcUs2WHNFRkFIazBBSVo2R2ZhMWRFS0laaEhZbzlMYWlaWFVNL083UlFBcTVITGNDZmJKY1JGYThtWW9Zc0hlVk1uNVdwNkU0cHYiLCJtYWMiOiJmOWZhZjIyNjNlOTQ5NTcxZmNlMjczM2YzNzIzNTA4ZGNhODg2ODFmN2I1MzA5MWE3ZTlmNmM2YzhlMWY2ODg2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InFLU01hb0FKWUhKNm9FOC9JQ2YrZnc9PSIsInZhbHVlIjoibDUreWpsVzJDQzA2bmpXa1JHWE83M1JYaDRDTkhLWFQ5N0ZEZjhhdUVmMllXOS9lU3VtYmtibzFPMExHdk5nME4waFZJTnBMaklWYXZ6bTdNWWdJaVpmWUF3dVhqVWxYVEZOZEdlbHVseXJOVGJJMW1GT00vT2V6dGVMV01mV2RrNURIYkFiMnRWaGhFNEEzeURBNDFiQkxxd3U4UTB5bVdKaFRCaHVQMDV3aC81b1BhcFhBczhpSW5CSHJXdWZFQ0RJS2U2dXVXbUZFTWlJTFdNSkJ4YitTSlJLSzRabjVYNm5pNnozVFZrMUZIclAzaWcxa1gzOEhWbDdGTlh3RGZmb3c4Nmo1a2liSkpQUEdVMExVYzVLOFZHYlJrQjFsR2dIQkRtdkxubEUzZWoyMk5aeGhkZEUwdDluQ1hLeEhkdEdqSnFVdUxzOXZCUHJKK2VsSFN5Yjhhb0IySHduN0tMbURsd2p2V2hJalVuS1RvWFg3ODFRNXRaenZIQnUyOUJIbldOcmplb1BmSk10Sk5jcGNSaHRuUy9PZU1mR0QvWlcwL2UzRC9aekVBMkM0dHZKL3FhQ0NBZGhPemdjNnU5Q3JMVHpOdTZQS1JudGh1c3NVSHkxSldneFJMYWVhL2twMkhqK0FMQlhmUzgwWWZwL1lLYjVkbXo5RVNtc1oiLCJtYWMiOiJlYzQzZGE2ZmY1ODBiZGI1ZjdlOGUwOTE1Y2Q0OGIzNjFiNGVhNWY3ZDRlZWI3MTZkNjZkYTNhZjBkNzJiYjA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1123539857\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1349971523 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349971523\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:58:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii90N3FoRnZBZ2d3eHBwRFBoM3V2K1E9PSIsInZhbHVlIjoiSVBkMnJGQlFVaUJIeHpiUkMveTlnV0ZGdlh2ZWFHd1F6cEhwakpLRDJHVWQraDNZYks1eGx1Q29QWmtYL2RKSVl2eHRHbUJxUldvVDQxM3hodXBEQzE3NUFUaGhocWVIMWgrQlpYQkVqRVRuVjB0OHRTdy9CZmU0NVF2TlRxUVptY0cyS2VRMXJwZ0FZVkV0K3VhZHAvSTRFOFFLVVh3YnUyVjU3dEFmbFZseGF6WkZlRFEzdVZKRjNNd0JLd1o0QVZnRkdudXZic1hsVysxMThsYzlWdWhadlVBSWYweHNrZWluSUFOR3BnL2l5NUFuekxTWUhjcUY0VExlS1N2RWhQQlJudU9Ma3FRYzdkN3pxd1R0VVdVVEl2a1BiOS9tdWpISE9MUnRkMHUvajlMbW5HZjNnRERIWWs5VXpzdkFSRXUwVDZ0aG8wN2F6ZUlLeXV4VlNVbjlJdTVjOU10MS9wZjg5TW1oWTVNekhHVzNGOU9GSVAzN094cVRSZWdoODJpakl3eTQrQU03ZnZTRjh2NFlWWi9ZRjVWb1pjR2NGekRTL3NVUHFjUnBTWUh5eUliemNXRjZXTGVnY29MekZCNXhmbnlXVjdXZTVIaS8zQzl0MGg3VFY0eFRxM1BvZm02K2RmVmpJTGh3dk1HTGNRS1M3dHJWK0F0ekFRWm4iLCJtYWMiOiJiYzhlOGM4NGI0Y2QwZDY5YTEyNTM4NGY3MTZmODJjY2U5ZGMxMmFkZTA3Mjg0NGRkYzc5ZThiNTQ0NDFjNGE5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:58:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjN1R2FGOHQ3SlBrT0lBWUJUY2ZaZkE9PSIsInZhbHVlIjoicFRhaDNDT1BMUmR0Vys5cVZ0RVhPb01kY1cyNjFCWTN6a1lGOWRLeU5ncmxKUUV3V1RQbnRXaUw5by9lQkpReUNDeHRGeU1rbHdnZ2lSSU96UnZtV3M1ZUpwNXJkVUVvaEEwNWorZjVTb3R0bUVWRkJOcElPaFllRTdOWVN2bkZwY3RadERzOGF1dVNpdjdzc3dCaTdEREpFYjRGM0lFNEtMTitQaGE3UFBRNEZJKytQeGRiWDIzbGhDeUpBbWdYbGdWSFd6WEYvVUoyZE5RSll5WkUwcmNXMTBlcURaWkhBMlIzTU1BelJ0Y0FYV1VnL2lPVWpVUkpSWEhkSXBOVkdHWDYzRlczdEhmNjk4Z3B3ZmhLN1FnVjNzb2dwY1paWWN3cmVZOTJobWdQVzFlQ2FOc0xvY0JwSzlPRk5NUXprbm9XYmpEckV6NEZESlRvcThnK3pCVUxnU0xXZVM3cGt6Wm4zNHJORFNXMW9HWUlOSXg5WGU4cjdUS0ZlTmVOQTNOTnFOVzdvY3ZaZlhkcGVma1k3ZlRNU2JuRzBiMWtISzNyb0NRc2lJUUxxUElWcTgxSFJITEpzSlRTV1B0cUNLTG0zS0RIUEMvMWFySFFkQTJEQko1T3Y1S2Rkcm1lS1d3bkNGV0xmU0VRZk9qWUdaN1o3d1M2ZHVjUnpuYlciLCJtYWMiOiIzYjI3MGMwZjY2NTgwZjg2ZjY3ZjE0ZGViYjhjYTliN2I2YWVkY2M0Njk4YTliMTk5N2IyODE5YThkY2YxNzNkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:58:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii90N3FoRnZBZ2d3eHBwRFBoM3V2K1E9PSIsInZhbHVlIjoiSVBkMnJGQlFVaUJIeHpiUkMveTlnV0ZGdlh2ZWFHd1F6cEhwakpLRDJHVWQraDNZYks1eGx1Q29QWmtYL2RKSVl2eHRHbUJxUldvVDQxM3hodXBEQzE3NUFUaGhocWVIMWgrQlpYQkVqRVRuVjB0OHRTdy9CZmU0NVF2TlRxUVptY0cyS2VRMXJwZ0FZVkV0K3VhZHAvSTRFOFFLVVh3YnUyVjU3dEFmbFZseGF6WkZlRFEzdVZKRjNNd0JLd1o0QVZnRkdudXZic1hsVysxMThsYzlWdWhadlVBSWYweHNrZWluSUFOR3BnL2l5NUFuekxTWUhjcUY0VExlS1N2RWhQQlJudU9Ma3FRYzdkN3pxd1R0VVdVVEl2a1BiOS9tdWpISE9MUnRkMHUvajlMbW5HZjNnRERIWWs5VXpzdkFSRXUwVDZ0aG8wN2F6ZUlLeXV4VlNVbjlJdTVjOU10MS9wZjg5TW1oWTVNekhHVzNGOU9GSVAzN094cVRSZWdoODJpakl3eTQrQU03ZnZTRjh2NFlWWi9ZRjVWb1pjR2NGekRTL3NVUHFjUnBTWUh5eUliemNXRjZXTGVnY29MekZCNXhmbnlXVjdXZTVIaS8zQzl0MGg3VFY0eFRxM1BvZm02K2RmVmpJTGh3dk1HTGNRS1M3dHJWK0F0ekFRWm4iLCJtYWMiOiJiYzhlOGM4NGI0Y2QwZDY5YTEyNTM4NGY3MTZmODJjY2U5ZGMxMmFkZTA3Mjg0NGRkYzc5ZThiNTQ0NDFjNGE5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:58:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjN1R2FGOHQ3SlBrT0lBWUJUY2ZaZkE9PSIsInZhbHVlIjoicFRhaDNDT1BMUmR0Vys5cVZ0RVhPb01kY1cyNjFCWTN6a1lGOWRLeU5ncmxKUUV3V1RQbnRXaUw5by9lQkpReUNDeHRGeU1rbHdnZ2lSSU96UnZtV3M1ZUpwNXJkVUVvaEEwNWorZjVTb3R0bUVWRkJOcElPaFllRTdOWVN2bkZwY3RadERzOGF1dVNpdjdzc3dCaTdEREpFYjRGM0lFNEtMTitQaGE3UFBRNEZJKytQeGRiWDIzbGhDeUpBbWdYbGdWSFd6WEYvVUoyZE5RSll5WkUwcmNXMTBlcURaWkhBMlIzTU1BelJ0Y0FYV1VnL2lPVWpVUkpSWEhkSXBOVkdHWDYzRlczdEhmNjk4Z3B3ZmhLN1FnVjNzb2dwY1paWWN3cmVZOTJobWdQVzFlQ2FOc0xvY0JwSzlPRk5NUXprbm9XYmpEckV6NEZESlRvcThnK3pCVUxnU0xXZVM3cGt6Wm4zNHJORFNXMW9HWUlOSXg5WGU4cjdUS0ZlTmVOQTNOTnFOVzdvY3ZaZlhkcGVma1k3ZlRNU2JuRzBiMWtISzNyb0NRc2lJUUxxUElWcTgxSFJITEpzSlRTV1B0cUNLTG0zS0RIUEMvMWFySFFkQTJEQko1T3Y1S2Rkcm1lS1d3bkNGV0xmU0VRZk9qWUdaN1o3d1M2ZHVjUnpuYlciLCJtYWMiOiIzYjI3MGMwZjY2NTgwZjg2ZjY3ZjE0ZGViYjhjYTliN2I2YWVkY2M0Njk4YTliMTk5N2IyODE5YThkY2YxNzNkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:58:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-507967126 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-507967126\", {\"maxDepth\":0})</script>\n"}}