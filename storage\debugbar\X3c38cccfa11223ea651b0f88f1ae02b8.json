{"__meta": {"id": "X3c38cccfa11223ea651b0f88f1ae02b8", "datetime": "2025-06-30 14:57:19", "utime": **********.55869, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751295438.942532, "end": **********.55871, "duration": 0.616178035736084, "duration_str": "616ms", "measures": [{"label": "Booting", "start": 1751295438.942532, "relative_start": 0, "end": **********.455235, "relative_end": **********.455235, "duration": 0.5127029418945312, "duration_str": "513ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.455246, "relative_start": 0.5127139091491699, "end": **********.558712, "relative_end": 1.9073486328125e-06, "duration": 0.10346603393554688, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45348624, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0034900000000000005, "accumulated_duration_str": "3.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.531236, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.335}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.54848, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.335, "width_percent": 16.619}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.551441, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 83.954, "width_percent": 16.046}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-2051246490 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2051246490\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-755014848 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-755014848\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-561711174 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-561711174\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1039738377 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; _clsk=hcmbe9%7C1751295430794%7C1%7C1%7Co.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjdrUnF0UzVBZkovQ3RnSE1zZ1lXeUE9PSIsInZhbHVlIjoiRk04RWpRT1JNa2VXWmdqYVhKemplUnlIUDlXREpHVjFpWnYyVUdkTE14MzU5dkc1K2ZueWtGWnczb0NscHNvVVZ1Q1NRMDhkWklMTkZ5Zm5UZWVzYnpzUFRLaE9ia2RBR1RyNUxZSVM2eTNZSmNkMGszRThVbWJITWRPNXl3Ymg3b0pmREQ0WFM0MFo2NVdMSEV5aFYvdFZuWmRsVW1BWlpGQmZrQWZ0NituWEFhTlFNR1RhdXRWcVJBbmowQngyYlhLaFh4UmR4eHMvWWh0SDJBL25RTUt2S2F2UVk0YVBqYzlNbkhVTTdvNndwUGRycTV2UHlyd0d0ck9tNWpkOGs0c1ptd1R0QWZTRjk0VlN3dEdRbGF4ZGkxdThlSnd6SVpyVkdTc2pOTXhSeC9HckZnWEpoMFpBd09iS3hiZm1SSzY1N1VYcXhhVVYzRE1hY205c2pqZEQveEhaUWpZYVYvU1FqQjR5NDUxbnp4MSswbjZyeCs1MWFMbUQ2bVp1akttZTBsZ0I5bjk0dndTL0lwZHQrNnBieWlrM0JpSHlSKy8yVUlQeHFaK0Y3M0ZLUytaQkdmTldka3dmTmhETFFBMGp3SExTZkI0MHN4K252MzhVL1JmZ2toS0N1cnRHRW1iUGNIMEdmYmdhUWRCUHR1cjBYaHNtcG05M2M0Z2MiLCJtYWMiOiIxNTgxMjI0OTlkOWNhZTAyODY2NTMzNDBhYjA3MjY5NDViY2Y2ZmM3YzJhMDU1ZDU0MjgzZGNkNDBmNmE5NmIzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IndiODJHTGhmUFdFTVI0L1ZyVWpPOHc9PSIsInZhbHVlIjoiWk1JRFIwcUhOcU1YamxnSFI0Z3RCRjcrNlV1WHR0eXVUVVp5c2hwaFJyamFlMXN2cldVdXp3anVoK2dOaU9LZ042ZHlEdXdhcnpBOFBjTzNaeG1rZ3pOVXlydlVXWVd2LzJuWjBQSFJXaGZoN2NRNlVIZGZHK1NLdVJiZktQcnNjM2daRkc3RFhmZ29UQUl3R2VvVTE5bSsrN2hvSUFxVUxua2F2ei96QXVYcit0SnVDaHRoaEF2eU1EaFp5M3c2ZU5Gem1uTkEwR1dDcXhRQjdoSUJtc3puVHVLTUhDVGpiSStJZTNaQlZNVWtpZkc5UWdPd2tpalVqRThZeXNsV2NsekVzYlZhWEFQdDRubTVMWHFSRFd0NnJyUDhONndQSUlsWVZERFZDWEhKamxyWDJsblFxRWxqdXVXUS8xbkhwaWxUSWIrTnlHZmRjMm1TbUhWYjM0SGx4YkJyR0JocUpWRVVDNlQ2SWZFUFd3ZjZhZ3RFN2tic3Z1cXZEVzlvT2RQSFpOSGNjZG1TbjJvYzJXT3JWNmRXdkhvbmNzajN3SjZLRHVsLy95RmNkSi9pcGJ0ZFVQRndPczNTeVg0UllTTWFmd0pqNVdtQWg3VUJaWjZhYTUzQ2VWRjZ1dm9qRmF3bnoyVXVrTzRJNU51MVBRenppNnpoNHFleHhTYWwiLCJtYWMiOiI4NTU0YzZmNmQ1M2QwYjA3ZGE5YjcwOTE5ZDc0YjM2YmEyM2EzZTU4MDk1ZmY0NDJkZWQwZjNhNzNlZjRhZTRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1039738377\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1889602090 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1889602090\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:57:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9lVEcyUWRtMUNmc0NGTlNIb0FZd3c9PSIsInZhbHVlIjoicXA5dFFSTzYzQTRadmtPRmtoakp6QUVwZVVEdUN2cFNBTTJZODhoZ3pkRFdyNG94alFYKzBOTUhkVFcwNHJyRmdFYnovMVhqTnJGc1RIWEFzVkwxa3JQMko1VXg3M0ZsSEtJYVpDVStFVlhjbzQ5YWVUcUpHS2x6QmU5YU5WZ1pGNkhBYmFENUZCZzdIcllmRTRmZDlYV2FIL0c4TExzWWxrU2FyYThQaTBVTVdzYkVLVDhMSi9UL0dqMC95VmhHeERvUVc2c2pNVTJmQXJXUWZ3Yi9WVUdxSDJKYVhZZzFBZkhKaUNMMUxUTXhuaER4Z2doRWlFTTBUbTRXUG4wbU03S0V4Rm5kbGRtTlNDMGIwMmpaWFJlbjBZaHl2WlQzQlFZZ2FMK2NiLzhUUWhoMWVXdGRRdjNhV0VjQlFUelp2RUZrNkpGeFF4dmdUVWFzU2tVK1JoeisrVEY2VkJpTDBBdWFZTEsrODBWZzZ1UWdsTG1TWXJsK2FWWVJXSDRvb1ZlaVZ6QVQ1V2k2NWVSeWRsYm5vNDRsWkl0UnIvNXRQMlVLbVh0SWM4VllEUmRiY1g5TDhNT0tqaDY2OSs5clRKRUFkbldVa1VNUVJTSnNBWmZ6SHhJZVNkTlVCVUVwbmNzU3pyS2JkUyt5R0VBM2ZPS3l3eWZ5aFkzWUtXSUoiLCJtYWMiOiI5YzVhOGQ2YWM4MWMwODBhMjA0ZDNjNjEzMTg1OGEzNWI3M2RjZDE0YWMxNDY2N2VjN2Q5ZGUzYzU5MTAwNjQ5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:57:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlRtTllUZDFsSWk2aEQzejVoQ20vMEE9PSIsInZhbHVlIjoiejJYTFNMbzZFSW9ZUHdZWGQwdTE3bUJBTy9TaFgyeWpLZUhHMHpaZDdDSzFhclljRk5XdklUYzZMQzErZHhtVDBxb0o0SkhWSktDWWFvWXkzZ3oyMUtMY2ZSZVJEYjNrTzE3T0JWVGpLbFUra1padFJCK1UrS0ZKUjFqQzFzQ1FyU2tpUjJjV3BQdHNNYkxqTUJpaWJLZEErRXJXRVZrQVlqVUc4NW0rQzY5dDVGQXJUOVpkVzdlbDFnL05pSFJ1eEtVbElxZTE2YXBlMVRiSXMwRmZYOW9OWEluM3lLMEZUbCt2TS9QclhSbDZhWWE0Nm82ZmpqUi9jR1BrRXd2UTVJY2JDN2l6QUQyUmRYVTN1czVUYks0aHArRkdhbzZjRWV4d3B2SFdvSW83M2hYLzhBWTdReU5hNnY1Tjc2QnIrODA3V2wwNnY0cXhJQ3RXdFI0MGpqTkI3WDVpMi9KNW5NNjBnUlgwQ0Vmek4wcjJhd2NicmJkZjYzSWNITGQzYUkvMXBjWTBrNjRHb2dPcThEMDN0V3ZsVTRmeVVScHI3QzRQcjk2YjQyY3JqTGFvOXA3QmVjdldIVHFNczBuQ0x4NzFQT3IzeXk3WlQ1RVNHekt0Sng0TXNuREVuV2dwdGViK0NUOUxxV3RkZm1iWHZpWUtJQVRwVDNqT05zWmMiLCJtYWMiOiI4MmQ0MzIzZGIxNDhjMjE5MzhjZjhkZGJkMzRmY2YyNTkzMTEzMmE5NWE0ZjBhMzM5NGZhMDM3NWFjZWU2YzIzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:57:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9lVEcyUWRtMUNmc0NGTlNIb0FZd3c9PSIsInZhbHVlIjoicXA5dFFSTzYzQTRadmtPRmtoakp6QUVwZVVEdUN2cFNBTTJZODhoZ3pkRFdyNG94alFYKzBOTUhkVFcwNHJyRmdFYnovMVhqTnJGc1RIWEFzVkwxa3JQMko1VXg3M0ZsSEtJYVpDVStFVlhjbzQ5YWVUcUpHS2x6QmU5YU5WZ1pGNkhBYmFENUZCZzdIcllmRTRmZDlYV2FIL0c4TExzWWxrU2FyYThQaTBVTVdzYkVLVDhMSi9UL0dqMC95VmhHeERvUVc2c2pNVTJmQXJXUWZ3Yi9WVUdxSDJKYVhZZzFBZkhKaUNMMUxUTXhuaER4Z2doRWlFTTBUbTRXUG4wbU03S0V4Rm5kbGRtTlNDMGIwMmpaWFJlbjBZaHl2WlQzQlFZZ2FMK2NiLzhUUWhoMWVXdGRRdjNhV0VjQlFUelp2RUZrNkpGeFF4dmdUVWFzU2tVK1JoeisrVEY2VkJpTDBBdWFZTEsrODBWZzZ1UWdsTG1TWXJsK2FWWVJXSDRvb1ZlaVZ6QVQ1V2k2NWVSeWRsYm5vNDRsWkl0UnIvNXRQMlVLbVh0SWM4VllEUmRiY1g5TDhNT0tqaDY2OSs5clRKRUFkbldVa1VNUVJTSnNBWmZ6SHhJZVNkTlVCVUVwbmNzU3pyS2JkUyt5R0VBM2ZPS3l3eWZ5aFkzWUtXSUoiLCJtYWMiOiI5YzVhOGQ2YWM4MWMwODBhMjA0ZDNjNjEzMTg1OGEzNWI3M2RjZDE0YWMxNDY2N2VjN2Q5ZGUzYzU5MTAwNjQ5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:57:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlRtTllUZDFsSWk2aEQzejVoQ20vMEE9PSIsInZhbHVlIjoiejJYTFNMbzZFSW9ZUHdZWGQwdTE3bUJBTy9TaFgyeWpLZUhHMHpaZDdDSzFhclljRk5XdklUYzZMQzErZHhtVDBxb0o0SkhWSktDWWFvWXkzZ3oyMUtMY2ZSZVJEYjNrTzE3T0JWVGpLbFUra1padFJCK1UrS0ZKUjFqQzFzQ1FyU2tpUjJjV3BQdHNNYkxqTUJpaWJLZEErRXJXRVZrQVlqVUc4NW0rQzY5dDVGQXJUOVpkVzdlbDFnL05pSFJ1eEtVbElxZTE2YXBlMVRiSXMwRmZYOW9OWEluM3lLMEZUbCt2TS9QclhSbDZhWWE0Nm82ZmpqUi9jR1BrRXd2UTVJY2JDN2l6QUQyUmRYVTN1czVUYks0aHArRkdhbzZjRWV4d3B2SFdvSW83M2hYLzhBWTdReU5hNnY1Tjc2QnIrODA3V2wwNnY0cXhJQ3RXdFI0MGpqTkI3WDVpMi9KNW5NNjBnUlgwQ0Vmek4wcjJhd2NicmJkZjYzSWNITGQzYUkvMXBjWTBrNjRHb2dPcThEMDN0V3ZsVTRmeVVScHI3QzRQcjk2YjQyY3JqTGFvOXA3QmVjdldIVHFNczBuQ0x4NzFQT3IzeXk3WlQ1RVNHekt0Sng0TXNuREVuV2dwdGViK0NUOUxxV3RkZm1iWHZpWUtJQVRwVDNqT05zWmMiLCJtYWMiOiI4MmQ0MzIzZGIxNDhjMjE5MzhjZjhkZGJkMzRmY2YyNTkzMTEzMmE5NWE0ZjBhMzM5NGZhMDM3NWFjZWU2YzIzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:57:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-822934941 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-822934941\", {\"maxDepth\":0})</script>\n"}}