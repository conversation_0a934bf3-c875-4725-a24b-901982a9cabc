{"__meta": {"id": "X867b1771c19f8c604a06f210873dea4a", "datetime": "2025-06-08 00:09:43", "utime": **********.759098, "method": "POST", "uri": "/empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.031404, "end": **********.75912, "duration": 0.7277159690856934, "duration_str": "728ms", "measures": [{"label": "Booting", "start": **********.031404, "relative_start": 0, "end": **********.646236, "relative_end": **********.646236, "duration": 0.6148319244384766, "duration_str": "615ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.646247, "relative_start": 0.6148428916931152, "end": **********.759122, "relative_end": 1.9073486328125e-06, "duration": 0.11287498474121094, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47349592, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@emptyCart", "namespace": null, "prefix": "", "where": [], "as": "empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1655\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1655-1669</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01737, "accumulated_duration_str": "17.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.691217, "duration": 0.015449999999999998, "duration_str": "15.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.946}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.718871, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.946, "width_percent": 3.569}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.740956, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 92.516, "width_percent": 3.685}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.744388, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.2, "width_percent": 3.8}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-112087103 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-112087103\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.750732, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Cart is empty!", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-106842614 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-106842614\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-53825211 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-53825211\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-123453487 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-123453487\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-483866718 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBST2trc2xkNVV6cTREV0NPdjZsK3c9PSIsInZhbHVlIjoiYnNXVmFpUFpaWlVrMCs2V0htSGtzb29RMEtxd3UwK2VhM2QvdWo3MkVhNjJOOVB6VU93cTh1Yk5JOGcwZ2FiZlMzWjllY0NoT3lIWUdjVmt5bnhualJCZll0NnZGSlpxTTFUMkZHVmlMQ0VYaTMreWZiVURXVmZqdmwybWhaYUw5TlNYUlREVGQ5cjMvRU0yenlhREF2UTRQcEcyUTE4c2RyNXNJOWl4V0J1ejVLMGtnSVNaR3c0aUdRUmdtNTdpeWZCN2VnWFdaYjMyL3BwdFhiNUx0d01ZbDJxNnRZcUJnalFNNWp2eGFIUVptN2Q0L011d2hwWmZVQk1LVzRuTjBtWlQxMExmaS9NVDkzbnlMaGsyWE10aitXaUZqQ1V3d3VzdzM0b3lGYVo3T2kzNSt1K2ZldElZWjNBN0Fsd3ZCQTl2WDZlMFRrcU5STkxoSVJ6MFJwRGltdHZTanJLVzhjalozUTZmdm1tSER2ZDNtK0lmb3ZQaG03WFhWZzBvK0lpMGwyb0RXM3cySWpSSHlpd1k3SUdKYnBnUmxYNmtIUHF5RHJORHZrdFl4aUJzNGlCbWNSdUdSTloyNFFoVXVuUTkybFVyelVwZ2g2SkFidStBS1JiNmoyRHBOS1FtWG5NSExBNlg2ZGE5a1Z1eG5QcUpsbTZValdaZTIwMk4iLCJtYWMiOiJkYjQxZjlmOTBlMWZiYjBjYThkYTkyNDQ5NjNiMjdmNzUxZjYzNzI4ZGYxNWFhNjIyYjYxZGUyMDA1OTMwNTExIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Iklvc0ZEQXkxMFNSSjNwalVEcmI0S0E9PSIsInZhbHVlIjoiK0RIaXQ2U1prZml4VmJ2TTlPOXFUUGxzWUhvL1VyaE5zOWx5a0RvVW1EUzRlVHBMOU9wbE9sckRPaHA4RmVkY1NlSnJRMWNjNEZwNXQ1YVpqNzU5NzRmeXVNSklMc09PU01laDI4VWErQXlxTjZoRld0bytPbnJZT2dzK0NSdEVEYjc2dUkwL09oSmhnUjFXVUkwL1NabW85SzE5N2FPY1NGMGN0eVQ1eW5JdysxRjBWTVM4aTFBSnpNQ21CZ21SMHhZS2krRnpwRmg5MzJlYnhPT3BORjlxOXJ1ZHZkR0NGNXhtendWWktpNG1aaUVnT1ozR2tPOE5DbjNtK2JhZ3V0OVE2YVBNRTRDSnRPZWRCZ1lkejV5c09jZHZVY0Q4U3UxNEU4RlhjcU9VTFZyb05KQ2JBYWlFd2svdnI2UXhlT2pmU0pEUmFsNmt1c1NGRnJsb3JjdER5d0JaOW52VmF0dVZBTFRySlhtSTdGcHp0RXZYV0lwRWpmOWVOSEZoeTZHeXJDTlU4bkRvR2N5VTltOS9MaDErQWxXd2IzSmlhbnBhTWY2Z0FOL2Rvc2ZjRTl5Wkp1SFpBUjVqekY5UnMrRmtybXdMUzh4bTJqOUxKSXRFUWZMc3hwcllOK1NORUxTK01RSjRESFFEdFdlUzI5WlBlczBZUHdCbk0vRFciLCJtYWMiOiIzYzdkYTUzNzZlNGVkMGQ5OTc0ODJhMDdiYjdlNmRjYjc3YzMwNmY5ZjUwZDM2YmE3MmNiNTk3MTM4ZjM2YTYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-483866718\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-705123907 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-705123907\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-750982852 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:09:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImgrTUtlUkpKdTRFcWQzZDRUblFyQkE9PSIsInZhbHVlIjoiSU5qT1lYdFczU3NPZnBqOFgyUlFVaTluWVkvNmNNd3M3VTBoM0Y4aDBwUmgvNkJjazUxcm8rZ2o1WnNaWE9aSWUrZ0tQQm9YZWRaMXJhQjhpT01pN2premYyR0QwRVRBaDZWQU5QQ21HbStBMEgyV0hJcXhiV2dEWHdzczdIaUpQb3ZvU2RuSTRJc3ZQL0llMmJkZGRZaW5PcVoveFkzKzNYMC9Ldk5jQm5TeWlrdEdOL1B4VnZYQWtBOWN2QVllR0RTc1RpeW1YWi8xOGhsZ0lsODVBcTg0NFpaQ1JpVmhhcDJmM2lUN24yZkdGeStxZkgzU3FjT3pZVWhkY1dlY3JqdzF1V1ZianhhR2F4dG53ditmMHd3dnJKcUxsMjF6VFZ2b1ZmUGFhWGlwQXlqU0NNOTlWQnNkSXFDZVVYS2JWUGVHRlBCRW0vZmJ2UFdyMmRVenYzazl5Nmt3eThUc0F2S3AyR25Qc0l1b3A2UWEzaFNDcU1WSjh6WjN4UVUwNkloMno0OG1aSENMcDRONUxlOUJiVWUyaXhVcHpzaG52MjZQWWZ3TzJxTEo4cUVpQUdnMnBOTkF5OHRiZUQweUlYa0lOR0RJa1o3TUNBamEzVXdTbXBXeitrMlV2OUFadVhrMjlKQ0hoaHVpQ3lkRGtJdWpOOEdLZWtNaTFWclkiLCJtYWMiOiIwOGVmZGEzNWNmNTQyNWVlMmVkZDhlNzU0MjA2N2JhNWQxNGUzOThkNzkzNThkNmFjMDBjZDRlMWE3MDY2ODg5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:09:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IndNdmN0T0tndmI5aTF5d3dLVGt4REE9PSIsInZhbHVlIjoiOWZPaWNCWkNoZ2E1TFdFL2YybHBPVDJ2NzBPU3FDK0lPUGVkcEtTU2RZS2V6d0tHUTVSaUI3Z0R4NldmenBzbUpLaGhvNVR5Z2JoQ0FWVU1QZXVpcUxIZG4zS2VKRitGS1lVVHp0MEl1TTdER1IvWGs5cVh2YjdOUXJmOEd1dkozMkhwQlZGRURuZUJHaTVLSUxRZWphbDlEbENCQ0gwSDV3dVBHVnBvaWZma3laWXROc3BqaVJqd05KTE9QZ0JseG5aRFhnekU5QXRpTmdGV0N0STl6RGMxSmJveEt4c1drNE1NMzQ0bU10NlhtbDBkK2NnZkQ0OGlKT2hveUZvclRScWFiN2luWHpGcGhiY3RBT1FMdHM2NXBhdncreGpWcitsLzdHcGlMNGxuZ293QnovYzV0NlA1dlRFQWxHdWJuNFFiclBqdUE3K3doNUhGYTFGS0xJU2hzNVR5RFM5US9nL2U2b0RHNnVxczVtc25jbkFDcC9RMDBaUkFENzREdWhhS0plbTFZUmpIclZZTUkySDlMWjBzUjB5ajJzMzZjU3pHQThQeUhBbEF4eDI3YnFwOHdoVTdYY2xhZjNSQTZueXQrNEJYWjdiUEk3VUc0NWN2NGFLd1EwcGtxMDNXSDg2VjNMaXl5SVJaVHlsWmRNUEo3Tm5lQW9nYVowa0MiLCJtYWMiOiJjMWU0NTZhODA0MTZhZDMzN2FmZjQwMjUzYWFmZTBlNjhkYTc3ZDg0YmMwZDA3ZWRkN2U0ODU0MDY3MWQzYmVkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:09:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImgrTUtlUkpKdTRFcWQzZDRUblFyQkE9PSIsInZhbHVlIjoiSU5qT1lYdFczU3NPZnBqOFgyUlFVaTluWVkvNmNNd3M3VTBoM0Y4aDBwUmgvNkJjazUxcm8rZ2o1WnNaWE9aSWUrZ0tQQm9YZWRaMXJhQjhpT01pN2premYyR0QwRVRBaDZWQU5QQ21HbStBMEgyV0hJcXhiV2dEWHdzczdIaUpQb3ZvU2RuSTRJc3ZQL0llMmJkZGRZaW5PcVoveFkzKzNYMC9Ldk5jQm5TeWlrdEdOL1B4VnZYQWtBOWN2QVllR0RTc1RpeW1YWi8xOGhsZ0lsODVBcTg0NFpaQ1JpVmhhcDJmM2lUN24yZkdGeStxZkgzU3FjT3pZVWhkY1dlY3JqdzF1V1ZianhhR2F4dG53ditmMHd3dnJKcUxsMjF6VFZ2b1ZmUGFhWGlwQXlqU0NNOTlWQnNkSXFDZVVYS2JWUGVHRlBCRW0vZmJ2UFdyMmRVenYzazl5Nmt3eThUc0F2S3AyR25Qc0l1b3A2UWEzaFNDcU1WSjh6WjN4UVUwNkloMno0OG1aSENMcDRONUxlOUJiVWUyaXhVcHpzaG52MjZQWWZ3TzJxTEo4cUVpQUdnMnBOTkF5OHRiZUQweUlYa0lOR0RJa1o3TUNBamEzVXdTbXBXeitrMlV2OUFadVhrMjlKQ0hoaHVpQ3lkRGtJdWpOOEdLZWtNaTFWclkiLCJtYWMiOiIwOGVmZGEzNWNmNTQyNWVlMmVkZDhlNzU0MjA2N2JhNWQxNGUzOThkNzkzNThkNmFjMDBjZDRlMWE3MDY2ODg5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:09:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IndNdmN0T0tndmI5aTF5d3dLVGt4REE9PSIsInZhbHVlIjoiOWZPaWNCWkNoZ2E1TFdFL2YybHBPVDJ2NzBPU3FDK0lPUGVkcEtTU2RZS2V6d0tHUTVSaUI3Z0R4NldmenBzbUpLaGhvNVR5Z2JoQ0FWVU1QZXVpcUxIZG4zS2VKRitGS1lVVHp0MEl1TTdER1IvWGs5cVh2YjdOUXJmOEd1dkozMkhwQlZGRURuZUJHaTVLSUxRZWphbDlEbENCQ0gwSDV3dVBHVnBvaWZma3laWXROc3BqaVJqd05KTE9QZ0JseG5aRFhnekU5QXRpTmdGV0N0STl6RGMxSmJveEt4c1drNE1NMzQ0bU10NlhtbDBkK2NnZkQ0OGlKT2hveUZvclRScWFiN2luWHpGcGhiY3RBT1FMdHM2NXBhdncreGpWcitsLzdHcGlMNGxuZ293QnovYzV0NlA1dlRFQWxHdWJuNFFiclBqdUE3K3doNUhGYTFGS0xJU2hzNVR5RFM5US9nL2U2b0RHNnVxczVtc25jbkFDcC9RMDBaUkFENzREdWhhS0plbTFZUmpIclZZTUkySDlMWjBzUjB5ajJzMzZjU3pHQThQeUhBbEF4eDI3YnFwOHdoVTdYY2xhZjNSQTZueXQrNEJYWjdiUEk3VUc0NWN2NGFLd1EwcGtxMDNXSDg2VjNMaXl5SVJaVHlsWmRNUEo3Tm5lQW9nYVowa0MiLCJtYWMiOiJjMWU0NTZhODA0MTZhZDMzN2FmZjQwMjUzYWFmZTBlNjhkYTc3ZDg0YmMwZDA3ZWRkN2U0ODU0MDY3MWQzYmVkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:09:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-750982852\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-593253042 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Cart is empty!</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-593253042\", {\"maxDepth\":0})</script>\n"}}