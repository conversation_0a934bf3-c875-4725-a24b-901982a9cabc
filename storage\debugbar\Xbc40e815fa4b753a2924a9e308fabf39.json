{"__meta": {"id": "Xbc40e815fa4b753a2924a9e308fabf39", "datetime": "2025-06-08 00:08:11", "utime": **********.099444, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749341290.4017, "end": **********.099471, "duration": 0.6977710723876953, "duration_str": "698ms", "measures": [{"label": "Booting", "start": 1749341290.4017, "relative_start": 0, "end": **********.01029, "relative_end": **********.01029, "duration": 0.6085898876190186, "duration_str": "609ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.010306, "relative_start": 0.6086058616638184, "end": **********.099475, "relative_end": 3.814697265625e-06, "duration": 0.08916902542114258, "duration_str": "89.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45269856, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01559, "accumulated_duration_str": "15.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.056712, "duration": 0.01465, "duration_str": "14.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.97}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.086277, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.97, "width_percent": 6.03}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-406861467 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-406861467\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-54902669 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-54902669\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1052384408 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1052384408\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-45826820 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im8rWXNLalphdjVBL2ZDMzg3Sy9NUWc9PSIsInZhbHVlIjoiM3JIRzliOUJqUEZUYms4N0lqTW1MT244WlhDTnNhZ2s2NVNaS2Z0ekkrTWhWMVE4SlVNLzN2dHE2WUZBY081Q0d0M010Zng1SGlPclIxN0lvMGV0QUJGc3dUa3RZQXdnZGN6Z3FjbXhOS2FhQjVGWENmOTF1WENFOWhrSG5NYUE5KzVsL25tMXFKbEM2OGRpcUFvdDB6WEo1ZGMrTEU3VEprYkpQODV0MHhlOXgyVVlmNXhZcG1jY1ZiT3RDVG5aaXF3M3BGSFhtTUN2M3hEZk5SNHljT0grVFBkbytwMVpPY1BNR1NBOFloYzhRN3RxczRnQXZRTCtkOEhacEZqK0JBeGNvbjdDYk54RzJ0aUZ2eTZPK3NQeS9IM2JWNHZHTE1DeVR1RHZKeVd1bk0xZHRWdzN2OGdvR2xtY3QyZ09qMFF5Z2ZWYnlsUVJMbjNNNC92NUtDZG1PbEpvYzNEc2hYTG5HQitwR0ZWK3ZVbzROY041Nis2SGxHQStRZXZRNEtyMldnYnhwVUFFQVlQeXFrSk5wZFFPZ09manl0UHFsMWtOUmorWjFuejEyZ0c5eWxVTTRTc0dFOTMyV3pCUmMybmhvT2dzMkpVSVoxalFBS2RHWWtkemRxdE55Ny90Qkc3UGF1NUo4ai8zQkZ4cnlyQzRPYzk4M213bzFsYWsiLCJtYWMiOiJjZmZhZTM0ZDNhNDRjN2M2NjQ0Y2Q2OTE3YzcwYWNmZWU1MzliOGUxNmQ2ZGRjOWQ1MzdkN2RkMDIxZmYwMWE5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlNjS3Zvc2dXV1BZaDZPeTdVUkNSeUE9PSIsInZhbHVlIjoiMFR1VmJnMFQvY0RwV2ZkUnUwZUcwZVd5dnc5TUNvazRwUFZncy9iZ0NQbmlzajduZTJRQllLWXBDYVNCaU9zOGM5TDRFUkUwb29IU0lBb2lFdlBGMS9NL3JGVUpXRTg5Z29HK2YvRGR2RmtIdSt6L0o2RnlkZ1RNNkNmSWlYQzlSK1p4VFMwa3htcXZKWVZHb1RrOVNSVEkvd0hZczg1R0N6VGF5anVKYTBqRUF0cEN0V3pua3Y2aGU5dVRLVkxXL1pMWWFpeWF2b2hJSkVQUWE3dHRCaTBwYi9MWlhJSkwxVDZETG16cUZYKzRkYjJKRGlvNkk5NG9zL2ducHo3cG9TYUpJTUc4cWNIL1RuMkUxSzFXQUtYQmdkaDVpdm92VEk2SzdidW93TXlKQm5SdEN4RmpJQUlPWnlmVmMwVUZoWEhGbFI0YjNBUWFBdUFlNnB0QnkrSEgzUFl5czZleDZraERhZDk4MTIxRWg2WGtpa1BpQkR2ektnUTU2OC9UMnQ5dFN3VVUvdmM4U2JmZkR4Y0ZIK1BKR2h2YlpFU0tIek1LNGhwb0dnSllzUVpzRkM3aHgvWTl2RDI3TGs4TVJQUWdTdXlyUko2aEJRVktZajRIU1pvN2l4NTJvYTNLL1p6d3VMMUp4ZHZKUmg1Vkk3WHJ5dEhYbWNJM0o4d0wiLCJtYWMiOiJjODZkYTEwNzc0MDVjMDc1NmNmM2UzYTcyYWNlMGE2OTcxNjBiYmFjY2FkOWMxODg4MWVkNWQ1M2U1MTBhMTFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-45826820\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-37841668 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-37841668\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1130019350 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:08:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJCc1pGK2ZPSk5pZURRV0R4WS9VcHc9PSIsInZhbHVlIjoibzcxazhWMFRXY0VvVnh0U2htY0x6dzI0MC9tTHpFeEx5R1VPbk5aYXdGenhoWGE5N1dsRWtwcGVwdEkyZTlYbW1KK0VqTWhJUm1IUEtNYTdCcS9lME9UTnQrMzR2VWtFMEcrVUxRenNVY3k0VEs2U2xyVGtaejN1aHhRbXEzREF3MmFKOEE5amtQL3M4LzllZXQydndnYy9pWURMVHk2S29TMmtEaTdDbWRHQjd6MGJVZnhiOVRsVE5sSzFWOXJWL1BhWkFlRlluVFFXZkJ0V2JhbWdrVnhEVm5sRit3TEp5QTJaZDRTRnlpdUszT3VKR1pVY3hrZSttb0xvUmlEMjh2a0QvOGFjSmVuOGFQQW1hUjFqNGZXdERacE1MVUJnNWpzUzFMcjNuNmpvam5lQWhmWFJ2RDlqMDVyd1pSczAwam1jd2t6eEF1OWZoK09mTkNlQ1dVcjRrUWNSZzhxNURHK2ZtS2RGTG9nSHhXYUhkZVk4ekxCakRsNk5tN1oyZ1YxamVBZGFweHpVdDJPWnhWZkVJWWRremd3eWtia0tDb1NzT0VBZEwxUEtmMSsrZzNOSFk5YnZpa0NNUGJQTGZUOXZVTGJPNFd6dEdxekQwaWZLbnU5K1lXN1JXR3cvZVVDN1JuSkxLTVNreUZPaitlcWpxcVNqa1J6a2o5aEoiLCJtYWMiOiI3NjUwNTFlMWQ1ZDlhNDIzYmZlNTU2YmMyNGRlOTg4OTJlY2NmNTgyYTU3MWVlMjdkZTY2OGQzNjhjYmNlYzkwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:08:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkVEejNVRWhjK1FUSWsrYkNKWUowVHc9PSIsInZhbHVlIjoib0xaanBKUDRBUWJHdlY4SUZFRnNieExNOE0rVm5iY285WVZBempUZTlpZ3Mwb29Jd1lpMlphUW5KUUs1YldRT2l5MVF1S0FuTC8vMFdxdGZDenBXbk02QXRaek5qUHM5ZHAyZHZIZjlTVVNFRmtUdnBZRHN5NnJmdzhEREhVMHpDUklnaWkwcVBoajFUYzU2bHM2Q0MzVWdiNUdsTmplV1hMbHJpSmRXMDc3aHZaSjZpbCtRdTNlQzN2WEE3VVVEWDJWQ1NUaTFnR2gvZDZ5Z0R3WFVyWUFvY1hIZ29OaGEzVHVKdkJEekdhNTZpTEwxYWN6QUZoMGpVREx2YXhiSFlFRUZwS09hRU9uQklRditpK0liNnVKcmV4eW9tL1l5SUtORm9mNit5eGZ3OXJkbFhuWi9EZC93YXB1bnAzZlpBWDg1ZlRDalZXdmxZaXc4enlHbSt0SVdIL1hqQThIUUpWaE9LeTRKQjlDWHpPVTlaeERMdXRCY3laSTFHWXNxOVNON3VWRDZ6cE1oV0dZcVdUSzdCWis5NUlLVDY4ZGtKZ0c5UUYwK0dpanZjNWdDTkxUaEtCU3dHcDMxRFMyWk1oM1VTRm45NE1jcHMrMUNYMVdYZ1Y5VjRIV2RKeEpGZW9kS0JNWThPNGhmQjBpdTNoK1hLVTFkNHhaVStDVDMiLCJtYWMiOiI5OWY2ZjVlOGVkNDc4MTRiZjM5NzViNzBjN2E1Mjk2Mzk5NGY0ODAzNDM4OTA0N2VjNTg5OTkxMDY4ZjcwOWM1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:08:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJCc1pGK2ZPSk5pZURRV0R4WS9VcHc9PSIsInZhbHVlIjoibzcxazhWMFRXY0VvVnh0U2htY0x6dzI0MC9tTHpFeEx5R1VPbk5aYXdGenhoWGE5N1dsRWtwcGVwdEkyZTlYbW1KK0VqTWhJUm1IUEtNYTdCcS9lME9UTnQrMzR2VWtFMEcrVUxRenNVY3k0VEs2U2xyVGtaejN1aHhRbXEzREF3MmFKOEE5amtQL3M4LzllZXQydndnYy9pWURMVHk2S29TMmtEaTdDbWRHQjd6MGJVZnhiOVRsVE5sSzFWOXJWL1BhWkFlRlluVFFXZkJ0V2JhbWdrVnhEVm5sRit3TEp5QTJaZDRTRnlpdUszT3VKR1pVY3hrZSttb0xvUmlEMjh2a0QvOGFjSmVuOGFQQW1hUjFqNGZXdERacE1MVUJnNWpzUzFMcjNuNmpvam5lQWhmWFJ2RDlqMDVyd1pSczAwam1jd2t6eEF1OWZoK09mTkNlQ1dVcjRrUWNSZzhxNURHK2ZtS2RGTG9nSHhXYUhkZVk4ekxCakRsNk5tN1oyZ1YxamVBZGFweHpVdDJPWnhWZkVJWWRremd3eWtia0tDb1NzT0VBZEwxUEtmMSsrZzNOSFk5YnZpa0NNUGJQTGZUOXZVTGJPNFd6dEdxekQwaWZLbnU5K1lXN1JXR3cvZVVDN1JuSkxLTVNreUZPaitlcWpxcVNqa1J6a2o5aEoiLCJtYWMiOiI3NjUwNTFlMWQ1ZDlhNDIzYmZlNTU2YmMyNGRlOTg4OTJlY2NmNTgyYTU3MWVlMjdkZTY2OGQzNjhjYmNlYzkwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:08:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkVEejNVRWhjK1FUSWsrYkNKWUowVHc9PSIsInZhbHVlIjoib0xaanBKUDRBUWJHdlY4SUZFRnNieExNOE0rVm5iY285WVZBempUZTlpZ3Mwb29Jd1lpMlphUW5KUUs1YldRT2l5MVF1S0FuTC8vMFdxdGZDenBXbk02QXRaek5qUHM5ZHAyZHZIZjlTVVNFRmtUdnBZRHN5NnJmdzhEREhVMHpDUklnaWkwcVBoajFUYzU2bHM2Q0MzVWdiNUdsTmplV1hMbHJpSmRXMDc3aHZaSjZpbCtRdTNlQzN2WEE3VVVEWDJWQ1NUaTFnR2gvZDZ5Z0R3WFVyWUFvY1hIZ29OaGEzVHVKdkJEekdhNTZpTEwxYWN6QUZoMGpVREx2YXhiSFlFRUZwS09hRU9uQklRditpK0liNnVKcmV4eW9tL1l5SUtORm9mNit5eGZ3OXJkbFhuWi9EZC93YXB1bnAzZlpBWDg1ZlRDalZXdmxZaXc4enlHbSt0SVdIL1hqQThIUUpWaE9LeTRKQjlDWHpPVTlaeERMdXRCY3laSTFHWXNxOVNON3VWRDZ6cE1oV0dZcVdUSzdCWis5NUlLVDY4ZGtKZ0c5UUYwK0dpanZjNWdDTkxUaEtCU3dHcDMxRFMyWk1oM1VTRm45NE1jcHMrMUNYMVdYZ1Y5VjRIV2RKeEpGZW9kS0JNWThPNGhmQjBpdTNoK1hLVTFkNHhaVStDVDMiLCJtYWMiOiI5OWY2ZjVlOGVkNDc4MTRiZjM5NzViNzBjN2E1Mjk2Mzk5NGY0ODAzNDM4OTA0N2VjNTg5OTkxMDY4ZjcwOWM1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:08:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130019350\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1567362586 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1567362586\", {\"maxDepth\":0})</script>\n"}}