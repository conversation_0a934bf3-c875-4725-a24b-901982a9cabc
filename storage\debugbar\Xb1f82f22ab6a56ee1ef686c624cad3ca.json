{"__meta": {"id": "Xb1f82f22ab6a56ee1ef686c624cad3ca", "datetime": "2025-06-08 00:29:52", "utime": **********.942256, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342591.922807, "end": **********.942283, "duration": 1.0194759368896484, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": 1749342591.922807, "relative_start": 0, "end": **********.812846, "relative_end": **********.812846, "duration": 0.8900389671325684, "duration_str": "890ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.812862, "relative_start": 0.8900549411773682, "end": **********.942287, "relative_end": 4.0531158447265625e-06, "duration": 0.129425048828125, "duration_str": "129ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45593240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.017429999999999998, "accumulated_duration_str": "17.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.873883, "duration": 0.0157, "duration_str": "15.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.075}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.908216, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.075, "width_percent": 5.049}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.922823, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.123, "width_percent": 4.877}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1994214306 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1994214306\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-393538079 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-393538079\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1118660520 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1118660520\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1702756325 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749342587944%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtTQ0t4bWI2UHJ6WC9Ld1dEOVUrS3c9PSIsInZhbHVlIjoiUWNVTzkyOW1vLzZBWi9vL2NTRjBsaG82bFVKYkRRb2xYUzMyUUhZcjc1S2cxemNNb2F1L2dZZXkycXFPaXZKMmNWWU1WZjl2eXdmeGpkcTBaNnlNY3UxS0dUZWthUWEvNkY0dVVQYUZ1MmlPOGhwOEQwZmNjbXBTU0phWHRma3MxQ1VMMjFGNUtrTlArUFI4ekZWUFlrZ0E5VnB4QXROcDZkNFJlMUhKSFhuN2dXeDdlRnNqajJxcUxjNkRBenVreXJCQmN6blVSaWwwOURzbnFmNWMzQXBSQnFHTGxkbU1oUzFOM0RGc3ZOK25WaXY3NjVYa0JHSnpsWDh3Mm42S0Z1alZKMTUyQmJIbVJMM2ZiUjlEYzhqdjNRenJReVUzUXB1OWJFYXZRRGxTSVJZNEQxTGErSHBmd3orczZFTjZPRzVrNUcyWmsyVHA2eFdFZTZWcDlkUS9wN2l4bVlOMXd3VVRBV0VmQ2RTb3cwdVFOQ0kzbzVzYzliQWtLaEs5ODJXdmhjNUJFYXl3VlE3eGJGMXdvZm1DejVpQ2lTMDQxVnArQitCcXk4Y1hDMmlkYzZTSUZ2R1liSXF5Z21ITllWVEdjckxpRjdST2dhdnh2d2VSRVpDZXV4cUNwTjUwcCtGYmhiUkVtd1UxM2hnSGNsa2o0eGxFQmRCNE1kcC8iLCJtYWMiOiIyNjU2M2IxOWIzZThkN2E5YTVlNmI3OWY3Y2Y3NTU1YmYyNjAyMGQxOGUxZjg4Y2I1Y2Q4YTE5ZDBkYTZiMzM4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikl3Z3BlZGpxdGlDSnprdzZpTlJXelE9PSIsInZhbHVlIjoiTE8yTEpVV0FjRFRMTUZrR0c4ekQrYTE5SmFzRGFJNlpSVlNycFpVWkszTkQvYW5MYU5sTjN2bEhuSTRvb3MvL05YOTE2NGQzakMvZUpqSC9oSHMrc2lTZnd6ZTJOUC9VNEZ4blFlTDdaV3RhYkVQZXVQL0dEdzV3SFN5Q0RzZDMwdG1ZbFEwelVabU12NW42bCswZGVoZ3NrcXBObk8zWk5DOGZySENjbHF5b09yTFFJL2twd0Q0dHFCWkFVd1hHdk1YZmRmVS8yeWhBbEZCZXN5NWp3c01qTldUZS85eExtdDdjRlQrRHk4RFV4ZjV6Z25kNGtkL0FCUjFTR0t4blRqNnJGY2F0SUZqeWJqWU5Uczk3QjRRSEQwR3JzNGp2RURIUHkwOEJ5aDhTVDB4WUhqR2J2cmk2aXQvbFltVU40ZlNXYkF1akJhOG1OODFmS0NmdDFaYkE5bk1GdTFlRHNIZlgyVDdBZEk4SnNKdlVKSGR0RUJNOUxzU0dmZ2JZRkJ5N1l5dG5aWXNTOVJJcW9Ed3ZMRTJiTUdmN2lVbWszbmhHTnRrMXhiSU5mRVUwZFpvcnY4anc4QjZvTVhNQWJSZGxCcC9zU1BtOHA5ZjM1eUZ3dnlmMWtIK0ZPaFFIcmkxdjlqT3ZTTHhXUER3NXpLQ2RrWkFtT3dCN3A3OXAiLCJtYWMiOiJmN2ZjOWQyYmMwYjQzODhhZTAzNzFmOWM5MjBiNjViY2QzZTllOGRlMTBkNDQyYTJjYmJkZTI1YzUzZTI3ZjMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1702756325\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1957241992 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1957241992\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:29:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhLUTZudGhSUFdBUTNLVEJ5WjZ3Nnc9PSIsInZhbHVlIjoiNVVKK0hqT01GaU5uUGZ3Z2ZGbVg4cVRENTdjSk10STliZm11NnErOG1IUDZiVGJZU3NiUk9YaXFmRVBrMGREOHQxd3c0bWlhTGhkN2ZGQzk3bmhJdCtSWmZkcm9odmFUcy9yY0pMeVlvUUttU2lIeDJTZEtzOU5CRjlhK2dub3hUNk12czVZUm1lVWxhTnhhTk5INlBPNUpFVDBqRnVMMTNkSEZxeWg0Q2JqamhIR0hZU05LdWR0aGl4QTRhODVWNk9Nb051MzV1Qm5NN0R2SWQxRXl4bkZpTjlFNVBIRnQzSHdpTGc5ZTBsbVl6NEw1ejBHblVxamZOYlgvYXFEb29CUk85dlc5b09MdWJaQlRZMXpZdS8zYzFucVRLbVFKZkh4cSthMG5FemNiUytjL2YyVFJEYkJRRjhRYnBqRTRQTUd4eUJTZ015a05ORGEzZnNFWnRZMUhoYzhBbzRndjhUMHY4WS9hSHZhMG4xOVFpVmpMUXd6bXZlcmx5STU4ZERPdUJhT3hXb3BvWTJJTGZDWnl0YUV4d0pRdFJwZVlRSnJ0byt0Z0FXN1poWm9LVjV5bTlTUVErVFZmWHNVcXJxVU9GbEo5YlNDa3JiT09qZXEzVFVsUTBISERZMTBTOFZ5WjJzdFQ0eVJMb29GOWJ4YVJWelZobzBPWStxemciLCJtYWMiOiI4NzczNDFiNzliMGRmOTk0ZWM0OWQzYzRhYWZiZTI5NDYwNDM4ZDRiYTI0NDlhM2E0MTkxZTJjZTc3Njk3MzgwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:29:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZoNDQ1dlA5UnRwVUpQSXFBYlREK0E9PSIsInZhbHVlIjoicjNYRUx5ajQ1YjhYdkgzL2JJczNQNFJPeFVFS0gxVzZTWGhpQld6WlVkeWd1c1pOL3dVRWFNYWk4S0FsVHRVcWVHRUhSMXgwQUJqVVgxUEJLanRLUUtCV3dvbGFtaXduYjZubW5MbkFCMjZyMEIyOVAycElxaUc0YXErOHlGSkdhYlhWckN1M0gzb29iVG44OGJMMUtYVS91dUJrZ1NKRHkwTEU2c2Vac2VldVFGZ1VTazJML0FCbHRzYWFPN04zV2FTZTNUYStVVFdwUDVHU3I3RW9ZY1AxZ3gvemtLVnBJd1RJVDlxNmN0dEVZSm1NYTZ4M0dqUTFyTExWczg5ZFlPTmJvdkJvVmpNaEhObEUrWVprRnp3dkV4NU5ZREx3S0ZySC9YUmxSMlQ4UjFxNGp1czlxbnJSem81and0NlFlSTZwbHR6V3JhLzFLK2dHZXdSZERMcGQzZUkwNENyOGlIWWgxUnJNdkRkTlNPMm1idm9KU285REplQklMQmxhSHFZSmtTS0xYWVBteTQ2UTJKNFZRc2xzWkFwNWFlWEhESXlhNGRKQjdFbGRnQ28yOXZsaWFHQmFBcVFKSjhTU0Y4RnQwSWRYM3d4WXpYay9uZHVTSW1qLzBTNUNLR2JtZFJQMGdiYkI5ZU5CUVJaNVB0M21pL3VLZklhMFQ2THgiLCJtYWMiOiI2ZmFiZmNkMTZiNmEyODM0Y2E5NDNkNmEyY2FjYjMyNjM1MWEzN2I0NzgyNTAyNzgxYTk3YzRiYWVhOWI2OWZlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:29:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhLUTZudGhSUFdBUTNLVEJ5WjZ3Nnc9PSIsInZhbHVlIjoiNVVKK0hqT01GaU5uUGZ3Z2ZGbVg4cVRENTdjSk10STliZm11NnErOG1IUDZiVGJZU3NiUk9YaXFmRVBrMGREOHQxd3c0bWlhTGhkN2ZGQzk3bmhJdCtSWmZkcm9odmFUcy9yY0pMeVlvUUttU2lIeDJTZEtzOU5CRjlhK2dub3hUNk12czVZUm1lVWxhTnhhTk5INlBPNUpFVDBqRnVMMTNkSEZxeWg0Q2JqamhIR0hZU05LdWR0aGl4QTRhODVWNk9Nb051MzV1Qm5NN0R2SWQxRXl4bkZpTjlFNVBIRnQzSHdpTGc5ZTBsbVl6NEw1ejBHblVxamZOYlgvYXFEb29CUk85dlc5b09MdWJaQlRZMXpZdS8zYzFucVRLbVFKZkh4cSthMG5FemNiUytjL2YyVFJEYkJRRjhRYnBqRTRQTUd4eUJTZ015a05ORGEzZnNFWnRZMUhoYzhBbzRndjhUMHY4WS9hSHZhMG4xOVFpVmpMUXd6bXZlcmx5STU4ZERPdUJhT3hXb3BvWTJJTGZDWnl0YUV4d0pRdFJwZVlRSnJ0byt0Z0FXN1poWm9LVjV5bTlTUVErVFZmWHNVcXJxVU9GbEo5YlNDa3JiT09qZXEzVFVsUTBISERZMTBTOFZ5WjJzdFQ0eVJMb29GOWJ4YVJWelZobzBPWStxemciLCJtYWMiOiI4NzczNDFiNzliMGRmOTk0ZWM0OWQzYzRhYWZiZTI5NDYwNDM4ZDRiYTI0NDlhM2E0MTkxZTJjZTc3Njk3MzgwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:29:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZoNDQ1dlA5UnRwVUpQSXFBYlREK0E9PSIsInZhbHVlIjoicjNYRUx5ajQ1YjhYdkgzL2JJczNQNFJPeFVFS0gxVzZTWGhpQld6WlVkeWd1c1pOL3dVRWFNYWk4S0FsVHRVcWVHRUhSMXgwQUJqVVgxUEJLanRLUUtCV3dvbGFtaXduYjZubW5MbkFCMjZyMEIyOVAycElxaUc0YXErOHlGSkdhYlhWckN1M0gzb29iVG44OGJMMUtYVS91dUJrZ1NKRHkwTEU2c2Vac2VldVFGZ1VTazJML0FCbHRzYWFPN04zV2FTZTNUYStVVFdwUDVHU3I3RW9ZY1AxZ3gvemtLVnBJd1RJVDlxNmN0dEVZSm1NYTZ4M0dqUTFyTExWczg5ZFlPTmJvdkJvVmpNaEhObEUrWVprRnp3dkV4NU5ZREx3S0ZySC9YUmxSMlQ4UjFxNGp1czlxbnJSem81and0NlFlSTZwbHR6V3JhLzFLK2dHZXdSZERMcGQzZUkwNENyOGlIWWgxUnJNdkRkTlNPMm1idm9KU285REplQklMQmxhSHFZSmtTS0xYWVBteTQ2UTJKNFZRc2xzWkFwNWFlWEhESXlhNGRKQjdFbGRnQ28yOXZsaWFHQmFBcVFKSjhTU0Y4RnQwSWRYM3d4WXpYay9uZHVTSW1qLzBTNUNLR2JtZFJQMGdiYkI5ZU5CUVJaNVB0M21pL3VLZklhMFQ2THgiLCJtYWMiOiI2ZmFiZmNkMTZiNmEyODM0Y2E5NDNkNmEyY2FjYjMyNjM1MWEzN2I0NzgyNTAyNzgxYTk3YzRiYWVhOWI2OWZlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:29:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1712518259 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1712518259\", {\"maxDepth\":0})</script>\n"}}