{"__meta": {"id": "X93eca8393002c2bd4820e43ce8966852", "datetime": "2025-06-07 22:20:15", "utime": **********.751226, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334814.28711, "end": **********.751261, "duration": 1.4641509056091309, "duration_str": "1.46s", "measures": [{"label": "Booting", "start": 1749334814.28711, "relative_start": 0, "end": **********.475754, "relative_end": **********.475754, "duration": 1.1886439323425293, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.475783, "relative_start": 1.1886730194091797, "end": **********.751265, "relative_end": 4.0531158447265625e-06, "duration": 0.2754819393157959, "duration_str": "275ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45983352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.633911, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.654871, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.721963, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.731867, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.023360000000000002, "accumulated_duration_str": "23.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5740988, "duration": 0.00588, "duration_str": "5.88ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 25.171}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.587365, "duration": 0.009460000000000001, "duration_str": "9.46ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 25.171, "width_percent": 40.497}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.606235, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 65.668, "width_percent": 3.425}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.636312, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 69.092, "width_percent": 6.036}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.657233, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 75.128, "width_percent": 4.837}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6898131, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 79.966, "width_percent": 5.051}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.700017, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 85.017, "width_percent": 5.736}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.70821, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 90.753, "width_percent": 4.837}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.724935, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 95.591, "width_percent": 4.409}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DCK491V1GUyngHueDf4bnOw7DOzApt305JPGLVcA", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-969128831 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-969128831\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1862331622 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1862331622\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-978695879 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-978695879\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2073392578 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749334789141%7C1%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImN6YkJTZm5kWWh2VWpTbkMyY3RFYlE9PSIsInZhbHVlIjoiNk16bGdnMktpQjhKZzNOa05VU0RZWTZUMTdxRnVqbnBCUHZpWjAxM2RyU0xHaTVPcmlpOFNibXFhSTBFeWJwbUJCNjJkOVc0a1B4Z1VEV3p0UitTMm5PWXRVeE1MT1RpSUtjR2d2WU5LZlg1aFJRRnFaL2kxYkRncUVGcVF0YjZhNGlNc09yUStnUGJjTjJ2MFhRajF6ZXZSL0UzRUI3WDdmZEprZiszcDl3VTJueHJiVnFhUTF1ZUdoR2xMUTRtTzA2SHdac1dMKzBlOXF3aTBRakt3d2k1TmJ1Q3lGWERaU1dyRzBJM3hqWGFOZkJOaHJ1YW1xVGVURUdtbmhMcG1UdkFvQjhQeWFFellKTzBtRDlXSm1Yd3NyUjIzamdEd2JxZTQ3RGFPZ3JIaVFiaWZhZ0tkR2hHZVY5ck10VzJ3SGMwZUFCbXFTNVdlckVwZzZsU3k1ZSt2T3huaFloL3d1YmpZUGF0ZytqWGlLc09WVWh2T0QrdHFYSWtsbHNVR2gwUytWUnBXWlU2Y0pmNTk4ZUsrRGxobzd6NERmc25XQnJYWGJCSHR4WUpjUVlDdlc0K21aNEJyQkE1cnVGYmI4dUxha1dXRmpHejg4TGdUR2Z5S0J1MXZOYnVvM3VXN0F2ekRid3dYV1N0eGU3SFgyUExKMnRxNkxtdmtDS1giLCJtYWMiOiJmZDk1OWJkYjliZTI0NjI2OWRhMTdjYzZmZTA1YTU0ZTYxMGMzNTIxM2QxMGJhYzFmYzliZjE2MzVmNmRiMTcyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlNoS1NsVTRndTE0WWhRSGNTc1owYkE9PSIsInZhbHVlIjoiT0hZM2o5aGhzYU01Q3d3NDhWYk4xL2w1b1BSdGovN0ZjbUVYSTdOemFPSVh6NTJidlFENm9jcFV6UmJ3Y0pETEVPNGN3UzdhcWhjdnpJcnZnd3ZFVm00TGY0UlNRcVNlZnFXUzBEUUNUQS8xTHFHQkVJb0pBMnc0aHpKNVdDVzNTbjFqbXFtV3o2MTlVbi90VlpwRlp6VEhOOEh4WXBWbmpJUjNkQ2lQZWNxOEp6Um5TRGVyU1c1MEFhS29SSDJvbzczUVdQSGFFell1NmkyMldnaEo3N2pxRHA2YXA0ME56Q2RiT3ZablNvNHVhMCtHc2RCVzlZYWJlRk9mQ2hBM2FuVitPTE56blZITUxzR096WG9TYklQdmNTV0J2bHQ3a0FGOVljeWxlM0dteFFNZFhUMDJjckZ5UE1hWnFzQjdOZUNRSHk0eC85M0YzVFpZL0paVTZZQjBSMWJPSWxsWGpkblJJV01KYndDTmlmUHFIVzhuL0hoemc4NHhENmhyYzc1UXNHR252enhtNmkzNmdYMXd1NkZwdHBHaHFzVlQvV0pkK052SGh0WnBHWDBmWnN5UDBuSW1RcFNWM3p4aXhzbWo5ZjN1a3VFczhoK2dBeFY4Wml2VURwejk0aGdaTUkya0xYSi9Ec21ETE1xYXYrR3FHRXZLa2JxRjJQaWQiLCJtYWMiOiJlYjgwMTQ3ZWI1MzU5NDI4ZDU0ODM1ZTU4YzFiZWJmMmI5N2M5MWM0YzRjZGFiYzkyMDc4Njk5YTdlZDAxMTljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2073392578\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1677612068 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DCK491V1GUyngHueDf4bnOw7DOzApt305JPGLVcA</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9hFyIirUIgn7VMVjViW9Tfz3n0yci3AztutQe7vH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677612068\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1876847524 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:20:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InplSVo1Nm5wM1JsRmE2RFp6RERidVE9PSIsInZhbHVlIjoiRGtRMUljc3EyMCtUWWFvWkZzZGxvR2JRN1IySjR5djZkRW0xZ3I3bGtURmlYUkFGM0pGU2xlWDdRZXBHYTZ1S3M3cWhRcnpodWttQlpMMDgyaUVzR1U5c0VDbjBmOUtOWlRRejBmUkRsWEFlR2lyTEdoVldodDJKMGl3YXpCMGFXQWFjZWt4TjVIWHA4R3g5VFptTXFFN2ZBSmhzNnplSFNFemZQYmlFdDVJMDlHVTI5bWNCUnh1K21HcGRjdFFUN0lJUkt6VW1hajIvUHhQRnB5UGlHZGZQSVlJd3pPeld6d01YVVBUeFA4RG5iRWZKR3B2Vk5NZGJSUkd1Zkt3V0lNekZTYlVzUnUxbktubnI4SmdydGxjZVVlbXVYdmVVb1VmZldDdXNRK24xYzZiNU53UVZEV1RscmNQeTE4WWlmZlJCemZOL042YlU1TnRod3ZtQWVRek5JZmFYdEJzNVhBaFA1NEo2M0lHa2VmS3hmWlMyWndZQXllMmpIUW9manFIZk0xSTU2cmp5ZGltcGFWUHVsd0VmQ3NjU3AvZW5CTXV5VVhsZVpuMmR6eTFIR2gxMS9OOXU0bTlTS1RZTU9OZnlMRTMzZE9pYXI3d0docVhOdXREaHQ3dGErakxYVWZBNmxKU2E2NnUxZFdwWTUyMVllTTJ4T0NTNW9hTmYiLCJtYWMiOiJmNmIyZTE5MWI5YzM3NjI0OTUzNjYxNWNhM2JjZGM4NDE2ZTA2NThmYzgyNzQwYzRmYzM3MjQ3N2YwMjU1Y2I2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:20:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjJ0NURnU3NXWVVZVHNpaS9OZktlMXc9PSIsInZhbHVlIjoiOXpLZm9zWmVyZjl2OG1zWXVjb3NtTlhrTmRPWnlOOFdrenFyZzZ5VjIwRzQwMy8vNW5IK0NGQkZGY1J5aUJ2REU0MUp1QjgvZkJqRTVWZmc0RHpTV1J6THVMU0YrUGtXWlhuZkhjMFZtd2JvYjBqUkE1QXFOQjV4MFRGUzUvc0hHMkdFcW94OEM4Mnp4OHZXMXVkSCtrT3lEQzZ2MkdqNHpQa2NaVTJtUlhDd0M2SUxySisxWkVVT2UyUzNvdG12d1NKWlNLdEE1NTFUUnFBS1M5YzRBSVdxVDBGS1ZGSWZlREUzWStKakZ5bmVXekFob0d1dXpsSGE2T0NtZ2d2VWxHa2dQcTluVUp1eFlFTi9pM2VKQ0hxUjl2aEdmWnY3OE8vSzdOeVhPbE1NaEdWNjB5NXY3dHZEY1l2V1pNK1hkeis0VGVVSm5vZVNDTTNxS3E0L1RHQy9lc0RsM0E5akVmamsxL0FSUktKMmhzbUxCNndmK2hobDNhbm5pYVBIdkhZNitnaGtwdGxnOXNNM0Y3YVZsNjArQ2pPNHdPaWV4ZWhiOFRDOUpPbFlVeHo2R0VDVStoUzZNOENnZXhibmUyK2MvTEJYVTVHQkljUXhjSXZwZStuWGowNVEzSFJteHVtelZZWlpkRlRhMGtSdmtnTkVmVk5Na2V3Z1JQVmwiLCJtYWMiOiI0YjhiMzliNTVmNDM5MThmOTE4NDgzOTQ5YTdlNTMzODFkNTdmZWE2YTlhNzY0NzdjNWQzY2Q5Nzg5MTc5YWI2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:20:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InplSVo1Nm5wM1JsRmE2RFp6RERidVE9PSIsInZhbHVlIjoiRGtRMUljc3EyMCtUWWFvWkZzZGxvR2JRN1IySjR5djZkRW0xZ3I3bGtURmlYUkFGM0pGU2xlWDdRZXBHYTZ1S3M3cWhRcnpodWttQlpMMDgyaUVzR1U5c0VDbjBmOUtOWlRRejBmUkRsWEFlR2lyTEdoVldodDJKMGl3YXpCMGFXQWFjZWt4TjVIWHA4R3g5VFptTXFFN2ZBSmhzNnplSFNFemZQYmlFdDVJMDlHVTI5bWNCUnh1K21HcGRjdFFUN0lJUkt6VW1hajIvUHhQRnB5UGlHZGZQSVlJd3pPeld6d01YVVBUeFA4RG5iRWZKR3B2Vk5NZGJSUkd1Zkt3V0lNekZTYlVzUnUxbktubnI4SmdydGxjZVVlbXVYdmVVb1VmZldDdXNRK24xYzZiNU53UVZEV1RscmNQeTE4WWlmZlJCemZOL042YlU1TnRod3ZtQWVRek5JZmFYdEJzNVhBaFA1NEo2M0lHa2VmS3hmWlMyWndZQXllMmpIUW9manFIZk0xSTU2cmp5ZGltcGFWUHVsd0VmQ3NjU3AvZW5CTXV5VVhsZVpuMmR6eTFIR2gxMS9OOXU0bTlTS1RZTU9OZnlMRTMzZE9pYXI3d0docVhOdXREaHQ3dGErakxYVWZBNmxKU2E2NnUxZFdwWTUyMVllTTJ4T0NTNW9hTmYiLCJtYWMiOiJmNmIyZTE5MWI5YzM3NjI0OTUzNjYxNWNhM2JjZGM4NDE2ZTA2NThmYzgyNzQwYzRmYzM3MjQ3N2YwMjU1Y2I2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:20:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjJ0NURnU3NXWVVZVHNpaS9OZktlMXc9PSIsInZhbHVlIjoiOXpLZm9zWmVyZjl2OG1zWXVjb3NtTlhrTmRPWnlOOFdrenFyZzZ5VjIwRzQwMy8vNW5IK0NGQkZGY1J5aUJ2REU0MUp1QjgvZkJqRTVWZmc0RHpTV1J6THVMU0YrUGtXWlhuZkhjMFZtd2JvYjBqUkE1QXFOQjV4MFRGUzUvc0hHMkdFcW94OEM4Mnp4OHZXMXVkSCtrT3lEQzZ2MkdqNHpQa2NaVTJtUlhDd0M2SUxySisxWkVVT2UyUzNvdG12d1NKWlNLdEE1NTFUUnFBS1M5YzRBSVdxVDBGS1ZGSWZlREUzWStKakZ5bmVXekFob0d1dXpsSGE2T0NtZ2d2VWxHa2dQcTluVUp1eFlFTi9pM2VKQ0hxUjl2aEdmWnY3OE8vSzdOeVhPbE1NaEdWNjB5NXY3dHZEY1l2V1pNK1hkeis0VGVVSm5vZVNDTTNxS3E0L1RHQy9lc0RsM0E5akVmamsxL0FSUktKMmhzbUxCNndmK2hobDNhbm5pYVBIdkhZNitnaGtwdGxnOXNNM0Y3YVZsNjArQ2pPNHdPaWV4ZWhiOFRDOUpPbFlVeHo2R0VDVStoUzZNOENnZXhibmUyK2MvTEJYVTVHQkljUXhjSXZwZStuWGowNVEzSFJteHVtelZZWlpkRlRhMGtSdmtnTkVmVk5Na2V3Z1JQVmwiLCJtYWMiOiI0YjhiMzliNTVmNDM5MThmOTE4NDgzOTQ5YTdlNTMzODFkNTdmZWE2YTlhNzY0NzdjNWQzY2Q5Nzg5MTc5YWI2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:20:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1876847524\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1259546254 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DCK491V1GUyngHueDf4bnOw7DOzApt305JPGLVcA</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1259546254\", {\"maxDepth\":0})</script>\n"}}