{"__meta": {"id": "Xa0ef5670851476e6b4aa3125b4a17159", "datetime": "2025-06-08 00:08:54", "utime": **********.987561, "method": "POST", "uri": "/inventory-management/update-quantity", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.370341, "end": **********.987589, "duration": 0.6172478199005127, "duration_str": "617ms", "measures": [{"label": "Booting", "start": **********.370341, "relative_start": 0, "end": **********.835628, "relative_end": **********.835628, "duration": 0.4652869701385498, "duration_str": "465ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.835639, "relative_start": 0.4652979373931885, "end": **********.987592, "relative_end": 3.0994415283203125e-06, "duration": 0.15195298194885254, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51913992, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST inventory-management/update-quantity", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@updateQuantity", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.management.update.quantity", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=111\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:111-200</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.016640000000000002, "accumulated_duration_str": "16.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.876699, "duration": 0.01059, "duration_str": "10.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 63.642}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.899123, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 63.642, "width_percent": 3.546}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9081578, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 67.188, "width_percent": 3.606}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.925962, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 70.793, "width_percent": 4.928}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 126}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.948267, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:126", "source": "app/Http/Controllers/InventoryManagementController.php:126", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=126", "ajax": false, "filename": "InventoryManagementController.php", "line": "126"}, "connection": "ty", "start_percent": 75.721, "width_percent": 0}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '5'", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.948676, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 75.721, "width_percent": 6.13}, {"sql": "select count(*) as aggregate from `warehouses` where `id` = '7'", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.953322, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 81.851, "width_percent": 6.31}, {"sql": "insert into `warehouse_products` (`warehouse_id`, `product_id`, `quantity`, `created_by`, `updated_at`, `created_at`) values ('7', '5', '15', 15, '2025-06-08 00:08:54', '2025-06-08 00:08:54')", "type": "query", "params": [], "bindings": ["7", "5", "15", "15", "2025-06-08 00:08:54", "2025-06-08 00:08:54"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 164}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.958612, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:164", "source": "app/Http/Controllers/InventoryManagementController.php:164", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=164", "ajax": false, "filename": "InventoryManagementController.php", "line": "164"}, "connection": "ty", "start_percent": 88.161, "width_percent": 4.507}, {"sql": "insert into `stock_reports` (`product_id`, `quantity`, `type`, `type_id`, `description`, `created_by`, `updated_at`, `created_at`) values ('5', '15', 'manual_inventory_add', 3, 'إضافة يدوية من إدارة المخزون - المستودع: 7', 15, '2025-06-08 00:08:54', '2025-06-08 00:08:54')", "type": "query", "params": [], "bindings": ["5", "15", "manual_inventory_add", "3", "إضافة يدوية من إدارة المخزون - المستودع: 7", "15", "2025-06-08 00:08:54", "2025-06-08 00:08:54"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4038}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 172}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.969414, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4038", "source": "app/Models/Utility.php:4038", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4038", "ajax": false, "filename": "Utility.php", "line": "4038"}, "connection": "ty", "start_percent": 92.668, "width_percent": 7.332}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 184}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.981032, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:184", "source": "app/Http/Controllers/InventoryManagementController.php:184", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=184", "ajax": false, "filename": "InventoryManagementController.php", "line": "184"}, "connection": "ty", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage inventory, result => null, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-706163281 data-indent-pad=\"  \"><span class=sf-dump-note>manage inventory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage inventory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-706163281\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.930918, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-852266394 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-852266394\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.931733, "xdebug_link": null}]}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/inventory-management/update-quantity", "status_code": "<pre class=sf-dump id=sf-dump-2044050839 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2044050839\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1450755420 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1450755420\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-291012314 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-291012314\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1369418943 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">87</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749341329306%7C29%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InI3d0pqZEhQSkdnYTlWVXdiZjl5MkE9PSIsInZhbHVlIjoicFpzd0NjYlBZNFM3MlRpeFNWVnh6NlBveFdPMXMvayttVWErNkFEZmprbzUyOHY2MEZNVGh1QldKR2FidE9PaTgxeDMrUi9oUmJxNmU1SE1vNjB4RStRQVNJaDlvb0ZNT3hRN24ydjVuK1dVN0F2V1ZMdGhIaDVYbzlmbWF6TzdnWHZwTnBRbzUwUjhHYWFQRFRkZm5wQ0dWei9BUTFWekI1MDJidE03RzBhVnYvOER0YU9sc2pDdHV3aUV6T01HWlFVdlRUdWxaSkdHbFNaUzlzcVIzdVBBaFRzVmY5d0Q4UzJZekFhbC9KTlVLUyswK3dScDlhM1c2OTd4bFNVZURFT09FVXBYWlpSYW1vcHJkV3dEK3g5dFZKbmwyTEVrUzFQSktEZ1RHT014WEUzQzZlWVh5TzEvRVRtcTc3YTJHa096KzB0dkJMSFY3Uk5pTS80QmROOG0vTU5DVFBlY09YTnFBQUpTdHpmWmw1dFBxQTBycExTak9qMlJ6QmVpZ1E1SWw2QmFacnZvbDBXMkVueHRNeHYyc0E1bmRkeVpJYTN3dW1PV2RKdnc5L2wzNWx4Z2MvK0hhaUdRYlRKbXpwenRDNHlvTmZWYUpZeWwwV00vUXB3Q0F4b0RxekVmYUcwMG1kdU9Qd3doelEzS3FEVmNrRHZMNzlPemw3OFEiLCJtYWMiOiI4YTI4MjJhZjIxYTdhOWFkNzBjNDRjZTE4ODNjZmYyMmUxNDlkZmM1OGFmMDFkMTg5NTRmZWE4OGM2NDJhZmQ5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjM1bVhCRExaRUpFemxXMDBHTVVqRFE9PSIsInZhbHVlIjoiVUd5ZDZLU0dQRXlrK2JTdEVFZXNYbktwYWJGaTRqQTJVZEx1Qm45dmx2WlNHbWtPdklqWVRxKytpMitqbHBmaE03ZWVDYlduc0s4Smg2dDUrQzFJNjJKZ0x2cFBhekVuK2xFS0YzQ1U3YUtWMGUzMFVJclJHK1RUWXVCOFlWdmZnanhTZVlKMjhEZWZMQUJCUStZYjZ5d0NhRzA2SWRLRDNIZ2tSM25Pc0FPMU5SZ3VRSVAwekhLc0daUXRmUGQzMjE0V1lkOUZJMXVIZVg0Z2JKVzk5NFdTWGlpMHUyNUFkVThDbTZkTUNSREpkM2M0UWtpQ3FQMmlLMkdXSkpvQ2lrVjUzeDJudjladVE4UHJxSGFSOGFsNjdwbHpndU55OU5JS2NSa0c0aytOdnNDcjVNSTVxN0g3Tkp3d004Y0RoaThZdWorVlprWXV6MFUzRWZuQVlxamlYU1EzeGFONEl4cGxWNllUR3owQk91cldabmgzVUVuQ2ZENEFYTVIwY05sWWxKaFpjQWV5Um5yd2ZWTjFXb3J6Q1E0QVlocWNtY3N0Ym1WMFJMbmhIaUFWRWhxbWM2LzF4V2lGczAzeHFySWtQdzdPelQ3NUtScFlkdXNYNXFpTDM3eVBPRkRtMHc4VGxkUkdpYVhwNzIrVTc4MGQ3ek9DNjUxUE1wVlEiLCJtYWMiOiIwNGVmZTI1MWU4N2JiMjY1OTViNTlkMzk4ZWY0NDRjYWIzNThjNzVhNjc2ZTcwNjUxOTFmYWM1MjYyOTM0MmM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1369418943\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2030003639 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2030003639\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-486078345 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:08:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImN3Ylh1NERWdFloWlVNU0c3Rkt4TVE9PSIsInZhbHVlIjoiSXlOVGI0aytkUXBnNFd6N01iZUh3UmxNeW5CeVJxRkp2eVQrTkREZDJTc2xQS1N4VTU5SWRkcE02TWZYbDlyWXZCWWJrYU9wYjRLZUxXK3Uza2RLZFJucFhHV0UycXlUTWZFZDc0ejdib0NnRnRXV3pWbjRuRUtJbXRITGFPK1ErL2FTNXF1WktwYmRIRms0dWZaYmtxblRqUmZVbjZiaEQ5c2ZjNFVMTi9JT2RPekNOWW82emJxODUwVE1RRTlnQ05vdEZNcU80VjlpNHd0UlB0Wk5zd2ZadnJ5MmZjd1kyMUVnazZGaE9Zd0tsUUtBdFVPZXRCRkNGNkF4dGxWQmJVa01taXBva2I5Ui9nYlI1MkhQdWtOdi9hMCtTUTZaT1BZS1NiRUVPWUNUaGd6aDIwa292T3lqZTRZeDFXNk5IOFk4Wjh6VmdGbFhqb3VKMVpqOWtlWXEvbHpGTzVndERDdndvRk5Ia3kva0RYbThEQjVWQXl0R0R2M01zeVlFUDhBRnZNUnJJVmlQZkd4cGdRRUxjRkd0VVhNa2gzZmNJYnhMQkMrbXlFYm1JL05UNmppeXJ1U3RQV2k3SmlVOVJLU0dhaU4xUStsbHV4OEI0QktjVVFhUTA4S0xjZGVCSzFUVzhwSVlSYjdQUTIxd3FHNlhibUp1ODI2V0hGLzgiLCJtYWMiOiIwODU2M2VhOWNlNmRmOTA2NGY3YjEwZjY1YzM1MzJhNzU1MDk1NTU5NjYwNzRiZDQyOTkzYzg2MDFhZDA1NzUzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:08:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikp2MURBM283S0R1N0hBdWp4Q2dVSWc9PSIsInZhbHVlIjoiUlVOTG5kUkNLZGNrenk4OFBKaEFlNkptTXpaY2p2K3ZKY0JQVEcrQ1gwODl6eEsyTVREWEhzb1hrR3ZPUWhWeHhOTXRQVWNjRXhRc202aWFDTDA0SEhwbDkrbXhTdEZiZVFFSzRIQUJoWlhCSzZmNmo0RXhhWVdnQkxiMGY1Y1cvRTRTbWZLTngzTCtPT242OGN2YnZxOXdyMnZDWi9lVlJSR3A3SUtPbFBBR3pBWWx2UkljS1BlWWFJR3J1cTdvVzFobXRudWt5YWhZZG8wOWUrenNSTHJ3czBJM09JVkVJQXdiM3pPeW9VeGhaVEVhRDE3cVJaYTFWaGlXS3dHaUlhT1MrWTRsVWcxV01paDBuREt4ZWlrZUQ3YWFlRmx5aWVVMUdUYVlnMmtJTGppdTEyQ2VJYW1TUFBiN2RmNm1yeDdiUE0wZXBjRHhUYUgyYkZFK3NjV3lxTlF3RXlwU0xzRi9iMjFSTFV3S2JvUkhNcUxHWDFaUVVNYkFBdk4wdk9ubXhVRGRsVjdvMWFNTE1SUVY0MS9la09zTnlEcy9adEpqTlRiVDFVamUxdjFhR1Z2c2RNb3lmeG1tNGRtbTdFaXM1K1pBYktXZ3hlQlBxMGhGOEFhTkt0U3FXZTNxN1FLUzZGRHVPeDZGTXNad1BEZ3ZIOXZvMGtDblNsUGsiLCJtYWMiOiI1YjgzZDNmYjAxYzhjMzZjZDhlMGQwYzVlMGU4ZDU5MzE1NTQ5MmNmZmJkMmJhN2MwMjI5ZWQwN2Y3M2I4MTQxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:08:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImN3Ylh1NERWdFloWlVNU0c3Rkt4TVE9PSIsInZhbHVlIjoiSXlOVGI0aytkUXBnNFd6N01iZUh3UmxNeW5CeVJxRkp2eVQrTkREZDJTc2xQS1N4VTU5SWRkcE02TWZYbDlyWXZCWWJrYU9wYjRLZUxXK3Uza2RLZFJucFhHV0UycXlUTWZFZDc0ejdib0NnRnRXV3pWbjRuRUtJbXRITGFPK1ErL2FTNXF1WktwYmRIRms0dWZaYmtxblRqUmZVbjZiaEQ5c2ZjNFVMTi9JT2RPekNOWW82emJxODUwVE1RRTlnQ05vdEZNcU80VjlpNHd0UlB0Wk5zd2ZadnJ5MmZjd1kyMUVnazZGaE9Zd0tsUUtBdFVPZXRCRkNGNkF4dGxWQmJVa01taXBva2I5Ui9nYlI1MkhQdWtOdi9hMCtTUTZaT1BZS1NiRUVPWUNUaGd6aDIwa292T3lqZTRZeDFXNk5IOFk4Wjh6VmdGbFhqb3VKMVpqOWtlWXEvbHpGTzVndERDdndvRk5Ia3kva0RYbThEQjVWQXl0R0R2M01zeVlFUDhBRnZNUnJJVmlQZkd4cGdRRUxjRkd0VVhNa2gzZmNJYnhMQkMrbXlFYm1JL05UNmppeXJ1U3RQV2k3SmlVOVJLU0dhaU4xUStsbHV4OEI0QktjVVFhUTA4S0xjZGVCSzFUVzhwSVlSYjdQUTIxd3FHNlhibUp1ODI2V0hGLzgiLCJtYWMiOiIwODU2M2VhOWNlNmRmOTA2NGY3YjEwZjY1YzM1MzJhNzU1MDk1NTU5NjYwNzRiZDQyOTkzYzg2MDFhZDA1NzUzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:08:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikp2MURBM283S0R1N0hBdWp4Q2dVSWc9PSIsInZhbHVlIjoiUlVOTG5kUkNLZGNrenk4OFBKaEFlNkptTXpaY2p2K3ZKY0JQVEcrQ1gwODl6eEsyTVREWEhzb1hrR3ZPUWhWeHhOTXRQVWNjRXhRc202aWFDTDA0SEhwbDkrbXhTdEZiZVFFSzRIQUJoWlhCSzZmNmo0RXhhWVdnQkxiMGY1Y1cvRTRTbWZLTngzTCtPT242OGN2YnZxOXdyMnZDWi9lVlJSR3A3SUtPbFBBR3pBWWx2UkljS1BlWWFJR3J1cTdvVzFobXRudWt5YWhZZG8wOWUrenNSTHJ3czBJM09JVkVJQXdiM3pPeW9VeGhaVEVhRDE3cVJaYTFWaGlXS3dHaUlhT1MrWTRsVWcxV01paDBuREt4ZWlrZUQ3YWFlRmx5aWVVMUdUYVlnMmtJTGppdTEyQ2VJYW1TUFBiN2RmNm1yeDdiUE0wZXBjRHhUYUgyYkZFK3NjV3lxTlF3RXlwU0xzRi9iMjFSTFV3S2JvUkhNcUxHWDFaUVVNYkFBdk4wdk9ubXhVRGRsVjdvMWFNTE1SUVY0MS9la09zTnlEcy9adEpqTlRiVDFVamUxdjFhR1Z2c2RNb3lmeG1tNGRtbTdFaXM1K1pBYktXZ3hlQlBxMGhGOEFhTkt0U3FXZTNxN1FLUzZGRHVPeDZGTXNad1BEZ3ZIOXZvMGtDblNsUGsiLCJtYWMiOiI1YjgzZDNmYjAxYzhjMzZjZDhlMGQwYzVlMGU4ZDU5MzE1NTQ5MmNmZmJkMmJhN2MwMjI5ZWQwN2Y3M2I4MTQxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:08:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-486078345\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1424940704 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1424940704\", {\"maxDepth\":0})</script>\n"}}