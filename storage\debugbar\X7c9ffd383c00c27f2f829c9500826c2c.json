{"__meta": {"id": "X7c9ffd383c00c27f2f829c9500826c2c", "datetime": "2025-06-08 00:09:28", "utime": 1749341368.01384, "method": "POST", "uri": "/inventory-management/update-min-quantity", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.192393, "end": 1749341368.01388, "duration": 0.8214869499206543, "duration_str": "821ms", "measures": [{"label": "Booting", "start": **********.192393, "relative_start": 0, "end": **********.83053, "relative_end": **********.83053, "duration": 0.6381368637084961, "duration_str": "638ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.830541, "relative_start": 0.6381478309631348, "end": 1749341368.013885, "relative_end": 5.0067901611328125e-06, "duration": 0.18334412574768066, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46718696, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST inventory-management/update-min-quantity", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@updateMinQuantity", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.management.update.min.quantity", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=205\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:205-242</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.029529999999999997, "accumulated_duration_str": "29.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8956459, "duration": 0.02163, "duration_str": "21.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 73.248}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9295301, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 73.248, "width_percent": 2.133}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '5'", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.941333, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 75.381, "width_percent": 1.727}, {"sql": "select count(*) as aggregate from `warehouses` where `id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.944834, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 77.108, "width_percent": 2.303}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 214}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.959798, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:214", "source": "app/Http/Controllers/InventoryManagementController.php:214", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=214", "ajax": false, "filename": "InventoryManagementController.php", "line": "214"}, "connection": "ty", "start_percent": 79.411, "width_percent": 0}, {"sql": "select * from `warehouse_product_limits` where (`product_id` = '5' and `warehouse_id` = '8') limit 1", "type": "query", "params": [], "bindings": ["5", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 217}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.960811, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:217", "source": "app/Http/Controllers/InventoryManagementController.php:217", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=217", "ajax": false, "filename": "InventoryManagementController.php", "line": "217"}, "connection": "ty", "start_percent": 79.411, "width_percent": 4.741}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 217}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.974724, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:217", "source": "app/Http/Controllers/InventoryManagementController.php:217", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=217", "ajax": false, "filename": "InventoryManagementController.php", "line": "217"}, "connection": "ty", "start_percent": 84.152, "width_percent": 0}, {"sql": "insert into `warehouse_product_limits` (`product_id`, `warehouse_id`, `min_quantity`, `created_by`, `updated_at`, `created_at`) values ('5', '8', '3', 15, '2025-06-08 00:09:27', '2025-06-08 00:09:27')", "type": "query", "params": [], "bindings": ["5", "8", "3", "15", "2025-06-08 00:09:27", "2025-06-08 00:09:27"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 217}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9754472, "duration": 0.00468, "duration_str": "4.68ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:217", "source": "app/Http/Controllers/InventoryManagementController.php:217", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=217", "ajax": false, "filename": "InventoryManagementController.php", "line": "217"}, "connection": "ty", "start_percent": 84.152, "width_percent": 15.848}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 217}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.988083, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:217", "source": "app/Http/Controllers/InventoryManagementController.php:217", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=217", "ajax": false, "filename": "InventoryManagementController.php", "line": "217"}, "connection": "ty", "start_percent": 100, "width_percent": 0}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 228}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.995781, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:228", "source": "app/Http/Controllers/InventoryManagementController.php:228", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=228", "ajax": false, "filename": "InventoryManagementController.php", "line": "228"}, "connection": "ty", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/inventory-management/update-min-quantity", "status_code": "<pre class=sf-dump id=sf-dump-1880540189 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1880540189\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1434965587 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1434965587\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1040780557 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>min_quantity</span>\" => \"<span class=sf-dump-str>3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1040780557\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1816842758 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">90</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749341329306%7C29%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZ2YWdqcFpoTk9MYUI1ZHJSZWljV3c9PSIsInZhbHVlIjoiYVpNSmxnR01PaHR2THZuR3JDQUJsbitaVDlscW03SE5XWHhaOHBWWFRjblM2a1FKdmN4VDYyZGt3TTIvNEx3OHVaYzhFdzhCNldwOENKUDU3cFhjSXpVRDNza1dXWjZ3SEMybFVlMjEzek8vUWNIT2hmK3NudjZNdVozWTRwSUxuY0lUc20wazVQYkxXL0FuVFo2dnNxYmh2dmlXZ1kzUUNZY2RjQ0JFZmpSRnFzVlZ3TGFqRm1pT1U1cm5UaTF2U3FhWnJqU3VoajBMOUt5RStESXYxLzkwRzBnR0JrK2p6NUVlTGlhclA2S2YxamZiaFV5SWVwOWtSd2JwYjgwNFF6UUdZZU1aS2ZOMStuMktXSTNQU1E4UU80WHJ1U2F1K1BnT1NrNzlDTGFBZzhmMUZTUTRqNUovZXRNZnJqZUI1RGZnY1dTRG1rYTBicE8xUUU3OFViNVgyQXFIc1F2d3htS01ZSmFhRlo3WXR6TEg5TEg3SURVYlllSHVTcTAwU1V1OEZWMVRjMTZWRTZaNVhKcUw1c0wxeGFyNEg1RHNSNExvR3h6TG5zVGZFV1N2M2RBSzROVFJOeHVJVC9jQitXRnRMNy9kNTcyZ2t1blk0V2lGTVQ4NUJERG1HanZ1aE5YRWh5c1Z2S0tnWjZpSVl1ZTJYR2VBaGtocmN2dVkiLCJtYWMiOiI3OWIwZjlkNDNkNzE4Yjg0MjcyNGU0ZmZiZTUyNjgzNmFiNWUzMzczNjAyOGRhNmMzM2Y3Y2Q3NWI2YjVkYjViIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjhBeFMzMzIwSjV5N1Z3dmlrSVVCQUE9PSIsInZhbHVlIjoiN2paSEEwbHlzTWJpdlljUjVCMFdHbm1TdEM1MGpFOHBCaUNEQ3ZyWjcxT0xaSkVRVFhFMDlJZGt6anhNQm5FSGhOQytzYklrZzhUaU1NVzJHaUtNdFBEN2l6cXF0ME5xTlVKYTlRZmVlM0swU3NobEVwNXF1THliUzBhL2N5Nko2ek0wMkErRldaR0dvOUErOVhiWGgzYThKUzJOVWY5MGVxNUVjbGlvcUhCUzBuVDN3ZHFMVVhYU2dTUFlGYU9aK3RwYUd5czYxUmhmTE96eGxzSnRnMDlxU1lLd09SUzN1UmlrQzQzTUlSSW1LQnRoWXlvVndicm9uLzQ0cXEzRmR5ZytMK3F1dFE3WDM3alBTY1FCalNmYmZLOWpVbWgwdFpndnN5by92eVFtZVJqb3M3ZE1UTmN4QmZRVld6WXhXZGVHN2FPQTBhbGh4bzc2RVJTOXlkTUc0ME5lYkZOc1kyaHRkZC84UTgrbVVjb1BzRnFoWEF5OERiMmVLd2tpVDFPSnpuaUM2eXpmejNMNDM2S1JkR0hJYllxdDl4T2d5R3l1MktQME51VFMvZlRyb2RhanRVQmd1MGUvcnVRQ2xDK2tHdmVWYk1pejhvLzFiWElUREtnanpMUWRkZzRDRWhOd21kZ0Z1ZE1RTjhNczEzSjZ2N2xsazBpR3U0bjkiLCJtYWMiOiIzNzQzZmMwOWU2OTQyZGRmYTZhNTMyODJlYTE3ZTU0YmUxMWJiZWMxMWRkZjk0N2Y3YjViN2FmNWEzMTFjNWU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1816842758\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1049568547 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049568547\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1724609886 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:09:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1QU1QyOVNCTi9jcDlPbFdsQlNkK0E9PSIsInZhbHVlIjoiSWVtS1QvdEQzUkxTb3BDbjZObWhLQWJ0ak90SUtNQ3dpNG83aHZIcm1lSW1uS291YjBHZGsyTDlaOVloc1lqVVUzb08xTXZjQUN4QytUQVdpWWttMklJVVJVZWdiRjhaQzVuRWNodnlBRC93eEMxbXNLWVVUcGFiZVpwT3dUbGJjNkozeGdvZDJJeUpRZ3BiUGhNNlgyMzVsUFVpbmJ5YmFpU080dHc4RkZNNnFvSk9zL2I4bTErZWVkdi9FSFhpZ0VWY0RTaVNvbWdlVzROOXc2emFjUi9WZllSUGJYRmlmNnY4QmttQUhxR2VVdWpybFQ5Qm0ydTB6bHRwU2tmZ3pQNHZFREJjR051SEJjcDR5dU43WE5wZVpsWU5oSmFrV2Jqb0dzVGF5Y2NCeDc5YVdiejN6Y3hPSW1ZcWQ2QWE0S05MQkhyV08zYWFLSnpQalI2Q2V1OURSWjhiTG1pMDFkRXh6VnZlbkwzNkNBRmhQQ3NNT2Z0M09OVHVpdEFVckRodnZkQm5uYmVHYVhaQkJJNmtTSVBsQjhWemRpVFBxN2dtajlieTJGMXkzQml4QkRTbitLZUxTSmVSUWo0TTRPTDNDLzlSajd4Vkd6eUhOMXR5T0E3eW9LSlBjRlN3THp2ZElER3RpOFdUNUVmdmFNdmZUaG95d2NvWjNxTkMiLCJtYWMiOiI5OWU2YjQ2MjkyZjFjOTRhYTU5NWY0M2Q1NzU4NGZiNjljMzU0ODM0MjlhMzliNzdkNjhiYjQxZmYyZWM4ZDQxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:09:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im91ZFZwbEJKdHdMRkJnanN2a0NhNVE9PSIsInZhbHVlIjoiTXFCaUd5aWVJNitzVG1QbHl0b1BKcXd5Nis1UitOV2xXd0dkd2lQVFZKYk1xZkdSbTd1elNwWHBiZ3owb0tLNkZHelNRNStYQXlsRUpXOE9lcHVUa0FNVEo2UFFZenFpaUxycTc4V2lBNW53RUIrUmRQZnJFcEQwdURuWTduS0g1WTJYQUUvVHhtSXFlT05PeURVYlpXWU9wS0NjcDR3cGVxYjFwTEVIQTVmZVR3NTJYN3ppeXJKZS9SS0tzdlJPM0RsOHZkMXBlNnE3Zm1VYlQyTlhvVEFNZWZvaDBVWHhHV21QL3o5QUxYK3lTZEkzdi9aSHB5eFZJYkJrdm1FeG9DUXBFTitvaWxDdWxzZ1NMZVpZa1dkR056V0kvYUoxTzNoV1hEZHlRZ3NVL2IrRXdDZzlPb2hEMXFWR1hXZ0svU0dQQ3A2OWtRUVg1d3k3bDlFYmF4VmZmcmszczVnZVNKQWduNmFxUkNnSnpoYzdXOUd3Z1BkYzd0R1ZTTXl3N2F5MnVIUUJSbjV1MnFvejJNbU9DTW5BVGJuMHhOZGFIYldsSVVrUXJOektnMWs3VmpUMGFPbkxSUVBRYk1rcWI5UElOWXZuNHFXSjhiU3gyRjczTW9XanVQRjIwMFU2ekpTSFJscGVqR05FN0tYajU3TDV2RWFoUytVWFhvb00iLCJtYWMiOiI2NzRiZWFmZGNkMTJmNTkzYzUwNmYxZDFhMjFiZWM4OTIwZWUxZTJlYzE4Yzg4N2YzMTU3MzJiNWY4NWRkMmVkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:09:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1QU1QyOVNCTi9jcDlPbFdsQlNkK0E9PSIsInZhbHVlIjoiSWVtS1QvdEQzUkxTb3BDbjZObWhLQWJ0ak90SUtNQ3dpNG83aHZIcm1lSW1uS291YjBHZGsyTDlaOVloc1lqVVUzb08xTXZjQUN4QytUQVdpWWttMklJVVJVZWdiRjhaQzVuRWNodnlBRC93eEMxbXNLWVVUcGFiZVpwT3dUbGJjNkozeGdvZDJJeUpRZ3BiUGhNNlgyMzVsUFVpbmJ5YmFpU080dHc4RkZNNnFvSk9zL2I4bTErZWVkdi9FSFhpZ0VWY0RTaVNvbWdlVzROOXc2emFjUi9WZllSUGJYRmlmNnY4QmttQUhxR2VVdWpybFQ5Qm0ydTB6bHRwU2tmZ3pQNHZFREJjR051SEJjcDR5dU43WE5wZVpsWU5oSmFrV2Jqb0dzVGF5Y2NCeDc5YVdiejN6Y3hPSW1ZcWQ2QWE0S05MQkhyV08zYWFLSnpQalI2Q2V1OURSWjhiTG1pMDFkRXh6VnZlbkwzNkNBRmhQQ3NNT2Z0M09OVHVpdEFVckRodnZkQm5uYmVHYVhaQkJJNmtTSVBsQjhWemRpVFBxN2dtajlieTJGMXkzQml4QkRTbitLZUxTSmVSUWo0TTRPTDNDLzlSajd4Vkd6eUhOMXR5T0E3eW9LSlBjRlN3THp2ZElER3RpOFdUNUVmdmFNdmZUaG95d2NvWjNxTkMiLCJtYWMiOiI5OWU2YjQ2MjkyZjFjOTRhYTU5NWY0M2Q1NzU4NGZiNjljMzU0ODM0MjlhMzliNzdkNjhiYjQxZmYyZWM4ZDQxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:09:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im91ZFZwbEJKdHdMRkJnanN2a0NhNVE9PSIsInZhbHVlIjoiTXFCaUd5aWVJNitzVG1QbHl0b1BKcXd5Nis1UitOV2xXd0dkd2lQVFZKYk1xZkdSbTd1elNwWHBiZ3owb0tLNkZHelNRNStYQXlsRUpXOE9lcHVUa0FNVEo2UFFZenFpaUxycTc4V2lBNW53RUIrUmRQZnJFcEQwdURuWTduS0g1WTJYQUUvVHhtSXFlT05PeURVYlpXWU9wS0NjcDR3cGVxYjFwTEVIQTVmZVR3NTJYN3ppeXJKZS9SS0tzdlJPM0RsOHZkMXBlNnE3Zm1VYlQyTlhvVEFNZWZvaDBVWHhHV21QL3o5QUxYK3lTZEkzdi9aSHB5eFZJYkJrdm1FeG9DUXBFTitvaWxDdWxzZ1NMZVpZa1dkR056V0kvYUoxTzNoV1hEZHlRZ3NVL2IrRXdDZzlPb2hEMXFWR1hXZ0svU0dQQ3A2OWtRUVg1d3k3bDlFYmF4VmZmcmszczVnZVNKQWduNmFxUkNnSnpoYzdXOUd3Z1BkYzd0R1ZTTXl3N2F5MnVIUUJSbjV1MnFvejJNbU9DTW5BVGJuMHhOZGFIYldsSVVrUXJOektnMWs3VmpUMGFPbkxSUVBRYk1rcWI5UElOWXZuNHFXSjhiU3gyRjczTW9XanVQRjIwMFU2ekpTSFJscGVqR05FN0tYajU3TDV2RWFoUytVWFhvb00iLCJtYWMiOiI2NzRiZWFmZGNkMTJmNTkzYzUwNmYxZDFhMjFiZWM4OTIwZWUxZTJlYzE4Yzg4N2YzMTU3MzJiNWY4NWRkMmVkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:09:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1724609886\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-10764471 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-10764471\", {\"maxDepth\":0})</script>\n"}}