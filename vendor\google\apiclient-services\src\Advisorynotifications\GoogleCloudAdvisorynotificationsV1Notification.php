<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Advisorynotifications;

class GoogleCloudAdvisorynotificationsV1Notification extends \Google\Collection
{
  protected $collection_key = 'messages';
  /**
   * @var string
   */
  public $createTime;
  protected $messagesType = GoogleCloudAdvisorynotificationsV1Message::class;
  protected $messagesDataType = 'array';
  /**
   * @var string
   */
  public $name;
  /**
   * @var string
   */
  public $notificationType;
  protected $subjectType = GoogleCloudAdvisorynotificationsV1Subject::class;
  protected $subjectDataType = '';

  /**
   * @param string
   */
  public function setCreateTime($createTime)
  {
    $this->createTime = $createTime;
  }
  /**
   * @return string
   */
  public function getCreateTime()
  {
    return $this->createTime;
  }
  /**
   * @param GoogleCloudAdvisorynotificationsV1Message[]
   */
  public function setMessages($messages)
  {
    $this->messages = $messages;
  }
  /**
   * @return GoogleCloudAdvisorynotificationsV1Message[]
   */
  public function getMessages()
  {
    return $this->messages;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param string
   */
  public function setNotificationType($notificationType)
  {
    $this->notificationType = $notificationType;
  }
  /**
   * @return string
   */
  public function getNotificationType()
  {
    return $this->notificationType;
  }
  /**
   * @param GoogleCloudAdvisorynotificationsV1Subject
   */
  public function setSubject(GoogleCloudAdvisorynotificationsV1Subject $subject)
  {
    $this->subject = $subject;
  }
  /**
   * @return GoogleCloudAdvisorynotificationsV1Subject
   */
  public function getSubject()
  {
    return $this->subject;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudAdvisorynotificationsV1Notification::class, 'Google_Service_Advisorynotifications_GoogleCloudAdvisorynotificationsV1Notification');
