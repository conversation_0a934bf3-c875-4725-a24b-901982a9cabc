{"__meta": {"id": "X1d4e1ccf1a1d31f8eecfc6533ff6f83b", "datetime": "2025-06-30 15:34:30", "utime": **********.790011, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.32334, "end": **********.790032, "duration": 0.4666919708251953, "duration_str": "467ms", "measures": [{"label": "Booting", "start": **********.32334, "relative_start": 0, "end": **********.707402, "relative_end": **********.707402, "duration": 0.3840620517730713, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.707411, "relative_start": 0.38407111167907715, "end": **********.790034, "relative_end": 2.1457672119140625e-06, "duration": 0.08262300491333008, "duration_str": "82.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45554352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.025419999999999998, "accumulated_duration_str": "25.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.738091, "duration": 0.02413, "duration_str": "24.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.925}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.772053, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.925, "width_percent": 2.36}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.778915, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.286, "width_percent": 2.714}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1553710831 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im5XaVFGTkZqRU5yM0RsclFrY2xjaWc9PSIsInZhbHVlIjoiS3Yyc3MwZWlqdGZxL2puMlIyaWtucHA1dVF4UEFuMmFzRHB2eGVFZUVINjBkUnlCeXJxZE1lcmNGUk4zUGlrVDRXRHlqWTFPZzdrN1NxNXNXUWFBbHp5OEhXNzlYWU9CUjZ2M3lVV29jSTU5MVlOakRVNEl4REVvZWFqZXd4ZXI2TlI1UnZEWm1VbVNMRFZON0RIc1hrWDZIUE1HUnBuVFZkN20vZGlQK3hrYUdUVTVTUDVMYmJNeG9rZG9iWGEwWk1aZzMra2lVWnJSbWRtMzUzWXFrR25LRlhodGFuZmRZNlFNa1lmSlRPTk1Bd2FYR2NQMHRGQXQ1MSs3c3pPUjhBRUtxa1N6ZXVkS0kwOXVRUTdUcEhTb2FWWUNZaTE1ZXI3YmRrcG5wM1Q2VS9ySWxjbEM4ZUZITGQ2N0IyV0RYbjdnbHc5b2haVWlXR0EzeVk0bGdpTmZQcURGZTVPd1QrZEZ5TWJKRW9USCtkYkFYUFhOeEVPTi9IVzMwT3pkWXVWYUZLdXhSOThwcVZMQVZrRFJFWXZYWDhnb0E1ZEpTbDdYQTFjdHVYVmxmclg3VFZML2pmSVo0QVZZbEg2bldqejFTMFZIVEkyNU1PUGV3K0NwV0xabDFaTW5wV3BwYmNxcVZYUkRsdUxDL05zcGZCSGhKZFBzR3Y3VSt5eGMiLCJtYWMiOiJjNDExY2RhNzg0NThkZGJiNjBiYmZmZmUzMTI5ZmNhZGJjOGRlNDBhMzBmYmIwNzNlMGJhYzc2ZWZhYTlmNzYzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjY0MWZrMWdySVBlTUtyc3lsUllrZUE9PSIsInZhbHVlIjoiQ05oV1BpWDlHWGxXSmQzdHNQQTJ5dkxUNTBDVEpyQ2crT2ZyemdIT0RVbWFmM21aVW5zTVdoK0RSM0RKaUVUZE96NXpGVU5mZEpvb2pGcXNEOVY1aXltL0Yzbzc4RDRzeW5XQXZ3N2QvSndrcUN0Z3BMamJGc3ZqUVFmeWV2OHZwbGkxWEJtWUJpR0VJb1N5a1dIdHU1eW9aK1AwSVF5QUJscUI0N05SejlQdE4vTjlPNWpKWW95OGtsTjAzbFdndytCblBNd3U0Wi9KbXI5eEJCT3U5YStOM25iaWNUUCtQUUVYMUVmekg3MHZPc1lFNEFRN21DWXR5SXBnZVBUUTJSL0l4aUx1M0ViTXNnMXVwajBIZWp5RWFBNlROdW5rUjVwUDNRMHd5aFdmRGdUak9OZkNrNnNmaUpYVFlCeTZjcGRzeVNTYnpZZS9OajRIcnhCSk1ta1FKcXRaNHA5T3J6UlZncTNDVUZBTWFtM0c2Y0xrbFZtVTROcktIVVN5ZEZYa0lzemhvQXdUYWNrdVgyckxMaEhYVHdpeW1OOGJsS1RYQmxPeGZIQUtVR1VobGVnZDhGczV6U0VMcUE2ajk3aVBYTDVqWU8wZDhOWlVBVGF1VnV0dVVudi9lY3pBYjJZcUprVVZMcWJlc3RyN0Nvc2llM0VRbTRSSjR1eHgiLCJtYWMiOiIxYjBkMzVlOTViN2UzMzE0NjM4MGFmNWJlMzNmM2JhYzczM2M2MDVhODQ2MWI0NjU3NjRiZmNmODIyNTczNjk0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1553710831\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-897917132 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-897917132\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkQ4clV1cVRHRkpoTzVmeXFxMTRGRnc9PSIsInZhbHVlIjoiWmRZWnR6ZC90aEJJUWVST25rMjVkTVdibG84djNHL0Z2VG1hMlFhdEw5SFpHcERmM2N0QmYrVC9Sb1ZrZi8xL1ZjT0xmZEF6Vnl0ckRzaUR2RGpNQ28yRWkrMjQzNFdBbGMxVVVhVW9RVVZYUkRnbGFCaHViUnMxMUNJZ2dJSG82WXU1eVNwM21tbkVNVmdMZXRnV2FnWE5NbVUxZlYvRXBQeVBvS1d5dVhHbkMzQ1NTYmYvSGpmQXdpdDViTW5HRTRlZXVZZmY3azRtR1ZEOG11OTFjeGNZT05ieU85QkM0a2Z2Q0g3cGVVSW9mckF2YjZIaTNUQ0wrNkNrcllKUVQyWksxZldQeWwyOTJiQlhHRWRhalBhSkJML2paYyt4aTI1NnZxa1d2d1hMSzJEb1NlZjl2VTNzMmQ4UFFhdFkzNjRBVEVkdG93ZjVlcjhUalNQeWtpaFMxSGh6MGVOSko1RE5uNlZ5SWtvV2dVQ0dwMVpVTlhiZHhtaWFaa3RUVElSbFR5NkwrNWp0Ny9tbUtyK0FBK0NnM002d1NmazdNa1A5TCtqT2ZrWDlzYzR4NWRkSTluZzFuazFnK3dmdlNtYnhIYXUxWEMvdmtxQnRTdkhmMFBWUHhIRFoxWVhaeDZpdmFyMXpDZHMzdllkdnJaTHU3NVk0Y0ZseGZoNUIiLCJtYWMiOiI5ZTE5ZWUzZWU3MzJjNmExMWYzZDAwNDVkMDY3NWFlMzU1NzI5ZmE4N2I5OGM4OGM3Yzc4NTg1YjYzM2NiZWZkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImdxTFhVYXJCVGV0VVBCd2NudjZkc2c9PSIsInZhbHVlIjoiNmVNMHlKOVNtUnlpRElYcDBrbHQ2ZkJCYkRNSEZEZ2NwcHdNa2hVais5d1J3ZUxMbHVvNS92M080eitVNHRqa3RsSVRzd1dwUFErVkdXaktFNnhZb0ppNVFYT0lhUTE2cXVyZ3VOcnBFYjE1L1BvUmpMckpIeFJ4M21RNHFmT1o5bkY2TFMyM2NMbUVjSjg3OExwMzNiRTQzcnNma211WExKSkU1S0EzYVdTREFKRVhYbzhzb2VkWStBOWZNUzdEOXVzQ013L1JXNkFrbUJ0VklncFZVL0lTNWhxYXNmaFE0bjhFa0pXb1ppSk9XRUpYTjdXR0ZRa2ErRjIrbXUxTUtuVDRXbXJwZnc5TzlpRS9BMzBFYS9JdFBZOS9oZWdLdVUyakhWSlVjZ3R4SWErWjhINldMNzh2Um9FV1lVOWhOZ3FrYVo1Q3NTTEExMDBtMkdPRlBvUWZCMEkyWVM2cGZkNUhsOW5zRWpWUHdPWlpscVV2anhTQ3lzVWZtME4vbW5xWnFsZkhlRVhCMElCNHluMTl5RFZEMnNncGdiM1hwaHpPNWpXTHhwcmtIZjVSakFROXZJaXlibXV2QUlyLzRXcGNVRUVmd3RXVDJ2aHZLVUVZQWR1cXhnTll2ZUVvM0ZDZDlzS3E5eHIweTlLNG9wRjhHRWQ1dkdIQ3Qwa1MiLCJtYWMiOiJmMTAyZjRhNDhjNWJmMGY1Mzg4OWZkYTc2ODBkMGQ1YTYzYzcwYWNlMzc3YjQwODFjNTFmYWUwMWQzYzQ3YmYxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkQ4clV1cVRHRkpoTzVmeXFxMTRGRnc9PSIsInZhbHVlIjoiWmRZWnR6ZC90aEJJUWVST25rMjVkTVdibG84djNHL0Z2VG1hMlFhdEw5SFpHcERmM2N0QmYrVC9Sb1ZrZi8xL1ZjT0xmZEF6Vnl0ckRzaUR2RGpNQ28yRWkrMjQzNFdBbGMxVVVhVW9RVVZYUkRnbGFCaHViUnMxMUNJZ2dJSG82WXU1eVNwM21tbkVNVmdMZXRnV2FnWE5NbVUxZlYvRXBQeVBvS1d5dVhHbkMzQ1NTYmYvSGpmQXdpdDViTW5HRTRlZXVZZmY3azRtR1ZEOG11OTFjeGNZT05ieU85QkM0a2Z2Q0g3cGVVSW9mckF2YjZIaTNUQ0wrNkNrcllKUVQyWksxZldQeWwyOTJiQlhHRWRhalBhSkJML2paYyt4aTI1NnZxa1d2d1hMSzJEb1NlZjl2VTNzMmQ4UFFhdFkzNjRBVEVkdG93ZjVlcjhUalNQeWtpaFMxSGh6MGVOSko1RE5uNlZ5SWtvV2dVQ0dwMVpVTlhiZHhtaWFaa3RUVElSbFR5NkwrNWp0Ny9tbUtyK0FBK0NnM002d1NmazdNa1A5TCtqT2ZrWDlzYzR4NWRkSTluZzFuazFnK3dmdlNtYnhIYXUxWEMvdmtxQnRTdkhmMFBWUHhIRFoxWVhaeDZpdmFyMXpDZHMzdllkdnJaTHU3NVk0Y0ZseGZoNUIiLCJtYWMiOiI5ZTE5ZWUzZWU3MzJjNmExMWYzZDAwNDVkMDY3NWFlMzU1NzI5ZmE4N2I5OGM4OGM3Yzc4NTg1YjYzM2NiZWZkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImdxTFhVYXJCVGV0VVBCd2NudjZkc2c9PSIsInZhbHVlIjoiNmVNMHlKOVNtUnlpRElYcDBrbHQ2ZkJCYkRNSEZEZ2NwcHdNa2hVais5d1J3ZUxMbHVvNS92M080eitVNHRqa3RsSVRzd1dwUFErVkdXaktFNnhZb0ppNVFYT0lhUTE2cXVyZ3VOcnBFYjE1L1BvUmpMckpIeFJ4M21RNHFmT1o5bkY2TFMyM2NMbUVjSjg3OExwMzNiRTQzcnNma211WExKSkU1S0EzYVdTREFKRVhYbzhzb2VkWStBOWZNUzdEOXVzQ013L1JXNkFrbUJ0VklncFZVL0lTNWhxYXNmaFE0bjhFa0pXb1ppSk9XRUpYTjdXR0ZRa2ErRjIrbXUxTUtuVDRXbXJwZnc5TzlpRS9BMzBFYS9JdFBZOS9oZWdLdVUyakhWSlVjZ3R4SWErWjhINldMNzh2Um9FV1lVOWhOZ3FrYVo1Q3NTTEExMDBtMkdPRlBvUWZCMEkyWVM2cGZkNUhsOW5zRWpWUHdPWlpscVV2anhTQ3lzVWZtME4vbW5xWnFsZkhlRVhCMElCNHluMTl5RFZEMnNncGdiM1hwaHpPNWpXTHhwcmtIZjVSakFROXZJaXlibXV2QUlyLzRXcGNVRUVmd3RXVDJ2aHZLVUVZQWR1cXhnTll2ZUVvM0ZDZDlzS3E5eHIweTlLNG9wRjhHRWQ1dkdIQ3Qwa1MiLCJtYWMiOiJmMTAyZjRhNDhjNWJmMGY1Mzg4OWZkYTc2ODBkMGQ1YTYzYzcwYWNlMzc3YjQwODFjNTFmYWUwMWQzYzQ3YmYxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-11******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11********\", {\"maxDepth\":0})</script>\n"}}