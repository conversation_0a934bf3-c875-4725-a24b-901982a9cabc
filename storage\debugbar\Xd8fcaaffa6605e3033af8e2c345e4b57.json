{"__meta": {"id": "Xd8fcaaffa6605e3033af8e2c345e4b57", "datetime": "2025-06-30 15:34:35", "utime": **********.51984, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.110233, "end": **********.519858, "duration": 0.4096248149871826, "duration_str": "410ms", "measures": [{"label": "Booting", "start": **********.110233, "relative_start": 0, "end": **********.46409, "relative_end": **********.46409, "duration": 0.35385704040527344, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.464099, "relative_start": 0.3538658618927002, "end": **********.519861, "relative_end": 3.0994415283203125e-06, "duration": 0.05576205253601074, "duration_str": "55.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45569064, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0028599999999999997, "accumulated_duration_str": "2.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.489903, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.336}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.49975, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.336, "width_percent": 19.93}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5049932, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.266, "width_percent": 15.734}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1897426976 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1897426976\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-227778776 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-227778776\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1992492897 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992492897\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-580111374 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297674054%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhaNHFUTXk4WGZZR0VGNWlmR0FiQkE9PSIsInZhbHVlIjoiaFR5R1lTdm40elNEVHVEeDY3VFNnTVdUQnppVTRTWVJydENOY1I2M0lIcW4xNFkrTU5rUVVpNFdPdFhPNCtiRE1PVTZJUkU3REtQTzl6Mk8xVFRuRkw2MjdFYitRTlMvWWJ1R3NxRUtOZmdpb1c2REgvVjA0QmVmMTVnVnBTQ2l1VDVqYmF5N0ZLLy9ZYko2UVljN01mamprUUV6L0RVb29tbzhFQXg0bk9vdThtUE1BTlg3RFJBdlZIcXEzS21PRHIva01ZL09Scm9UOU1uTDJnYU5yQXN6b1ZJWTJrVTlMMWJ2dCsvZ0dWbVorRWVoWXJJbTQxRTVtekcxa0d3eWx2cGNvK0xvVjZ4M21tWStBMkIvcTBzQzNPMkpGemsxTDljNEV0aTVGRnVaSWEvY0ZpaW5SM2NYVlEzQW41OWxwMVBaMUZOQnhncm1FS200b2Nzd2JWOFN3QW0yNHg1NUZUOWJoelFCaUhEdVd0VWRCQ0htbU1WTDAxQWFoMnJLeFNLbXB1UnhBdlZIZ0M3REd5VUM5QXlYbndpWEJTVjQ5d3ZOSkJJQ01oZEEzcVEwNjJsYUxEZkdMVzlsUFNwOTNDVnRxaFpGbWxBZ2dxTWxVRzlBbjlJcndvdmtVY3Q1S2hHc2REaDFybHV4Rk5JVHVETzJZcjZMMFBMOHVWSWsiLCJtYWMiOiI5YTI1ZDlhZjY5NWY0ZjllMzM2MzJhMzA2MDc0ZjhiZGM2MzZmOThlMWE0NGQ4YWU1YjUyYTk2ZGEwYTJmYWI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjA4czU1YkVBU0lNYWRDODNlSERqbFE9PSIsInZhbHVlIjoidnNIWDMwUGlTaURuYW4xY1F4NUUzdFlUc2pQVVd2dzArQVlOajI2bFRHRHgyQ3I1ZW1jbW1vZ09GRUFOUDBZdmI5NzJ0aGNVQTVBSlg2US9TYkZUNUVaWU9OWHRJOGd2VkJIL3lnV3FHTlAwbGpTb2o2dzBrV1pkb3JLYUFMWGg1eGFDL0w1cHJaTERia3ZrL2piN2VuUUxFaFhnYm55OGxZR0NGMHFzZTlGeUJ4b3h2MzRBOVAvZUlTMXdrejErZzZFZ2s0RTc1aTB5VXlCVVcxRmYwV2lTVUFiekxhOWVROE9xbi9mMVM5UHlPVGJnK0dJcWJTMGVoWHhEQ09EakZrN2F3eXpwOGtMc21DRkZJWTd2Y1Jvc1RsZzU1bk9sbDlZOVAzWnpKVEYxZHByYUVBY1loTHNJaTZvQ0lMSWt3dndZQXlLdERlcU9Ya2tLaGRSdVJPMG1OeFQzOS9IN2hhSVdSMVNyNEdEV2NGNytqSFdISno5eUpPRXRPVmRQbUR3NzBxTmJvOFhNZ0ZMTmlUNEliME9GcjBucmZ2cUdXSzFiRm9iZ0JRYUUzeTZQZkdrTW56a2lIZkZiTzl5NDFzcit6VERYQnBIbGJ4SEZxWlRjczI3STZsUzdZZDV5RC9vU04yZk9rQUR0Qkp5L0NhNmh4OXlsUUYzcnFUTCsiLCJtYWMiOiIxMzMxNWNmODJjZjk0OGQ5YTZjODNkM2Q5MzIyMWU2MDEzMmU4ZDI3N2EzMmEyZDc1MGFiNTk0NDcyZWY3YTdmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580111374\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-501543187 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-501543187\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJhS3E4V0hNS2Iwa3JtZlc0SkwrK1E9PSIsInZhbHVlIjoiZ2tZZkFENEI3UnZVb2tDTyt1SFNJQWk3OUtiaUxyQ3gyS0NzVTBidlZLbXFsNVNuZzh1Ym5RbW00ZFNrNll2Z3FEQnRSdmMxZW40aGtkN1JyWndqb3BVNTNMQXYrQnRGN0FTTHBUODBXcHhWNFgrUkd0R084TnpWUlQ5UkhxdExGeVltb0hPdS9tTFljY0pkbGM1ZXo0L0lDcG1aek1PNXErY1VobjRGUCtiWjdkRHdFV0NqNFN1eWZlUzNyc25xNVp5cXlqRjJjMWRONEliK3NQSjFqTFpCQlo3V0F3U0JkMHR6anRES3pRb0M0Vk5KUUhCRTQ1YlNEbTNEZkhDeTBYeGJsSi9iNWErSGdPdDNGKzZmQ2VvdkpDVzM4b0N4d2ZGTVJjemIyTTBHYVBuaWZxdnViRFNoajZ0eHRjSXZuN1lWdjhGUU9GdEhicnNnUDdMZVVDckhpRDJKY2trdFZ4ZHlIeUhvU0lCY2lQQkJnTzdCakhucWU5S2cvdU1zYU4xZHV5VThDWDVTQlJwd1BxaHZLQnpKclVHYU1FVUF0M0ZXRUJHS2duck04STBUQmF5eWUxbDZQSFBZeHBNOWVhRXJVUkVkTmVZR3h2TlNkRC9wZ0tRRmExN0YwOGpocTVvQU9lZWRWelR6VEhPUVdTb1BRN3Vpdzg2QndBRkciLCJtYWMiOiIyNzc0YmNmOGFlODE2YmQyMTA2NzhkYmU1ZjRiZGRiMWVjODhiZTAxN2QzNDM1YTkwZjQxYjMzY2Q5YzM3MmQ4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImFjaFNoSFVkd1UvK3hFdW9Ra2RScWc9PSIsInZhbHVlIjoiUDVWOU15d3VxZjkydkR1L0orbHowcWxwWGpHV1ErcDlGRnVIQnlPTkYwSEI4Z1FydDc3MWNuWW11QkpzcWJIOUdMMXhkRjh4ZGpVMGVjeHpkUEZFTkRVVm1jb3ZJaDNuNVAzRUhUdlRXc1RlWkxvV3daWEovdXc2Tk9BYmJvUWRCS0FMYUh1ZFdSci9vMFo5dVZaaVNCZ0wzWTFFeC9RdXhxS2Y4WUVIdmsrSlo2Z3V1VW5uQmNpdm1jcVcwK0RXa01zM3IwVFdjeGNreXBKc3gvNVA4VTZtc3BpTzFlSE5FR2RtT05UZ3kxcEV4NDQ1cHlYdC9aOTdaZjVoODFEeEhGZU4wQzN6Sy93RG1lVzdpV01DcVNvcWxlVG9ZODgxME9JRmh4enpiOTJod0xpTDZUZ0RHdmZlOE5FaWdkZnZVN1pJREo1d1lIdCttbklTZ25BZGFWTk9hOGZRLzNLcXl2Smw2KzFpQllVQk1OaHRGYjMzdFlRR2JNNG5HL2grZTMvZENtYVFGZXFTdnZCWEZGS2F1aDJzWmFLU3lob3Znb3U2bkpmU0JxbmNFZEVFUWNpRGkzWGNqSUFxU2gyaDFlOXZMa1RGbnV2MHZwQ2dxK1ZucVRNQjFLQ0VsSmdmc093ZE9iTmhNQjVEMjIwU2VGTWsvYmgxK29raG5ONmMiLCJtYWMiOiI4MWIzMjlhODIwNGNjOWMyZjdjOGUzMWI2ZmU3ODYwMDc1OWMyMjE2MGQ0NGRkYzcyOGFhMzA2MDNiYWJjN2NmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJhS3E4V0hNS2Iwa3JtZlc0SkwrK1E9PSIsInZhbHVlIjoiZ2tZZkFENEI3UnZVb2tDTyt1SFNJQWk3OUtiaUxyQ3gyS0NzVTBidlZLbXFsNVNuZzh1Ym5RbW00ZFNrNll2Z3FEQnRSdmMxZW40aGtkN1JyWndqb3BVNTNMQXYrQnRGN0FTTHBUODBXcHhWNFgrUkd0R084TnpWUlQ5UkhxdExGeVltb0hPdS9tTFljY0pkbGM1ZXo0L0lDcG1aek1PNXErY1VobjRGUCtiWjdkRHdFV0NqNFN1eWZlUzNyc25xNVp5cXlqRjJjMWRONEliK3NQSjFqTFpCQlo3V0F3U0JkMHR6anRES3pRb0M0Vk5KUUhCRTQ1YlNEbTNEZkhDeTBYeGJsSi9iNWErSGdPdDNGKzZmQ2VvdkpDVzM4b0N4d2ZGTVJjemIyTTBHYVBuaWZxdnViRFNoajZ0eHRjSXZuN1lWdjhGUU9GdEhicnNnUDdMZVVDckhpRDJKY2trdFZ4ZHlIeUhvU0lCY2lQQkJnTzdCakhucWU5S2cvdU1zYU4xZHV5VThDWDVTQlJwd1BxaHZLQnpKclVHYU1FVUF0M0ZXRUJHS2duck04STBUQmF5eWUxbDZQSFBZeHBNOWVhRXJVUkVkTmVZR3h2TlNkRC9wZ0tRRmExN0YwOGpocTVvQU9lZWRWelR6VEhPUVdTb1BRN3Vpdzg2QndBRkciLCJtYWMiOiIyNzc0YmNmOGFlODE2YmQyMTA2NzhkYmU1ZjRiZGRiMWVjODhiZTAxN2QzNDM1YTkwZjQxYjMzY2Q5YzM3MmQ4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImFjaFNoSFVkd1UvK3hFdW9Ra2RScWc9PSIsInZhbHVlIjoiUDVWOU15d3VxZjkydkR1L0orbHowcWxwWGpHV1ErcDlGRnVIQnlPTkYwSEI4Z1FydDc3MWNuWW11QkpzcWJIOUdMMXhkRjh4ZGpVMGVjeHpkUEZFTkRVVm1jb3ZJaDNuNVAzRUhUdlRXc1RlWkxvV3daWEovdXc2Tk9BYmJvUWRCS0FMYUh1ZFdSci9vMFo5dVZaaVNCZ0wzWTFFeC9RdXhxS2Y4WUVIdmsrSlo2Z3V1VW5uQmNpdm1jcVcwK0RXa01zM3IwVFdjeGNreXBKc3gvNVA4VTZtc3BpTzFlSE5FR2RtT05UZ3kxcEV4NDQ1cHlYdC9aOTdaZjVoODFEeEhGZU4wQzN6Sy93RG1lVzdpV01DcVNvcWxlVG9ZODgxME9JRmh4enpiOTJod0xpTDZUZ0RHdmZlOE5FaWdkZnZVN1pJREo1d1lIdCttbklTZ25BZGFWTk9hOGZRLzNLcXl2Smw2KzFpQllVQk1OaHRGYjMzdFlRR2JNNG5HL2grZTMvZENtYVFGZXFTdnZCWEZGS2F1aDJzWmFLU3lob3Znb3U2bkpmU0JxbmNFZEVFUWNpRGkzWGNqSUFxU2gyaDFlOXZMa1RGbnV2MHZwQ2dxK1ZucVRNQjFLQ0VsSmdmc093ZE9iTmhNQjVEMjIwU2VGTWsvYmgxK29raG5ONmMiLCJtYWMiOiI4MWIzMjlhODIwNGNjOWMyZjdjOGUzMWI2ZmU3ODYwMDc1OWMyMjE2MGQ0NGRkYzcyOGFhMzA2MDNiYWJjN2NmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}