{"__meta": {"id": "Xadfa2aab06964afa60c6c6e285e9f4a6", "datetime": "2025-06-30 15:34:56", "utime": **********.473447, "method": "GET", "uri": "/add-to-cart/1839/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.051199, "end": **********.47346, "duration": 0.42226099967956543, "duration_str": "422ms", "measures": [{"label": "Booting", "start": **********.051199, "relative_start": 0, "end": **********.383388, "relative_end": **********.383388, "duration": 0.33218908309936523, "duration_str": "332ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.383398, "relative_start": 0.3321990966796875, "end": **********.473462, "relative_end": 2.1457672119140625e-06, "duration": 0.09006404876708984, "duration_str": "90.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48582472, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.01917, "accumulated_duration_str": "19.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4156349, "duration": 0.01448, "duration_str": "14.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 75.535}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4378881, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 75.535, "width_percent": 1.617}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.45085, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 77.152, "width_percent": 3.026}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.452733, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.177, "width_percent": 2.087}, {"sql": "select * from `product_services` where `product_services`.`id` = '1839' limit 1", "type": "query", "params": [], "bindings": ["1839"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.45712, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 82.264, "width_percent": 1.356}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 1839 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["1839", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.461145, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 83.62, "width_percent": 14.658}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.465315, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 98.279, "width_percent": 1.721}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-936330918 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-936330918\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.456327, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  1839 => array:9 [\n    \"name\" => \"ميداليات شكل محتلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"1839\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/1839/pos", "status_code": "<pre class=sf-dump id=sf-dump-1466877720 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1466877720\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2103414975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2103414975\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1223008145 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1223008145\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1926481820 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297684545%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJhMG1ld3M0cUxHSk83WEdzSzNyaEE9PSIsInZhbHVlIjoiaWRLODdjYUtVTjZTTXpQSjR6bno0V2VIT05ESXRobUNZK01IMGFIY002OVJURDJ4akJ3czJMQktMU09VRWNCeEhuZWl5czQ0bTlKUm02d0lwRWJNVDlITXR1MWVaOEJza0h3UWh5aFZlOFluTlNMSGt5VFVPWkkzWmRsQ3dNd05rVmpiVUdOOUdSeGRGZE1teTNZT2VyNW1sTDRMYTlOUjFaQ24zMUdPSS9IejcvanVQRkV1L2tiNjhLc3VKc1I2ekNzTnlvc2t3MUVzMzV0ZkRBLzhqSzJxK1hPMk1jNllqbjFKSzZFY09YdG1aSEczMzBWMXdSYVBUOWlLcGNrWngyWVYyRnNKQXVTVWd5VzZYU2NKeWFjVUtLM0crdEs0Qy9HWDJuai9MU1V5blRJMXdnMXdhSHM2SElaZEpIZ1J3K2VPY2w1T0RZVW1PMTdlUHQ3QkwrQnZLclEzRXFOdFhpOE5mT0tIb2gzQnQ2WlNjN3BQVjdWZkZVamo0YUxTVkdVTXhUSFR4S2l3OWtKVDVwZGNHaTI4bE56UkRwUStlN3lXNm5lUkdDaGxYaW5MWnpnd3Y0dnUzNVV6OGtObTNNcVpueS9XOXlXdW1GenVJNG1LbHNtaGpEb0JYSmxEcTBpcjJqbU5xajgyaTU3dHBvUmwwY1RxWENSNG5zdzciLCJtYWMiOiI1MDg5OTRlMjFhZTJmMjNjOWVjNWRiMGZlZWEyM2Y0YzMyMTg1MjdiMTA0ZDJhNmFmY2RkMzEwZjk2ZjI3ZjZiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5IM3Zab0FBZ04wbzY5ZUovOG9iSmc9PSIsInZhbHVlIjoiWU5EcGtwd241OVNEZWMzT0oyeTRUR3hXeXpCWEtEbDE5Yjc0U2pFVWVHeEVKN0tVcklVR0ZZcERwekFZRk9McW4wdnE5Y1gxS0RmMDhWTGl6NVRPT082Z2RiQlgzR2JDbXExV3lWMCt1WjhlR1c3MWl2UGU4Snhyb1hYRTZXbWZ5RGZmdVc2c1NvcjlMeDRwSE9PTyttckRSaE04TjFJd1hPbDAraGVuYVdGaDNJcmRxZ0luWERJMUVzb2IvMlJlUFBUS2FTMW5pNWZKZkxMRmM2aCtWYkhLT2Rtd0hnd0c3UEJJSzJIWE8rY2JVZ08zdmNFSE5JcWdvZHZVM0FlVk80dGFNNzhXZ0hzYkhKcTNrK21Ea1RwU0lObThDTW5wQ1JHWWNFRGxYTnRLSXVDd2tab0dSWkNrcGs0UGdJYWN3c3FZbDNqUkRnZkx2MGc1Sjc1eHRHaTlZdkt4OTRXQWo2YmxuOWZsb2hrVkI2QUp1ZW5tSE9ZTEVHM3E0dktBa2x1SXJIankrUDBTUnV0aFNIRVNhejVZRHFUZy9ZVVlIZnNGQXJCNE95ak50RlBuTXNVOGVJdmw5MFFhMzJvSlpTQTVUL0YzK3dOOU8rd3pNeEVHUWhzdkhsZEJOMEI4RXlVbGYvSDZMRDJXYUNOQjIzYld1a0kycnhMVy9CaC8iLCJtYWMiOiJjOWYxNTY1NWJkZjZmOTRhODYyYzlkNmZiYTFhMjUxODNkNDA4NWI4N2FiYTIxZDM4MTEyMDNkYzc5MWVjNjFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926481820\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1856184640 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1856184640\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1379784476 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZUQVJDZEt5Yy9mZDJZYmxsQ3NjbUE9PSIsInZhbHVlIjoiRGNJZG1VZE1pS2lKY3Zic1FUWXVkREhMa3F4Qnd6eWRHK0krTGJmU3VpZmcyV3RUU2x5UUtINkwvUnQva1NQUjhVVi9sckM2bDdsYmZ4MDFodE42T2RjWGFqbDdGdXdIZlZZVTJoa3ZIK2trRmRMUEx1Q2s3Z3NKMmdMUStIT0k4R1pobVNVUkNwc1hPenJPVk44VDZpRXVUendPVWUrNWxYWWlnSy9ReDI1M1ZTbmlua005WURDTTFCY0wvTmNKZ3dQV3NFeUUrK1FlRjJmRHhIK2xWTURCamN4cnQ0N1Q0T1pyMFpJY2psSkp5RXZSV3F1TDMwL1ljU0FkTnA5a1pLMWIzOHdMTGRoWkhjTDF5ZlJvMllYT1RrWnd0RzVlME90ZXBESWRZaUZtTmhkMHhoaVIyS1V6UHRTMWNlMzJwNjVUQllQQUs2NFMycEI1M2NsT0hodEhOWk9TeXdPUk9SWXVnaUFITktoeVYwME9VdkVndG41c1h5OGVaakR2M3g2T3J5UnJQcjN0YndBSjY3M0l6VzVZQnp1YzFmMHNmSDZFdUU5ajAxK3JpRDh5QXZ3T1puOEtTVkJlZDNIaWRwWVkydjV5SG9lTUhEMGtLdlFCN0dqQjZ1L2owOS9NcUtRQmR1MmxEbzhRM1ppVXBwREhLbzFGWEY1T2hIVHUiLCJtYWMiOiI5NzQ4ZmFiZDg5OTQxY2M5OWY0MzlkMTNiMGUyMWIzNzBmOGZhNGE1MzE2ZDVhM2M3MGNkZjUzNjc5YTEwZmZmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJDa1ovYSt2TSt3OUlrYll0Y3hzVmc9PSIsInZhbHVlIjoia2RFcHR5NnB0L05Dc3JzTjF0R1FsY21hSWJQK0svTS9YZ050aXY1QlBUa1ppT0s0aDlIREVhMHp1VGl5Wlp3bEdtbzQzOHpoQU5xR2V1b2lqaHd4dFN6VHZYWnhDWUVabWJmclRZODlicmUzOVJmV2FsMjVDNVJnQ0dLak9pbVZYNXE1K2hLSE1sa2hIdHNNa3Vmd0ZKZk0za3d6eFNpaVpPNm5UaEE2cVRsbDhTZzZxa1dHMFBZa21RTTNqeURadzVyWW9peGp0NjlIRDNSOFIyOHlhem05ZFhFdlJ1RTlmY0VocHpPejYyWHVWN2FXOGxCcExWREdyNHZzYlp5VzVQYjFpMXlVVlRubEFqZkUzQ2x5aWoraUxFNjZ0cE9VdUkwcUx6eEh1ajdCNFBRTUd4STJGdzdzN1RZaDB1aFJSLzZPZnd0azdRR3FaU2ZLenRmakJzQ2VSYzVpWEh3ckl5b1FzWk9Ga0YydDV5VFJYNHVxWmRTVkd1OHFIWS9PWElrV1pEZmt4clRmNzF4ZklRSFhRREhZYzlrSCtmV2N4VFVvajNGeVk0M09ILzdPa0NmcDAwOTFKRTU3VlVJKzIzWUk3enZqUk15T05ka09EK05ReEI2anBsZlcwdERUKy9KTHgxSmtGWTRXMWgwR3NzTzdrVE5PdXV0cDNPZW0iLCJtYWMiOiI0NmIzZGI3Mjk1N2ZjYTVlMzQ3NjEzNzM2ZDEwNTdlNmM4MWRjNjdlYmVmMmJiZTM1OTQxZDM0Njc0OTQxMjE3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZUQVJDZEt5Yy9mZDJZYmxsQ3NjbUE9PSIsInZhbHVlIjoiRGNJZG1VZE1pS2lKY3Zic1FUWXVkREhMa3F4Qnd6eWRHK0krTGJmU3VpZmcyV3RUU2x5UUtINkwvUnQva1NQUjhVVi9sckM2bDdsYmZ4MDFodE42T2RjWGFqbDdGdXdIZlZZVTJoa3ZIK2trRmRMUEx1Q2s3Z3NKMmdMUStIT0k4R1pobVNVUkNwc1hPenJPVk44VDZpRXVUendPVWUrNWxYWWlnSy9ReDI1M1ZTbmlua005WURDTTFCY0wvTmNKZ3dQV3NFeUUrK1FlRjJmRHhIK2xWTURCamN4cnQ0N1Q0T1pyMFpJY2psSkp5RXZSV3F1TDMwL1ljU0FkTnA5a1pLMWIzOHdMTGRoWkhjTDF5ZlJvMllYT1RrWnd0RzVlME90ZXBESWRZaUZtTmhkMHhoaVIyS1V6UHRTMWNlMzJwNjVUQllQQUs2NFMycEI1M2NsT0hodEhOWk9TeXdPUk9SWXVnaUFITktoeVYwME9VdkVndG41c1h5OGVaakR2M3g2T3J5UnJQcjN0YndBSjY3M0l6VzVZQnp1YzFmMHNmSDZFdUU5ajAxK3JpRDh5QXZ3T1puOEtTVkJlZDNIaWRwWVkydjV5SG9lTUhEMGtLdlFCN0dqQjZ1L2owOS9NcUtRQmR1MmxEbzhRM1ppVXBwREhLbzFGWEY1T2hIVHUiLCJtYWMiOiI5NzQ4ZmFiZDg5OTQxY2M5OWY0MzlkMTNiMGUyMWIzNzBmOGZhNGE1MzE2ZDVhM2M3MGNkZjUzNjc5YTEwZmZmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJDa1ovYSt2TSt3OUlrYll0Y3hzVmc9PSIsInZhbHVlIjoia2RFcHR5NnB0L05Dc3JzTjF0R1FsY21hSWJQK0svTS9YZ050aXY1QlBUa1ppT0s0aDlIREVhMHp1VGl5Wlp3bEdtbzQzOHpoQU5xR2V1b2lqaHd4dFN6VHZYWnhDWUVabWJmclRZODlicmUzOVJmV2FsMjVDNVJnQ0dLak9pbVZYNXE1K2hLSE1sa2hIdHNNa3Vmd0ZKZk0za3d6eFNpaVpPNm5UaEE2cVRsbDhTZzZxa1dHMFBZa21RTTNqeURadzVyWW9peGp0NjlIRDNSOFIyOHlhem05ZFhFdlJ1RTlmY0VocHpPejYyWHVWN2FXOGxCcExWREdyNHZzYlp5VzVQYjFpMXlVVlRubEFqZkUzQ2x5aWoraUxFNjZ0cE9VdUkwcUx6eEh1ajdCNFBRTUd4STJGdzdzN1RZaDB1aFJSLzZPZnd0azdRR3FaU2ZLenRmakJzQ2VSYzVpWEh3ckl5b1FzWk9Ga0YydDV5VFJYNHVxWmRTVkd1OHFIWS9PWElrV1pEZmt4clRmNzF4ZklRSFhRREhZYzlrSCtmV2N4VFVvajNGeVk0M09ILzdPa0NmcDAwOTFKRTU3VlVJKzIzWUk3enZqUk15T05ka09EK05ReEI2anBsZlcwdERUKy9KTHgxSmtGWTRXMWgwR3NzTzdrVE5PdXV0cDNPZW0iLCJtYWMiOiI0NmIzZGI3Mjk1N2ZjYTVlMzQ3NjEzNzM2ZDEwNTdlNmM4MWRjNjdlYmVmMmJiZTM1OTQxZDM0Njc0OTQxMjE3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1379784476\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1793496933 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1839</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1605;&#1610;&#1583;&#1575;&#1604;&#1610;&#1575;&#1578; &#1588;&#1603;&#1604; &#1605;&#1581;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1839</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793496933\", {\"maxDepth\":0})</script>\n"}}