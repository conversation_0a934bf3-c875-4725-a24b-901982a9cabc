{"__meta": {"id": "Xb5bf26e862a68bb92088da0254e1ee91", "datetime": "2025-06-08 15:42:40", "utime": **********.907771, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.272653, "end": **********.907792, "duration": 0.635138988494873, "duration_str": "635ms", "measures": [{"label": "Booting", "start": **********.272653, "relative_start": 0, "end": **********.83486, "relative_end": **********.83486, "duration": 0.5622069835662842, "duration_str": "562ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.834869, "relative_start": 0.5622158050537109, "end": **********.907795, "relative_end": 2.86102294921875e-06, "duration": 0.07292604446411133, "duration_str": "72.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139448, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00501, "accumulated_duration_str": "5.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.873092, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.06}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.888036, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.06, "width_percent": 14.77}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.896906, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.83, "width_percent": 15.17}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1127099958 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1127099958\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-589285715 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-589285715\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1374029575 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1374029575\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1955955254 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397357416%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjcxTWNPcmRNU0hZbHpsNmpnQ0dYa1E9PSIsInZhbHVlIjoiZzlsYjFxY3RSaUNpdkU3aVZiNlhSdEVDbXZYQndYVWtWNXo1ZDYzUjZyV0Y1bmRKNHlDRUV2N2l1c3hKVkFvZjRRUjM4V042bjJYTHZnYXhPNU82R1dWVnIrMlNqcTRHaG0yRE9kRlMrZXJhUUdDcWd4K3NnTVNza1UwMWlKSXRUSkZPUHM0cVJBUlJTQXIrb3NmRUh1cDh2YlhtUlR2SFBsekE5TGkrby93YjhiZWllTy9GRFZzdExXbUp0NFlORkJwNnFDeW1Ya3JsNTdITkU0bnpNOTdLM0lIbTNvTUYyZzlvaSthZGpVZkVwYkNEcVo5amxnR09yTnVIY2xUQXMwT0JJcE1DbWNhVGFGSDB4b2o4c2ZMRTROQUFVbnBCYmkvVGFUc1ZGZ0NtUmd6ZHRLTmpUM0ZZa3R4S1lzUmNYUGtXc2dHeDJ4Kyt1RGRKU3lmOW9Kd0c0T2d3S1A1cEZHU2tZaFU5Q0pKSkpYZ09vMCt0OHZhYldKY0c2UHA3VVh1YzNWWlBSQVNaZVBQd29XeHFodzF1R21sbEcvVEJUdExHek4yRzlUQVR2U1I5dVFmSzJCRVYzanpqQXNaRERMSUQzTWJqdjZMMnlRUVhmMWNuZlVHWjNheGlJWTNwRUgvRTdWdkozaUI4ZDBNTjBqeHhzTmZrMDBCZ1N1MVIiLCJtYWMiOiJkYmZlY2UwOTI0MjVmZjI0NmZhYmVlYjdhNmI3MWY2YTk3ZjQ0ZDJkNzY3MjIyMzM5ZmU3NzZlMjAwOGUzNmUwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik0ySkZkOUw3d1dwUTBFYWRrLzd2Q0E9PSIsInZhbHVlIjoidGRNazlZVm8yL21wMnlKQTN4a0VRQWtoTEtnWUpYTHAwUzAyek9CT25iQzdSUkpodlRlVkFQUDhyZUMvVDZteXc0L2NzTzdJdnk3amYxOWdCQ214VXpXUUIyZUJVRGVqMzcySkdsanU0aXV0S2oxdmlkTGJxSTZ3WlpIV3dLRldyWS9VN2xldTRKalZTVUpoeUVFWEI5eWpiMHJ5QTBzS0h6MmxuY1JCUEdxYjI3RngwbXpiNzc3V3h5MmtPcm9aU2d6ZUlZZVc3emNLU216R1ZZOFBnSnNYNlFGRml1eFBBRURxUGVmSXdFRXIvUy9ZYzlLcjh3eGc3czFmcWVkc011TkloVWNEbTRldE5kL0hPR3NjZnpwVDRhUE5BOGk3azZJTDVKM3dKZ0FNaEI0QzJHcjVoZWJ6d0pmWGFETnd5NFJibEZGeFlJSGFIeHg1a01SdHNyTUN1TERPT3paVXRFTGtIdkJESGJ4MFNxMWxTMXZWZFYzYURJWUlYUXRycmNVZHUvU3UwbTB0c1RaWWJ0NlNOc3ZpeFMxbkZQRGZRb0NxYUtJZVBEQWlSSUVuRzJ0cnQ0TlcvT2xRUkxPcWlkY0ExbE0wUHdRVXF1MFpBK3U1NlVyNCtEZVZPZk9ueCtVNXoxek45enM5OWc0YkpiNjhjM2d0enhBMnQ2ZTkiLCJtYWMiOiJmYTNiNjdmYzFkMjgyNDcwNjhlYWQ4NzMxYWI2MWE5MDU4OTQyYjliZmE5MzNkNzI2OGU1NTU2ZmI4ZDcwMjc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1955955254\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1185428047 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1185428047\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1290681 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:42:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImM4c2JyZUtJZzROeHVmYk96UjV2ZFE9PSIsInZhbHVlIjoiRlN1MW9NYnlGYzB3RllSdmpNTkJKUDI1QS9IUWhkdnUydmRTNnhqekhtNGlkN3dTTGl4YmNEbml6U1B2QUgyeCtwclRvdlZUM0MrNUxxS2xNOGNNRGRZUW1HNytHdEQwOTFucitEUXpjYkJTNloyNm5YRW1KVExaR0ZaMDJTYk1YZXNUbWFQZkZhZU9majlLTk1hM0F4Mm1BMFpKODB5M3NOUGIvVDNSTjBNOUpuaUtTM0VGVWpvZmxRNytzQWtneVkwbnJSbks3SzM3cDllc1VkNFBVdGJMZjlBQ29CSU92SitadVJhekpmZGg1d0NjTDg5aG9yTUZtb1R4MkhPcmJZTE4wd0dzeFhySXdqWENHY0g4bWdwY2FoQzJQOVhVME1lM2gybDljaUFQOHFOak5MZGdNY2dBb3M3bVRsYzZQK1dPd0hFMFp6ZzBZUEc2eHFtMlc5VDBmVkJhSkxlMnVMUnRQVk9lWSs0enk1WTdDSGZRTEpkOEdzdWJ5UDN6WWR3MzJYMHovZWhzZllaMG83VlJyNGkySGlWYi85SG83dU1qZzFIejNhaS9ZU3UvWWpIT3lrTzFBUklWVWR5UUY3bm83WDBicytpaW52cHhHY1V2TllteFpzWkUvTklmYVNORFNuYVNNWEFtdDRoOG1FM1NqdUVWUGVNRGhTRDAiLCJtYWMiOiI1M2QxMDkwZTI1YjBiN2FhYmZjNzFiYzk5YWEzMTdkY2Y2OThiZTIyMDRhMjBiMTNmZDZmNDFjZTJkMjU4OTZjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im9Hak9VMHBTWUtBUWFBc28yZXVpeHc9PSIsInZhbHVlIjoiWmVQQ2xEQ201TEtNcW9PblBodVAveVJ6RTFsRW90SkRlS1A1R213V3VQclhyMjcvbldEZDlFQWJMZWxsOFlTSGc0Vk55OGlPL0JuMGVMY21YZzBLRUF5UXpoektxMDRGWVl4SWNJaUZIZDVsR1N0ejZyZ1FzL2pDZUltMnI5NWYwbEg3ajdIRUxjNm4zWkVLY0VjYXhhR013MklUcVZ2cksxNlMvVHpXZHBIdDk2eis4dlJGazhKd2tUaHdDUWJ0VVNoNUZwSkIvNGI0UTRYMlVZeVF2M2pJaFc5NEppRklNNGUxMVhSMnF5T3o2QVJsQ0g2aElnWUxzNjVmN0J3akV1OHZycHQ0cG9sbVRDNlcrYlpaLzVma29lSFZVYVhwbDFSNGZXVVh1aEh1bFlyZk9RcThLZEgzeXo0RTRhLzRabzIvUVBMUStYSUhCYmdmdWNUeEF4clg1QjBPaHA3cG5mRURJUFZ1VkhWOEcvZXZ6dllMZnBHdERTQTFDWThFVWMzK05la2dlWTg4OUZtOC83Q0NwWmExMXNGbHNheFJrbzNRWnpOUVoxbU16SGdTRG01bHowTDVXK1U0OU9sbHhaaC92Uzltc1gwclJHeDJkZmsvUUg3eWcvNlVncWZ5V1h4ZG9CZXdLY0Z6YU1rN00yNVoxMWhwVmhmTWRZZnciLCJtYWMiOiJhY2VmZDQzNzc4MjUyMWU2Yjk0MjliMjhhMDgxYjUwOGE5MWU3ZjA3NGJiN2Y1OWY5ZjBjNGE5YTBiOGJlYjA4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImM4c2JyZUtJZzROeHVmYk96UjV2ZFE9PSIsInZhbHVlIjoiRlN1MW9NYnlGYzB3RllSdmpNTkJKUDI1QS9IUWhkdnUydmRTNnhqekhtNGlkN3dTTGl4YmNEbml6U1B2QUgyeCtwclRvdlZUM0MrNUxxS2xNOGNNRGRZUW1HNytHdEQwOTFucitEUXpjYkJTNloyNm5YRW1KVExaR0ZaMDJTYk1YZXNUbWFQZkZhZU9majlLTk1hM0F4Mm1BMFpKODB5M3NOUGIvVDNSTjBNOUpuaUtTM0VGVWpvZmxRNytzQWtneVkwbnJSbks3SzM3cDllc1VkNFBVdGJMZjlBQ29CSU92SitadVJhekpmZGg1d0NjTDg5aG9yTUZtb1R4MkhPcmJZTE4wd0dzeFhySXdqWENHY0g4bWdwY2FoQzJQOVhVME1lM2gybDljaUFQOHFOak5MZGdNY2dBb3M3bVRsYzZQK1dPd0hFMFp6ZzBZUEc2eHFtMlc5VDBmVkJhSkxlMnVMUnRQVk9lWSs0enk1WTdDSGZRTEpkOEdzdWJ5UDN6WWR3MzJYMHovZWhzZllaMG83VlJyNGkySGlWYi85SG83dU1qZzFIejNhaS9ZU3UvWWpIT3lrTzFBUklWVWR5UUY3bm83WDBicytpaW52cHhHY1V2TllteFpzWkUvTklmYVNORFNuYVNNWEFtdDRoOG1FM1NqdUVWUGVNRGhTRDAiLCJtYWMiOiI1M2QxMDkwZTI1YjBiN2FhYmZjNzFiYzk5YWEzMTdkY2Y2OThiZTIyMDRhMjBiMTNmZDZmNDFjZTJkMjU4OTZjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im9Hak9VMHBTWUtBUWFBc28yZXVpeHc9PSIsInZhbHVlIjoiWmVQQ2xEQ201TEtNcW9PblBodVAveVJ6RTFsRW90SkRlS1A1R213V3VQclhyMjcvbldEZDlFQWJMZWxsOFlTSGc0Vk55OGlPL0JuMGVMY21YZzBLRUF5UXpoektxMDRGWVl4SWNJaUZIZDVsR1N0ejZyZ1FzL2pDZUltMnI5NWYwbEg3ajdIRUxjNm4zWkVLY0VjYXhhR013MklUcVZ2cksxNlMvVHpXZHBIdDk2eis4dlJGazhKd2tUaHdDUWJ0VVNoNUZwSkIvNGI0UTRYMlVZeVF2M2pJaFc5NEppRklNNGUxMVhSMnF5T3o2QVJsQ0g2aElnWUxzNjVmN0J3akV1OHZycHQ0cG9sbVRDNlcrYlpaLzVma29lSFZVYVhwbDFSNGZXVVh1aEh1bFlyZk9RcThLZEgzeXo0RTRhLzRabzIvUVBMUStYSUhCYmdmdWNUeEF4clg1QjBPaHA3cG5mRURJUFZ1VkhWOEcvZXZ6dllMZnBHdERTQTFDWThFVWMzK05la2dlWTg4OUZtOC83Q0NwWmExMXNGbHNheFJrbzNRWnpOUVoxbU16SGdTRG01bHowTDVXK1U0OU9sbHhaaC92Uzltc1gwclJHeDJkZmsvUUg3eWcvNlVncWZ5V1h4ZG9CZXdLY0Z6YU1rN00yNVoxMWhwVmhmTWRZZnciLCJtYWMiOiJhY2VmZDQzNzc4MjUyMWU2Yjk0MjliMjhhMDgxYjUwOGE5MWU3ZjA3NGJiN2Y1OWY5ZjBjNGE5YTBiOGJlYjA4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1290681\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-236829629 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-236829629\", {\"maxDepth\":0})</script>\n"}}