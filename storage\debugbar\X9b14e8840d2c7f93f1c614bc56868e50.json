{"__meta": {"id": "X9b14e8840d2c7f93f1c614bc56868e50", "datetime": "2025-06-08 16:24:56", "utime": **********.637776, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.010075, "end": **********.637796, "duration": 0.627720832824707, "duration_str": "628ms", "measures": [{"label": "Booting", "start": **********.010075, "relative_start": 0, "end": **********.535765, "relative_end": **********.535765, "duration": 0.5256898403167725, "duration_str": "526ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.535795, "relative_start": 0.5257198810577393, "end": **********.637798, "relative_end": 2.1457672119140625e-06, "duration": 0.10200309753417969, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152752, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.030350000000000002, "accumulated_duration_str": "30.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5740068, "duration": 0.02851, "duration_str": "28.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.937}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.613889, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.937, "width_percent": 3.031}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.624846, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.969, "width_percent": 3.031}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-558074582 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-558074582\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-973769149 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-973769149\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1305161255 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305161255\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=1y4k55%7C1749399864948%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im41amhVdm9XcGwwS203L0xGWVlHalE9PSIsInZhbHVlIjoiZmhyRmdoNzhnWUNrVlpseUtUYWNlUHFJR1pyNE1zY1lBSVJwWmtEa0VSNmJmZHY4QmFoU2lYTWRnK0ovVHBhSnM3MjZhaE12WnY4RzFDeWZOd0EzcVRueWhVWmVHTzIrT1dJWmkyZEFiWVRjdUh2bHZNMXJlSDBtcWF3clBSbFpiQXFTc005ZVhodGwyeGI2cWhQTTd0YWNyZzhCeEpvS0NHamJGeFFLWWFoSGNzbFJiMUlrWUhFUU1iMGQ2SS9Kb012TFNkK2NBcmdXb1dvQytTeTVseG5ISGRkSWFCczBGU0pvc2srdVN4dnBMd3pCbjljN2ZucXMxa3R5V2ZiTkVTaXVJUGZNd0MxV3ppSC9NM0w1c2pkODFEZUZ0cThXSTJvSnhocGNyQnhSNDdjWTZzLzY5SndVWFdqS0hRaXRWNjhXRnBlTTdyaEJ3ZDkrUVA1MVVOU1p3U0M5MVQvamM3Mlc2TXhuNG13S0xSLzduZFRrYzVFMnBUTC94dGd0dUZxbjd5akhCdUg3YVlBRFhITVNyV2RvcHVlYkNjb01SWUJVQVliVjkvQjZXS09CQUJRNVl6eHdQY0FheFJkWXdSMXMzaWFTRG5vdGxxdWNwZm56eWRZN09vY0RyV3JOaUhMNXc0My9USmRNU3REL1pYWTVkLzR1UDZvV29HRU0iLCJtYWMiOiI3OGZkYTJjMGU2Y2FmMDVlNDNhNWExY2IzYTcwMTBhZWY5Mjg0M2IxYWYxZTY4YjY2MzM3N2Q4ZDEyY2Q3NTZlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlV6azlFTEluOEZQeHlKWFVMWTZYdHc9PSIsInZhbHVlIjoiV2FHRmhQd3laMnVuS3ZVMmpVN2hRZHI0QjV5RU16V3pLZnBXdnBOeTJSeVpvYmU5cTZZdmJ5V0F6UXlGMmsrWHRCK3M2T2lSdVgrUXZad2tocnNjTksrZ2NFalhWenlUZHRoTkZoNUJURE5HWCt4WERlcVQ4dldzaWd5OEhaNkZIbnpITzA4Z2xCNEx1TGIyK1k3NGxhWm5mTkZPM2xvNDN0bkgwdDY2eFNJZmhrM0lyQjVkZjlYZWpSOFVIenVFaUFDK0RsQlRhcXdjNWc2cnRGYW5vTjBIY3dnaExZMjI1UjFGeGloNnhlRTcvN29FdEk1M2RlbndSMjkxbmtPK0w5bDloREs0RVB4RHY0LzhoWi9Cemo4Mm81Y2ExSXpjYW9lcHA3Rnp0d1ZhNnZIaHJVTjFqMnVmT205S0tINmVUeHAxS0dIZEhDb3dVV3cyb3BxZDhUa0ZwVGJ1bGtnTHNiVFA3MldUc2ZlbG9yZDlXUHRXN04wbFNMYjBKaTdaYldkR1Bhck1vaVFmRzdRMUJwYmxuMUlTUi91UWhTN0k5b0h1NGdTeCtVdkRUVWIrRlRWeDd4b1ZPWFdmcVYwT3gzdWw1U210R1RFeXNGelZ1QTZpWkxONTZaR0JtTk4xMEd3SDhhZEMwQ0dWbitHMVhqUnZJbnlnbHZUZEtXU0EiLCJtYWMiOiI3YzFhNWI4M2UyY2NjMTMwODg3YzYwZjNkZmZhNzdhMjkzN2VlYWFjNGRmMjU1NzVjNWEzNjhmMjdjOGU0M2U0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-916026851 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-916026851\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1629279949 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:24:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9MTGpsN0JDdlVwcXlOUDlLTVlEclE9PSIsInZhbHVlIjoiU1BGVFdETnJVWTdvQ2VENHIvcmFyekNBOFEvaEpmcFBOL2huSUkxTElEU3NvTFJHYm83U2JuNzFKV1NibXFENHA1MlFNU2NsaDRxblY5RW1jZ0JpS3J6dmR6RVpDbloya2Jlbk4vak5rTmk3MXFkeUMxbWNacTNkbWZsYUpFcCt5MXNhenJQc0NtTTNtSWF5MnV0VmthNzRIYjBwaHJKb2RCVWRUd2pxNjlrRTdPaGsrc2oyUndtVmZ5WlVPK0xBamd4MnJXMURaRmVGcnAyb3Nwa3JaSXBWcStNUVh0ZFNPR1c5Z09WMm9iVkZIVjlzcy91R3ZyWmVUWm1oTXpnQjhtekhNaFc1ZmdtNXBCQ0U3SURmUDg0Nmg5OGZYbFgxMHcvb0czVHlTTFF0bFd3cGxDa3RjNFFFQkoySER0S0ovdUdWZUR1T21KNzltTlI0cXR5ekNpZ2p1Zlk2NFZzUXR1WnhCOWhXNXBiSXJ3L3pUVzd6K25PcmViU0tDWEtONFgvaDVpeVRUanFsclZ0WEN5NytUenVQK2QrMnlGLzhTSEwxMWJtMU5FRHpHK0l6Q1pXYzR1MDRCVVdsakNRb0lOUnpuNzdlemYyd1ZyNmZJVmU2eXRMVzBPS0dQOGN4aGgwN042ZHpQcWtYTEUwckNyL2t0a1hoNWdSQ0FUUSsiLCJtYWMiOiJjMmYzMDQ0MjdlYzJlYjRiNzM0ZTU4YzMwNTAyNTA0ODE1YzJiZmY1MzQ3YTI2Y2I3NDM4ZGM0YmFmZjIxZjQ3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjRRejZzRjdSNlZEM2gvM1V5cThzZkE9PSIsInZhbHVlIjoiOTBOU0pjcEJyaUtENlVxTUJEOEJaZ0JmNlcxd0VwYnBIZ2xBMzJMVWlhQkVhQ0FlM0xkbjZLM2c4L25tU0lLTzJKZTR1REN0aDViK3VkWTJMelhTOVFmcitpdEYrd21LTmJHUUFxdUFLTjd2bTIxa0g3Y3R5dCtJS3Z2L0cza0VmcDNjcy9VcVBjRGtSWC9DejFDN2xRN2Z1M0pMbHZudjFDSFpOYXZPT0JtZExZVG94TnA0OHowLzFCaFZaSlJZaGJScmJQUlU1TWkxKyt5T1V6MXBNMG1qc24wdWZRNzgwcWtFSVNPRm0wZWFpZm1URFdheVRWc1hsWmEvT3NvNkZoVWlrSE1aWTFUVXdwYUtKNi9kNHVXblJhY2hnVUpwL2xZUzJNaHAxSHlMdVB1MFNiZ2svckhsZURhSG9lWFNjQk1rTHNNZFBZM21ZbTV6NG0wbjNJeW5HNEV4bkYraGU4VU15OEdnYUtWajNXQzZ3Z0gzeE9ZQ0JQbGZxWWlpbzEyb2JWU3ZlY2gzcjJjeE9uMkd1UU0xUS9PMDc1ZnRCa0VVdnRwODJkRFIraWFHVHJCemJPVzNBaUlLZDdwQ2tvT2xLNGpoSDRVSnFDRUpVSVlzRW1NZjNaaWZ1ZmJSN2NtM2ptdzIvRlNSTkxKcUN3NzI1dE8xTS95QjJQQUwiLCJtYWMiOiIzZDQyY2I2MDY5ODUwYzBhMzczNTFiYWI2NDIyNjM5NGI2YWRhODY1ZjY2ZDk0MjU0ZDRhZWViNDA5MmE0MDcyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9MTGpsN0JDdlVwcXlOUDlLTVlEclE9PSIsInZhbHVlIjoiU1BGVFdETnJVWTdvQ2VENHIvcmFyekNBOFEvaEpmcFBOL2huSUkxTElEU3NvTFJHYm83U2JuNzFKV1NibXFENHA1MlFNU2NsaDRxblY5RW1jZ0JpS3J6dmR6RVpDbloya2Jlbk4vak5rTmk3MXFkeUMxbWNacTNkbWZsYUpFcCt5MXNhenJQc0NtTTNtSWF5MnV0VmthNzRIYjBwaHJKb2RCVWRUd2pxNjlrRTdPaGsrc2oyUndtVmZ5WlVPK0xBamd4MnJXMURaRmVGcnAyb3Nwa3JaSXBWcStNUVh0ZFNPR1c5Z09WMm9iVkZIVjlzcy91R3ZyWmVUWm1oTXpnQjhtekhNaFc1ZmdtNXBCQ0U3SURmUDg0Nmg5OGZYbFgxMHcvb0czVHlTTFF0bFd3cGxDa3RjNFFFQkoySER0S0ovdUdWZUR1T21KNzltTlI0cXR5ekNpZ2p1Zlk2NFZzUXR1WnhCOWhXNXBiSXJ3L3pUVzd6K25PcmViU0tDWEtONFgvaDVpeVRUanFsclZ0WEN5NytUenVQK2QrMnlGLzhTSEwxMWJtMU5FRHpHK0l6Q1pXYzR1MDRCVVdsakNRb0lOUnpuNzdlemYyd1ZyNmZJVmU2eXRMVzBPS0dQOGN4aGgwN042ZHpQcWtYTEUwckNyL2t0a1hoNWdSQ0FUUSsiLCJtYWMiOiJjMmYzMDQ0MjdlYzJlYjRiNzM0ZTU4YzMwNTAyNTA0ODE1YzJiZmY1MzQ3YTI2Y2I3NDM4ZGM0YmFmZjIxZjQ3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjRRejZzRjdSNlZEM2gvM1V5cThzZkE9PSIsInZhbHVlIjoiOTBOU0pjcEJyaUtENlVxTUJEOEJaZ0JmNlcxd0VwYnBIZ2xBMzJMVWlhQkVhQ0FlM0xkbjZLM2c4L25tU0lLTzJKZTR1REN0aDViK3VkWTJMelhTOVFmcitpdEYrd21LTmJHUUFxdUFLTjd2bTIxa0g3Y3R5dCtJS3Z2L0cza0VmcDNjcy9VcVBjRGtSWC9DejFDN2xRN2Z1M0pMbHZudjFDSFpOYXZPT0JtZExZVG94TnA0OHowLzFCaFZaSlJZaGJScmJQUlU1TWkxKyt5T1V6MXBNMG1qc24wdWZRNzgwcWtFSVNPRm0wZWFpZm1URFdheVRWc1hsWmEvT3NvNkZoVWlrSE1aWTFUVXdwYUtKNi9kNHVXblJhY2hnVUpwL2xZUzJNaHAxSHlMdVB1MFNiZ2svckhsZURhSG9lWFNjQk1rTHNNZFBZM21ZbTV6NG0wbjNJeW5HNEV4bkYraGU4VU15OEdnYUtWajNXQzZ3Z0gzeE9ZQ0JQbGZxWWlpbzEyb2JWU3ZlY2gzcjJjeE9uMkd1UU0xUS9PMDc1ZnRCa0VVdnRwODJkRFIraWFHVHJCemJPVzNBaUlLZDdwQ2tvT2xLNGpoSDRVSnFDRUpVSVlzRW1NZjNaaWZ1ZmJSN2NtM2ptdzIvRlNSTkxKcUN3NzI1dE8xTS95QjJQQUwiLCJtYWMiOiIzZDQyY2I2MDY5ODUwYzBhMzczNTFiYWI2NDIyNjM5NGI2YWRhODY1ZjY2ZDk0MjU0ZDRhZWViNDA5MmE0MDcyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1629279949\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1506514441 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1506514441\", {\"maxDepth\":0})</script>\n"}}