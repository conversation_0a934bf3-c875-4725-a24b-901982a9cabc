<?php

namespace App\Http\Controllers;

use App\Models\VendorRepresentative;
use App\Models\Vender;
use App\Models\ProductServiceCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class VendorRepresentativeController extends Controller
{
    /**
     * Display a listing of the vendor representatives.
     */
    public function index()
    {
        $user = Auth::user();
        
        // Check permissions - both SUPER FIESR and company can view
        if (!$this->canView()) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $representatives = VendorRepresentative::with(['vendor', 'category', 'creator'])
            ->where('created_by', $user->creatorId())
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        // Statistics
        $stats = [
            'total' => VendorRepresentative::where('created_by', $user->creatorId())->count(),
            'active' => VendorRepresentative::where('created_by', $user->creatorId())->active()->count(),
            'inactive' => VendorRepresentative::where('created_by', $user->creatorId())->inactive()->count(),
            'vendors_count' => Vender::where('created_by', $user->creatorId())->count(),
        ];

        // Get vendors and categories for the create modal
        $vendors = Vender::where('created_by', $user->creatorId())
            ->where('is_active', 1)
            ->orderBy('name')
            ->get();

        $categories = ProductServiceCategory::where('created_by', $user->creatorId())
            ->where('type', 'product & service')
            ->orderBy('name')
            ->get();

        return view('vendor_representatives.index', compact('representatives', 'stats', 'vendors', 'categories'));
    }

    /**
     * Show the form for creating a new vendor representative.
     */
    public function create()
    {
        // Check permissions - both SUPER FIESR and company can create
        if (!$this->canCreate()) {
            return response()->json(['error' => __('Permission denied.')], 401);
        }

        $vendors = Vender::where('created_by', Auth::user()->creatorId())
            ->where('is_active', 1)
            ->orderBy('name')
            ->get();

        $categories = ProductServiceCategory::where('created_by', Auth::user()->creatorId())
            ->where('type', 'product & service')
            ->orderBy('name')
            ->get();

        return view('vendor_representatives.create', compact('vendors', 'categories'));
    }

    /**
     * Store a newly created vendor representative in storage.
     */
    public function store(Request $request)
    {
        // Check permissions - both SUPER FIESR and company can create
        if (!$this->canCreate()) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $rules = [
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'vendor_id' => 'required|exists:venders,id',
            'category_id' => 'required|exists:product_service_categories,id',
            'is_active' => 'boolean',
            'notes' => 'nullable|string',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            $messages = $validator->getMessageBag();
            return redirect()->back()->with('error', $messages->first())->withInput();
        }

        $representative = new VendorRepresentative();
        $representative->name = $request->name;
        $representative->phone = $request->phone;
        $representative->vendor_id = $request->vendor_id;
        $representative->category_id = $request->category_id;
        $representative->is_active = $request->has('is_active') ? 1 : 0;
        $representative->notes = $request->notes;
        $representative->created_by = Auth::user()->creatorId();
        $representative->save();

        return redirect()->route('vendor.representatives.index')
            ->with('success', __('تم إنشاء المندوب بنجاح.'));
    }

    /**
     * Display the specified vendor representative.
     */
    public function show($id)
    {
        if (!$this->canView()) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $representative = VendorRepresentative::with(['vendor', 'category', 'creator'])
            ->where('created_by', Auth::user()->creatorId())
            ->findOrFail($id);

        return view('vendor_representatives.show', compact('representative'));
    }

    /**
     * Show the form for editing the specified vendor representative.
     */
    public function edit($id)
    {
        // Only company can edit
        if (!$this->canEdit()) {
            return response()->json(['error' => __('Permission denied.')], 401);
        }

        $representative = VendorRepresentative::where('created_by', Auth::user()->creatorId())
            ->findOrFail($id);

        $vendors = Vender::where('created_by', Auth::user()->creatorId())
            ->where('is_active', 1)
            ->orderBy('name')
            ->get();

        $categories = ProductServiceCategory::where('created_by', Auth::user()->creatorId())
            ->where('type', 'product & service')
            ->orderBy('name')
            ->get();

        return view('vendor_representatives.edit', compact('representative', 'vendors', 'categories'));
    }

    /**
     * Update the specified vendor representative in storage.
     */
    public function update(Request $request, $id)
    {
        // Only company can edit
        if (!$this->canEdit()) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $representative = VendorRepresentative::where('created_by', Auth::user()->creatorId())
            ->findOrFail($id);

        $rules = [
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'vendor_id' => 'required|exists:venders,id',
            'category_id' => 'required|exists:product_service_categories,id',
            'is_active' => 'boolean',
            'notes' => 'nullable|string',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            $messages = $validator->getMessageBag();
            return redirect()->back()->with('error', $messages->first())->withInput();
        }

        $representative->name = $request->name;
        $representative->phone = $request->phone;
        $representative->vendor_id = $request->vendor_id;
        $representative->category_id = $request->category_id;
        $representative->is_active = $request->has('is_active') ? 1 : 0;
        $representative->notes = $request->notes;
        $representative->save();

        return redirect()->route('vendor.representatives.index')
            ->with('success', __('تم تحديث المندوب بنجاح.'));
    }

    /**
     * Remove the specified vendor representative from storage.
     */
    public function destroy($id)
    {
        // Only company can delete
        if (!$this->canEdit()) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $representative = VendorRepresentative::where('created_by', Auth::user()->creatorId())
            ->findOrFail($id);

        $representative->delete();

        return redirect()->route('vendor.representatives.index')
            ->with('success', __('تم حذف المندوب بنجاح.'));
    }

    /**
     * Toggle the active status of the representative.
     */
    public function toggleStatus($id)
    {
        // Only company can edit status
        if (!$this->canEdit()) {
            return response()->json(['error' => __('Permission denied.')], 403);
        }

        $representative = VendorRepresentative::where('created_by', Auth::user()->creatorId())
            ->findOrFail($id);

        $representative->is_active = !$representative->is_active;
        $representative->save();

        $status = $representative->is_active ? 'نشط' : 'غير نشط';
        
        return response()->json([
            'success' => true,
            'message' => "تم تغيير حالة المندوب إلى: {$status}",
            'status' => $representative->is_active
        ]);
    }

    /**
     * Check if user can view representatives.
     */
    private function canView()
    {
        $user = Auth::user();
        return $user->type == 'company' || $user->hasRole('SUPER FIESR');
    }

    /**
     * Check if user can create representatives.
     */
    private function canCreate()
    {
        $user = Auth::user();
        return $user->type == 'company' || $user->hasRole('SUPER FIESR');
    }

    /**
     * Check if user can edit representatives.
     */
    private function canEdit()
    {
        $user = Auth::user();
        return $user->type == 'company'; // Only company can edit
    }
}
