{"__meta": {"id": "Xf1d2216b5005f0557c58ddc2c09a0eaa", "datetime": "2025-06-07 23:06:20", "utime": **********.817772, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749337579.955079, "end": **********.817801, "duration": 0.8627219200134277, "duration_str": "863ms", "measures": [{"label": "Booting", "start": 1749337579.955079, "relative_start": 0, "end": **********.667538, "relative_end": **********.667538, "duration": 0.7124588489532471, "duration_str": "712ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.667568, "relative_start": 0.7124888896942139, "end": **********.817805, "relative_end": 4.0531158447265625e-06, "duration": 0.1502370834350586, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48119080, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00865, "accumulated_duration_str": "8.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7334518, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 43.237}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.752929, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 43.237, "width_percent": 8.208}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.781786, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 51.445, "width_percent": 11.676}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.7862248, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 63.121, "width_percent": 10.636}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.796247, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 73.757, "width_percent": 15.029}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8028882, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 88.786, "width_percent": 11.214}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-381071736 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-381071736\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.794067, "xdebug_link": null}]}, "session": {"_token": "nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1586518096 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1586518096\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1819487538 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1819487538\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1297431667 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1297431667\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1866709315 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2811 characters\">_clck=erumib%7C2%7Cfwk%7C0%7C1984; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; pos_system_session=eyJpdiI6ImVNL2JmZ05iY3huemxuNWZOWkJqeHc9PSIsInZhbHVlIjoia2taNWtSajhLZmtWVmhqZzM2a1VRQ1ZpNk8zdVZaUHlpUGlZVkRwREY2dy80cVN5Rk55RGZldFJIempxdmFVSGdtRDVQRkxyMEtpNU84THFWSlpJQWVjL1p0MzZnSFBQbTFFanNPcytGU1R1cjV0bDAyQkl1RURud1JKeHBNVjZRcUN6WGF5cDhZYlV0UVBGVXY4dURZYTdIY0dZcHdUb2JPcHM0V3FuSFNHblJLSTVwTlZJazU1SG1pZzl6aGZMbHpkOFRTMnRGWDNKSVVrdWNIa29wcGlUMTFtWTI1bTducU92LzZIcXNFamFDZUVlN1FTVHB3bXJSWHh5MFVEVmYzZ3ZRL0EvNHg4bjVNUWhjTFJPTWlGL05GeDNUbUt4d3NJY29rVXVOVGxka2Fkc1cxR0Q4UGxpWGVGVTVMUnU0UTQ1bXBXcmE4ZGIzTnpuWjNtMnQ0ZEdsbjIzOERhTWpFaHNtbHFLRkZQcW1ZQW1zeXdINmNmMERIWDcyZFhTenUrWklDQllEa0k3TUJkM3Uxd3E5UWhZRU1vT283ODNXU3VSVVBvTFZGK04zVDE0MU5YdXNqZVdjS0VJVi9POVJRRU80dkxsYmRpZWlkYXlwV0g1cEZvTys5ckhNUUZFTThmc2VrQWp5RmhWMTUzdlNYY01henM1aXFRUFZFRHoiLCJtYWMiOiI3ZmVhMjFjNGMwODdjZWJkNDZjNzY1NGVkZDYxODkxZTUwOWExNWIzYTE4YmVmMDhiMzQ4YjkzNjM1NDE3YzY5IiwidGFnIjoiIn0%3D; _clsk=vcxus7%7C1749337576086%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImR4aStiTVhvTjh1ZEE2Z3Q3TDFMUUE9PSIsInZhbHVlIjoiNmpGeDNtWFRCcDlRM2Z4NEpaTndEQzRacnRSSUdwY3lrME1FUWllaW45N2FNMmlTRVlSV2NURkxvbkxmTGZ2VFN6dUM1MnFwUmZTMVRJMHh6aXdFbElZZXFGL3FmQkF6WVVwOE03SHZDOTVoSTRhWWczMnZJT1BVYkUwbzBrRUFTK254UGhwc2c1N0NhNHl3WGZrcWJzK0lFT3orUmI1K3VkZDhVME1EQ3NjN3dXOXJTL09PVGd2dm1pMnM1ekJQWEcxcHJtU2NTSDRKSldRTTR6bXJSZmtCUkRuVGYrN3FzNmJXSWwrYWR0MFhwMDNpK0Y1OTRDZU14SGRMS2hhRmdJVXlJYTVUYnpTcDhwZmV4Vit1TzF1bGVZa3dmUTlnaWpkTVRIQ2F2NWZybTdhclo5ckFXZEp3SmUyMVlGV3pxbUxKZEd3NExDRVg0ZGpPS04zR0Z5ZTVVclF5akRtVFViZTVndmNRenZKMTN4aUJ2WU1lMCtrRFUrbHZzaFhPY2Qzb3NnNk9EVWU3RzBvMlV3bFg1VTVJR3Zjb2RDMHJiODdqellETkw5Mytnb2gvRksyUkh0NGhEQWxxYmY1Y25qYkw2UFNtSElSdmhJV2pnV2N1V3pveTJvOUg3UU9SUDh2K2dtci9XT0w4bmR3R3BLTjN2cXFDbnprYU54WTAiLCJtYWMiOiJlYjM2YmQ3YTU4MGY1MGJlZmIyNzZjMzc3YzU5MzNjMzc4NjMyMjU0ODc1OTQ5NDM5ZTZmMzU4MzA1Njk4MGNmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImwrV1NYMnJ4MDQyVjBZNzdyWlIya3c9PSIsInZhbHVlIjoicVpLYjZIS2lIaWNhMTVIVVRNckFnZlhxaGN6QWYzZURkUEtMc0h1OEhhOTBKcDlaK1JDMVNUYjkreG9ERGpNaEx2OER4ZnNhRWQ5RDlqMncySW5DTmllNXcrQk1oV3JNVXZyaGoxMnVBMFFsemh4Wm1TdEtxQjUyTittZXFlL1VkZWNFRXFtQmhGYjdJdVN4TFltZDVGc2YrK3F4dkc3aVYreStpa0NDNVlqYnU0bTRUd2F1ZWd6K0tPU1FPak1VTUs5Z3E4UVBONXE2aXRWYUtkRWhCNEFCVFFKanJoUkZrRUFIdXdBc3YvRXJWaTg5SmJac1JaNUxvM0JrMy82azRNNGg5WmxqTlQxaTBabERBbmRaaHFXUTA1MzdZRGNqR1UrSmFBVjZtK3JHN0VoVjlrdit4S2k2bTN6cDhzQmNzVmNCeDNrWjF6bGYvT0dnbExseUU5bU0zRk5jWEJYc2xXWFZpbDdMaG5oUWUxZDlBc3FrS2dZSGNmM3hKeXF2S1FqeDkrbE5FWERDaVFlcE9pcDZ2dlllYUVnU0RnN2pjTjRwVGNaT3B2am5TNlMranpHdis4aW1zYWkxK3gwSmVUNHNqMkc5d2lsV1RqTW9yRHBSMmtENGlMNnJhSG13eCtnTE8vSzhwaWt5eDZaOVJYZ21HM2RkQmpjVS9LS0MiLCJtYWMiOiI5MDAzZWExODczNWVmMTk0YTlhYWEyNjhkMTg2NTU3NWU0ZDNhZGYzMjAzODY4OGJmMmJkYzMwNDI5MmJhNzY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1866709315\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1503862445 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pos_system_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k8SQqyhwBkgpW9lptEbMoTN8iNDNeZ6DzX94m50O</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1503862445\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-540995809 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:06:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlpdUhlSXVqZzdjY2ZwT2s3cENOU1E9PSIsInZhbHVlIjoiY2ZoZ1lOTnVoTFJkV0ZDSHRiVkRFZ3A5ZWFrNDNBM3NFYzNQY25ZUG9YNjBDMFNRUU5nenVLbExYYjdFMllKd05YWFlCY2hUUTZ2bW03blBlUHpybzRCQXE1VVVnUjlwWlRaL2pPZ1FzQXdvUjNNZUxoK3FCaTVOZ2Z3K2xWOUt2NWdPNEM2bGFDRkszbTVWTVUxYzhWSkRGTmtQb3BFN2hmd0tBY3YxVHlDTThRVE9kdE1hWnVRWUxFNmRDTnBBTGo2SU85Z1FOT25lMGE5LytqaWhNWjU0b2hXWi8vcWtleXVLN24ySzN3blU4TDBwUGNNWWNZSzJuS1laVjVIM0N6OWVxc2ZjUU1lc3J3a21TQ0dLOE9sKzZKUEJ5anFUdUozREtHY3BrZFhid0dSYlJNaGNlb0VDUnZnRkl6VENQaFp4YVU4dXoyQUlUYVRsMTVCaGJNYnBLWUo4aTA3RWROcWlhQXg1eEozckI3UE5nbkh0MHV3T0RNbVVyMW1UQTBHeHVSdnNnVUtXY1JBK2JuSHB4QWhCRnBKMVJKdW1hUTdhYmovNkJuYlUwd2tvTGNzVkNZNzhMYjQveFY1V3ZjUFd6bDYyRG5HNjVSNzV0bmsva2lJQ24xQXAzQlN3eXNjSGsrd1hoeXpiN0dnT3Q5dldoajduYkl6a3ViV2MiLCJtYWMiOiI5NTQ5MmQ0ZDNhMzJlYjVjZTRiZDQ4MWMyOTliZDRkMGVmYjk3ZWU1ODhlNGZmMDUwY2FjNmQyMjY4ZmU0NjAyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:06:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IndyTUNGRlFkbzZsaEFTZy9VOElVeEE9PSIsInZhbHVlIjoiaHcyalJXcy9samZGOUVmMXVJU09RU1ZOT0s3aTBUL0t1RmI1VU1oUmU4YjlwOTB0NUU0bFhNUjZaSDdrUHlqN0xtQ0VrdXVXNEdaOFo5d054Q0NYN2VGZDBjVlpMYjJUNE9NSUEvaTJkNnlSWUZzd0xqMXVsb2NHMG1pbWU0RUI2N0VyemNOQkZWdmJWZlFLejFNY3FBR3lTcE1jOHJDT25DOUhDRlVNNDNGK1JxSjIyN3ZWMklZYmxhWi8vb0twc0dLb3pPbHNSR3FOdGZpQVJreEdjYURCYnV2RTBUWHJTTE8zWVdJRElBWkpJWS9mVE9ZcjZOVjU3d3RhU040WUZhYjlWdnhDWVpoNUN2N1ZXUnJsM3J3ekVhSDd3SlRGb2J1aDdnQW5ZSnhpN1dUaHB4eDlQRW1hOWh2cHFXWWNaVTlFV1d4MjJNT2JVUHZrL2pJUTY5SXNHVEluWlFqdldMMmp4cnpDWStnU1Q4ZloxUlRINy9TUTA3WnUzdEpodmRuNVh3NDh5eEFEOFRqY1grM3N0bjRrV1dBam43Zy9XNGd6bWwrSytTajdqN1hTTmluc1ppYTNpL21jK0hiYWFXdnU3LzVkWnR4MnRRMmEwcXEzc2FrYktDUTZlUEwxdi9mT04rK2Z3S2Nyby9rQldKcE5kcXgxcXpsY1NVeXMiLCJtYWMiOiJlNzAwNzRhN2E3OTczZTBhMzQ4MGI5NjQ2YzA3ZmE3MTFiMDJhMGYxMDJjMjYzZDc0ODZlOGYwNTcwMTM5NzRmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:06:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlpdUhlSXVqZzdjY2ZwT2s3cENOU1E9PSIsInZhbHVlIjoiY2ZoZ1lOTnVoTFJkV0ZDSHRiVkRFZ3A5ZWFrNDNBM3NFYzNQY25ZUG9YNjBDMFNRUU5nenVLbExYYjdFMllKd05YWFlCY2hUUTZ2bW03blBlUHpybzRCQXE1VVVnUjlwWlRaL2pPZ1FzQXdvUjNNZUxoK3FCaTVOZ2Z3K2xWOUt2NWdPNEM2bGFDRkszbTVWTVUxYzhWSkRGTmtQb3BFN2hmd0tBY3YxVHlDTThRVE9kdE1hWnVRWUxFNmRDTnBBTGo2SU85Z1FOT25lMGE5LytqaWhNWjU0b2hXWi8vcWtleXVLN24ySzN3blU4TDBwUGNNWWNZSzJuS1laVjVIM0N6OWVxc2ZjUU1lc3J3a21TQ0dLOE9sKzZKUEJ5anFUdUozREtHY3BrZFhid0dSYlJNaGNlb0VDUnZnRkl6VENQaFp4YVU4dXoyQUlUYVRsMTVCaGJNYnBLWUo4aTA3RWROcWlhQXg1eEozckI3UE5nbkh0MHV3T0RNbVVyMW1UQTBHeHVSdnNnVUtXY1JBK2JuSHB4QWhCRnBKMVJKdW1hUTdhYmovNkJuYlUwd2tvTGNzVkNZNzhMYjQveFY1V3ZjUFd6bDYyRG5HNjVSNzV0bmsva2lJQ24xQXAzQlN3eXNjSGsrd1hoeXpiN0dnT3Q5dldoajduYkl6a3ViV2MiLCJtYWMiOiI5NTQ5MmQ0ZDNhMzJlYjVjZTRiZDQ4MWMyOTliZDRkMGVmYjk3ZWU1ODhlNGZmMDUwY2FjNmQyMjY4ZmU0NjAyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:06:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IndyTUNGRlFkbzZsaEFTZy9VOElVeEE9PSIsInZhbHVlIjoiaHcyalJXcy9samZGOUVmMXVJU09RU1ZOT0s3aTBUL0t1RmI1VU1oUmU4YjlwOTB0NUU0bFhNUjZaSDdrUHlqN0xtQ0VrdXVXNEdaOFo5d054Q0NYN2VGZDBjVlpMYjJUNE9NSUEvaTJkNnlSWUZzd0xqMXVsb2NHMG1pbWU0RUI2N0VyemNOQkZWdmJWZlFLejFNY3FBR3lTcE1jOHJDT25DOUhDRlVNNDNGK1JxSjIyN3ZWMklZYmxhWi8vb0twc0dLb3pPbHNSR3FOdGZpQVJreEdjYURCYnV2RTBUWHJTTE8zWVdJRElBWkpJWS9mVE9ZcjZOVjU3d3RhU040WUZhYjlWdnhDWVpoNUN2N1ZXUnJsM3J3ekVhSDd3SlRGb2J1aDdnQW5ZSnhpN1dUaHB4eDlQRW1hOWh2cHFXWWNaVTlFV1d4MjJNT2JVUHZrL2pJUTY5SXNHVEluWlFqdldMMmp4cnpDWStnU1Q4ZloxUlRINy9TUTA3WnUzdEpodmRuNVh3NDh5eEFEOFRqY1grM3N0bjRrV1dBam43Zy9XNGd6bWwrSytTajdqN1hTTmluc1ppYTNpL21jK0hiYWFXdnU3LzVkWnR4MnRRMmEwcXEzc2FrYktDUTZlUEwxdi9mT04rK2Z3S2Nyby9rQldKcE5kcXgxcXpsY1NVeXMiLCJtYWMiOiJlNzAwNzRhN2E3OTczZTBhMzQ4MGI5NjQ2YzA3ZmE3MTFiMDJhMGYxMDJjMjYzZDc0ODZlOGYwNTcwMTM5NzRmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:06:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-540995809\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-477353641 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-477353641\", {\"maxDepth\":0})</script>\n"}}