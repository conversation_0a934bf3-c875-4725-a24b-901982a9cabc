{"__meta": {"id": "X3108760a0165809468bb1a2a25fa2990", "datetime": "2025-06-30 15:44:10", "utime": **********.096026, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751298249.667204, "end": **********.09604, "duration": 0.4288361072540283, "duration_str": "429ms", "measures": [{"label": "Booting", "start": 1751298249.667204, "relative_start": 0, "end": **********.021806, "relative_end": **********.021806, "duration": 0.3546020984649658, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.021815, "relative_start": 0.3546111583709717, "end": **********.096041, "relative_end": 9.5367431640625e-07, "duration": 0.07422590255737305, "duration_str": "74.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48095536, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00625, "accumulated_duration_str": "6.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.053896, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.12}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.063585, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 25.12, "width_percent": 5.6}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.076797, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 30.72, "width_percent": 6.4}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.078468, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 37.12, "width_percent": 5.28}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.083086, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 42.4, "width_percent": 37.12}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0877008, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 79.52, "width_percent": 20.48}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-505956243 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-505956243\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.081976, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-846825992 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-846825992\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-769449811 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-769449811\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1096914328 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1096914328\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2110483703 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751298248439%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImgrVkFFN2dtdlhXQjF0TzRIMHJ2cHc9PSIsInZhbHVlIjoiWVFCZzNaQ1N3TU9zNDZUQTFsWmR1eFVBTm1KbFNvdm81N3lWT3JPRW5Hd0tzaDduMGg3M28zdU1ERzdwb1ZnUkM3bWhCWmoxeG9ROXNQTVRMV0lZTmVFaGtXZlhRNjlpWXlkT2lCMWpMdllOLys0d2xLc21qYXQ4YlN5MWVIY3Z5N1QyYkplZ3pFcGxtaFM2bVgyc2lIb3UwbjNLK21XUkIzc1JTbGo0bHVOOVBlaXY3OERUQlpyU1dRVGFUVTRxbDRyRE9iZ3ltZ25Eakttb1g3S3VhNDd3VVBGVWYzU1AvMEJNd1NwNktySmNoM05Sa29ONG1IVGVTMkY1cmdkaHNCc3NrdW9WZ1E1UkNScTg3bGZVbU9meUdxc2VYeUJXYU1rRWRjMjFUdlZvN0tVRVRqOW1UL1YxYXpvNDBJZ0RzZWllaUYwYXYveGdhMjRaWWMzVkp5RkpUQld6S25JVFRnaXI5NGR0Um9ueGo5bENVSU9qcFBoandqTHgzb1ZCVWdCdWxiVytCZmxud3U3c3pscm5vbDd4TkhsRng2ZWRLM256NlBQaERRblJWSkZab3p0dWZkTkltdGhZZmozNjhVWFlESGxCdnhvUDJQejUxRHVkbUNkdS8yVDJkL3hWNWdpZUZSMVUyVVAvYXdrNmNETEJoTmtiakpIaStYL0QiLCJtYWMiOiIzZDBhZDE4YmM2MmYwZGRhMzUzNDVlNDZlYjFiNDlhODg5NTQ5OWM0YzAwZjc1MjhjMmQyOTJhNjZhNDYwZDg1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZNaHRWOUprVm1hVDBSalc1MUluR0E9PSIsInZhbHVlIjoiUGVJaEVyWXd3aXN1Sm52WTk5cjRJaVJXU3dCd1Z3bzQ4ckVMN3Z3cGczdUNZWm9GZFpMaEJ1N28yTnhISGViVFJ4dVA3Z29YV0J0anVzckN0N1UvdGtyWUlPTXFnYXllYm5lQnBJOEFST3RMbXhSWDQ2VFZvOTk4VXhpWm0xN0oxR3BtbDhMdkprN1YvRWI5V1laQnpLZE0zSWU1Y0JlL2JnWkxldWRPQVp4M0RhZHpsVlBvYzBpaGM2Q3U2WjQ2aUFsODlVQktIaUNYL09JcjdaeUFBK2hyMVZJbkVkaWJadDdWTHJmUHNvWHhmR1ArMXcwVnB4VEtCL3p0TURYZ1pZT3BLTmZGUTRsZlFzRXhhbGJSc042RG9tdURIdndWS0s3TTNHRFFoTW9Sb3NwbXVqYVE5SkViMEsxUXhoNGR3R0kvM2dhbk1aS21mVTVJeEJQaWxSZGowS0NsYnFtQTVnV1QwY3piUmZoN2hEdk1xcy9oZnkxQUF0SnF4cGltMk40c0JUZzRDT2tUaXQ3cm4rakR4bjJibzVFWDd3OTZKV2h5aFZNUnZZTkMvcHdMUEdRWVdRSWRTWkcvVHpCOGRGdlpsU3IvN3VaU051NVl3M0NRMGlBNVB2ZHcvelE3MUhORDl1ajE0cVAwdElZNlVuV0ZOdDR2dHY4akMzYnQiLCJtYWMiOiI4MDhkZWE5NzBmMGMxYjE5MjJmMGRhMGFiODcyZjg4MDlkOTMwMzE0MTM4NTQ0N2FlOGFlYTc3N2U4ZDA0NDllIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2110483703\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1229251806 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1229251806\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-806786723 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:44:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZNVEtIdTJUcEpHZDJ3T2ZwYStPNFE9PSIsInZhbHVlIjoiUmFpM0xVMU9tN25NTDVEbXgwaklEcGQveDNYSGFXb2JHMTNyUTJaTHFRMnVWSEY4MXBtRWxPTjJGOTdxL3FjRFBjbENtYkRqeEFJV2Z6eEdDWVorT0lnNThDNGFjTTNrLzYyS3k2Znpnd2RYTUs3K1N6NEFUbTNWMi9NUFVsWEdwdmMyT09jVHRERkpKQ1JhRHZzOHJzdlZnZS96Q1BmWGxwVFpQb2lKVGhScGtleE00eko1MWQ5MzdvRmtiRm1COGFzZGdNcjlWYThaTVF1SDkrZWZnV0ovSmNMcEkxVGJYZVQvWTFJWVRrVHhQeVVoRU9VSEJZWUp3NnVHVGMzTEI1Y1RER2t1Q3VnRWtPWi9XVU5WQnRBV21rTk1NQVFQSjYycUl4TVF2S0Q4NWlmRTZ0N3R6TXRFNXd1eEZMTXBFOHhvYlhWMm1sUEpBWDhNbnlrL09UMHZkZTQxNlNpUU9QTEloNTJBRm9xWDFrMm1PODVjZUsvV05yUitxVncwWGZZa2kxQVFFV2phSmF4dlF1RlVEV1BGUk9nekpuTmdEbTA3QlJLcW1tUEZhT1FmelZvbU8xSnZucE1DRGttV08rTEVHbWJXSm9sUkE4S2ErV2puYlFkNWFESUEyQnZob0RIbHNxRkhPWnZpbG0rWDBNd0h4M0pTd2c1Z0gxTEkiLCJtYWMiOiI5YzkwYWIyODM2ZmJkNGVhMTZhMDNhYzYwMzE5OTllNjY4NWYxNDhmNWE0MWFjOWFiNTNhNjU1N2E1YWVjZjJkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InpZd204RW1pdTdqL3ZKanpmc0lCRVE9PSIsInZhbHVlIjoiTytsQ2ZEVkRqYXBKUGpxYjh6V2c1cE5SdnpTT3NTY1AvZ2U4c0dsMGh0eEZKSGMvMEpJRk9GZEtUK2grLzNJUHcwd1c2MS9JNTJNcFBEWmRFU1VrSmkza1ZHRU02QUhRUGlRbEFQd2Rqc0RUdVovTlM4bjQvNzk2ejJWZzU1QVlYdGZ1d05SdkEzVkNVWVFMUzdTUkM3eU5vSU1KWldsejdzQjllV3hoTTNML1JJMVRoS3BBZ2NNSm1TaUpsUWdKdC94SUhaZlVKZlpTNzl4ek9GblRrdWNQSjh4Tk9lTndCVUFOdW5IRVVyVERwTDB5eEFnZmR1RzFGYnEzb1ZkVisyR21Wc29ReG9ISnZRZHBQQUJYVjBFdklpTXZOUTA0UTJ4cUN4TUZ4Mk9wSmxncC92TzJoV0syRFMrR1czUWJkT09QR1BxOStqam5qR2FxeUllSjlwc1VSQ3psSjRtNkN2OTgzQmhlRGJPSTFYV0J4QzdCQXRtV1Z5Q0wyelhpQmZQNWIvMTM2TXd2SkRsZDV4cnFESkljNldlaGtBazdERWR4djc1TVpKdElnY05hRVZmMVYzSDBVOEtzWUkybks3cXVGS0xMcUZkbFpzYnRFMnhxVjhOODNMalVZRU1jUFBwdUdja2QySElCRVhRQzdUd1RtcVJ0MFpOY1E5djUiLCJtYWMiOiJmMGQwMDQyZGEyNDE3ODE2ZWYyYzU3OGIwNDNhNTlhYjc3MGQzNTJkOTlhZWM0YzM1YTY5OTY4N2FhNTA0ZDQzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZNVEtIdTJUcEpHZDJ3T2ZwYStPNFE9PSIsInZhbHVlIjoiUmFpM0xVMU9tN25NTDVEbXgwaklEcGQveDNYSGFXb2JHMTNyUTJaTHFRMnVWSEY4MXBtRWxPTjJGOTdxL3FjRFBjbENtYkRqeEFJV2Z6eEdDWVorT0lnNThDNGFjTTNrLzYyS3k2Znpnd2RYTUs3K1N6NEFUbTNWMi9NUFVsWEdwdmMyT09jVHRERkpKQ1JhRHZzOHJzdlZnZS96Q1BmWGxwVFpQb2lKVGhScGtleE00eko1MWQ5MzdvRmtiRm1COGFzZGdNcjlWYThaTVF1SDkrZWZnV0ovSmNMcEkxVGJYZVQvWTFJWVRrVHhQeVVoRU9VSEJZWUp3NnVHVGMzTEI1Y1RER2t1Q3VnRWtPWi9XVU5WQnRBV21rTk1NQVFQSjYycUl4TVF2S0Q4NWlmRTZ0N3R6TXRFNXd1eEZMTXBFOHhvYlhWMm1sUEpBWDhNbnlrL09UMHZkZTQxNlNpUU9QTEloNTJBRm9xWDFrMm1PODVjZUsvV05yUitxVncwWGZZa2kxQVFFV2phSmF4dlF1RlVEV1BGUk9nekpuTmdEbTA3QlJLcW1tUEZhT1FmelZvbU8xSnZucE1DRGttV08rTEVHbWJXSm9sUkE4S2ErV2puYlFkNWFESUEyQnZob0RIbHNxRkhPWnZpbG0rWDBNd0h4M0pTd2c1Z0gxTEkiLCJtYWMiOiI5YzkwYWIyODM2ZmJkNGVhMTZhMDNhYzYwMzE5OTllNjY4NWYxNDhmNWE0MWFjOWFiNTNhNjU1N2E1YWVjZjJkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InpZd204RW1pdTdqL3ZKanpmc0lCRVE9PSIsInZhbHVlIjoiTytsQ2ZEVkRqYXBKUGpxYjh6V2c1cE5SdnpTT3NTY1AvZ2U4c0dsMGh0eEZKSGMvMEpJRk9GZEtUK2grLzNJUHcwd1c2MS9JNTJNcFBEWmRFU1VrSmkza1ZHRU02QUhRUGlRbEFQd2Rqc0RUdVovTlM4bjQvNzk2ejJWZzU1QVlYdGZ1d05SdkEzVkNVWVFMUzdTUkM3eU5vSU1KWldsejdzQjllV3hoTTNML1JJMVRoS3BBZ2NNSm1TaUpsUWdKdC94SUhaZlVKZlpTNzl4ek9GblRrdWNQSjh4Tk9lTndCVUFOdW5IRVVyVERwTDB5eEFnZmR1RzFGYnEzb1ZkVisyR21Wc29ReG9ISnZRZHBQQUJYVjBFdklpTXZOUTA0UTJ4cUN4TUZ4Mk9wSmxncC92TzJoV0syRFMrR1czUWJkT09QR1BxOStqam5qR2FxeUllSjlwc1VSQ3psSjRtNkN2OTgzQmhlRGJPSTFYV0J4QzdCQXRtV1Z5Q0wyelhpQmZQNWIvMTM2TXd2SkRsZDV4cnFESkljNldlaGtBazdERWR4djc1TVpKdElnY05hRVZmMVYzSDBVOEtzWUkybks3cXVGS0xMcUZkbFpzYnRFMnhxVjhOODNMalVZRU1jUFBwdUdja2QySElCRVhRQzdUd1RtcVJ0MFpOY1E5djUiLCJtYWMiOiJmMGQwMDQyZGEyNDE3ODE2ZWYyYzU3OGIwNDNhNTlhYjc3MGQzNTJkOTlhZWM0YzM1YTY5OTY4N2FhNTA0ZDQzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-806786723\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-239871091 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-239871091\", {\"maxDepth\":0})</script>\n"}}