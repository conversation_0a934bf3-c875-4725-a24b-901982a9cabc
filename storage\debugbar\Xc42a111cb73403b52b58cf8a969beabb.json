{"__meta": {"id": "Xc42a111cb73403b52b58cf8a969beabb", "datetime": "2025-06-30 15:44:17", "utime": **********.79214, "method": "GET", "uri": "/add-to-cart/1621/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.388688, "end": **********.792152, "duration": 0.40346384048461914, "duration_str": "403ms", "measures": [{"label": "Booting", "start": **********.388688, "relative_start": 0, "end": **********.704219, "relative_end": **********.704219, "duration": 0.31553101539611816, "duration_str": "316ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.704228, "relative_start": 0.3155398368835449, "end": **********.792153, "relative_end": 9.5367431640625e-07, "duration": 0.08792495727539062, "duration_str": "87.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48582440, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.01893, "accumulated_duration_str": "18.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.736027, "duration": 0.01439, "duration_str": "14.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 76.017}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.758453, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 76.017, "width_percent": 1.902}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.7708461, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 77.919, "width_percent": 2.536}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.7725549, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.454, "width_percent": 3.064}, {"sql": "select * from `product_services` where `product_services`.`id` = '1621' limit 1", "type": "query", "params": [], "bindings": ["1621"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.776887, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 83.518, "width_percent": 2.113}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 1621 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["1621", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.780391, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 85.631, "width_percent": 12.256}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.783918, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 97.887, "width_percent": 2.113}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1735349340 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1735349340\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.776123, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  1621 => array:9 [\n    \"name\" => \"ورق لعب اون\"\n    \"quantity\" => 1\n    \"price\" => \"5.00\"\n    \"id\" => \"1621\"\n    \"tax\" => 0\n    \"subtotal\" => 5.0\n    \"originalquantity\" => 14\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/1621/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-10182423 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-10182423\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751298248439%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5tOVU5YUJDOHhITDdsSmxuWlE4ZXc9PSIsInZhbHVlIjoiWldnTEtjbEV5OWlxRWFhVHBPc2RWc2VwVFhoSWk5dWZla1hBdHErS0xjTHJncGFWVnNxK3lGa0RsTHF4TW95c3RKZDMwZ21vWFJ2Skt6VHJzNWFsNTllM3VCUFUwRDM5N0xvaXZHRGM1K1ZkbFZEbmpjRXF6WE9xbkpGS2doeWlYTmpubldVWU1RS2tOekVZa1dMWm1yU2xpeE00VmdSTmNGbVk4TldkSmxjRUc3VHA2bXR3UkwvVVNJQkUvZjRMNE9mOXE4YnNoV014NEZTUTAvUE5yZjBodWlxaHlWQ01meXcyalZHMWROeUU0MTdPYU9iNlpLeEkyQXQxSjMrWnIzdGExbEJnY2VIYlptTUtwM1RkbVM2cDRrOU12bDc1aCtVMndwVENnNnFBQ3B1c3dROHFTaW5KSDk0WTJVZjR0T2lmS3FyUnpDb2hvaXZvSEhKWmoyR3k4SG5sSy9ZMEYvUGxYVWs2b2owS0VxeGxTVUlSUDh5b1ZBY21mWDZUN2VUMFArL3E1Z21vS2s2K0FRS0cwMDB1Vzg5VWxIWFlLZHNmNzIzSHZwMFU1TE1SdjFKR0J3MjhobXdFRHlrdEZSZll1SUxNYkM1VS9KUVNSTzVWUmxmVGRmOUNUMWdyTnlMOXhhVU96bXNWK1Z2emlkSjJNUnNUVkM3QWRKQW0iLCJtYWMiOiJmN2IxY2Q4ODFiMGY2OTYyY2ExODA3ZjQ2NDhlOGE3YTE5NjQxN2M4NTYxMTRkNWQ5NzQ4NjY1MWE3NjRlMDI0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjlDWTIrK254NUw2a3JlemlNbThxUWc9PSIsInZhbHVlIjoibFpDWnd2TjJzcEQxdFdJdlNHRlI4V1hadVQxbTh3dXpjUUtBelpZanhNdTh1OHU3WjA4bDBvbUNoL21BZDd5TWxSQW5RWXgyZFhHT1lBaFBGUVMzTEtHSk5VR2N2RFp3eHM1ZVBSdUFrSGorSERhWmZsRGhmVU5CekI1b1dhMXhaQXBHQmtQRVlRMmUvbzlyb3M1N2QrQm9EMVJLRyt2SE92Wk84VXpZU1RjVkUxcmYyUzVZUzQwdjZWYzdnTDFZb2RRa0s5Y3B5K29QdU84M2E2K0s2bWNJbkptRG9hV3E3YkdpUWx6Y3pnY084QzVpTHBvcGt1bmFOekZSTzNsYlR0Y3htWExiT0o0Rjk2aXRneXNLVTFVcWZCdTlGTFl0cVBsUGxiQzBFd0VsNExGNHljeVRzZ09WcVRGUFF5V212THdnWWhmMGtGSk1HblRzdHdKeTRxNTBEZG5zcVdhcnhDUWkyOENscHEzY0hIV2tNU2h2d0VlNXJqL0x3eHpGUmZ2SkZ2OEwyRHZRQ1d2ZTNaK0MrWUFIWVJoNUFJVTVWMDJMbFZvMDQrSzRvZE9qVzJONU9hRmFya21MVGV5NTUxTnc1bWU3cE9vdmhVTE1DU1JBY2JOZU5zdGtRQnZYanpzc1B5K3E4V25hSU5La3hoNGJiTVQ5Ynp2NU44WWoiLCJtYWMiOiIxNjIwZTNiOWUzOTYyZjZmZGRlNjZiMWEyNzQ5MTZmOWJmMjlhMzczYzFlY2FkODczMzhhMmU3ZGNmOTE4ODMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-892550914 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-892550914\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-677765296 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:44:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImltdkNaK0J1SUxtQ2luUnFtdFFsRmc9PSIsInZhbHVlIjoiVlBCMmR2UGJQclpSaEJBUkxTSXVpVDJwRVM2UzZ1YWIvTjNMM0lnVjdMOGo3dFFDb1dIL2QxZHNQWmpxTi9qU281eTh1VnlKNlBLSERQbWhFMFVNbkpHU2JWemFFS0lrYzZQYWlsV3pHNDEvbVJGeVgva1ZJZEwyaFdlVk1Eek82Q3dPOFFNQVBZTWZqYjJpSXFTYU9mSUR1V2xseFpmOTNxZlhOOG5pZGFtSVJJd2xHa3Fxbm8vVWE0Snk4SWZLWTBrZHhwSURMZ1lXdTloMEZEYlAwRm5oS2ViM2liQUJwT1J5b1dqdkZkcTJYWm9mQnI2Ti9Pb1lmMWUvdHpzci8zUm1WbkUzL2dvWmgxaU8xRlY1OXZyTnlYQkIyN2M5S2dtdVc5M3ozY1puL09oU1U2OTkrOTE0UUd6QmdzTDdrUzNYY1U3WTNZcytEb1pQRVIvMW5Pd3NtVko4MklIVWhyVmVCTmZ2aXREcnVhejF3WVNsajNoaGZqRDNETHE1bTN2RnBCZHhibmpuRmZrRHp1eExtc2FKNkJYNEI5eVd5M1AvQXo2bnUwTlQ2QWNzTEhzclJOaFBCMkJMMWhCeFdKRHVIZFU4cVZ4a3hLNy83aHV4K1BMQXpIckdST1BSM0EyK2tXU1lZVXgxTytCVHNUalV1b05Ub3VVWk9Db0ciLCJtYWMiOiI2NmM5ODE1ZTIzYWRhNzM3N2JiMDU1NmExMDE1NjI3NDljMTYwMjk2OTVhMTgwZTQ4MjhmNWRjZmEzM2U1NjA0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkF6aXAxM0J3anZqTU1Gc2hBcjRZbnc9PSIsInZhbHVlIjoiOU5TZXJyNHQrRW5ha1RlUGUzSmJlSDBVWmRxVTRUalY4ZU5YMkg2K2pPbUpQdU5LQUNTRTB2UUpHU2FIa1FCSjJFKzYzdnR2d0NhV3V4UVRMbXFqdDZzTXJzdURYZStEbHZmelpLK0VlV20xaUVyTWZPd0M0SXpndytGSlFDN2dFckZGZ1NyQ1psd1FENVF4UkxDaEFXVmlZMEhnbEpvcG9Yb1VhL3JzNU0wSGJ0UGp2K2hLZE9yWGR5dSs4SkpBcis0enBrVXEyQnhBK1YwdUEwcnRaOU5YVFBCZDg0QStlaFg1MzQ0NWRWY210SnpRSEYzRy9mSEk4NHNWTzdQemwwVzFoQ2tFelNydzM5cEsyYmdZdlBoenRvRGZLVlFDNWpLd2loYzNOWXlmWlNreWlnV2x6WDk0bm9YRG5WaE1WUGpRMWpuWU5CUGF3OFpTVWN3YjFRMGNsV0hGVHp5SXZNWUsvU0t0ckVlVjlOKysxTHE4WVVVWmZRdWc5YWF0ZnNHSDFKQmJvSkVaQmdjQ003Z3NvZjF6blJjQmZ5R1V2bDhBcXpROEVpL2haNXNmVGNOdjRmUWxNODRjK0lSYXlxN0plR0VXbFpTYlhJbnNIZGh5QmcwNVJIajl6VXZFVDBxSUVRRmR3T0Y1T01hNWZUU2hPd3I1aUoxS2cxREQiLCJtYWMiOiIyMGY2ZDNkNzhiNDhkNWU2YmJmMDJjMTQ0ZDE3MmE2NjU5ODI0ODk4NjAzYzg4YjQzMWZhNDQ0YjY1N2E5MDcwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImltdkNaK0J1SUxtQ2luUnFtdFFsRmc9PSIsInZhbHVlIjoiVlBCMmR2UGJQclpSaEJBUkxTSXVpVDJwRVM2UzZ1YWIvTjNMM0lnVjdMOGo3dFFDb1dIL2QxZHNQWmpxTi9qU281eTh1VnlKNlBLSERQbWhFMFVNbkpHU2JWemFFS0lrYzZQYWlsV3pHNDEvbVJGeVgva1ZJZEwyaFdlVk1Eek82Q3dPOFFNQVBZTWZqYjJpSXFTYU9mSUR1V2xseFpmOTNxZlhOOG5pZGFtSVJJd2xHa3Fxbm8vVWE0Snk4SWZLWTBrZHhwSURMZ1lXdTloMEZEYlAwRm5oS2ViM2liQUJwT1J5b1dqdkZkcTJYWm9mQnI2Ti9Pb1lmMWUvdHpzci8zUm1WbkUzL2dvWmgxaU8xRlY1OXZyTnlYQkIyN2M5S2dtdVc5M3ozY1puL09oU1U2OTkrOTE0UUd6QmdzTDdrUzNYY1U3WTNZcytEb1pQRVIvMW5Pd3NtVko4MklIVWhyVmVCTmZ2aXREcnVhejF3WVNsajNoaGZqRDNETHE1bTN2RnBCZHhibmpuRmZrRHp1eExtc2FKNkJYNEI5eVd5M1AvQXo2bnUwTlQ2QWNzTEhzclJOaFBCMkJMMWhCeFdKRHVIZFU4cVZ4a3hLNy83aHV4K1BMQXpIckdST1BSM0EyK2tXU1lZVXgxTytCVHNUalV1b05Ub3VVWk9Db0ciLCJtYWMiOiI2NmM5ODE1ZTIzYWRhNzM3N2JiMDU1NmExMDE1NjI3NDljMTYwMjk2OTVhMTgwZTQ4MjhmNWRjZmEzM2U1NjA0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkF6aXAxM0J3anZqTU1Gc2hBcjRZbnc9PSIsInZhbHVlIjoiOU5TZXJyNHQrRW5ha1RlUGUzSmJlSDBVWmRxVTRUalY4ZU5YMkg2K2pPbUpQdU5LQUNTRTB2UUpHU2FIa1FCSjJFKzYzdnR2d0NhV3V4UVRMbXFqdDZzTXJzdURYZStEbHZmelpLK0VlV20xaUVyTWZPd0M0SXpndytGSlFDN2dFckZGZ1NyQ1psd1FENVF4UkxDaEFXVmlZMEhnbEpvcG9Yb1VhL3JzNU0wSGJ0UGp2K2hLZE9yWGR5dSs4SkpBcis0enBrVXEyQnhBK1YwdUEwcnRaOU5YVFBCZDg0QStlaFg1MzQ0NWRWY210SnpRSEYzRy9mSEk4NHNWTzdQemwwVzFoQ2tFelNydzM5cEsyYmdZdlBoenRvRGZLVlFDNWpLd2loYzNOWXlmWlNreWlnV2x6WDk0bm9YRG5WaE1WUGpRMWpuWU5CUGF3OFpTVWN3YjFRMGNsV0hGVHp5SXZNWUsvU0t0ckVlVjlOKysxTHE4WVVVWmZRdWc5YWF0ZnNHSDFKQmJvSkVaQmdjQ003Z3NvZjF6blJjQmZ5R1V2bDhBcXpROEVpL2haNXNmVGNOdjRmUWxNODRjK0lSYXlxN0plR0VXbFpTYlhJbnNIZGh5QmcwNVJIajl6VXZFVDBxSUVRRmR3T0Y1T01hNWZUU2hPd3I1aUoxS2cxREQiLCJtYWMiOiIyMGY2ZDNkNzhiNDhkNWU2YmJmMDJjMTQ0ZDE3MmE2NjU5ODI0ODk4NjAzYzg4YjQzMWZhNDQ0YjY1N2E5MDcwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-677765296\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-20096722 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1621</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1608;&#1585;&#1602; &#1604;&#1593;&#1576; &#1575;&#1608;&#1606;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1621</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>14</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-20096722\", {\"maxDepth\":0})</script>\n"}}