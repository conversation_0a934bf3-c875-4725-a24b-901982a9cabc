{"__meta": {"id": "X54e14bdc8017e0450a82bc3e950f488d", "datetime": "2025-06-30 14:59:41", "utime": **********.45053, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751295580.83274, "end": **********.450544, "duration": 0.6178040504455566, "duration_str": "618ms", "measures": [{"label": "Booting", "start": 1751295580.83274, "relative_start": 0, "end": **********.26526, "relative_end": **********.26526, "duration": 0.43251991271972656, "duration_str": "433ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.26527, "relative_start": 0.43252992630004883, "end": **********.450545, "relative_end": 9.5367431640625e-07, "duration": 0.18527507781982422, "duration_str": "185ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48095536, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.07335, "accumulated_duration_str": "73.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3193889, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 2.481}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.331078, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 2.481, "width_percent": 0.627}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.347861, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 3.108, "width_percent": 0.954}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.350296, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 4.063, "width_percent": 0.791}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.356183, "duration": 0.06814, "duration_str": "68.14ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 4.853, "width_percent": 92.897}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.440935, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 97.751, "width_percent": 2.249}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-610619827 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-610619827\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.354866, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-479557537 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-479557537\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1720416992 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1720416992\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1825158291 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1825158291\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-447745455 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRRQVc1UWs1ZjJkdzlmVWR6L0N2UkE9PSIsInZhbHVlIjoiNHM5Z1ZTOGFsK2RDVHdkdlArcEsrU0tLbFRtbnFUR3FZSmV6bitocFFsN2c0bkU1MUxTYTNZb2FkbVp0OUdkSEVWcGwyR3FMVHlEWkFJeTd6N25lNkc3ampoWEt1TCtsYldKN3VOSUxoOUdLekVGUWIvSStDbThWWEoreVo3RXozRmFJWmRpVG84cnBHSXpxTjhmdDVJb0RTdXZRbG94UTlpOHNVRjFMYWFhRWVwd0loSTlOQThrRk1vWmNDTTVUUHV2Q0diNS9NVjVTbVFlNmpXenpQY1RQbWlvbS9ObFBRMzhPVXBNVGVOQVZ5VHhieXU5SEpGMVl5UzJXd1BYbk5JemtQMEo1YzYxb1BaYjllSnpRUHdpaTVpNzdQSC8yeEtkc2RldmREZXBQZTF5Y3lpdTFQVHEvaWd2NXBIVjEzemJGUGtVWkltYXp1aXAwcmg0VnFnZkMvZzBzOGRnUHg5STdYZndra2F3SmllYk5TR24rL3VXRzQ2Z28rVzFKUVcvUitVMDdHeHpzTXBCRXRqUlJwNTNLRXVhMENhdm1sWm9XZ1pjOTZWM1RRQVlyRlE0ZjhFcE1qdWVRcVlxV0xJYXcySzRwL0RZQ294ZXBOcm4vRHdoTUdnKzQyNWFTNXEvYUw4QUN4VTc1NzZyZXFpZ3k3UE83aW9PTm9kOVkiLCJtYWMiOiIyMjAzZjk4MmIwYjJlOGQ2M2ZiODQyZjA1MzI0MjIzMGE1Y2I2YmFhZmNhOTQyOTNiOGU1MGJkOWY5MWU5MDBmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InRDZHdZSHl3MkdtVWxIS1liSmF6QkE9PSIsInZhbHVlIjoiZWJRdXpSQlhYalpDK0drM0hGTUxMVkFqd240bDJVMVRrL0ZtVjdjdTJQNmpvSW92ai9SZCt5MnlYRjk0UFBxMkRqTWxVZW9oMTJkdkdla3hRYXlSMTdqSWJnd3RvN1JlMVg0Zzc2VVlyTHJpYU5uZlozY0w4a2FUNlByeUtDZkd4TGcxQ0ptMDR4K1FLdWZONCtKS0tINGJWS1U3V3lnTnU4Q1d0amxLVCs3Zm1pMEN0dGxJT3NjY1BVUGdyYnFsMmtPVUl1b21jRVlNOFdSeUFGTXFGMXdta0xkV3BtbmRHc0lLTEpTY0tYQmJIS002aHNPVEhqdFZvQnY0dFRrd1pYSzZaMjFDNjdFNHdKMS9HYlFITDVXMVl5SmpWcGU0Y25tUnZPVUFrbGFRSEwvYXJvQUFiVytoYVNFTGRLcXBqMUJCczlIODJ1K3d4MytDc2tVSjZDbTMzMTFRTXA4ZkwwR2pGVTJXOWhDa1JYUEZrZWRqWGxoR1dLWGVRVnpQb3RFRVZVSm1Kclp0b0dJMGF6NFVhR0pOaTBPaG4zdy9QaS9YWWRjdzRvNFhmVEEzUjFoeDFnQXNEeFNuRlI4bENtUkJJaE05clNyanJHWm9Edmd6UnVMTEdEMElEYVArQUIzRXM1bE9wWDgwdFlGQ0M0cTdzRVoxcThZelFYbW4iLCJtYWMiOiIzYzE0OWUyZjAwMGZhMjY3NTg0NWNmMTlhYjZkNzA0NTNiODExMjAwZTNkMDY2Y2NiYjMwNGVmM2E0ZmExYmUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-447745455\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-874066425 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-874066425\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1541559505 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:59:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhzOGFFclBhVVFzNHR6WXJxN2JhREE9PSIsInZhbHVlIjoiMk4vU0NxMjk0ZDlZVmt2c1BjVUN0Y3JwNnRiYXdSaktHZnMxQVlGcHBCTlFvQ3NKR1pMN1h2SjU5d245YnVWLzIrUWQySlhBSzVoRkVLdnhDL2FCbHFXczMwcnJiUVJhT2VLOSt2YzJ5TTdrK2xDb2gyNGpvL2JxL2xpWWxEZXYzWWFTR0tJRWNpQ0xHTXJCRXlPRDZxTU9TMkRMMzRlQ0I3UmZkd0ErM3FJbUd3U0xLMVdMR1Y0S3pUSktSRXZTSThUemd5Y3UvSC9zdkNldFdFRUVrQTlhb0kvWVlKa1UvM1BWVUFFSGZENlVIZHZKcHBpT25jV3kwWTBoRkdhSHZHTThHT0VkN0Nldmtsb29ZZWxEUWdHTW5uR3Jra0NuTFBwd1dQRjhNV3pqRVQrbW1mSEk0SDRFYlB5NHZnQkJZRkJVRWVTVTRucFo5dndCNW1kQkN0WFdEYmd6d0ljclVBc3BLYUdZKzBPS3Uxc0hkdFNPZGQrOWtCTjY4Z3dxMHhmV2EwZ0hQQUwvYnhHQTNjZzJsSGdMLy9STVk2cHJNM2tSblV2djdpNktHb0VWeTYxQXJubTdMY3pJNThRY1I1MEt2TjdkTkVndU1DM2dQNk44bWduaXhvR2hnY3JrL1FubDhIZ0VrcEE4eE5vazFoQzcrT3NwZnNocE51ZFYiLCJtYWMiOiJjNzQzYmE3YWJiN2JjNjFiZmY5MWM0NjIzY2QwNTQ5ZWUxOGI3ZDZmYzM1YzVjZTEzMzM3NmU1MTdjNTY1YzAzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:59:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkkyNWRWNDhxb3ZBSlNzOXU0WGVjL3c9PSIsInZhbHVlIjoieXpYM3dBRVNFejFvMkFRUGt0ZnRNbUdHN1ZCaktlMEtYb2JpUWFtNVZ4c0NBbkx2OVdueUtRWnlPei84SFg1cklPT1FwWExnQkx1TmFFMFk1VVM5NzBobFFyd1JmOWRWRTRhRDNnazdIWDUrUFUxaEVTWlduNlRYNW8rR0cwUXh0UjRrL0xtaE9kWXF0d1VlV21ya2tXSUlCZzBCcVVJUUpnM0JmaFJNRWlmMmJySFI5UVBvTmhab3MyalBLRUhPdGt5SUFZbGFYUDhyVUt2OU0ybEhTNkZSdVNQMmtkR0x2TlJabkFNYmFKZWxjUFAyNHVZNHJBcllTWithbTFUSWRLSjQwU2hRNHlLSjQzYzFucHRISDZqcUpRWXVOUHkvR1hoUFVHRVNRZXRScEk1MkRBZkhDQlZ6ODBZRG1RWVQ2QklObHJYVy9RU01HNTZLaFUreUcwSldOUUxlbVRkb1RNZGhiYVpkY0N6dUplRC9SejFiS29qblpEZlBqK2daM2txVThnWXZMSnl6TDNFMW9RU1JQandpd3QyUTZMQWl2SkdRMU9iWWVYRmFCbjdOdmJiNGV1V1RFRHRPKzRVL0wrZkxnQkdpTkNnSEt0d1Bmb1pHaFVrTlo1YjQzQ2lBbStkVHpON05WQlAzbHZDUnpJcGorYTN3MU5aRWZua0EiLCJtYWMiOiI2ODM5MzViYTk5MmM4YTdmZjRkNTgzOTFmOTVmYjdkOTMyZTJkOWQ2YzdmODY3ZGE0NDIyYzliZDE0ZjRlYzIzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:59:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhzOGFFclBhVVFzNHR6WXJxN2JhREE9PSIsInZhbHVlIjoiMk4vU0NxMjk0ZDlZVmt2c1BjVUN0Y3JwNnRiYXdSaktHZnMxQVlGcHBCTlFvQ3NKR1pMN1h2SjU5d245YnVWLzIrUWQySlhBSzVoRkVLdnhDL2FCbHFXczMwcnJiUVJhT2VLOSt2YzJ5TTdrK2xDb2gyNGpvL2JxL2xpWWxEZXYzWWFTR0tJRWNpQ0xHTXJCRXlPRDZxTU9TMkRMMzRlQ0I3UmZkd0ErM3FJbUd3U0xLMVdMR1Y0S3pUSktSRXZTSThUemd5Y3UvSC9zdkNldFdFRUVrQTlhb0kvWVlKa1UvM1BWVUFFSGZENlVIZHZKcHBpT25jV3kwWTBoRkdhSHZHTThHT0VkN0Nldmtsb29ZZWxEUWdHTW5uR3Jra0NuTFBwd1dQRjhNV3pqRVQrbW1mSEk0SDRFYlB5NHZnQkJZRkJVRWVTVTRucFo5dndCNW1kQkN0WFdEYmd6d0ljclVBc3BLYUdZKzBPS3Uxc0hkdFNPZGQrOWtCTjY4Z3dxMHhmV2EwZ0hQQUwvYnhHQTNjZzJsSGdMLy9STVk2cHJNM2tSblV2djdpNktHb0VWeTYxQXJubTdMY3pJNThRY1I1MEt2TjdkTkVndU1DM2dQNk44bWduaXhvR2hnY3JrL1FubDhIZ0VrcEE4eE5vazFoQzcrT3NwZnNocE51ZFYiLCJtYWMiOiJjNzQzYmE3YWJiN2JjNjFiZmY5MWM0NjIzY2QwNTQ5ZWUxOGI3ZDZmYzM1YzVjZTEzMzM3NmU1MTdjNTY1YzAzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:59:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkkyNWRWNDhxb3ZBSlNzOXU0WGVjL3c9PSIsInZhbHVlIjoieXpYM3dBRVNFejFvMkFRUGt0ZnRNbUdHN1ZCaktlMEtYb2JpUWFtNVZ4c0NBbkx2OVdueUtRWnlPei84SFg1cklPT1FwWExnQkx1TmFFMFk1VVM5NzBobFFyd1JmOWRWRTRhRDNnazdIWDUrUFUxaEVTWlduNlRYNW8rR0cwUXh0UjRrL0xtaE9kWXF0d1VlV21ya2tXSUlCZzBCcVVJUUpnM0JmaFJNRWlmMmJySFI5UVBvTmhab3MyalBLRUhPdGt5SUFZbGFYUDhyVUt2OU0ybEhTNkZSdVNQMmtkR0x2TlJabkFNYmFKZWxjUFAyNHVZNHJBcllTWithbTFUSWRLSjQwU2hRNHlLSjQzYzFucHRISDZqcUpRWXVOUHkvR1hoUFVHRVNRZXRScEk1MkRBZkhDQlZ6ODBZRG1RWVQ2QklObHJYVy9RU01HNTZLaFUreUcwSldOUUxlbVRkb1RNZGhiYVpkY0N6dUplRC9SejFiS29qblpEZlBqK2daM2txVThnWXZMSnl6TDNFMW9RU1JQandpd3QyUTZMQWl2SkdRMU9iWWVYRmFCbjdOdmJiNGV1V1RFRHRPKzRVL0wrZkxnQkdpTkNnSEt0d1Bmb1pHaFVrTlo1YjQzQ2lBbStkVHpON05WQlAzbHZDUnpJcGorYTN3MU5aRWZua0EiLCJtYWMiOiI2ODM5MzViYTk5MmM4YTdmZjRkNTgzOTFmOTVmYjdkOTMyZTJkOWQ2YzdmODY3ZGE0NDIyYzliZDE0ZjRlYzIzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:59:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1541559505\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2122202591 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2122202591\", {\"maxDepth\":0})</script>\n"}}