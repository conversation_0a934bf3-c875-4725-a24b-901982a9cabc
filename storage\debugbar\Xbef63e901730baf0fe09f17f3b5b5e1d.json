{"__meta": {"id": "Xbef63e901730baf0fe09f17f3b5b5e1d", "datetime": "2025-06-08 15:43:17", "utime": **********.845191, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.290571, "end": **********.845214, "duration": 0.554642915725708, "duration_str": "555ms", "measures": [{"label": "Booting", "start": **********.290571, "relative_start": 0, "end": **********.761691, "relative_end": **********.761691, "duration": 0.47112011909484863, "duration_str": "471ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.761702, "relative_start": 0.4711310863494873, "end": **********.845217, "relative_end": 3.0994415283203125e-06, "duration": 0.08351492881774902, "duration_str": "83.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45282792, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02174, "accumulated_duration_str": "21.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.798252, "duration": 0.01943, "duration_str": "19.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.374}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8301508, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.374, "width_percent": 3.542}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8349931, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 92.916, "width_percent": 7.084}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 11\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 32\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1517898039 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1517898039\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1118416202 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1118416202\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-95391467 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-95391467\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-*********8 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396510751%7C76%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRaRk0rU0N4VnREMjdjamJTK0x6MUE9PSIsInZhbHVlIjoiYk8rd3V6bGtaeTlRVEk2cTljbDluQ0tQYTBwekVaZ2crNVhORnhHaWdjWE5IV2pPQ25TVkJxS1l3ajM5M0IwSmZUSlkwd0FVWkZ6OFEvSE1hS2tERDZzV0ZSaWRZVmJnS3BZbHJKZm1SRUtYcDE5ZmMrcnpoTm9YUlpvd2R0Y2JxbUd4Ukt1ZDVwYW5hNEtPaUxEdDNUT3FLYzYweXBJWmdHVy9XZnVZYmhuODBlKy80VFZudkJBVTBacnlIanFmZFdZNDVDNmtpdWFjcmVjTFVaT0RZb1o5MlhIYVN4QnlaSUlPd2VDN3lTditudHU0UFVBdXRpOXNsYnF3R0Qra09YRXoyU0VSL0gzVDFnMmVNYllNSGU1SGJwMFQ0bkRGNllMOFBITll5UjZDRVpIQ1VqNDZRUVdETEFPNDVoOU5QaDc1MDBJYUpPeGgrcFpUM3Y4TytPUzZJVDJyaDBRczBlS2ovd21CYjg4cllOZStqeTZ0M3hUU2x4ZGJUU2hra1VEL1RCSHJHT21MNHBmWE84aFdQenAyQWkxRG1hWC9CTDdna2hlZTRIbTZHRFc5K0tYSGw3Yld6b1FuUHV6eTZmVG1pSExNWmJ1SGlKcTd1cUNYdit6MmVHK3Y2ZE5qUnNxNk5HeG9uVmhERXpYOUNyVUwxUkdlcGYzY1FwbEsiLCJtYWMiOiJhNzhjZjUyODlmMzYzMzExYzdhOTRmNzljMmUxMjRiYmZhZDZhOTdlYzE5ZWFhMTk2NTUwODc3OTI0ZWY2MTkxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImYvV3RRZjJDUWxJTjVySlF5cG5aMHc9PSIsInZhbHVlIjoibFlLTVYzNmlWU1hkTElQMWFaeVpyaU1pUWFSTXFKeHU5WWhINmF0NXRsWk1QdW5EVy90Nm85aVJDTFdPMUdReUo0OFloWFZLbDVYQWlBOFJHTnUrVmFHZncxWVhaVnQyRXNKaVJGbmdvVUpJNnVUbGE4Y1dpUjlOYUwwL3RRbmo4cUFsdUtwUnNsdENaM2Y3NFhGRjlOTk1oT0M2VTBjcDhMUVV4ckZPUGVZQlFpM0MvNERZRjVKZzgxOElaeUZ4N2ZOZjBBU2FOUEkyNjQrOGVyUng2Q3hiODFmS2VUTXkzM2s4OGxRRHQxNUxYamZjc055Nzh4WWpqcWZqK0h2d0NXL0F4U3I0cW45Z0JxcWRoa3hIQVUwOEl5blFyK3hFVzQ4QzhwTnJRRWxpR0JlZW1nVklad21QRmxTZG9mMGQ5ZWFaV29OWENNRERodUx6TkRnei94VEoxZWlaMzN4MU54dUxuZmc1RE5VMk5DOHZSZHJKRjRVb2hxZkRhRHYySjIwSzgvaTluVzZqQndzVmo4QUI5TkhZRTYvd2c5cVFNcGFEUHVHN1REVFp6RnJidkpzM1NCZ0JHN01Idlp2TkJ5UnFhMHZrYTMxNk9GUTlmTUsyTVhQU29FeFhjaHUxcmtzdkNmTGM4TTJDL2dnU0pjUlFiWWtRdmVjU0c3V0siLCJtYWMiOiJhNjE2MzQyYTQ0ZThlY2U4NzgyNDI2MzMzZDRjZjE3YjgzMjNiMDViMzdlN2QyYTBmOGQwNjU3MmY1YzA3M2U2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********8\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-141537636 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-141537636\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-483282606 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:43:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlROMEJLcFQvL1ZMaFdsQ3dNZzk0bUE9PSIsInZhbHVlIjoiOXNhVmZsK2RMd1BpOHpDYmRoYjhTSVRKOU1vaFZPdnRMS1hYdjBlWEdEQzdvdXNqakxXQ1RmNEgrR1NJbkdJWmY2T2ZvQUhqaUw5NW9xaW5DbmNUOXN3WHM1eXRsZGN2V0tCT2kvQ1RBY0p0SjNUaFl2aGRaNnRCNmFLbjgveTNQaGtUNjNPOEpxOUFlN3RzRVgwMklYN24yaDBUYjRkK3FtKzVQS3FiT0F3V29KdkxpbTdWUXVqdTl5YTNiUndtUjM3UGZ1aE9IT3ZQelZ6d1pLaDNoQnluNXl5d2J2QjJEeklGd1JKK3lJek5ENDZVcE13NU9jZ3g3eGk5KzBpazU2c0ZvcVJmVGMyNmdDZGhxVFJxUkg1eXQ5cEYrSUE3V1llVVoxMlpLV1I3WjM0SzVpR2NrYUFaaGlvMTZWODNlMzI0VVFiMEJHYUVzQ3pjN05kSnlZT2JDbmlQdWZwOFlNQkZFRXRCQ2M5czcySU9jN1pEQWVCS09pTlNLdFhEcXZPSGtPN2J0anBETDhFRUZ2SFI4ZW95WnVGOFFVa3h5b3ZucXQwNVNEMEkrT1V4c2d6dWtQVDlUQVN0aUdVWCtZRTdOZjV1OHpKNENpU0tlS0NMN3RFclNMOFA1UDNIVG9GRmk4akRDSEVNWlRDRkRZaGxIYjVnaWJjNEJRVEQiLCJtYWMiOiJmYjE0MGQ0N2I1NDY1Nzc5MGVlZTgxNTM3MzFhYjU0Mzg2ZTFhODFmZmY3MDUxNzlkYTE5YzIzNmI0NDAwZTE3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:43:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImpxeHVVWUJBdTdicmJLbHcxTTRONkE9PSIsInZhbHVlIjoiZ1o0dlVIUDNWOGVnT0pVNHRXNGdiSTliODl2Y2NyQUxHY2R6TUxBb0FKQW5nVm5ibUJqdjdacUQxMFNDWm5mcFkrUndtamN3TVJPMDJsNVJuYVVkTjlzWFQ3RXVMNVZ2RnRXMEJoeHZRS2o3cXY3MXREWVI0THI3Y2p0Rk11OTJZQW9WZTg2ZUluZ2tVK1BxRm54c0YxTVFrRlRLT09JaG1WcXY5T28yUGRNWXNRZm5JOHpyR0VUM2ZCZDFsbEZtRUZSMzJycUVPZklRaENTdVg5cGlaZThJTGZzL0tDeEJWZkp5SGdMMUVZYU9UMzYzSkhqVGJZWEJobnNVZkMwQUxOVXNublZiZVFia0hEWm9LNDhYNWZISjBVQmdwQ280ZTRnU040VzFTT1Q2aEhiTThBTWRYYm1DK1hxMXZGTjVtUlhDOUlNRFR6RVZ0QWs5Q2VseU9yMkJHQ2RuOXJnYTNwYlBneXlNNmhyWG1oWHBRekxjYkxsekphN1VCb1RLRUNrTWxBdkV0bW5xd1hFN0MrMXZxTEtCT3pBN1VIT3M4SlFXTC9JRWdVMnNnWFdwT0piNG0ydzhDZUpYV1UzV1lxeUJ2UVIrTEV3M0I5emZpY0htV0R5UU5KL0pQbktEUHBlOFM3bTZWSVNsdk5od1hqQmYyRmgyZnkwY0xGVlAiLCJtYWMiOiIwMGRiYjBhMzRkNzYwYTVhODk2MTYxZjRhNWM3YjkwZjlhZTZlNTk0ZmM4NjViMjgwMzQ1YTRmZGRjMzYwODhjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:43:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlROMEJLcFQvL1ZMaFdsQ3dNZzk0bUE9PSIsInZhbHVlIjoiOXNhVmZsK2RMd1BpOHpDYmRoYjhTSVRKOU1vaFZPdnRMS1hYdjBlWEdEQzdvdXNqakxXQ1RmNEgrR1NJbkdJWmY2T2ZvQUhqaUw5NW9xaW5DbmNUOXN3WHM1eXRsZGN2V0tCT2kvQ1RBY0p0SjNUaFl2aGRaNnRCNmFLbjgveTNQaGtUNjNPOEpxOUFlN3RzRVgwMklYN24yaDBUYjRkK3FtKzVQS3FiT0F3V29KdkxpbTdWUXVqdTl5YTNiUndtUjM3UGZ1aE9IT3ZQelZ6d1pLaDNoQnluNXl5d2J2QjJEeklGd1JKK3lJek5ENDZVcE13NU9jZ3g3eGk5KzBpazU2c0ZvcVJmVGMyNmdDZGhxVFJxUkg1eXQ5cEYrSUE3V1llVVoxMlpLV1I3WjM0SzVpR2NrYUFaaGlvMTZWODNlMzI0VVFiMEJHYUVzQ3pjN05kSnlZT2JDbmlQdWZwOFlNQkZFRXRCQ2M5czcySU9jN1pEQWVCS09pTlNLdFhEcXZPSGtPN2J0anBETDhFRUZ2SFI4ZW95WnVGOFFVa3h5b3ZucXQwNVNEMEkrT1V4c2d6dWtQVDlUQVN0aUdVWCtZRTdOZjV1OHpKNENpU0tlS0NMN3RFclNMOFA1UDNIVG9GRmk4akRDSEVNWlRDRkRZaGxIYjVnaWJjNEJRVEQiLCJtYWMiOiJmYjE0MGQ0N2I1NDY1Nzc5MGVlZTgxNTM3MzFhYjU0Mzg2ZTFhODFmZmY3MDUxNzlkYTE5YzIzNmI0NDAwZTE3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:43:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImpxeHVVWUJBdTdicmJLbHcxTTRONkE9PSIsInZhbHVlIjoiZ1o0dlVIUDNWOGVnT0pVNHRXNGdiSTliODl2Y2NyQUxHY2R6TUxBb0FKQW5nVm5ibUJqdjdacUQxMFNDWm5mcFkrUndtamN3TVJPMDJsNVJuYVVkTjlzWFQ3RXVMNVZ2RnRXMEJoeHZRS2o3cXY3MXREWVI0THI3Y2p0Rk11OTJZQW9WZTg2ZUluZ2tVK1BxRm54c0YxTVFrRlRLT09JaG1WcXY5T28yUGRNWXNRZm5JOHpyR0VUM2ZCZDFsbEZtRUZSMzJycUVPZklRaENTdVg5cGlaZThJTGZzL0tDeEJWZkp5SGdMMUVZYU9UMzYzSkhqVGJZWEJobnNVZkMwQUxOVXNublZiZVFia0hEWm9LNDhYNWZISjBVQmdwQ280ZTRnU040VzFTT1Q2aEhiTThBTWRYYm1DK1hxMXZGTjVtUlhDOUlNRFR6RVZ0QWs5Q2VseU9yMkJHQ2RuOXJnYTNwYlBneXlNNmhyWG1oWHBRekxjYkxsekphN1VCb1RLRUNrTWxBdkV0bW5xd1hFN0MrMXZxTEtCT3pBN1VIT3M4SlFXTC9JRWdVMnNnWFdwT0piNG0ydzhDZUpYV1UzV1lxeUJ2UVIrTEV3M0I5emZpY0htV0R5UU5KL0pQbktEUHBlOFM3bTZWSVNsdk5od1hqQmYyRmgyZnkwY0xGVlAiLCJtYWMiOiIwMGRiYjBhMzRkNzYwYTVhODk2MTYxZjRhNWM3YjkwZjlhZTZlNTk0ZmM4NjViMjgwMzQ1YTRmZGRjMzYwODhjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:43:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-483282606\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1964854043 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>11</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>32</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1964854043\", {\"maxDepth\":0})</script>\n"}}