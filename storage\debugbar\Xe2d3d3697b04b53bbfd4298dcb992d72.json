{"__meta": {"id": "Xe2d3d3697b04b53bbfd4298dcb992d72", "datetime": "2025-06-08 00:28:25", "utime": **********.489837, "method": "GET", "uri": "/pos/create?vc_name=7&user_id=17&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342504.444331, "end": **********.489862, "duration": 1.0455310344696045, "duration_str": "1.05s", "measures": [{"label": "Booting", "start": 1749342504.444331, "relative_start": 0, "end": **********.279262, "relative_end": **********.279262, "duration": 0.8349311351776123, "duration_str": "835ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.27928, "relative_start": 0.8349490165710449, "end": **********.489866, "relative_end": 4.0531158447265625e-06, "duration": 0.2105860710144043, "duration_str": "211ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53208888, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.show", "param_count": null, "params": [], "start": **********.464579, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/pos/show.blade.phppos.show", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpos%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.show"}]}, "route": {"uri": "GET pos/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "pos.create", "controller": "App\\Http\\Controllers\\PosController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=116\" onclick=\"\">app/Http/Controllers/PosController.php:116-218</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.02035, "accumulated_duration_str": "20.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3465722, "duration": 0.0068, "duration_str": "6.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 33.415}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.366972, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 33.415, "width_percent": 4.423}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.396195, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 37.838, "width_percent": 17.346}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.403374, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 55.184, "width_percent": 11.302}, {"sql": "select * from `customers` where `id` = '7' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["7", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 130}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4143, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "PosController.php:130", "source": "app/Http/Controllers/PosController.php:130", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=130", "ajax": false, "filename": "PosController.php", "line": "130"}, "connection": "ty", "start_percent": 66.486, "width_percent": 10.713}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 131}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.419802, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "PosController.php:131", "source": "app/Http/Controllers/PosController.php:131", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=131", "ajax": false, "filename": "PosController.php", "line": "131"}, "connection": "ty", "start_percent": 77.199, "width_percent": 6.683}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 517}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 135}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4277349, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "PosController.php:517", "source": "app/Http/Controllers/PosController.php:517", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=517", "ajax": false, "filename": "PosController.php", "line": "517"}, "connection": "ty", "start_percent": 83.882, "width_percent": 10.663}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "pos.show", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/pos/show.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.477661, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 94.545, "width_percent": 5.455}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-254989692 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-254989692\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.412481, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1765803232 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1765803232\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.426419, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 15\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/pos/create", "status_code": "<pre class=sf-dump id=sf-dump-1168139340 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1168139340\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">17</span>\"\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-69582988 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-69582988\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-780849014 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlMyM0pUdGxQTVhxZ3pYQjlaZkFlRFE9PSIsInZhbHVlIjoiOStjSWlrejZObjU3dkhMOUZkZUJ5Um52cVZmSmlIMGVsV2ZmNFowYWFPVnEvMy96aDlBay9EYUdKNEhzVmJWY0pYSjR1aGVVRVdzTlBnTllxU2hNZW9HZW1ZNzJoNnF4ejY4bnZGd2NDZ1lNMjNaTUNxTDZoMXRURFpqbXFYdU5HL2hEYXppNHJscXFvRkJHT1hDOVh2eUJTVm9pR1lQN2h4U2ZzdExEWlluOTdqclZMZzdFYzh4YzdPYy9SMCtVTGczYWRRbmo0dUgxTW9rMXY5WmpyalI5MlJweEtOb2ZNOWdxcW0wMHJwN295VjM3N1VYYVAveVIySkl4VzV1Vm53cnZFZERwK0lNSEUyL3RPaUppYkFhVkgySlE0dmpydmI3ZmZEZUllbDNOYkVCeVhjVTZtUEFQQXhKMG1nczVjTnBYQncwSm10d2RTUkxsZjBOTVcyTGFwYnFveERtbWcyek5qbzJ5UGRodGRzM21NU3o1SzhKY09VSUUvTy8rcitQNzJzMDBxckdaUE52dWRKRTZDQStGaC9GV2FBWFBHL005UVJTSmlFSlRSN1VmTTdIcHJhVzk5QkxMZ1czaVpoNjZHeGVtdTlzRDJoTHROeXFVbWFmcWIra2VZZ0NUMzU2TlJ1d3lMYUdIMXlIeG1IaXFHYzBVSE1CSngvbmgiLCJtYWMiOiJiYWYxZDM0ODg0MDIwMGM3ZmQ5OThhNjVlNDc4ZDFkZGQzYjIzMmQyNjI2YzMwZGJjMGVkYjBmYjFkNDQyZGY2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlpMQzlMNGNhdDZUUWoyWHZOeWs5WFE9PSIsInZhbHVlIjoiRjNwS2lCcEFDbVZsODBYOGMyNit2SHh5U0dTcDgrdlR4eDZ3Zm9reUVUTFVVMElFR1FRdm9FcU92SWg3TTIxVyt3VkJ6VXkvTFRHYVp4ZC90NG9VTFFxYXM3THJQUzB4aXdtNTl4Uy9nOEVsMDFSdDg2QWtMZGNJcDJsZy9WUzhLeVlnQUlhejFXRjd1OVNHYkh6TUJvN2ZvZkRPMG5UY1lLa1Y0bVQ5NmpMVkd5dmV2amEzbWxiOXFZNzA4eE5MZW9MY0V0T2lNRHBOWFZobE92YkIrMXdZdVdVeTRRMHNCNVdUazZqMHVVdHhxZ1I5TnZnS0F1MlVJcDJHRm5keXRUZzVWQ0xDb3l0ZG56cjlqYmZzakVzYjF6eGo3OVNOOUZWVmZpV3JHZ0E1RWZmS0lCVlRvYXAveHU4VDJqS2RFMTc3R1Q3UzVyTCtwUkt2QWh3QUhtejd1Z01JVCtRNllBYXF3REx6M245bXU4Yk14ME50Y3VMcXQ2QjcwU05iTVhmckVxdEpZdEIxblhjeW8xM2VMVkhzOFYxOGNqbThyS0t6ZmdBNVhqT1pZcFc0cFp5ajY3N1kwWTlTaCsxR3VVeE5nK2EyOTMxNnZUcHdtQ1Fua0xwOFZiT0Q3SzkwREZPZVFCT0JIVExlNjh2ZXVsVnh4WC9OT0dnMHlIeFMiLCJtYWMiOiIxNDg1MjU1MTQ5M2VhOGI3OTg4NzIxYTU0NmIzMzQ4MzNmMTE3ZTdmZjljMzM1NzZjN2E0MzJhMmRhYmQzZmJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-780849014\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1750935928 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1750935928\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1046636231 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:28:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjUrTmVEbXpMK0RGQllDL3JKeXNKeVE9PSIsInZhbHVlIjoiWnpPY2REL3owK0VLeTg4UUkzd3Q3MWRwdjg4R2NPd1daYnRDSUVjMDU5M1RjR1VUZHlHaVdLNG5WWjV2YzQvYTc1QTFpVW1sUWd0UmVaRjlqOEZMVzkyRmJVSlU5UGJJS1EwWFA0cHBOVWhMdjVBZTRmeUtQV25CN1V4T2hyNmZaamRXWmhTSzZ2VVBsRkp0Yzk0Mlc1dmhyQU1DS0loQXFvTnN6eE96K09RQmhmTlI1MktjVjNpUXdPTWkrc0l0VFl3SHJYZ0FYYjlSTStTbm1ZV0tGaUF4VmRGVlZzbnlIbjlUTktYZEV2U0xRTCtTQ3lUYW9DK1ZyQnR2Y1E2dUdIZStJOFBuWDBNT0granNEa2tNcE1wWGJtcjhUZmFtbmpKMS9lK2pOZnNNZFBKL0FKQlF3SkpqRDRmdU1QdDMvd0dJYWpwYTdrcmdDSDhoT3p5YVpVaDBtMnE0cFB1b3lrLzh1L0tjb1Z1UU5NMjBaYlovd2I4VG5RUWQzdkVuSFg1dGlvTDBpN3RoNHNmK1NJRmNsN0N5T2l0OEk3clVnUGNqRzVpaTFNYkVINkFHR3lLbGg3enB4RGt0ZFpsdlFicWxoZWk4djdUL2VFYktXSHlxLzFzeEt4S2ZlR0xvdnhtdEo0L3VGdjl0Q2lLa0kySEp0Z3p5WW9RdWN6WlkiLCJtYWMiOiI1ZDlmOWUzZWVlZjNjNmZiOWZkMGRlNjk0OWUwMjgwNTNhZGM3Y2UwMjc1ZDM4OWE0MTg3OTc0YTc0OTc4YWRlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImRTbFRyV3hJOHQ1N1piSkoyTElzaEE9PSIsInZhbHVlIjoiYzVhdkRNZUEzTkdZWlJ2QzJrWHhUSmp1WHM1dHdUSDZNZ210NkxheUkwZTZWYXQxaWtEMTFwSEpKYm8xOGh2V2NBN2VueDY0NnVIbXZTMXdLVVVVRVIzekpZWExrQ0dnRzJIdTJObHJVTUdrODhBYjFsbFdHcmgxTjVuY2dDWnVURWFCQ1p1c2FjU1c3V2Noazd3KzN5TnV3anBzdW94QjNTWkQ0Yi9KVTRYOWdwOURVL1lzaFJkQ1hvSDRDWVBCSnRHV2drY0pFS21NRm9DSURqRnJJOFhEdlUvOWdoRUFqRktNUjBrYWhjNWtHSkY0TUpIakJVQnQwZFlWMTNia094ZnovdXB5ZU9JNCtzTE1XWHZSRWEvSTZnaXYycmN5d3Rwbm1pZnIxUW05SGVEbmRtZng3Nk9leG9wSlQrNU9SV25MenV5ZDlTUDNjNU1kZjljSUZ1SUNNOUJaOXhMcDVkY2E4Z0FTTXdvNHIzeFVkbjh3VWwvc0RNS29hdkdGVElxd2txU2R6TkFUc3J5cnBhR2F1YmRoMzBKOUhYYWR4SW4rUnh6YjJJR2hkS1EySE9Wa2lIb2M4Ty9ackcybTRaTzIzTHRHLzZqVkdKcTBLbnl0V09IK3NGMlcwUXBEN3FtYnpHN0Z1NWVaU3d5eHNUazhlazJpeXF4TGpVQ08iLCJtYWMiOiI2NmVhZGQ3MzM4N2E4ZWNiOGEwNjYwMjhjOWQ2MzhmMmUxYjEzOWM1MzM4ODM1MjhmNWViZTQ2NjMwMzU0NGNiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjUrTmVEbXpMK0RGQllDL3JKeXNKeVE9PSIsInZhbHVlIjoiWnpPY2REL3owK0VLeTg4UUkzd3Q3MWRwdjg4R2NPd1daYnRDSUVjMDU5M1RjR1VUZHlHaVdLNG5WWjV2YzQvYTc1QTFpVW1sUWd0UmVaRjlqOEZMVzkyRmJVSlU5UGJJS1EwWFA0cHBOVWhMdjVBZTRmeUtQV25CN1V4T2hyNmZaamRXWmhTSzZ2VVBsRkp0Yzk0Mlc1dmhyQU1DS0loQXFvTnN6eE96K09RQmhmTlI1MktjVjNpUXdPTWkrc0l0VFl3SHJYZ0FYYjlSTStTbm1ZV0tGaUF4VmRGVlZzbnlIbjlUTktYZEV2U0xRTCtTQ3lUYW9DK1ZyQnR2Y1E2dUdIZStJOFBuWDBNT0granNEa2tNcE1wWGJtcjhUZmFtbmpKMS9lK2pOZnNNZFBKL0FKQlF3SkpqRDRmdU1QdDMvd0dJYWpwYTdrcmdDSDhoT3p5YVpVaDBtMnE0cFB1b3lrLzh1L0tjb1Z1UU5NMjBaYlovd2I4VG5RUWQzdkVuSFg1dGlvTDBpN3RoNHNmK1NJRmNsN0N5T2l0OEk3clVnUGNqRzVpaTFNYkVINkFHR3lLbGg3enB4RGt0ZFpsdlFicWxoZWk4djdUL2VFYktXSHlxLzFzeEt4S2ZlR0xvdnhtdEo0L3VGdjl0Q2lLa0kySEp0Z3p5WW9RdWN6WlkiLCJtYWMiOiI1ZDlmOWUzZWVlZjNjNmZiOWZkMGRlNjk0OWUwMjgwNTNhZGM3Y2UwMjc1ZDM4OWE0MTg3OTc0YTc0OTc4YWRlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImRTbFRyV3hJOHQ1N1piSkoyTElzaEE9PSIsInZhbHVlIjoiYzVhdkRNZUEzTkdZWlJ2QzJrWHhUSmp1WHM1dHdUSDZNZ210NkxheUkwZTZWYXQxaWtEMTFwSEpKYm8xOGh2V2NBN2VueDY0NnVIbXZTMXdLVVVVRVIzekpZWExrQ0dnRzJIdTJObHJVTUdrODhBYjFsbFdHcmgxTjVuY2dDWnVURWFCQ1p1c2FjU1c3V2Noazd3KzN5TnV3anBzdW94QjNTWkQ0Yi9KVTRYOWdwOURVL1lzaFJkQ1hvSDRDWVBCSnRHV2drY0pFS21NRm9DSURqRnJJOFhEdlUvOWdoRUFqRktNUjBrYWhjNWtHSkY0TUpIakJVQnQwZFlWMTNia094ZnovdXB5ZU9JNCtzTE1XWHZSRWEvSTZnaXYycmN5d3Rwbm1pZnIxUW05SGVEbmRtZng3Nk9leG9wSlQrNU9SV25MenV5ZDlTUDNjNU1kZjljSUZ1SUNNOUJaOXhMcDVkY2E4Z0FTTXdvNHIzeFVkbjh3VWwvc0RNS29hdkdGVElxd2txU2R6TkFUc3J5cnBhR2F1YmRoMzBKOUhYYWR4SW4rUnh6YjJJR2hkS1EySE9Wa2lIb2M4Ty9ackcybTRaTzIzTHRHLzZqVkdKcTBLbnl0V09IK3NGMlcwUXBEN3FtYnpHN0Z1NWVaU3d5eHNUazhlazJpeXF4TGpVQ08iLCJtYWMiOiI2NmVhZGQ3MzM4N2E4ZWNiOGEwNjYwMjhjOWQ2MzhmMmUxYjEzOWM1MzM4ODM1MjhmNWViZTQ2NjMwMzU0NGNiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046636231\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1300789527 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>15</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1300789527\", {\"maxDepth\":0})</script>\n"}}