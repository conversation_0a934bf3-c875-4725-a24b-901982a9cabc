{"__meta": {"id": "Xc2b0761f1f9e697682085a4a54ce1b78", "datetime": "2025-06-30 14:57:19", "utime": **********.437124, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751295438.944682, "end": **********.43716, "duration": 0.4924781322479248, "duration_str": "492ms", "measures": [{"label": "Booting", "start": 1751295438.944682, "relative_start": 0, "end": **********.365453, "relative_end": **********.365453, "duration": 0.42077112197875977, "duration_str": "421ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.365462, "relative_start": 0.4207801818847656, "end": **********.437164, "relative_end": 4.0531158447265625e-06, "duration": 0.0717020034790039, "duration_str": "71.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45571184, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0032600000000000003, "accumulated_duration_str": "3.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.401338, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.485}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.414114, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.485, "width_percent": 15.337}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.420426, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.822, "width_percent": 17.178}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1069578826 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1069578826\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1264210423 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1264210423\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1979245388 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1979245388\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-270336886 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; _clsk=hcmbe9%7C1751295430794%7C1%7C1%7Co.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjdrUnF0UzVBZkovQ3RnSE1zZ1lXeUE9PSIsInZhbHVlIjoiRk04RWpRT1JNa2VXWmdqYVhKemplUnlIUDlXREpHVjFpWnYyVUdkTE14MzU5dkc1K2ZueWtGWnczb0NscHNvVVZ1Q1NRMDhkWklMTkZ5Zm5UZWVzYnpzUFRLaE9ia2RBR1RyNUxZSVM2eTNZSmNkMGszRThVbWJITWRPNXl3Ymg3b0pmREQ0WFM0MFo2NVdMSEV5aFYvdFZuWmRsVW1BWlpGQmZrQWZ0NituWEFhTlFNR1RhdXRWcVJBbmowQngyYlhLaFh4UmR4eHMvWWh0SDJBL25RTUt2S2F2UVk0YVBqYzlNbkhVTTdvNndwUGRycTV2UHlyd0d0ck9tNWpkOGs0c1ptd1R0QWZTRjk0VlN3dEdRbGF4ZGkxdThlSnd6SVpyVkdTc2pOTXhSeC9HckZnWEpoMFpBd09iS3hiZm1SSzY1N1VYcXhhVVYzRE1hY205c2pqZEQveEhaUWpZYVYvU1FqQjR5NDUxbnp4MSswbjZyeCs1MWFMbUQ2bVp1akttZTBsZ0I5bjk0dndTL0lwZHQrNnBieWlrM0JpSHlSKy8yVUlQeHFaK0Y3M0ZLUytaQkdmTldka3dmTmhETFFBMGp3SExTZkI0MHN4K252MzhVL1JmZ2toS0N1cnRHRW1iUGNIMEdmYmdhUWRCUHR1cjBYaHNtcG05M2M0Z2MiLCJtYWMiOiIxNTgxMjI0OTlkOWNhZTAyODY2NTMzNDBhYjA3MjY5NDViY2Y2ZmM3YzJhMDU1ZDU0MjgzZGNkNDBmNmE5NmIzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IndiODJHTGhmUFdFTVI0L1ZyVWpPOHc9PSIsInZhbHVlIjoiWk1JRFIwcUhOcU1YamxnSFI0Z3RCRjcrNlV1WHR0eXVUVVp5c2hwaFJyamFlMXN2cldVdXp3anVoK2dOaU9LZ042ZHlEdXdhcnpBOFBjTzNaeG1rZ3pOVXlydlVXWVd2LzJuWjBQSFJXaGZoN2NRNlVIZGZHK1NLdVJiZktQcnNjM2daRkc3RFhmZ29UQUl3R2VvVTE5bSsrN2hvSUFxVUxua2F2ei96QXVYcit0SnVDaHRoaEF2eU1EaFp5M3c2ZU5Gem1uTkEwR1dDcXhRQjdoSUJtc3puVHVLTUhDVGpiSStJZTNaQlZNVWtpZkc5UWdPd2tpalVqRThZeXNsV2NsekVzYlZhWEFQdDRubTVMWHFSRFd0NnJyUDhONndQSUlsWVZERFZDWEhKamxyWDJsblFxRWxqdXVXUS8xbkhwaWxUSWIrTnlHZmRjMm1TbUhWYjM0SGx4YkJyR0JocUpWRVVDNlQ2SWZFUFd3ZjZhZ3RFN2tic3Z1cXZEVzlvT2RQSFpOSGNjZG1TbjJvYzJXT3JWNmRXdkhvbmNzajN3SjZLRHVsLy95RmNkSi9pcGJ0ZFVQRndPczNTeVg0UllTTWFmd0pqNVdtQWg3VUJaWjZhYTUzQ2VWRjZ1dm9qRmF3bnoyVXVrTzRJNU51MVBRenppNnpoNHFleHhTYWwiLCJtYWMiOiI4NTU0YzZmNmQ1M2QwYjA3ZGE5YjcwOTE5ZDc0YjM2YmEyM2EzZTU4MDk1ZmY0NDJkZWQwZjNhNzNlZjRhZTRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-270336886\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-806448049 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-806448049\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1063928622 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:57:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkR0cHd0MytuTS8yOUZuSEdxS3JJdmc9PSIsInZhbHVlIjoid3I0RzJjdElqSk0zMFVpcjBPN2RMNURrNXFUWitXOGFEQXYvZEZXTzlMTkFoMnowUGY2dGFHd0NaZjVRNHBKZ1g3bGhsNnlzaWwzUkxNd2ZXcGdVZE9KSlZ0YytVdEF2ZzJTTENaZXlkQS9GZDlUVSsrTEVZQ29BWWkzdEpPdlJEa29KbWNRUjJFK29DWUpQOVZiVGc1WVNpTkMzcHp3Z0Q1ZWkrWUg5TnZCdXFUTnhuMmtrWnl4QWRteFN3ZHc3V0hsaFQ0UzJyYm9EOHBhZ3BiU2RtR0dYdWl3cGZOS1lmQVRBdmdvVE9UaERFU01mWUlzT3ZoOU96UXlrc1RnZTZHL25qS1VucDlyaHBWRVFwYmdUZ05sUks5emhhL1MzMFlHUGVORUMxRDE0VjBEWGNJakZaMUMzY0RWbGJ2TStQVFlZMW1RMW5ncUdKaWFQdEExS1VhRmdzdFJNbWhaalNJYStveXJHOGpiclBVRXJNd3Iya2dFdDBHRkdyeUUreURGclZrMXJsRSs2aHMybHY5UlMwK2xaek1zR3hVd2JPTWdnZEp5cVdINlZOWTQvM3c3c3BDbkhyRDZuTWNWdHk4b1gxeTVkYTdXMDJrVmJwdm1xSmQ0OFJpQzhQdGdCT25EV01OZjVZdlFZUFFDeUZsSGNyaVNZeHhsbEpUNlYiLCJtYWMiOiI3Mzc5YTc0YjkwNzVmOTgyYWY3YmVlMjlmMjIwMjk2ZWNiOGM3ZjhjYjBmZDg5ZDJkYmQ3NmUwYTE3NDUyODRhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:57:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkFMUGdJbG02VytXd3d5eEpYTVRmTnc9PSIsInZhbHVlIjoiNkxRNkQ1cUVaUEg4UGFHWThlSndDZG1OK0ZibjZkdTZ3YkFaUWJQT1ZZNWJsR1Z4Rm9mVnVwamNRZ04zNmNWSHYyZVk0MWs3S0JGaFRtZEowK0dWWi9yNGZJQ05nSHI0cWZtUVlCL01oSDRTZEJraG1VWkNUTmk1SnlVb3RRQWhWcXJHRytCR2dmWWxXKzE5ZzEwSHBZenhBemFmR284SGlNakpuSlF5aWxHNUQ0MmFJbVpwYjR3b254YVAva05NTHc0MDY2TS9DbW9WS3UrNkpPcVQxVUcrdXNTdFFFU0VqRFNMa1d5NGJTVTRIbkpweXRIQk1uSndtOGZCNC9VTEVJdUk3dnlqTkxHejFuenlIRUlUUmlXTmhJNG45c2dPd0hrUjJHdThLd0FQcERUeWxzRENwUUlzMDZNYzg1RkJyejVDWkNrbVZQczJnMG41VC9YeXJqWExGYXp5THZiWURKYkpWa3NvZ0w3NXlYcGsycmRmdTBRbWJvaUxuYWRObUtqVldDclRTTElJZWRoQUNiTDJhRGtCNGdMc2lrVkN0RmZrS0RmeGJMRXRTRjAzVHdnMTFEak1YUHl3bFNHNWo4R2hBVXJnZ0RmUmE5VkR5OXhYZ1Z2dDA2bTQwWW1makNZNlZiUE90UmlBRXJXTGQ1SWdXYnN6QU1sRUtIREIiLCJtYWMiOiJkOTUyN2NlNzE5ZmY2ZWJiMzdkODA5OWMzNGM5ZTI1NWNiZDQyOTk1M2M4NzMyNWFjYTUwMmYyYTA2Njk4NTIwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:57:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkR0cHd0MytuTS8yOUZuSEdxS3JJdmc9PSIsInZhbHVlIjoid3I0RzJjdElqSk0zMFVpcjBPN2RMNURrNXFUWitXOGFEQXYvZEZXTzlMTkFoMnowUGY2dGFHd0NaZjVRNHBKZ1g3bGhsNnlzaWwzUkxNd2ZXcGdVZE9KSlZ0YytVdEF2ZzJTTENaZXlkQS9GZDlUVSsrTEVZQ29BWWkzdEpPdlJEa29KbWNRUjJFK29DWUpQOVZiVGc1WVNpTkMzcHp3Z0Q1ZWkrWUg5TnZCdXFUTnhuMmtrWnl4QWRteFN3ZHc3V0hsaFQ0UzJyYm9EOHBhZ3BiU2RtR0dYdWl3cGZOS1lmQVRBdmdvVE9UaERFU01mWUlzT3ZoOU96UXlrc1RnZTZHL25qS1VucDlyaHBWRVFwYmdUZ05sUks5emhhL1MzMFlHUGVORUMxRDE0VjBEWGNJakZaMUMzY0RWbGJ2TStQVFlZMW1RMW5ncUdKaWFQdEExS1VhRmdzdFJNbWhaalNJYStveXJHOGpiclBVRXJNd3Iya2dFdDBHRkdyeUUreURGclZrMXJsRSs2aHMybHY5UlMwK2xaek1zR3hVd2JPTWdnZEp5cVdINlZOWTQvM3c3c3BDbkhyRDZuTWNWdHk4b1gxeTVkYTdXMDJrVmJwdm1xSmQ0OFJpQzhQdGdCT25EV01OZjVZdlFZUFFDeUZsSGNyaVNZeHhsbEpUNlYiLCJtYWMiOiI3Mzc5YTc0YjkwNzVmOTgyYWY3YmVlMjlmMjIwMjk2ZWNiOGM3ZjhjYjBmZDg5ZDJkYmQ3NmUwYTE3NDUyODRhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:57:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkFMUGdJbG02VytXd3d5eEpYTVRmTnc9PSIsInZhbHVlIjoiNkxRNkQ1cUVaUEg4UGFHWThlSndDZG1OK0ZibjZkdTZ3YkFaUWJQT1ZZNWJsR1Z4Rm9mVnVwamNRZ04zNmNWSHYyZVk0MWs3S0JGaFRtZEowK0dWWi9yNGZJQ05nSHI0cWZtUVlCL01oSDRTZEJraG1VWkNUTmk1SnlVb3RRQWhWcXJHRytCR2dmWWxXKzE5ZzEwSHBZenhBemFmR284SGlNakpuSlF5aWxHNUQ0MmFJbVpwYjR3b254YVAva05NTHc0MDY2TS9DbW9WS3UrNkpPcVQxVUcrdXNTdFFFU0VqRFNMa1d5NGJTVTRIbkpweXRIQk1uSndtOGZCNC9VTEVJdUk3dnlqTkxHejFuenlIRUlUUmlXTmhJNG45c2dPd0hrUjJHdThLd0FQcERUeWxzRENwUUlzMDZNYzg1RkJyejVDWkNrbVZQczJnMG41VC9YeXJqWExGYXp5THZiWURKYkpWa3NvZ0w3NXlYcGsycmRmdTBRbWJvaUxuYWRObUtqVldDclRTTElJZWRoQUNiTDJhRGtCNGdMc2lrVkN0RmZrS0RmeGJMRXRTRjAzVHdnMTFEak1YUHl3bFNHNWo4R2hBVXJnZ0RmUmE5VkR5OXhYZ1Z2dDA2bTQwWW1makNZNlZiUE90UmlBRXJXTGQ1SWdXYnN6QU1sRUtIREIiLCJtYWMiOiJkOTUyN2NlNzE5ZmY2ZWJiMzdkODA5OWMzNGM5ZTI1NWNiZDQyOTk1M2M4NzMyNWFjYTUwMmYyYTA2Njk4NTIwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:57:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1063928622\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}