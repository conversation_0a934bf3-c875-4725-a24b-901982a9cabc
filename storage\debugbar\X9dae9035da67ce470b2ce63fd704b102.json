{"__meta": {"id": "X9dae9035da67ce470b2ce63fd704b102", "datetime": "2025-06-08 16:24:35", "utime": **********.680975, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.080787, "end": **********.680995, "duration": 0.600208044052124, "duration_str": "600ms", "measures": [{"label": "Booting", "start": **********.080787, "relative_start": 0, "end": **********.608999, "relative_end": **********.608999, "duration": 0.5282120704650879, "duration_str": "528ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.60901, "relative_start": 0.5282230377197266, "end": **********.680998, "relative_end": 3.0994415283203125e-06, "duration": 0.07198810577392578, "duration_str": "71.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45693440, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00737, "accumulated_duration_str": "7.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6438491, "duration": 0.00518, "duration_str": "5.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.285}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.6601532, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.285, "width_percent": 18.453}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.668783, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.738, "width_percent": 11.262}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1162371631 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1162371631\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1460871440 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1460871440\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1088722498 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1088722498\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-321856262 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=u1db3a%7C1749399860582%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNGOVJGT3IxZ0h2VndHSWZHQThZZ0E9PSIsInZhbHVlIjoiREh2MVo4Qk42aWZObjBlZnp1TnpWK3M5YzhUNFdSMVhwSXBsaW5FK3VyNHlETjA0UmVMc3EvdHJibVhDR3REci9DUG4xdG1GSnJ5bFp2ejVBS2xCbFdmM1pwZHZselBVb0pLcUVQNEZId1F6cFBuYkx0ZzM2WkptdGNQNGw5K2ZsYS93TWlRbkpXVStYamtiSitJU0tpMUl2dnBXSUxreTRESHRIbzdCL01FQ24xTW5IN2hLcnlKMm4rZU56M1RGbjA2K3lJQ296MVVwM3R1djUvN1NWUVh3b1pwREdVTDV1bWk3NnA3bVRkOERzQnJET2JpZThVSTlUeVBTNlRGa0ZYWjBTYnRtdXIvZmpmaXFQaVJacitkR0pPWjVZV2NtVGgxbGNvWHJoc2dVQm5sN0VOS1pyeVEzM05mTTltSUxkWlpIUzlXRjNpTzBxRVJxcDd2SDEwOVMxbWhXYjYrc2VlTjBCa2pvTGNlWXhOV3hkd3crVDFIWmxkZHgxMzNWdnJJc0pydUFxUHlKU0ljUnJxOE1xNlFycEIrWGU2T0lKRnhwa3FFanNEenlnUm1LNmROZ1hwaDNtQmowb0pHS1doNG1VKzFFblYyeHVyYTVUUk5CN01LNEgxWGtIM1l6UkQ0QVIydGtNMDErdkEvTW5YVGRZOUltME1oaGkwMW0iLCJtYWMiOiJhOTViZDRmMDEyM2MxNDgyN2Y0NGQwNWZiMDA0MTUyODU2MWFlNGE0MWNiNTE2MjJlODU3NzE2N2E5MDk0ZjI1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhhMFJoaDQvTGg3WHRiSmlPU2xBUHc9PSIsInZhbHVlIjoidm5XZEFjeHJBMHRoSXErVFpDcW9mV1l1eGMxMEp5cWtQdUlrM0pOKzhsSStDWnVJWnEwcWFxSzZOTytmSmYrSXRGT00zVDgyMkZuRHpTKzJ4K2dTZGJTYVc2dXRrQTZQL0YvdG5QTGJobTVYa0ZrZHlUSVl0WFByUGZsYmVPVlRsME5iU1VndzlvTkNVcnBRb08zNk1MQTROMWJWcGtMMFlDK2p5NFAzdENJUHI5UDgzeURqSjg4OHk5b21DSlBpcC9TTWhZZXZaakM0ZUhzNTBCN2xDamx1bDdqdjU2YW5aa2IvNnd0TW1JN0Ura2ZrZ2JsWkJ5N2U4QklUYUxnS2dhOFBWRTRSSWFXQmxLN282WlVjWWcxNnR5WXRpWWE2dDRQWVZJTWZmb1hnRkdnWlJjN0VqeW53UG81THZxYmVESzNGMVFwOFcvUmJQNlJBdUU4WDFCQUJweXhYWHFUc1pUeWdkY05kelRRVnpxNVRPemt1Z1laMFRwWnlPRUZQZGRrcGJmdUJ1ckEzMVpPNFdqRXhOaXNITkwydmwxd2JHQU9ZVzhtVEd4S0VoclZUWHlaTW54azdmaXRBNkpWUERqRlA1eFZvcDJKZjZJcGtVblhlcnV3WmRCT3NGWENPcUFETkJKUjRNams3MERMMHNaQWkyOWxRRC8rZFZ3OUIiLCJtYWMiOiI1ODg2NjhhZjJjOTYzMDg0NDg2MDQzNmVmODk5Njk0MTM0NmM1YzlkMWFjMjMyMDFlYzU2ZWM1NTQ4YjZjNzE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-321856262\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2058615426 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2058615426\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1560414037 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:24:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iitnd0xkZ2U2ZUNYOWh2VkNlYXIwbkE9PSIsInZhbHVlIjoiQklQVEJ6RGNpcDhMdkM4cHkvb1R1WDRSTEhSQ0lra0xjTk5vZG5OR0VTRndBNndJYngvcGJnZ1Jud0t6MWE3UmErSEVCU0dKaTBId2xmclRrUEJzNlJib2ErUDJldDVUeWJvTDU5UWFwUS9oTnA0T0tQUHRxeVpBL1BuWGo3U0FIRUZjL0VKR2ZkYTlaK2VaUGJhWW5EeURvN0tBZUpJeTdTT1FYcnEwUmhKdHJXdWYrcnJSV2I1aDhDTjNDT2xRckFQa28zNUsyYzBqU0UzV3ROcUpMdTQ2MWFzVm1sUXUxM0p6c0dIQ1hnVlBmTGc5OWYzL1JaZlJxOTUwbXprclBMM25EWkFyV2daNEJ0d0pjUGFHb1Z2NGl6RUNOVmVZZjBLNEZPUThmZTN6eUJ2MkNXTTEyY2cvT1JENzRncWR2anVrc1lBQXZtTnZ5d0lFUzlnUXRZMktUK0lWdFU0bEFiOGI1cHdpcmMyVVFJTldlN2RFbGVRWjFtT25RTTh0UndOK2I2QUZEY0FFUk9raVdtalVSa3l3T203OFVCZkhZSnJnb2J0eXdpQVhDcGVLNGdQeDFOZUx0T3ZLK3hJeHljZFVwbmFnOUgrRnFjSlZUVGpaSEEybWJxTWtiRWt1M0RXYTgwVkpCUmpHL1lMeUwwdHBGcmczdUtVVE1jRmYiLCJtYWMiOiJmZTRkYTI5MDkwMTcxZDU3NWFkOTI5ZGQ3NjQxOGQ4OTEyYWFkZGQzMmYwM2IyZDIzOTQzZGZjOWNlYzI4YzlhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZ5NENQbWZQcFUzd0Zyb2IrQ1RJdXc9PSIsInZhbHVlIjoiZU0vc0xnMjZIK0paMVBxZTdRSkFqZGpSZllWbURjWDFSY2ZzbWpZaW13cnlaL0dJV044QWw4WTVXTGlFblRHRWxKMHVVRTRhRVQ3NFFRVTB1b2xhUzlCM2tSUDJoVVkrK3FoNmpqVmFlbG5JKzNEeEY3WWJpYmh6bjYyTmdIMU1PN1d2M3dJTkRQNGNDVTdVUDNyRlptVURxZEY2NVlMR0JjcFRpZXcxTjJ2ZVlOREVIc0kxcjlLR3AzUlFEdmF0YnE4cVNTaytYbVp5TlJxT3dnUnBZNXFWNDU4azBmdXBUbHBUdjVSLzVCT3VMUnMzQ2p1R1QwbXVyZEFVV0dOaFF4YUhMcnByOXBDdVZDMGdLdTNtWmlVbkhLbS9nRXNIQU5NN1d1UGVvTDJJWEZYcXlNMkZjWUtiREo4eDdydDhRNEFEai92N3F1Zkw0V1hkZVFrdEhDdmFrdXZ5dDQrQ3RBQ1NiWkQ3VFVtUXEyWDFKRDlKRnRKdzF5VGdKaUpLRWt0TWhGNXR4RWk1MEtSTmtnYmtQbVNSU2htcUdkR1B1OU83Z0tBMDNWekQ1S0kyd2RIdmdYOHZoSGRRV0VZNFdPeTFrbUwxNWN0eHU5aGRxbDIwQU1ITWtlOVN1Z1ZSSUZHamNyY1BUaWxsOWd1d3hCSUV2VkdQNS9DUnZVbkgiLCJtYWMiOiI3MjIxZTYwYzAzM2FjMzc3N2JjMDQ1ODY5ZTFiZGQxOWJmMWNlNTQwNmU2NTQ5ZTZkZGUyZDdhYjBjNGZlY2EwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iitnd0xkZ2U2ZUNYOWh2VkNlYXIwbkE9PSIsInZhbHVlIjoiQklQVEJ6RGNpcDhMdkM4cHkvb1R1WDRSTEhSQ0lra0xjTk5vZG5OR0VTRndBNndJYngvcGJnZ1Jud0t6MWE3UmErSEVCU0dKaTBId2xmclRrUEJzNlJib2ErUDJldDVUeWJvTDU5UWFwUS9oTnA0T0tQUHRxeVpBL1BuWGo3U0FIRUZjL0VKR2ZkYTlaK2VaUGJhWW5EeURvN0tBZUpJeTdTT1FYcnEwUmhKdHJXdWYrcnJSV2I1aDhDTjNDT2xRckFQa28zNUsyYzBqU0UzV3ROcUpMdTQ2MWFzVm1sUXUxM0p6c0dIQ1hnVlBmTGc5OWYzL1JaZlJxOTUwbXprclBMM25EWkFyV2daNEJ0d0pjUGFHb1Z2NGl6RUNOVmVZZjBLNEZPUThmZTN6eUJ2MkNXTTEyY2cvT1JENzRncWR2anVrc1lBQXZtTnZ5d0lFUzlnUXRZMktUK0lWdFU0bEFiOGI1cHdpcmMyVVFJTldlN2RFbGVRWjFtT25RTTh0UndOK2I2QUZEY0FFUk9raVdtalVSa3l3T203OFVCZkhZSnJnb2J0eXdpQVhDcGVLNGdQeDFOZUx0T3ZLK3hJeHljZFVwbmFnOUgrRnFjSlZUVGpaSEEybWJxTWtiRWt1M0RXYTgwVkpCUmpHL1lMeUwwdHBGcmczdUtVVE1jRmYiLCJtYWMiOiJmZTRkYTI5MDkwMTcxZDU3NWFkOTI5ZGQ3NjQxOGQ4OTEyYWFkZGQzMmYwM2IyZDIzOTQzZGZjOWNlYzI4YzlhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZ5NENQbWZQcFUzd0Zyb2IrQ1RJdXc9PSIsInZhbHVlIjoiZU0vc0xnMjZIK0paMVBxZTdRSkFqZGpSZllWbURjWDFSY2ZzbWpZaW13cnlaL0dJV044QWw4WTVXTGlFblRHRWxKMHVVRTRhRVQ3NFFRVTB1b2xhUzlCM2tSUDJoVVkrK3FoNmpqVmFlbG5JKzNEeEY3WWJpYmh6bjYyTmdIMU1PN1d2M3dJTkRQNGNDVTdVUDNyRlptVURxZEY2NVlMR0JjcFRpZXcxTjJ2ZVlOREVIc0kxcjlLR3AzUlFEdmF0YnE4cVNTaytYbVp5TlJxT3dnUnBZNXFWNDU4azBmdXBUbHBUdjVSLzVCT3VMUnMzQ2p1R1QwbXVyZEFVV0dOaFF4YUhMcnByOXBDdVZDMGdLdTNtWmlVbkhLbS9nRXNIQU5NN1d1UGVvTDJJWEZYcXlNMkZjWUtiREo4eDdydDhRNEFEai92N3F1Zkw0V1hkZVFrdEhDdmFrdXZ5dDQrQ3RBQ1NiWkQ3VFVtUXEyWDFKRDlKRnRKdzF5VGdKaUpLRWt0TWhGNXR4RWk1MEtSTmtnYmtQbVNSU2htcUdkR1B1OU83Z0tBMDNWekQ1S0kyd2RIdmdYOHZoSGRRV0VZNFdPeTFrbUwxNWN0eHU5aGRxbDIwQU1ITWtlOVN1Z1ZSSUZHamNyY1BUaWxsOWd1d3hCSUV2VkdQNS9DUnZVbkgiLCJtYWMiOiI3MjIxZTYwYzAzM2FjMzc3N2JjMDQ1ODY5ZTFiZGQxOWJmMWNlNTQwNmU2NTQ5ZTZkZGUyZDdhYjBjNGZlY2EwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1560414037\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1393226065 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1393226065\", {\"maxDepth\":0})</script>\n"}}