<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ProductService;
use App\Models\ProductServiceCategory;
use App\Models\WarehouseProduct;
use App\Models\Warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ProductApiController extends Controller
{
    /**
     * Get all products with pagination
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            $perPage = $request->get('per_page', 50);
            
            $query = ProductService::where('created_by', $user->creatorId())
                                 ->with(['category', 'unit', 'taxes']);

            // Filter by category
            if ($request->has('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            // Filter by warehouse
            if ($request->has('warehouse_id')) {
                $warehouseProductIds = WarehouseProduct::where('warehouse_id', $request->warehouse_id)
                                                     ->pluck('product_id')
                                                     ->toArray();
                $query->whereIn('id', $warehouseProductIds);
            }

            // Search by name or SKU
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('sku', 'LIKE', "%{$search}%");
                });
            }

            $products = $query->orderBy('name')->paginate($perPage);

            // Add warehouse stock information if warehouse_id is provided
            if ($request->has('warehouse_id')) {
                $products->getCollection()->transform(function ($product) use ($request) {
                    $warehouseProduct = WarehouseProduct::where('warehouse_id', $request->warehouse_id)
                                                      ->where('product_id', $product->id)
                                                      ->first();
                    
                    $product->warehouse_stock = $warehouseProduct ? $warehouseProduct->quantity : 0;
                    $product->image_url = $product->pro_image ? 
                        asset(Storage::url('uploads/pro_image/' . $product->pro_image)) : null;
                    
                    return $product;
                });
            }

            return response()->json([
                'success' => true,
                'data' => $products,
                'message' => 'Products retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving products: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search products by name or SKU
     */
    public function search(Request $request)
    {
        try {
            $user = Auth::user();
            $search = $request->get('q', '');
            $warehouseId = $request->get('warehouse_id');
            $limit = $request->get('limit', 20);

            if (empty($search)) {
                return response()->json([
                    'success' => true,
                    'data' => [],
                    'message' => 'No search query provided'
                ]);
            }

            $query = ProductService::where('created_by', $user->creatorId())
                                 ->with(['category', 'unit']);

            // Filter by warehouse if provided
            if ($warehouseId) {
                $warehouseProductIds = WarehouseProduct::where('warehouse_id', $warehouseId)
                                                     ->where('quantity', '>', 0)
                                                     ->pluck('product_id')
                                                     ->toArray();
                $query->whereIn('id', $warehouseProductIds);
            }

            // Search by name or SKU
            $query->where(function($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('sku', 'LIKE', "%{$search}%");
            });

            $products = $query->limit($limit)->get();

            // Add warehouse stock and image information
            $products->transform(function ($product) use ($warehouseId) {
                if ($warehouseId) {
                    $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouseId)
                                                      ->where('product_id', $product->id)
                                                      ->first();
                    
                    $product->warehouse_stock = $warehouseProduct ? $warehouseProduct->quantity : 0;
                }
                
                $product->image_url = $product->pro_image ? 
                    asset(Storage::url('uploads/pro_image/' . $product->pro_image)) : null;
                
                // Add tax information
                if (!empty($product->tax_id)) {
                    $taxes = \Utility::tax($product->tax_id);
                    $product->tax_details = $taxes;
                    $totalTaxRate = 0;
                    foreach ($taxes as $tax) {
                        $totalTaxRate += $tax->rate;
                    }
                    $product->total_tax_rate = $totalTaxRate;
                } else {
                    $product->tax_details = [];
                    $product->total_tax_rate = 0;
                }
                
                return $product;
            });

            return response()->json([
                'success' => true,
                'data' => $products,
                'message' => 'Products found successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error searching products: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get product by barcode/SKU
     */
    public function getByBarcode($barcode, Request $request)
    {
        try {
            $user = Auth::user();
            $warehouseId = $request->get('warehouse_id');

            $product = ProductService::where('created_by', $user->creatorId())
                                   ->where('sku', $barcode)
                                   ->with(['category', 'unit'])
                                   ->first();

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product not found with barcode: ' . $barcode
                ], 404);
            }

            // Add warehouse stock information
            if ($warehouseId) {
                $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouseId)
                                                  ->where('product_id', $product->id)
                                                  ->first();
                
                $product->warehouse_stock = $warehouseProduct ? $warehouseProduct->quantity : 0;
                
                if ($product->warehouse_stock <= 0) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Product is out of stock in this warehouse'
                    ], 400);
                }
            }

            $product->image_url = $product->pro_image ? 
                asset(Storage::url('uploads/pro_image/' . $product->pro_image)) : null;

            // Add tax information
            if (!empty($product->tax_id)) {
                $taxes = \Utility::tax($product->tax_id);
                $product->tax_details = $taxes;
                $totalTaxRate = 0;
                foreach ($taxes as $tax) {
                    $totalTaxRate += $tax->rate;
                }
                $product->total_tax_rate = $totalTaxRate;
            } else {
                $product->tax_details = [];
                $product->total_tax_rate = 0;
            }

            return response()->json([
                'success' => true,
                'data' => $product,
                'message' => 'Product found successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving product: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get product categories
     */
    public function getCategories()
    {
        try {
            $user = Auth::user();
            $categories = ProductServiceCategory::where('created_by', $user->creatorId())
                                               ->orderBy('name')
                                               ->get();

            return response()->json([
                'success' => true,
                'data' => $categories,
                'message' => 'Categories retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving categories: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get products by warehouse
     */
    public function getByWarehouse($warehouseId, Request $request)
    {
        try {
            $user = Auth::user();
            $perPage = $request->get('per_page', 50);

            // Verify warehouse belongs to user
            $warehouse = Warehouse::where('id', $warehouseId)
                                ->where('created_by', $user->creatorId())
                                ->first();

            if (!$warehouse) {
                return response()->json([
                    'success' => false,
                    'message' => 'Warehouse not found'
                ], 404);
            }

            // Get products in this warehouse
            $warehouseProductIds = WarehouseProduct::where('warehouse_id', $warehouseId)
                                                 ->pluck('product_id')
                                                 ->toArray();

            $query = ProductService::where('created_by', $user->creatorId())
                                 ->whereIn('id', $warehouseProductIds)
                                 ->with(['category', 'unit']);

            // Filter by category if provided
            if ($request->has('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            // Search filter
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('sku', 'LIKE', "%{$search}%");
                });
            }

            $products = $query->orderBy('name')->paginate($perPage);

            // Add warehouse stock information
            $products->getCollection()->transform(function ($product) use ($warehouseId) {
                $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouseId)
                                                  ->where('product_id', $product->id)
                                                  ->first();
                
                $product->warehouse_stock = $warehouseProduct ? $warehouseProduct->quantity : 0;
                $product->image_url = $product->pro_image ? 
                    asset(Storage::url('uploads/pro_image/' . $product->pro_image)) : null;
                
                // Add tax information
                if (!empty($product->tax_id)) {
                    $taxes = \Utility::tax($product->tax_id);
                    $product->tax_details = $taxes;
                    $totalTaxRate = 0;
                    foreach ($taxes as $tax) {
                        $totalTaxRate += $tax->rate;
                    }
                    $product->total_tax_rate = $totalTaxRate;
                } else {
                    $product->tax_details = [];
                    $product->total_tax_rate = 0;
                }
                
                return $product;
            });

            return response()->json([
                'success' => true,
                'data' => $products,
                'warehouse' => $warehouse,
                'message' => 'Warehouse products retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving warehouse products: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show specific product
     */
    public function show($id, Request $request)
    {
        try {
            $user = Auth::user();
            $warehouseId = $request->get('warehouse_id');

            $product = ProductService::where('id', $id)
                                   ->where('created_by', $user->creatorId())
                                   ->with(['category', 'unit'])
                                   ->first();

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product not found'
                ], 404);
            }

            // Add warehouse stock information if warehouse_id is provided
            if ($warehouseId) {
                $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouseId)
                                                  ->where('product_id', $product->id)
                                                  ->first();
                
                $product->warehouse_stock = $warehouseProduct ? $warehouseProduct->quantity : 0;
            }

            $product->image_url = $product->pro_image ? 
                asset(Storage::url('uploads/pro_image/' . $product->pro_image)) : null;

            // Add tax information
            if (!empty($product->tax_id)) {
                $taxes = \Utility::tax($product->tax_id);
                $product->tax_details = $taxes;
                $totalTaxRate = 0;
                foreach ($taxes as $tax) {
                    $totalTaxRate += $tax->rate;
                }
                $product->total_tax_rate = $totalTaxRate;
            } else {
                $product->tax_details = [];
                $product->total_tax_rate = 0;
            }

            return response()->json([
                'success' => true,
                'data' => $product,
                'message' => 'Product retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving product: ' . $e->getMessage()
            ], 500);
        }
    }
}
