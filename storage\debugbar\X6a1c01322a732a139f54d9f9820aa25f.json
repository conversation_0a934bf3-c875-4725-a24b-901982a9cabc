{"__meta": {"id": "X6a1c01322a732a139f54d9f9820aa25f", "datetime": "2025-06-07 23:06:23", "utime": **********.341432, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749337582.4159, "end": **********.341457, "duration": 0.9255568981170654, "duration_str": "926ms", "measures": [{"label": "Booting", "start": 1749337582.4159, "relative_start": 0, "end": **********.236021, "relative_end": **********.236021, "duration": 0.8201210498809814, "duration_str": "820ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.236035, "relative_start": 0.8201351165771484, "end": **********.341461, "relative_end": 4.0531158447265625e-06, "duration": 0.10542583465576172, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45281192, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1585\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1585-1595</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00457, "accumulated_duration_str": "4.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.302823, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.525}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.325088, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.525, "width_percent": 19.475}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1438030596 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1438030596\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-216675300 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-216675300\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1831289184 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1831289184\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1397543350 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2811 characters\">_clck=erumib%7C2%7Cfwk%7C0%7C1984; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; pos_system_session=eyJpdiI6ImVNL2JmZ05iY3huemxuNWZOWkJqeHc9PSIsInZhbHVlIjoia2taNWtSajhLZmtWVmhqZzM2a1VRQ1ZpNk8zdVZaUHlpUGlZVkRwREY2dy80cVN5Rk55RGZldFJIempxdmFVSGdtRDVQRkxyMEtpNU84THFWSlpJQWVjL1p0MzZnSFBQbTFFanNPcytGU1R1cjV0bDAyQkl1RURud1JKeHBNVjZRcUN6WGF5cDhZYlV0UVBGVXY4dURZYTdIY0dZcHdUb2JPcHM0V3FuSFNHblJLSTVwTlZJazU1SG1pZzl6aGZMbHpkOFRTMnRGWDNKSVVrdWNIa29wcGlUMTFtWTI1bTducU92LzZIcXNFamFDZUVlN1FTVHB3bXJSWHh5MFVEVmYzZ3ZRL0EvNHg4bjVNUWhjTFJPTWlGL05GeDNUbUt4d3NJY29rVXVOVGxka2Fkc1cxR0Q4UGxpWGVGVTVMUnU0UTQ1bXBXcmE4ZGIzTnpuWjNtMnQ0ZEdsbjIzOERhTWpFaHNtbHFLRkZQcW1ZQW1zeXdINmNmMERIWDcyZFhTenUrWklDQllEa0k3TUJkM3Uxd3E5UWhZRU1vT283ODNXU3VSVVBvTFZGK04zVDE0MU5YdXNqZVdjS0VJVi9POVJRRU80dkxsYmRpZWlkYXlwV0g1cEZvTys5ckhNUUZFTThmc2VrQWp5RmhWMTUzdlNYY01henM1aXFRUFZFRHoiLCJtYWMiOiI3ZmVhMjFjNGMwODdjZWJkNDZjNzY1NGVkZDYxODkxZTUwOWExNWIzYTE4YmVmMDhiMzQ4YjkzNjM1NDE3YzY5IiwidGFnIjoiIn0%3D; _clsk=vcxus7%7C1749337576086%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlpdUhlSXVqZzdjY2ZwT2s3cENOU1E9PSIsInZhbHVlIjoiY2ZoZ1lOTnVoTFJkV0ZDSHRiVkRFZ3A5ZWFrNDNBM3NFYzNQY25ZUG9YNjBDMFNRUU5nenVLbExYYjdFMllKd05YWFlCY2hUUTZ2bW03blBlUHpybzRCQXE1VVVnUjlwWlRaL2pPZ1FzQXdvUjNNZUxoK3FCaTVOZ2Z3K2xWOUt2NWdPNEM2bGFDRkszbTVWTVUxYzhWSkRGTmtQb3BFN2hmd0tBY3YxVHlDTThRVE9kdE1hWnVRWUxFNmRDTnBBTGo2SU85Z1FOT25lMGE5LytqaWhNWjU0b2hXWi8vcWtleXVLN24ySzN3blU4TDBwUGNNWWNZSzJuS1laVjVIM0N6OWVxc2ZjUU1lc3J3a21TQ0dLOE9sKzZKUEJ5anFUdUozREtHY3BrZFhid0dSYlJNaGNlb0VDUnZnRkl6VENQaFp4YVU4dXoyQUlUYVRsMTVCaGJNYnBLWUo4aTA3RWROcWlhQXg1eEozckI3UE5nbkh0MHV3T0RNbVVyMW1UQTBHeHVSdnNnVUtXY1JBK2JuSHB4QWhCRnBKMVJKdW1hUTdhYmovNkJuYlUwd2tvTGNzVkNZNzhMYjQveFY1V3ZjUFd6bDYyRG5HNjVSNzV0bmsva2lJQ24xQXAzQlN3eXNjSGsrd1hoeXpiN0dnT3Q5dldoajduYkl6a3ViV2MiLCJtYWMiOiI5NTQ5MmQ0ZDNhMzJlYjVjZTRiZDQ4MWMyOTliZDRkMGVmYjk3ZWU1ODhlNGZmMDUwY2FjNmQyMjY4ZmU0NjAyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndyTUNGRlFkbzZsaEFTZy9VOElVeEE9PSIsInZhbHVlIjoiaHcyalJXcy9samZGOUVmMXVJU09RU1ZOT0s3aTBUL0t1RmI1VU1oUmU4YjlwOTB0NUU0bFhNUjZaSDdrUHlqN0xtQ0VrdXVXNEdaOFo5d054Q0NYN2VGZDBjVlpMYjJUNE9NSUEvaTJkNnlSWUZzd0xqMXVsb2NHMG1pbWU0RUI2N0VyemNOQkZWdmJWZlFLejFNY3FBR3lTcE1jOHJDT25DOUhDRlVNNDNGK1JxSjIyN3ZWMklZYmxhWi8vb0twc0dLb3pPbHNSR3FOdGZpQVJreEdjYURCYnV2RTBUWHJTTE8zWVdJRElBWkpJWS9mVE9ZcjZOVjU3d3RhU040WUZhYjlWdnhDWVpoNUN2N1ZXUnJsM3J3ekVhSDd3SlRGb2J1aDdnQW5ZSnhpN1dUaHB4eDlQRW1hOWh2cHFXWWNaVTlFV1d4MjJNT2JVUHZrL2pJUTY5SXNHVEluWlFqdldMMmp4cnpDWStnU1Q4ZloxUlRINy9TUTA3WnUzdEpodmRuNVh3NDh5eEFEOFRqY1grM3N0bjRrV1dBam43Zy9XNGd6bWwrSytTajdqN1hTTmluc1ppYTNpL21jK0hiYWFXdnU3LzVkWnR4MnRRMmEwcXEzc2FrYktDUTZlUEwxdi9mT04rK2Z3S2Nyby9rQldKcE5kcXgxcXpsY1NVeXMiLCJtYWMiOiJlNzAwNzRhN2E3OTczZTBhMzQ4MGI5NjQ2YzA3ZmE3MTFiMDJhMGYxMDJjMjYzZDc0ODZlOGYwNTcwMTM5NzRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1397543350\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2019826388 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pos_system_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k8SQqyhwBkgpW9lptEbMoTN8iNDNeZ6DzX94m50O</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2019826388\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1443688692 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:06:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdkQWlJU2VLVWtJUm5vVFR6Rk90bWc9PSIsInZhbHVlIjoiTm03YzUrZVFsakR4a21Rc1VKaFRZaGJHL25PVllVaDNCMHFkRjMyUEZwOGtDdDZPRU4vN0RpMGwveWpZRmdwV0F4aXErc3VDckRoVGgrclpmQWlHMlk4RVk0UkltbnUvaUMxZnZNdENMWVVtSDUySkhQUUFTMmhUSkVoWXVFY0ZPL054a2lvWGZRY3hqckErbEM2UTBHeUVPb1Y4bnUrTW05WCtBRUd4WjYrZGdOeE9ja05vYXFuT2pDUno3eEU5MkY3ZTlXSk1EY25jUlVja3g3M3hITGpDTm9WakJMSW1OdGJRSlF5ak1paEtYanY0NDFuYnB3QXVHVUZqZTJndHB0cG5oc1R0akZtNTFEb0c5a1dHd3JYNlBTVGhzdkhPR3UyMmdwcE05R29TZk9MS2FDTWJMY3g3RGtnbzJzTmxGUGRJZ21tM1kzRlhmNDNENS91V2h4ZHU5eVBOQXgyRzc0MTdmcEdnMnlTVU9qT1kzWjUrbm56Y0ZOL3FwbGlqWGMxa2FvcERXTDFVL3o2dDlXUEpWVWFTdFh2dit2Y0FYYmtPL1BkOURySnN0cHdETk5vVWptQVlneHJwbVBVYWNBdHZTd3hzQzlaZnlLZE5WTGYxbk1mVHFKU0E4blc5UEZ1bDZQYkRabzA4MmRTOTdBYWFZb1JMWDFYY3BpZ1QiLCJtYWMiOiIyZGQ4NmEyYzRhZTJhZjc5ODFhNDc3NTA2NDZjZDNmYzQ1Zjk0Mjc1ZWFiM2IyOTBiMDQyMmZkYjMxMzBlNTAzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:06:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InZJaWZXM29EaCtDVUpCVUZvV3dXNmc9PSIsInZhbHVlIjoiVkZVd1NwYkhXTzBWZGRWZXJRdUlpeTdDV3NlQUtkcFcvWTcrWm9IQVgvWEJUVGRLRnZ3NEhlaWZKWVh2OW9qRGlrWG1BYk9xWVZjc3c0dXY5SFA3Qm1DQkdZTzdFaTM3RGs1U2MrWmJjaXNkK2ZsOFJnczFXQlRBTldTSVg0S1VjUHFWeHNUSVl0OElzOVRZbHR6b3JDd0FZUXl4MXBFT0hReUpoZ2NsOGtGNVRoSDN5WEIxVy9VemZnbzF1WDQ1Qk8vdGJZYnkwbXJHcDk1andLTkJOSXZMYnlXSEs3VDF0RTU5dUFLVGMyZ0g0cDVURkxzWlFYNHRUSW0ybldWcm9sNzhpeFRjSFN3OThhU2hPdnAyaFpxcVdVUTNJMVJUbXQ0YTdhYW52aHpTUGdEWVFYKzFITWxZbjVSd1g2akt5Q211OHhQMjFscVY3bVF0eFZhWE95cVBNaE5NZE81cW84bUJ4UUtyZDAwdzZ3akV6MTlJTEl1SkQranJya1g0OWswNmRZbldTK0lpdVBCanpVMnpEWkdNNEkxZ2dIWlRFWWptY216T0xGZWNyYXRaM1Bkd1JQeDBoRUZlaWxhYXg3UEhyeTFjb1ZaNC9yQXpkdXNzaVV4ekNCdFhXMzJlWHRsQkRaVUZqbFUwRkdYMDFvU1VOOWNJbFVEcDJxbVYiLCJtYWMiOiI4OGE5M2Y1OTQ3YTI0ZTJhMjVlZWU0NGMyZjNhMTJjMmJjZTBkYWY2ZjFkZmI2YjI5ODVmMmI2ODVjNWQ2YWIzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:06:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdkQWlJU2VLVWtJUm5vVFR6Rk90bWc9PSIsInZhbHVlIjoiTm03YzUrZVFsakR4a21Rc1VKaFRZaGJHL25PVllVaDNCMHFkRjMyUEZwOGtDdDZPRU4vN0RpMGwveWpZRmdwV0F4aXErc3VDckRoVGgrclpmQWlHMlk4RVk0UkltbnUvaUMxZnZNdENMWVVtSDUySkhQUUFTMmhUSkVoWXVFY0ZPL054a2lvWGZRY3hqckErbEM2UTBHeUVPb1Y4bnUrTW05WCtBRUd4WjYrZGdOeE9ja05vYXFuT2pDUno3eEU5MkY3ZTlXSk1EY25jUlVja3g3M3hITGpDTm9WakJMSW1OdGJRSlF5ak1paEtYanY0NDFuYnB3QXVHVUZqZTJndHB0cG5oc1R0akZtNTFEb0c5a1dHd3JYNlBTVGhzdkhPR3UyMmdwcE05R29TZk9MS2FDTWJMY3g3RGtnbzJzTmxGUGRJZ21tM1kzRlhmNDNENS91V2h4ZHU5eVBOQXgyRzc0MTdmcEdnMnlTVU9qT1kzWjUrbm56Y0ZOL3FwbGlqWGMxa2FvcERXTDFVL3o2dDlXUEpWVWFTdFh2dit2Y0FYYmtPL1BkOURySnN0cHdETk5vVWptQVlneHJwbVBVYWNBdHZTd3hzQzlaZnlLZE5WTGYxbk1mVHFKU0E4blc5UEZ1bDZQYkRabzA4MmRTOTdBYWFZb1JMWDFYY3BpZ1QiLCJtYWMiOiIyZGQ4NmEyYzRhZTJhZjc5ODFhNDc3NTA2NDZjZDNmYzQ1Zjk0Mjc1ZWFiM2IyOTBiMDQyMmZkYjMxMzBlNTAzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:06:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InZJaWZXM29EaCtDVUpCVUZvV3dXNmc9PSIsInZhbHVlIjoiVkZVd1NwYkhXTzBWZGRWZXJRdUlpeTdDV3NlQUtkcFcvWTcrWm9IQVgvWEJUVGRLRnZ3NEhlaWZKWVh2OW9qRGlrWG1BYk9xWVZjc3c0dXY5SFA3Qm1DQkdZTzdFaTM3RGs1U2MrWmJjaXNkK2ZsOFJnczFXQlRBTldTSVg0S1VjUHFWeHNUSVl0OElzOVRZbHR6b3JDd0FZUXl4MXBFT0hReUpoZ2NsOGtGNVRoSDN5WEIxVy9VemZnbzF1WDQ1Qk8vdGJZYnkwbXJHcDk1andLTkJOSXZMYnlXSEs3VDF0RTU5dUFLVGMyZ0g0cDVURkxzWlFYNHRUSW0ybldWcm9sNzhpeFRjSFN3OThhU2hPdnAyaFpxcVdVUTNJMVJUbXQ0YTdhYW52aHpTUGdEWVFYKzFITWxZbjVSd1g2akt5Q211OHhQMjFscVY3bVF0eFZhWE95cVBNaE5NZE81cW84bUJ4UUtyZDAwdzZ3akV6MTlJTEl1SkQranJya1g0OWswNmRZbldTK0lpdVBCanpVMnpEWkdNNEkxZ2dIWlRFWWptY216T0xGZWNyYXRaM1Bkd1JQeDBoRUZlaWxhYXg3UEhyeTFjb1ZaNC9yQXpkdXNzaVV4ekNCdFhXMzJlWHRsQkRaVUZqbFUwRkdYMDFvU1VOOWNJbFVEcDJxbVYiLCJtYWMiOiI4OGE5M2Y1OTQ3YTI0ZTJhMjVlZWU0NGMyZjNhMTJjMmJjZTBkYWY2ZjFkZmI2YjI5ODVmMmI2ODVjNWQ2YWIzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:06:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1443688692\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-374038831 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-374038831\", {\"maxDepth\":0})</script>\n"}}