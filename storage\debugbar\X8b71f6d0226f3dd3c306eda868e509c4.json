{"__meta": {"id": "X8b71f6d0226f3dd3c306eda868e509c4", "datetime": "2025-06-07 22:19:46", "utime": **********.606, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334785.140719, "end": **********.606037, "duration": 1.465317964553833, "duration_str": "1.47s", "measures": [{"label": "Booting", "start": 1749334785.140719, "relative_start": 0, "end": **********.344695, "relative_end": **********.344695, "duration": 1.2039761543273926, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.344717, "relative_start": 1.20399808883667, "end": **********.606041, "relative_end": 4.0531158447265625e-06, "duration": 0.2613239288330078, "duration_str": "261ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45978064, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.49679, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.514457, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.578935, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.588649, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.027750000000000004, "accumulated_duration_str": "27.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4311068, "duration": 0.00903, "duration_str": "9.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 32.541}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.447339, "duration": 0.01055, "duration_str": "10.55ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 32.541, "width_percent": 38.018}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.466049, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 70.559, "width_percent": 3.243}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.499116, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 73.802, "width_percent": 4.468}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.516631, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 78.27, "width_percent": 4.108}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.547636, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 82.378, "width_percent": 5.586}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.55937, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 87.964, "width_percent": 3.892}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5671759, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 91.856, "width_percent": 3.856}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.5817301, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 95.712, "width_percent": 4.288}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DCK491V1GUyngHueDf4bnOw7DOzApt305JPGLVcA", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-560657752 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-560657752\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1701163558 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1701163558\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1506527557 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1506527557\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-396596080 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1874 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwg%7C0%7C1960; XSRF-TOKEN=eyJpdiI6InlqRTlFUWNUaFp5ZnBSQ0I0OTF5UEE9PSIsInZhbHVlIjoiN0hTdEhyNTRXeXdHaE55NU5xTWM4TTh0bHlBaHEyeExXNDNVbkVueEtxb0hWTGdwRXd3L0NWdW5Va0pVNlVmaTREcjFuZVZTZEZ3c01EQ2tHbGNqNUdwTXVacWtCQ3IzcDZpZ3BSNlJ3TkUxZ2Z6MVYvSE5vUFFIY1lVcEJlcldpVWlHN015cWdpTjcvQXI3RHBvSSswMVcycFhWMmh1MlhlcmxXTzNRR0c3SGI4d3hQeGdnRmdiWnFiWWMxYUFtOW12N29rVEZmQ1BYd3d3akpnV2lMMnVmTStENlBpVDIyNUJLQ216Vko5OXpsTmo4WHAxTENDZU13WkJlL1dmZE9XZWhOZDdkN0czOHVxVkVtMTd3SHhmTWQ0dm45a29JcGYwSTRybjZqclRxNksxaW1lWWpSQzYvUng3YnE1UU5rdytVVjRveXdmM3NYeG1HeHl1NlZBQ0ZLb21GTlBMSDFxQ2ZtVVFwR0hvdUZNOEV1TE5aYlVUbUZXNFArN25UZkV6ZFo1QmllMzlYM2lUalBmZUlGVHhxN3lrTDNnZ3g5MWhNa05GbVd4THVFeGNCODk0bTNTdE1QYTNMTTY2OTlFVGtBR29PSlBXdldUZ1RCeTJHa2NMWXE1eUFiMDNwakI3QjdLMEpUZ2Uxd2ZnWjRrdjBRc1c4WnBFdmZtWEsiLCJtYWMiOiI5NzAyYWVmNjhiYTkxMGE1ZGMyMzlmNzU5NjM3NDIwNDZhYmZmZTQ1OWVjNzZjZWM4MThhMDE4ZmEwZWVlODMxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InNOQ3RmU0VPV210Kzl1NlE1eUlMWlE9PSIsInZhbHVlIjoiUjhYQ0tVd3ZWbDMyR1dnRkN0QmtwV3ZkZk8wbWg1RHJ1OHZnc0ovcklRR2E0cVpkS2NiSS9XaHlmckZ6VkRZYVdSc2RkaUx2NU15VE1QcDZVM2xjNHJCbTdtdnRpdi9nNUY5ejRGVjdhcU5raXdnQVJVSXd0NjNXNE53TUYwSUEwcXFmekZLS25PR0IyblR1RlNyb3kveWMwcUloTlpYMHN0MGZXL00yaHd1eWwxb1F3T0FHRk5DeHVSZTZCNnZsaDV2YlNTMURRUFBJQkVFTTBEZmNJV3NyczJVaFVFeUliZ1BNeksvVVFsUTF5VTRsQWYvUFFHRjduL0wxRDhxOXU0am0rQlV1TjFSUmVtLy83MjhONzN4MjdtNnBQVi9iZWx2QmwxZzJCOUZXYUpvUk1ocGx2NzUrZXRZQjMxMGtTa0c1Zm9meThTQUhQbjhDUXVEV1FFbTJLZDR0MWNZNStIUEdpT21tbS95RWV5YW5hd2p1UWNzSjlFQ09DT3kzYU1RcGpxRVV5NVhaMUhodXhXbGtSaHloeWJDNDljSmF0QlBWcnBWNW4zSTlyYmRlaDZHSkk0REdnTjlNOXA1dkJJT0tvZDJRdVpSOHBESE1zcTRyWGsrd204NkxiVmNvdDBYU3RFbnRueVYvWHUwOW5WU3QxRDlmTE82SW5HRzkiLCJtYWMiOiI1OTRmZjRkNTlhMzhjMjE0MDhlYTQ0ZTdlMjRiMTRlYmNmNjZiNzcwYmM5ZmJjYmFkMjM1NTdhZmZiNjczY2JhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-396596080\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-955078872 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DCK491V1GUyngHueDf4bnOw7DOzApt305JPGLVcA</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9hFyIirUIgn7VMVjViW9Tfz3n0yci3AztutQe7vH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955078872\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-254787155 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:19:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZWNlBQWi9WTW0zeXBNaGxXTTZneGc9PSIsInZhbHVlIjoiS0NGL2Npai9TTG5mUU1uZVA2LzE5TFRKOXBMb1Jsa1RIRFB3cWY2L3g0M25HUDg0WDQ1NlRDejNmeFRiSnRxZVVVeUYrajlpbjJVckVuc25UVnNWMnFYUzFVVUlsM2lFaDFQekdCc0dBenFtWUc1V2hOUEhOdDRLRTBNckFlM0pOVGZCMlduZFo4S011RS9CLzMwYW45ZHRHOHp3NzJ0SVdWdHY2SGZYVE96YnRaRFpFWWtmMkV5OGhIZzFQZUh1aFFMOXZWOVRrWVowNHhkM2NHc2p4b21GOEdMY2E2QUFHTnlKVFR5SFRZQmhML1p1Rmo4WU9kNkZZejZQQkR2NnVjbGdmanFqZVByQmRkTmpGYWRjL243L1NISjZveTQzRk1WRFQxcjhVWVBScWZ4QldoRlU0TkJ4aURoM05lZzNMRDRsZnFEZkVjMTdrZHEwaFdtQVk0Q1lQcGJmckgzbUxVNHZNK2VDekVIT0dEZnRjVmJNL2dodXVQTHJKZmtnWHpzd2g4ZXh3cG1rOHRiR013VWxWZGNPNS9NcW9LaDgyWUt3cXJiM21vc1FyeE9aamNEZnUya0IycHBNVVFhZnFnZ0pISk5KLzNRQ3Bzb1RGNFhFbG1GZGRDM2IvL0xma0s1VlRlUG5oSFYrSGFmeW13NjZxeU0xTWZNTTNQbG0iLCJtYWMiOiJmZDg4ZjU1ODc5Y2RiMTRiYjdlZTliN2ZlNzM1NmJjNmU3OWYxNjlhNmM3MGUzNDY0Y2VjNjFlN2FmYmZlOGM4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:19:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imp3SmplTE0ydlh1cFhTMzZXRzJFckE9PSIsInZhbHVlIjoiNkRHa29RVUVoNHRSbW92eDFjLzNXQWNENDlrS1EvRmhTenBXRGE1SVFlby9rYzNzeTZJT1VpS0RqSmRuU05EM2swak1oemhvQmlLcUd6bXRlRFNHMStINWFkY3hDSmJtd0o2RGJ5amxWRzA3QmhJejhXVE9hYStSbTdKbDdWQlNQZTV0Mk9yZkJIeTYzK1BsYmw2d1NaNFRxdktyVEU3MzB1dy8zVXk3aE1nUFdnNUIrZkV6WUdXUGtyWWhUcmJBVnpsT2ZTZEluVURQNEZ3YlZyayt6SjQ2UTMyQ3JzdWtETUY3U1dSSE92eUUzUzZuUGpCamF4RGphcEVrY2VYU2kvWHhaWWZDVEE4aE9UVUcyTE52NDZHS2NmWE5QcTRDbHgyYzNOZVhwR1RlZmFOUDFGRThPaTdpTnhGbUhvV0ZYTXhUdy81MUgzN0p3VmlrazFXT3F2ZHNZVDZRVGtuQnFXMWptQ2Q0bktxYW9KTzE1TFY0MTVJSHAwMXlrRjZ3UXBzZHBmTm5Ec3NPcDdua290Zzg1emxwMHlPSjdxQTlCUWhoeW1HUWh4NmxWMjFSUlkzNmNTSGJLSkFYU0R6M0RFblJibzFpUStZR0xEMXRKRklWYnlMMGZjTWhGeTA2Q0ZxV2lscjRKLzMzd3RhNVlUT0c5UGxMRWxNdjZQOW0iLCJtYWMiOiIxNTQ3ODZmMjNkMDQ2ODk3N2E2ZGRlNGZhOTRiZDE0YWJlODBiMmY2OWYxMWMzZTU2NzYzOTUxZGNhNWNkZWJjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:19:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZWNlBQWi9WTW0zeXBNaGxXTTZneGc9PSIsInZhbHVlIjoiS0NGL2Npai9TTG5mUU1uZVA2LzE5TFRKOXBMb1Jsa1RIRFB3cWY2L3g0M25HUDg0WDQ1NlRDejNmeFRiSnRxZVVVeUYrajlpbjJVckVuc25UVnNWMnFYUzFVVUlsM2lFaDFQekdCc0dBenFtWUc1V2hOUEhOdDRLRTBNckFlM0pOVGZCMlduZFo4S011RS9CLzMwYW45ZHRHOHp3NzJ0SVdWdHY2SGZYVE96YnRaRFpFWWtmMkV5OGhIZzFQZUh1aFFMOXZWOVRrWVowNHhkM2NHc2p4b21GOEdMY2E2QUFHTnlKVFR5SFRZQmhML1p1Rmo4WU9kNkZZejZQQkR2NnVjbGdmanFqZVByQmRkTmpGYWRjL243L1NISjZveTQzRk1WRFQxcjhVWVBScWZ4QldoRlU0TkJ4aURoM05lZzNMRDRsZnFEZkVjMTdrZHEwaFdtQVk0Q1lQcGJmckgzbUxVNHZNK2VDekVIT0dEZnRjVmJNL2dodXVQTHJKZmtnWHpzd2g4ZXh3cG1rOHRiR013VWxWZGNPNS9NcW9LaDgyWUt3cXJiM21vc1FyeE9aamNEZnUya0IycHBNVVFhZnFnZ0pISk5KLzNRQ3Bzb1RGNFhFbG1GZGRDM2IvL0xma0s1VlRlUG5oSFYrSGFmeW13NjZxeU0xTWZNTTNQbG0iLCJtYWMiOiJmZDg4ZjU1ODc5Y2RiMTRiYjdlZTliN2ZlNzM1NmJjNmU3OWYxNjlhNmM3MGUzNDY0Y2VjNjFlN2FmYmZlOGM4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:19:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imp3SmplTE0ydlh1cFhTMzZXRzJFckE9PSIsInZhbHVlIjoiNkRHa29RVUVoNHRSbW92eDFjLzNXQWNENDlrS1EvRmhTenBXRGE1SVFlby9rYzNzeTZJT1VpS0RqSmRuU05EM2swak1oemhvQmlLcUd6bXRlRFNHMStINWFkY3hDSmJtd0o2RGJ5amxWRzA3QmhJejhXVE9hYStSbTdKbDdWQlNQZTV0Mk9yZkJIeTYzK1BsYmw2d1NaNFRxdktyVEU3MzB1dy8zVXk3aE1nUFdnNUIrZkV6WUdXUGtyWWhUcmJBVnpsT2ZTZEluVURQNEZ3YlZyayt6SjQ2UTMyQ3JzdWtETUY3U1dSSE92eUUzUzZuUGpCamF4RGphcEVrY2VYU2kvWHhaWWZDVEE4aE9UVUcyTE52NDZHS2NmWE5QcTRDbHgyYzNOZVhwR1RlZmFOUDFGRThPaTdpTnhGbUhvV0ZYTXhUdy81MUgzN0p3VmlrazFXT3F2ZHNZVDZRVGtuQnFXMWptQ2Q0bktxYW9KTzE1TFY0MTVJSHAwMXlrRjZ3UXBzZHBmTm5Ec3NPcDdua290Zzg1emxwMHlPSjdxQTlCUWhoeW1HUWh4NmxWMjFSUlkzNmNTSGJLSkFYU0R6M0RFblJibzFpUStZR0xEMXRKRklWYnlMMGZjTWhGeTA2Q0ZxV2lscjRKLzMzd3RhNVlUT0c5UGxMRWxNdjZQOW0iLCJtYWMiOiIxNTQ3ODZmMjNkMDQ2ODk3N2E2ZGRlNGZhOTRiZDE0YWJlODBiMmY2OWYxMWMzZTU2NzYzOTUxZGNhNWNkZWJjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:19:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-254787155\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-594522559 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DCK491V1GUyngHueDf4bnOw7DOzApt305JPGLVcA</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594522559\", {\"maxDepth\":0})</script>\n"}}