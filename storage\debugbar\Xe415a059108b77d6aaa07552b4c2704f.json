{"__meta": {"id": "Xe415a059108b77d6aaa07552b4c2704f", "datetime": "2025-06-30 15:34:04", "utime": **********.328806, "method": "DELETE", "uri": "/remove-from-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 3, "messages": [{"message": "[15:34:04] LOG.info: removeFromCart called {\n    \"id\": \"1839\",\n    \"session_key\": \"pos\",\n    \"request_data\": {\n        \"id\": \"1839\",\n        \"session_key\": \"pos\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.305394, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:04] LOG.info: Cart before removal {\n    \"cart\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.32235, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:04] LOG.warning: Product not found in cart {\n    \"id\": \"1839\",\n    \"cart_keys\": []\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.322466, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751297643.912584, "end": **********.32883, "duration": 0.4162459373474121, "duration_str": "416ms", "measures": [{"label": "Booting", "start": 1751297643.912584, "relative_start": 0, "end": **********.242038, "relative_end": **********.242038, "duration": 0.3294539451599121, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.242046, "relative_start": 0.32946205139160156, "end": **********.328831, "relative_end": 9.5367431640625e-07, "duration": 0.08678483963012695, "duration_str": "86.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47517240, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "DELETE remove-from-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@removeFromCart", "namespace": null, "prefix": "", "where": [], "as": "remove-from-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1639\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1639-1712</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.023629999999999998, "accumulated_duration_str": "23.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.272624, "duration": 0.02249, "duration_str": "22.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.176}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.303206, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.176, "width_percent": 1.82}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.3171701, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 96.995, "width_percent": 1.904}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.318863, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.9, "width_percent": 1.1}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1642919067 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642919067\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.322004, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/remove-from-cart", "status_code": "<pre class=sf-dump id=sf-dump-730812361 data-indent-pad=\"  \"><span class=sf-dump-num>404</span>\n</pre><script>Sfdump(\"sf-dump-730812361\", {\"maxDepth\":0})</script>\n", "status_text": "Not Found", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1685159747 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1685159747\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-181891819 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1839</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-181891819\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-219508831 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9DcURhSkRkZkZIb3BWZzhLZFBldnc9PSIsInZhbHVlIjoiSkx4UmJjVWpNSHEweHVGSmd0OXRnZCtPRUZEUEd5cHhOZ2wzRTlYM2VaL2o4byt4L2hxRGFKTlIyS08vdVBPNEd4TUUxdVU1SklBUmU3WVRxK2x4RjNHQnprM2UrdmtEWEdPTnZTbWltOXFLc2xVUzFoRnlMQTNHMEVLUDRLNVdxVTJiblB2NGp6R1UxdkdHZ2Yzd0s0MTdWOXdUMDJmNGFvZ2ZaeHRlS0JpVnZLTmtISldDWlVHbDJ2L0NSNzVHZDZuZ1ZrYVZxckJtVEVSQUEwYktCdmNmeHNYcVZHR2c2UWFvbG5BbEJld0dLMjFEcldnalNLYzMzMmRWQTE2eGFRM3A1ZjJPWGxZTmptaUdubUlKZC9yMDd4Sm9ITFA5ZnRmV2xMekxLRWIxWEE4SnRRQnNpMGZ4R0ljOTl5MHl5dm52akxkZFBocmJjV2dEWTY2ZnQwUzBBZmNWM1cwUk1zSzFJR0NOSE14K3BOUThOMmllaWFaR0ZYdEtlQW96dmttb0FyQzhyTXhUMnJRdkNtbE5pcFFwaEZPTStjZUNUMFZ3b2pCYzFCYjNvbG1UQWdLY1lpS3BqQnpTNUQ5QjMrQVQ3NDA1ZzRkNGEzRWxQUWxwTzBqaEY5RzczUWRJUFNHOVByZnZTTGllaGxRdGQvU0h5TjV4NG5zVlVWcjEiLCJtYWMiOiJlZTZhYTc4Nzc1NDMwMWY5MzAzOTA1MDg0MjgwMTc0YzZjZTNiOWVmZWFmOWIyNGVlZjQzMDk2MGFhNTJkYWU4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImdwN2x3NDg5K3ZEQlNka1c1a1YrMlE9PSIsInZhbHVlIjoiV0FkZG8xbVVsajZBdUh2OWFVVmlucGFCUklyVmpadGJteTlRZVlobFhvcjIxNHpuR0hsVGJTUlBmaGFMeDBBSmtSVW5VNkNRZzBjSHBveDhoK1JNWmRaQWpiNmxweTkzMEhmTjh6Z2VCc0oyNVJPcTdQOWwwZndKd0Y3QXlwRzVVMmlmU0t0bmxwdzk3bCt2amY0SFllTXJQOFNPVUlNbGpwME15ZjJaaWdMVWF5b0tWYWkxMDM3Z1NSc0FURnM0eEdPOHkyMkVjdkszaVh0Vk9tdkJiakZHS1kvQmNVWDNtNzFpMHhlVnppNDQ5c2l2OEJ6MndHNGRVcXJBREJsTFhOSzRuMkl2bXJaTU5YNWJMWWJiQS9laWN3UzRHUUZXTU1QUFYxYlFpNUFodXJzOWFGaUp4RXRtSlg2dFJ1eU1Rb2FYZS9yRkUxc09xZVRPSWgzUUw2ZmF2cW0rVnd6YjBQdUhCQW9PSVdaRmpJallOREZoV0wrUWFpTEZhS2MxUkMxb09vY1lMSzgzUHJGR1VHUDA3cDdwTmI4dVN4bXkvWHJvR2U1dm9WWFJURlA3UDI2UDNZUk5JSU1PYlJOZnhXM2FUZnFmcW55amFVZXVMMi9KVitubXBncWlQQ0V6MFpycGVsNmp6cTlDZUtSUWZmNlJ5V0ZHaklIVHV0QnciLCJtYWMiOiJiN2VhOGNjMzRiYmM0YTU4ZGEyNzc5YjU2Nzc0MjM1ZDY3NzA4ZGE0ODU5ZGVlNTA5MjJkYzUzZTljYTJhOGQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219508831\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-789868547 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-789868547\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNLUEhCNnVreTBmY0ZLd3hwclc3K2c9PSIsInZhbHVlIjoiK2krUmI0ZUpWd1AzTFZlaHVnNExwNlJ1dy9nVmxNYk1TcnF2a1pKSUx1cmZEeHorK0I3L2w1ay9vdzN3NURpQUVHOGJCclN4dlp6RXBZWWhPY3A0ZGk2VkZTOFdSK1JkRXJWeStLVVZZM1MvMmV3eWdtU211emdoa0FndEdwaUdtT1VJMEVqSWVYWFRxdkQxbnh1Rm5vNWRIR3M2YkthRjVUcEh1Nzhibk05dVRmeGFMam84STRZOTh1ZFBzSy9uY05NVmdiMllpTzI4ZFlEc2MyQ2VDd0tManNVcW0yaERST1JBRW9KbThZMEwzTkNsNkxGVm5RWVl5VmZUckNPSmwzc2UxYVVIdWYzblpvSWZySFNTTTBuZDBKazAwcXNvcUI1eGlkZTljZmlGVXJpWGN4OUN3bkRxd2dDeWt6ZEZZWSs3cVI0VGFqQ3dPUU4zRXIrVFV0WThZSDNTWWRhWVF6WFMzZmp1SEVKeGNUbHJIQWI1RHNBa2RLTDdEYTNQbW5TOElzdjZURjl2dUpnUzh1U01iV293clpPTEZjWllrV2QxN2V4Y1BBZlFtaXAyeENaVWt3RzBSYmpkdS9PVWJPbjFmdVFxcktJaHUxelpFVks5STZXeDdYZUhmUi9KT3JyNVRtVnJid0s2MkRKY0dUTDQzUThCTG5LUjRuYjAiLCJtYWMiOiIzNTMwODc5ZWNiZjliMDNjNGRkNmQ5ZDE2ODQzMmYzN2E3ZDAzZTk1ZTc3NDFhZmI4MDBmMmIwYTM2NjY2MjYxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjAzSHIrSGhYOWduTkpDUkxVRjlRZVE9PSIsInZhbHVlIjoiVkh1UTNaRy8wWGJWNTdVQ3NFbllkWTdUYjNkVWFTNjd0T3BpWWRIL1BZVWtZeVlZRXBwUFNRdHNlU09HeG9aVHNKNUgrTitzc2R4V3hYRjl1SXRXOG1wZ1BNaTJQREZPWklaOTNMbHZCZldleE5iTFZqeURjLytFY1MyMFJSdndvL0Q4NFpTcE9GS24xdjlvcUxDRnE2eTljN0NuQmpkS2Yrc1RCd1NRSHd4Ui8ySU5rVjRuN0dkMDZIWHEyVndzUGlNUzlNMmJTYXZ4aWlnV0NHclNYYkE1WThDOW9Bc0Z0VkhkVWloekt6a2FpcnVXYUdVcUR5eU9iQUExZW5jRjBjclJReU4yZTAxcEJ4Z2RVSGRCMHVES3ZDQTBlSVh3ei8vcVhLaGF3YndPdzA0clBuUlZhaVIwVWFCMk9kK2xXR1pmQVZxNE9aZEhtbXdONWFMbXhhMEM3dk1QMEdDMWVERFRnR1diTmNKaC8xKzBqakl3K2VZa1NEakdOQkNLeHliT1gxKzkrZXFEMXFrLzVSY1JUREJTbUdBTnB6alZaS0FuS0JFYlFpRzVPeUNiZ0hSOXIxc3A5NHppZkNySHN0cjNrY1lHbEtrVzZoajNDT0tlUC8zem1Ddm1qdnYzWUl5dmxoVjdYQ05JbnI0OGVLcmMyaGZpU3cyRFZKOUMiLCJtYWMiOiIwYjE3MTJkNzNhOGRlZTE5OWUxZGE1ODMwYmJmZGZkYmQyY2VlZTk0YzRkMGFiY2Q1MTE1NThiYzU2NjBhZjA4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNLUEhCNnVreTBmY0ZLd3hwclc3K2c9PSIsInZhbHVlIjoiK2krUmI0ZUpWd1AzTFZlaHVnNExwNlJ1dy9nVmxNYk1TcnF2a1pKSUx1cmZEeHorK0I3L2w1ay9vdzN3NURpQUVHOGJCclN4dlp6RXBZWWhPY3A0ZGk2VkZTOFdSK1JkRXJWeStLVVZZM1MvMmV3eWdtU211emdoa0FndEdwaUdtT1VJMEVqSWVYWFRxdkQxbnh1Rm5vNWRIR3M2YkthRjVUcEh1Nzhibk05dVRmeGFMam84STRZOTh1ZFBzSy9uY05NVmdiMllpTzI4ZFlEc2MyQ2VDd0tManNVcW0yaERST1JBRW9KbThZMEwzTkNsNkxGVm5RWVl5VmZUckNPSmwzc2UxYVVIdWYzblpvSWZySFNTTTBuZDBKazAwcXNvcUI1eGlkZTljZmlGVXJpWGN4OUN3bkRxd2dDeWt6ZEZZWSs3cVI0VGFqQ3dPUU4zRXIrVFV0WThZSDNTWWRhWVF6WFMzZmp1SEVKeGNUbHJIQWI1RHNBa2RLTDdEYTNQbW5TOElzdjZURjl2dUpnUzh1U01iV293clpPTEZjWllrV2QxN2V4Y1BBZlFtaXAyeENaVWt3RzBSYmpkdS9PVWJPbjFmdVFxcktJaHUxelpFVks5STZXeDdYZUhmUi9KT3JyNVRtVnJid0s2MkRKY0dUTDQzUThCTG5LUjRuYjAiLCJtYWMiOiIzNTMwODc5ZWNiZjliMDNjNGRkNmQ5ZDE2ODQzMmYzN2E3ZDAzZTk1ZTc3NDFhZmI4MDBmMmIwYTM2NjY2MjYxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjAzSHIrSGhYOWduTkpDUkxVRjlRZVE9PSIsInZhbHVlIjoiVkh1UTNaRy8wWGJWNTdVQ3NFbllkWTdUYjNkVWFTNjd0T3BpWWRIL1BZVWtZeVlZRXBwUFNRdHNlU09HeG9aVHNKNUgrTitzc2R4V3hYRjl1SXRXOG1wZ1BNaTJQREZPWklaOTNMbHZCZldleE5iTFZqeURjLytFY1MyMFJSdndvL0Q4NFpTcE9GS24xdjlvcUxDRnE2eTljN0NuQmpkS2Yrc1RCd1NRSHd4Ui8ySU5rVjRuN0dkMDZIWHEyVndzUGlNUzlNMmJTYXZ4aWlnV0NHclNYYkE1WThDOW9Bc0Z0VkhkVWloekt6a2FpcnVXYUdVcUR5eU9iQUExZW5jRjBjclJReU4yZTAxcEJ4Z2RVSGRCMHVES3ZDQTBlSVh3ei8vcVhLaGF3YndPdzA0clBuUlZhaVIwVWFCMk9kK2xXR1pmQVZxNE9aZEhtbXdONWFMbXhhMEM3dk1QMEdDMWVERFRnR1diTmNKaC8xKzBqakl3K2VZa1NEakdOQkNLeHliT1gxKzkrZXFEMXFrLzVSY1JUREJTbUdBTnB6alZaS0FuS0JFYlFpRzVPeUNiZ0hSOXIxc3A5NHppZkNySHN0cjNrY1lHbEtrVzZoajNDT0tlUC8zem1Ddm1qdnYzWUl5dmxoVjdYQ05JbnI0OGVLcmMyaGZpU3cyRFZKOUMiLCJtYWMiOiIwYjE3MTJkNzNhOGRlZTE5OWUxZGE1ODMwYmJmZGZkYmQyY2VlZTk0YzRkMGFiY2Q1MTE1NThiYzU2NjBhZjA4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}