<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\AdSensePlatform\Resource;

use Google\Service\AdSensePlatform\Event;

/**
 * The "events" collection of methods.
 * Typical usage is:
 *  <code>
 *   $adsenseplatformService = new Google\Service\AdSensePlatform(...);
 *   $events = $adsenseplatformService->platforms_accounts_events;
 *  </code>
 */
class PlatformsAccountsEvents extends \Google\Service\Resource
{
  /**
   * Creates an account event. (events.create)
   *
   * @param string $parent Required. Account to log events about. Format:
   * platforms/{platform}/accounts/{account}
   * @param Event $postBody
   * @param array $optParams Optional parameters.
   * @return Event
   * @throws \Google\Service\Exception
   */
  public function create($parent, Event $postBody, $optParams = [])
  {
    $params = ['parent' => $parent, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('create', [$params], Event::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(PlatformsAccountsEvents::class, 'Google_Service_AdSensePlatform_Resource_PlatformsAccountsEvents');
