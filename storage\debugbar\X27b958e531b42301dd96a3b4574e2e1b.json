{"__meta": {"id": "X27b958e531b42301dd96a3b4574e2e1b", "datetime": "2025-06-30 14:57:17", "utime": **********.908554, "method": "GET", "uri": "/cookie-consent?cookie%5B%5D=necessary", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.095657, "end": **********.908574, "duration": 0.8129169940948486, "duration_str": "813ms", "measures": [{"label": "Booting", "start": **********.095657, "relative_start": 0, "end": **********.474873, "relative_end": **********.474873, "duration": 0.37921595573425293, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.474882, "relative_start": 0.3792247772216797, "end": **********.908577, "relative_end": 2.86102294921875e-06, "duration": 0.43369507789611816, "duration_str": "434ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50259264, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET cookie-consent", "middleware": "web", "controller": "App\\Http\\Controllers\\SystemController@CookieConsent", "namespace": null, "prefix": "", "where": [], "as": "cookie-consent", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=2370\" onclick=\"\">app/Http/Controllers/SystemController.php:2370-2422</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.0019, "accumulated_duration_str": "1.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SystemController.php", "line": 2373}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.542344, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "e3kH4K5uVae3WfgBbsYAezxVEctnj3wI1baLCUwO", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/cookie-consent", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1213582716 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">necessary</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1213582716\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-195850458 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-195850458\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-374492842 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">XSRF-TOKEN=eyJpdiI6InBIYkdXMnk0elR2VStDazJQeFJzbVE9PSIsInZhbHVlIjoiT1RsR0w5S0o3UzRXaHp6OS9FSGtDdXN4c0Y5UFl3bXNHeWNLcG9vc3A1WnE0OFVhcDRDeWFoN05SQlptTHcrenBmVitLcEp3S1V1ekJpblRyMUJPbmZadUE4VnBCRHZxZjNpdDVDZENwTVZoMmhCQ2RSWjU0ZlhkQlIrbUhTblVLYUN0MldNa2RZK2d1RnFaNVR2S2Q0NjU0ZHZxRGtJQzhpdyt3Z3ZyTmFGT2YyQzRuaFVXSkcyVm1jVVRlcnEreGFOSWE3cHU0N1N4NlU3bEdSTlFCckZGcC9VeHp6elV4b1FhYU44dVl1NHJrYnYyUHFQbmRhd3o1K0doMkhuVm1lQkhQSjNBUENPQU5HZUs4aWcvWTVZRUJtZlFlOXEwdDdrdXZma1lsYTVJQXUwZFIremt5V3NET3ltRzFxaTFtb0FvQlpzL0lYbTh1OWRWS3hGVDZMdmZjV2JienpVKyswa3h6UW5VazFKajJRUU94QmJQYW0zdTM0K3hiK3FmNlZjVVZadk15bE5UczZZbnNjektlZ2tOSm1iVk1ETi9YNXFMMGJYbE9Ud1lZQUdHVEY5TmlaNHJRSnF1N21mc2krK09XTk42UEg4YlZOWGkvUTR5OW1aRmN4cVpERnkxRmJPM0dibThHOVR4cW1ZZFN2clhFUWhFcGd4bUw3Y1AiLCJtYWMiOiIyZjcyMjdlZTg1ZTFlODE5YWZmNjVhYWMwNWZmZDFlNGRlY2NlMDE1MzlhYTJhNWY2MmMxMjA1NzhiYTYzMTQzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVIZDZaOVlzbVFtZ2xCcTNJM283ZFE9PSIsInZhbHVlIjoiaWhudE9HOTRZUG9JNEJxaUV3Q0JvNHdMQzVhVHYyaWJhMmVsMmwrbXRzbXJ3V0g1ZjdyLzZoM2ZkQ3lyVzcyd0RVRUNVWnljcU1RbDFDaVM5VFF5L1pxTWhHRi9BelFiU2VTbEI0cHk1K2UrSjZlWk1pNG1yNEI2OUU5eEtYU0ZzQm95S2NkallJcHVHRUNRejVCcjVHc2wzRERpc1MwR0d5eVFxOVNaSXZiK2k2YVFNdllBcGN0WDQybE1Td3pQcDl0NnNkcUJuQXJXbnEwWFdaTEZBc1E3cHNHRXQ4ZlMxcUV6Sm5aWVUvdEZKQUVpSUtRbHh1VTBDZm9ZUTFzaGNpcy9RdlZFcWtTOTFlM016QmN4bDlicm0zVjMrYnNXSTBFQ0dUMGRqdC9tdk16UW9kUCtXNTBNaTFQOUsvOTYrR2VxSkVxQ3JpTWpORzZ3cmoxbmtVUUR5UkswWXVQbHcvaGtrU1MzLzlDa1N3U0QrelYydjUxN2tiWXFpb2VwUEp4N2dYUGF2UGwyY28wQXVsbVlWeFUxdmF1aEE3SWdJVW5MbUQ2dTg5eHAvWFNpbTFISksraEFvaTlhVHVZaERFdFhIME1ENWZDNkFHdjJGVThxdVpvaFpXdzB5UHhUcWloRW55NmhnYlR4Z2JLMEJLNUVlMWFjUFUzRkpyRmIiLCJtYWMiOiI0NGIzZjA2OGM5YmIzNzY4NzVlMjQ0NDgxODgwMDc5NDQ5ZDk0ZDkwN2UwOWMwN2M3YjQ2ODYyZGVmMDVjZGNmIiwidGFnIjoiIn0%3D; _clck=i8cowp%7C2%7Cfx7%7C0%7C2007; _clsk=hcmbe9%7C1751295430794%7C1%7C1%7Co.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-374492842\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2050161062 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Qd30B4an5tjGtn2HYRgQNcEzwbiY3bgtpbkmWbP7</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lfxpeGirUvTl8QFE9D6aBM2vE7x681bBOtux88VX</span>\"\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050161062\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1100041464 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:57:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ild6cWVLc2trUUV5VXBhcGVzdDhFNVE9PSIsInZhbHVlIjoicW96YUZVZ1RUcEE0TW4wTU1zS0x5eGhnUldxRHJCY3E1bXR3MFhULzd5SE5SdEpaRllNVkVpelNPL00vbkFSRi8zY2hYT29wS0tlWlZkN2tEMi9DMnl5QlN6OWh4YXVTZXJqcW80TFVEYytqNFh1VHFtWE1WekJiRWpMbDZ3RlIvRWd3TUVQZjBWM0xJMHlMcDdNeGtEOXorY3N0b2IwSWtHak13QXEva3Y2Vm9iaUZkOE8rUXhESnM3TmdvS1ZaakxrNWV2SG8xd1NwdHJXUjZoWldnNlVqV2hHK3hhNS9VNWVwaTlHNWwrQjZNc1hCcFNHNjBvdHQyVE92YThnWEpLNUlzMEEybnh2UjJTQTF2dEhKajVBTnhBUW41R2FobTh1M2tLTHJuVnFnd25ZUU5UL2tqY0dOMXJROUJCdXVMYVVCRDk0bC95WFltYzdyRlhoMHR2YVpKVjZwejBrWFdhNTZyd0pjeGxEaDZaWGNvVGJOVHlpMXdzQjA0S1BwU1pISnluMHZMbkk1cXgxQzVIbWpNdmNFZnpadzRELzBPRzAvZ1RZeXV1Slo3ekhwNU04OHNlRDRIbzdqeEpXVVVwdXpGTDZRaUJGMVpUNDBwbC83YmdwWUNLVS9qUTdCaDFEY20zVDRCZW9OVmZRWkpScXZsaTRILzVvbk81cHYiLCJtYWMiOiJkZjcyM2E4NGE3ZGJmYmJlN2UyMDNlZGU1YTVmMTVjNDIzNDA4MzMxZTBkYzhiNGE3MThhYjRhZmZlMjQ3N2E0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:57:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkphZ1RWMU1aRFlJeit1ZGgvTWlRSWc9PSIsInZhbHVlIjoiRHYxNCtqNEovcjJRcXUvYnBMeE9FbkR5ZlByQW9ReHExek8zc2E1OVpwQmNERCt0ZFBjMHVRYVd0a0EzdUpQV0l4S2phTjNncXVYZEl6Unp4dEh4bVppVWdKRHVzZDU0cmtUREtFNUJwaGM2NFRoNDB4TGpUZDY4cTV3MTFsSEdIQ3Q4dVRUTGEzUmdhN2syblZZNW5hSG5tRFR4dkhVK09MdGNvWGZBdFNnYUZIMENNUHd3OVJPTDVnZC9DZ2Q4TGpucnpKdjY4TWRJOVpCdkR5Q21WdzhuR1crbzBvckQ0cXl4RGhxbW12cVVVVVd0aGNPWU11dUlqK09SK0ZBMUZ2RzhPY2xYT0pSTnFoc2pYbldGYmh4ei94N1Q3b3Y3NUFVZ0lsNWwzVUZRWDh3VlhxNmR0M0JEbnl0WjBxRFQrcjNQWlNjRlVzUEZxL2pzRkE0WklaYnplTS9ld0R1amRlRGx5T3FJMW5DOUhOMGlxc3d2TUZ5ZWtRYWEyWXJUVG1iUWt0U0JnRTVuRGhlZnF3TUhnRlVlRFRCMEhieEFacldjWHJNTkJ2WEZsNHRxT2NvNUxkL09XNG1abk1rZ3Q1NkxlNE9zS0hra2RVdDJZeGVOMVc5blY4VWxENExmbUFIdEVJWFNkY2dLZi81ZHZWanFxZDVOVGNzdXBCckIiLCJtYWMiOiI0MTc0OTc1MTRlOTM1ZWRiOGE2YTY2OTY1MjNhZmFiZDM0ODEwZTExOTNlYmZkMzAyMDQyOWE2YTdlYjRmMDExIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:57:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ild6cWVLc2trUUV5VXBhcGVzdDhFNVE9PSIsInZhbHVlIjoicW96YUZVZ1RUcEE0TW4wTU1zS0x5eGhnUldxRHJCY3E1bXR3MFhULzd5SE5SdEpaRllNVkVpelNPL00vbkFSRi8zY2hYT29wS0tlWlZkN2tEMi9DMnl5QlN6OWh4YXVTZXJqcW80TFVEYytqNFh1VHFtWE1WekJiRWpMbDZ3RlIvRWd3TUVQZjBWM0xJMHlMcDdNeGtEOXorY3N0b2IwSWtHak13QXEva3Y2Vm9iaUZkOE8rUXhESnM3TmdvS1ZaakxrNWV2SG8xd1NwdHJXUjZoWldnNlVqV2hHK3hhNS9VNWVwaTlHNWwrQjZNc1hCcFNHNjBvdHQyVE92YThnWEpLNUlzMEEybnh2UjJTQTF2dEhKajVBTnhBUW41R2FobTh1M2tLTHJuVnFnd25ZUU5UL2tqY0dOMXJROUJCdXVMYVVCRDk0bC95WFltYzdyRlhoMHR2YVpKVjZwejBrWFdhNTZyd0pjeGxEaDZaWGNvVGJOVHlpMXdzQjA0S1BwU1pISnluMHZMbkk1cXgxQzVIbWpNdmNFZnpadzRELzBPRzAvZ1RZeXV1Slo3ekhwNU04OHNlRDRIbzdqeEpXVVVwdXpGTDZRaUJGMVpUNDBwbC83YmdwWUNLVS9qUTdCaDFEY20zVDRCZW9OVmZRWkpScXZsaTRILzVvbk81cHYiLCJtYWMiOiJkZjcyM2E4NGE3ZGJmYmJlN2UyMDNlZGU1YTVmMTVjNDIzNDA4MzMxZTBkYzhiNGE3MThhYjRhZmZlMjQ3N2E0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:57:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkphZ1RWMU1aRFlJeit1ZGgvTWlRSWc9PSIsInZhbHVlIjoiRHYxNCtqNEovcjJRcXUvYnBMeE9FbkR5ZlByQW9ReHExek8zc2E1OVpwQmNERCt0ZFBjMHVRYVd0a0EzdUpQV0l4S2phTjNncXVYZEl6Unp4dEh4bVppVWdKRHVzZDU0cmtUREtFNUJwaGM2NFRoNDB4TGpUZDY4cTV3MTFsSEdIQ3Q4dVRUTGEzUmdhN2syblZZNW5hSG5tRFR4dkhVK09MdGNvWGZBdFNnYUZIMENNUHd3OVJPTDVnZC9DZ2Q4TGpucnpKdjY4TWRJOVpCdkR5Q21WdzhuR1crbzBvckQ0cXl4RGhxbW12cVVVVVd0aGNPWU11dUlqK09SK0ZBMUZ2RzhPY2xYT0pSTnFoc2pYbldGYmh4ei94N1Q3b3Y3NUFVZ0lsNWwzVUZRWDh3VlhxNmR0M0JEbnl0WjBxRFQrcjNQWlNjRlVzUEZxL2pzRkE0WklaYnplTS9ld0R1amRlRGx5T3FJMW5DOUhOMGlxc3d2TUZ5ZWtRYWEyWXJUVG1iUWt0U0JnRTVuRGhlZnF3TUhnRlVlRFRCMEhieEFacldjWHJNTkJ2WEZsNHRxT2NvNUxkL09XNG1abk1rZ3Q1NkxlNE9zS0hra2RVdDJZeGVOMVc5blY4VWxENExmbUFIdEVJWFNkY2dLZi81ZHZWanFxZDVOVGNzdXBCckIiLCJtYWMiOiI0MTc0OTc1MTRlOTM1ZWRiOGE2YTY2OTY1MjNhZmFiZDM0ODEwZTExOTNlYmZkMzAyMDQyOWE2YTdlYjRmMDExIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:57:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1100041464\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">e3kH4K5uVae3WfgBbsYAezxVEctnj3wI1baLCUwO</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}