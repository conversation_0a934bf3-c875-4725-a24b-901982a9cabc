{"__meta": {"id": "Xf534ae5c033fbf52c72a9bf6c478534e", "datetime": "2025-06-07 23:06:34", "utime": **********.444935, "method": "POST", "uri": "/pos-payment-type", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749337593.367311, "end": **********.44496, "duration": 1.0776491165161133, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": 1749337593.367311, "relative_start": 0, "end": **********.098361, "relative_end": **********.098361, "duration": 0.7310500144958496, "duration_str": "731ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.098381, "relative_start": 0.7310700416564941, "end": **********.444963, "relative_end": 2.86102294921875e-06, "duration": 0.34658193588256836, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52915648, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.payment_success", "param_count": null, "params": [], "start": **********.43057, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/pos/payment_success.blade.phppos.payment_success", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpos%2Fpayment_success.blade.php&line=1", "ajax": false, "filename": "payment_success.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.payment_success"}]}, "route": {"uri": "POST pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@financialType", "namespace": null, "prefix": "", "where": [], "as": "pos.pos-payment-type", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=190\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:190-260</a>"}, "queries": {"nb_statements": 30, "nb_failed_statements": 0, "accumulated_duration": 0.053649999999999996, "accumulated_duration_str": "53.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.159514, "duration": 0.00933, "duration_str": "9.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 17.39}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1846611, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 17.39, "width_percent": 1.864}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Services\\FinancialRecordService.php", "line": 585}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 200}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.194972, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:585", "source": "app/Services/FinancialRecordService.php:585", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FServices%2FFinancialRecordService.php&line=585", "ajax": false, "filename": "FinancialRecordService.php", "line": "585"}, "connection": "ty", "start_percent": 19.254, "width_percent": 0}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Services\\FinancialRecordService.php", "line": 681}, {"index": 17, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Services\\FinancialRecordService.php", "line": 614}, {"index": 18, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Services\\FinancialRecordService.php", "line": 589}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.197814, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:681", "source": "app/Services/FinancialRecordService.php:681", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FServices%2FFinancialRecordService.php&line=681", "ajax": false, "filename": "FinancialRecordService.php", "line": "681"}, "connection": "ty", "start_percent": 19.254, "width_percent": 2.926}, {"sql": "select * from `financial_records` where `shift_id` = 1 and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Services\\FinancialRecordService.php", "line": 615}, {"index": 17, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Services\\FinancialRecordService.php", "line": 589}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 200}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.204283, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:615", "source": "app/Services/FinancialRecordService.php:615", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FServices%2FFinancialRecordService.php&line=615", "ajax": false, "filename": "FinancialRecordService.php", "line": "615"}, "connection": "ty", "start_percent": 22.181, "width_percent": 2.684}, {"sql": "update `financial_records` set `current_cash` = 40, `total_cash` = 140, `financial_records`.`updated_at` = '2025-06-07 23:06:34' where `id` = 1", "type": "query", "params": [], "bindings": ["40", "140", "2025-06-07 23:06:34", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Services\\FinancialRecordService.php", "line": 637}, {"index": 15, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Services\\FinancialRecordService.php", "line": 589}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 200}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.209802, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:637", "source": "app/Services/FinancialRecordService.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FServices%2FFinancialRecordService.php&line=637", "ajax": false, "filename": "FinancialRecordService.php", "line": "637"}, "connection": "ty", "start_percent": 24.865, "width_percent": 2.032}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Services\\FinancialRecordService.php", "line": 604}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 200}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.230197, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:604", "source": "app/Services/FinancialRecordService.php:604", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FServices%2FFinancialRecordService.php&line=604", "ajax": false, "filename": "FinancialRecordService.php", "line": "604"}, "connection": "ty", "start_percent": 26.897, "width_percent": 0}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialTransactionService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Services\\FinancialTransactionService.php", "line": 21}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 217}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.230598, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "FinancialTransactionService.php:21", "source": "app/Services/FinancialTransactionService.php:21", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FServices%2FFinancialTransactionService.php&line=21", "ajax": false, "filename": "FinancialTransactionService.php", "line": "21"}, "connection": "ty", "start_percent": 26.897, "width_percent": 1.286}, {"sql": "insert into `financial_transactions` (`shift_id`, `transaction_type`, `cash_amount`, `created_by`, `payment_method`, `updated_at`, `created_at`) values (1, 'sale', '10.00', 16, 'cash', '2025-06-07 23:06:34', '2025-06-07 23:06:34')", "type": "query", "params": [], "bindings": ["1", "sale", "10.00", "16", "cash", "2025-06-07 23:06:34", "2025-06-07 23:06:34"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/FinancialTransactionService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Services\\FinancialTransactionService.php", "line": 27}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 217}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2369921, "duration": 0.00314, "duration_str": "3.14ms", "memory": 0, "memory_str": null, "filename": "FinancialTransactionService.php:27", "source": "app/Services/FinancialTransactionService.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FServices%2FFinancialTransactionService.php&line=27", "ajax": false, "filename": "FinancialTransactionService.php", "line": "27"}, "connection": "ty", "start_percent": 28.183, "width_percent": 5.853}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 233}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.247812, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "PosController.php:233", "source": "app/Http/Controllers/PosController.php:233", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=233", "ajax": false, "filename": "PosController.php", "line": "233"}, "connection": "ty", "start_percent": 34.035, "width_percent": 1.528}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.278541, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 35.564, "width_percent": 2.591}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.283962, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 38.155, "width_percent": 2.367}, {"sql": "select * from `customers` where `id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 249}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.295219, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "PosController.php:249", "source": "app/Http/Controllers/PosController.php:249", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=249", "ajax": false, "filename": "PosController.php", "line": "249"}, "connection": "ty", "start_percent": 40.522, "width_percent": 2.106}, {"sql": "select `id` from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/warehouse.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\warehouse.php", "line": 28}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 250}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.301526, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "warehouse.php:28", "source": "app/Models/warehouse.php:28", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=28", "ajax": false, "filename": "warehouse.php", "line": "28"}, "connection": "ty", "start_percent": 42.628, "width_percent": 1.808}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 517}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 251}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.309348, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "PosController.php:517", "source": "app/Http/Controllers/PosController.php:517", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=517", "ajax": false, "filename": "PosController.php", "line": "517"}, "connection": "ty", "start_percent": 44.436, "width_percent": 1.827}, {"sql": "select * from `pos` where `pos_id` = 4 and `created_by` = 15", "type": "query", "params": [], "bindings": ["4", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 255}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3142202, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "PosController.php:255", "source": "app/Http/Controllers/PosController.php:255", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=255", "ajax": false, "filename": "PosController.php", "line": "255"}, "connection": "ty", "start_percent": 46.263, "width_percent": 1.827}, {"sql": "insert into `pos` (`pos_id`, `customer_id`, `warehouse_id`, `user_id`, `pos_date`, `created_by`, `shift_id`, `updated_at`, `created_at`) values (4, 6, '8', '', '2025-06-07', 15, 1, '2025-06-07 23:06:34', '2025-06-07 23:06:34')", "type": "query", "params": [], "bindings": ["4", "6", "8", "", "2025-06-07", "15", "1", "2025-06-07 23:06:34", "2025-06-07 23:06:34"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 272}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3202438, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "PosController.php:272", "source": "app/Http/Controllers/PosController.php:272", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=272", "ajax": false, "filename": "PosController.php", "line": "272"}, "connection": "ty", "start_percent": 48.089, "width_percent": 5.033}, {"sql": "select * from `product_services` where `id` = '3' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 282}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.327849, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "PosController.php:282", "source": "app/Http/Controllers/PosController.php:282", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=282", "ajax": false, "filename": "PosController.php", "line": "282"}, "connection": "ty", "start_percent": 53.122, "width_percent": 2.013}, {"sql": "select `tax_id` from `product_services` where `id` = '3' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 259}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 286}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.332943, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:259", "source": "app/Models/ProductService.php:259", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=259", "ajax": false, "filename": "ProductService.php", "line": "259"}, "connection": "ty", "start_percent": 55.135, "width_percent": 1.528}, {"sql": "insert into `pos_products` (`pos_id`, `product_id`, `price`, `quantity`, `tax`, `discount`, `updated_at`, `created_at`) values (4, '3', '10.00', 1, '', '', '2025-06-07 23:06:34', '2025-06-07 23:06:34')", "type": "query", "params": [], "bindings": ["4", "3", "10.00", "1", "", "", "2025-06-07 23:06:34", "2025-06-07 23:06:34"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 295}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.338475, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "PosController.php:295", "source": "app/Http/Controllers/PosController.php:295", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=295", "ajax": false, "filename": "PosController.php", "line": "295"}, "connection": "ty", "start_percent": 56.664, "width_percent": 5.834}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 3961}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 297}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.345664, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3961", "source": "app/Models/Utility.php:3961", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=3961", "ajax": false, "filename": "Utility.php", "line": "3961"}, "connection": "ty", "start_percent": 62.498, "width_percent": 1.51}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = '3' limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 3975}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 297}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.351497, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3975", "source": "app/Models/Utility.php:3975", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=3975", "ajax": false, "filename": "Utility.php", "line": "3975"}, "connection": "ty", "start_percent": 64.007, "width_percent": 1.994}, {"sql": "update `warehouse_products` set `quantity` = 6, `warehouse_products`.`updated_at` = '2025-06-07 23:06:34' where `id` = 2", "type": "query", "params": [], "bindings": ["6", "2025-06-07 23:06:34", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 3994}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 297}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.356839, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3994", "source": "app/Models/Utility.php:3994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=3994", "ajax": false, "filename": "Utility.php", "line": "3994"}, "connection": "ty", "start_percent": 66.002, "width_percent": 4.567}, {"sql": "delete from `stock_reports` where `type` = 'pos' and `type_id` = 4", "type": "query", "params": [], "bindings": ["pos", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 302}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3643098, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "PosController.php:302", "source": "app/Http/Controllers/PosController.php:302", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=302", "ajax": false, "filename": "PosController.php", "line": "302"}, "connection": "ty", "start_percent": 70.568, "width_percent": 1.473}, {"sql": "insert into `stock_reports` (`product_id`, `quantity`, `type`, `type_id`, `description`, `created_by`, `updated_at`, `created_at`) values ('3', 1, 'pos', 4, '1   quantity sold in pos #POS00004', 15, '2025-06-07 23:06:34', '2025-06-07 23:06:34')", "type": "query", "params": [], "bindings": ["3", "1", "pos", "4", "1   quantity sold in pos #POS00004", "15", "2025-06-07 23:06:34", "2025-06-07 23:06:34"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4038}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 304}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.369697, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4038", "source": "app/Models/Utility.php:4038", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4038", "ajax": false, "filename": "Utility.php", "line": "4038"}, "connection": "ty", "start_percent": 72.041, "width_percent": 5.089}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 307}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3765578, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "PosController.php:307", "source": "app/Http/Controllers/PosController.php:307", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=307", "ajax": false, "filename": "PosController.php", "line": "307"}, "connection": "ty", "start_percent": 77.13, "width_percent": 1.417}, {"sql": "select * from `transaction_lines` where `reference_id` = 4 and `reference_sub_id` = 3 and `reference` = 'EXP' limit 1", "type": "query", "params": [], "bindings": ["4", "3", "EXP"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 5643}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 322}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.381895, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5643", "source": "app/Models/Utility.php:5643", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=5643", "ajax": false, "filename": "Utility.php", "line": "5643"}, "connection": "ty", "start_percent": 78.546, "width_percent": 2.572}, {"sql": "insert into `transaction_lines` (`account_id`, `reference`, `reference_id`, `reference_sub_id`, `date`, `credit`, `debit`, `created_by`, `updated_at`, `created_at`) values (282, 'EXP', 4, 3, '2025-06-07', 0, 10, 15, '2025-06-07 23:06:34', '2025-06-07 23:06:34')", "type": "query", "params": [], "bindings": ["282", "EXP", "4", "3", "2025-06-07", "0", "10", "15", "2025-06-07 23:06:34", "2025-06-07 23:06:34"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 5662}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 322}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3875651, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5662", "source": "app/Models/Utility.php:5662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=5662", "ajax": false, "filename": "Utility.php", "line": "5662"}, "connection": "ty", "start_percent": 81.118, "width_percent": 4.492}, {"sql": "select * from `transaction_lines` where `reference_id` = 4 and `reference_sub_id` = 3 and `reference` = 'POS' limit 1", "type": "query", "params": [], "bindings": ["4", "3", "POS"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 5643}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 336}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.394143, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Utility.php:5643", "source": "app/Models/Utility.php:5643", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=5643", "ajax": false, "filename": "Utility.php", "line": "5643"}, "connection": "ty", "start_percent": 85.61, "width_percent": 1.603}, {"sql": "insert into `transaction_lines` (`account_id`, `reference`, `reference_id`, `reference_sub_id`, `date`, `credit`, `debit`, `created_by`, `updated_at`, `created_at`) values (274, 'POS', 4, 3, '2025-06-07', 10, 0, 15, '2025-06-07 23:06:34', '2025-06-07 23:06:34')", "type": "query", "params": [], "bindings": ["274", "POS", "4", "3", "2025-06-07", "10", "0", "15", "2025-06-07 23:06:34", "2025-06-07 23:06:34"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 5662}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 336}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3989122, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5662", "source": "app/Models/Utility.php:5662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=5662", "ajax": false, "filename": "Utility.php", "line": "5662"}, "connection": "ty", "start_percent": 87.213, "width_percent": 6.076}, {"sql": "insert into `pos_payments` (`pos_id`, `date`, `created_by`, `amount`, `discount`, `discount_amount`, `payment_type`, `cash_amount`, `network_amount`, `updated_at`, `created_at`) values (4, '', 15, 10, '', 10, 'cash', 10, 0, '2025-06-07 23:06:34', '2025-06-07 23:06:34')", "type": "query", "params": [], "bindings": ["4", "", "15", "10", "", "10", "cash", "10", "0", "2025-06-07 23:06:34", "2025-06-07 23:06:34"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 457}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.408108, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "PosController.php:457", "source": "app/Http/Controllers/PosController.php:457", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=457", "ajax": false, "filename": "PosController.php", "line": "457"}, "connection": "ty", "start_percent": 93.29, "width_percent": 4.66}, {"sql": "select * from `pos` where `pos`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "pos.payment_success", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/pos/payment_success.blade.php", "line": 3}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.431896, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "pos.payment_success:3", "source": "view::pos.payment_success:3", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpos%2Fpayment_success.blade.php&line=3", "ajax": false, "filename": "payment_success.blade.php", "line": "3"}, "connection": "ty", "start_percent": 97.95, "width_percent": 2.05}]}, "models": {"data": {"App\\Models\\Shift": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\FinancialRecord": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FFinancialRecord.php&line=1", "ajax": false, "filename": "FinancialRecord.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 13, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1227493920 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1227493920\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.293085, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-95048876 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-95048876\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.307964, "xdebug_link": null}]}, "session": {"_token": "nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-1891981621 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1891981621\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2146341892 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2146341892\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1214437433 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>payment_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cash</span>\"\n  \"<span class=sf-dump-key>total_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1214437433\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1728884411 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">77</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2811 characters\">_clck=erumib%7C2%7Cfwk%7C0%7C1984; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; pos_system_session=eyJpdiI6ImVNL2JmZ05iY3huemxuNWZOWkJqeHc9PSIsInZhbHVlIjoia2taNWtSajhLZmtWVmhqZzM2a1VRQ1ZpNk8zdVZaUHlpUGlZVkRwREY2dy80cVN5Rk55RGZldFJIempxdmFVSGdtRDVQRkxyMEtpNU84THFWSlpJQWVjL1p0MzZnSFBQbTFFanNPcytGU1R1cjV0bDAyQkl1RURud1JKeHBNVjZRcUN6WGF5cDhZYlV0UVBGVXY4dURZYTdIY0dZcHdUb2JPcHM0V3FuSFNHblJLSTVwTlZJazU1SG1pZzl6aGZMbHpkOFRTMnRGWDNKSVVrdWNIa29wcGlUMTFtWTI1bTducU92LzZIcXNFamFDZUVlN1FTVHB3bXJSWHh5MFVEVmYzZ3ZRL0EvNHg4bjVNUWhjTFJPTWlGL05GeDNUbUt4d3NJY29rVXVOVGxka2Fkc1cxR0Q4UGxpWGVGVTVMUnU0UTQ1bXBXcmE4ZGIzTnpuWjNtMnQ0ZEdsbjIzOERhTWpFaHNtbHFLRkZQcW1ZQW1zeXdINmNmMERIWDcyZFhTenUrWklDQllEa0k3TUJkM3Uxd3E5UWhZRU1vT283ODNXU3VSVVBvTFZGK04zVDE0MU5YdXNqZVdjS0VJVi9POVJRRU80dkxsYmRpZWlkYXlwV0g1cEZvTys5ckhNUUZFTThmc2VrQWp5RmhWMTUzdlNYY01henM1aXFRUFZFRHoiLCJtYWMiOiI3ZmVhMjFjNGMwODdjZWJkNDZjNzY1NGVkZDYxODkxZTUwOWExNWIzYTE4YmVmMDhiMzQ4YjkzNjM1NDE3YzY5IiwidGFnIjoiIn0%3D; _clsk=vcxus7%7C1749337576086%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImF1bUI1V1lsVGJZQVpxMGVUUUt2N1E9PSIsInZhbHVlIjoiWVFGRmg4V0c3akZKS1RyVUFNNFNPbmVjU2hiLzNTbkMxMWtaNk95dXI5QUh2TUM2cGJ0SUJDZGV2Y1h3RVZIYzlQZ2VMUmF4aHVRdkFodmNUczVIZEM3ZTFQWVpuQkxZeTVOcUhpcmVzSlAwSVM1OHhESStNOENQaW1FZ21KRWppTnJURTBmSXdnYm9xYnpwT2QwclhCY0lESjRPeHgybHJOZWpVd1poS1lOMGYvTUxUZFRQYlI4NFFydXVENEJWd09zVjIzdE5CNHJlbmo1cnllN25aMjNkbWo5RjlWU0Z0N2RKcXNSRmJJRm9PVlo4clRnQlJ6ck9nc1lXd05CdFkvLyt0WUR5elB4WkwzR1B1TVFNV0w0WWw3WXMzcU5HOEtoMUJ2TlN4eHFLdGFKRmZVMThRNzRkU3F0M3BqNFdueHpNUEFseVMrWUtEeE9xNWJjOExyRUtIbElSWFBXS2lwYUVxSFJkRzlVMDJPd2xGS0o4UHdLdi9GQ2RrZzgwcElOUVFvcTRrajFTdEFWTEJTL3pvOW5MMklXbGRLNnFYdTNFTUV4VTdBTkgzUEFaT1ZCSUFyQXNNNXdWM1lRNkFBdHg5dVBlT0QxNjdxZUpxRWEzd2hMVThVcVR6Ym9Kcm5tQ3BkZkxFS1ZMRkJPTGRrWC84MUhHWFBHdCs0REQiLCJtYWMiOiI5OWI0MDM3ZjFkZWE1ZDFjYjlhNTQyNjJhMTkyMWQ4NjA2YzM4YzliZjJhNmQyYzAwYjMxZDUzNjA0MjZhMzdhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InU2bURiNmNYbjNheXZWSHVoclRiT1E9PSIsInZhbHVlIjoiZXdYZFJuMTRDNmM1Y0pTTDVNZ05VMndoTWJlWDFoY2FBSGU1ZGxBTUF3emNvaEtSdzBFanAyUWdhWTdsNlpkbWdEakRtWXh5NnNLRlV4ZTBCVHpUYzRTMS8xdWoyb3VqWktaYUkwTE05ZXl1MjZ4OXJld29nOU5QM3dIY3R0TEdGUTJjbEJZUVp5TE5BQStFeTdjR3AwRm5FMXQvV3lHNmRYcmg5cE1mOXVPdzNONzZXaW5yaG1LKzFPSTdKNFhiMEgvZ1ArVXpPRmpPTEliWHZPVDhVMTNJSEk1ZlNzZlRQOGVaTklFUlZhampsNkZmc1g3eFI0UjJqb0NGaEplYmZCa1BIS3JrWWpIaWFORzVFQXUrT1IwNk1Fd3pBT0VGMW9Pcy9sbmx6ckRodlpsa3g4Wm11T2tYNldGcmJTT0huZ2xUOXY4VHYxbDVndkViaW56a0VnSGhnakxEZ2drbDlEbVAzWStCUHNKby9Vdm8xWkNvelFxVTVvaUNpOUg1VVFoSEZSNHFGcGtoeFpJeVZmdUU3Rmw2MjBBZWtMNytMeDhCbktSU1N2VWNsZkM2eE5ZbE8xcms5OUJmZmJicFFZWTdWUEEwdzhTWkZGc1VqRG55TEN2Sk11dFRpNG16WWFZTEtSUmVYUkxQQW94ZytLSDFyQlJLdS93MktZcmYiLCJtYWMiOiJhZmY2NmE4YzAxY2M5ZjgzNzAyNzc5ZGE3YjEyZDcyMTA2ZDI2YjBiMDI2YjYxOTJlMmJiZDFhMGYwNDJjZmNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1728884411\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-449114356 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pos_system_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k8SQqyhwBkgpW9lptEbMoTN8iNDNeZ6DzX94m50O</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-449114356\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1596143969 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:06:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRSK1IvSHZ5VS9uZGRuMmNvYXdUSVE9PSIsInZhbHVlIjoiT2dWOHpaUXZHcUc3Y3NvRkx5S01Hb0NOTGFTVXlpTmFEY2hXcFllMjVINGxvUnJicHhUNllESXROTTlDeHpIN0p1eU5CNzNJRk55b0RmRGZQYzFvSmhleGNFcndlK2l4UDhFUGo5bXpjT3hCakRybnh4WU1oQTFleXJQWkFNZDFQTnZZSDhjR2xINndBcVluZEpSa2N5Nkx4bGFDYWM1WklHVzMvOG0vNHZMcWlHQU1FdW1pTHpJK2crSEpIcEJWc2E3Wm1hVEVCcTBxVTBabmJ4OGY2bDhUa3k4Zm80T0VxZThaeEJ5RW4vNEkrL0UwdUFUaVQzUEhMaDFBR1lxaERmVWVjZGZudW9DTWRLQWd0Ni84czZqckZ5WWgvUHNsbzVjTXliTkh5L0F6M1o1RjBUT0w4ODlrYXJpSlBxcDNucVlKMmRiRnFpdDJVcTNhanVUTmh4ZkRPUWtNNEVocG5oOHRzRnFhaEloemtSclZUeWZzTVFGVkhDWVQ4SGNoQWFwbGVMQW8xY1k4ZUxmZy9sYzgzQml6S24reXM4OFUxSnZaM0k4RnR6VEZCdFUrWW1SdUthVjdnWGxmTWpreEQ1d3FtVmthR2Flbng3U1ZrUk5XZEVxeXJWbVNpbFN0bGRLcGRrdmxuekdnd0QzbXRqanJMcjNNQzl4ZCt3ejkiLCJtYWMiOiIzNWNmMTlmZDRlZDU1ZDRjN2U3MjgxZjA1YWQwNjM4MTA5ODc2MWRhMTU0NjFiYzQ3ZWExZGY0ZjI5NGIzZDY2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:06:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InR1UUdPUkthUmMvUXB5WW5vTHRtMXc9PSIsInZhbHVlIjoiWHAyU0c2KzNiNTF0NnRpNkg2VmU5UkRXOVQ3akYyejZPZGJjd04xSitYZ0JsSEM5Y1NCRUhDRjlUNWxsQnZBNENOZGRaUWhCV3Z1SVhVbnBHeVErcm9GNkptZTBaMlh1ckM1VHFvSzVCTkR5WkU5OVZQSmhGbjR2Y0hYTHFQVzBiS1ZWL2NVNzNTOTJrVkZkL28xUzA1RW9RZ3ZaOVZwdUdrUm83OFg0NzhqT0dRQmtQeWduSTAyOTdIZmJnRHhLdGh3bExJQlBtdk9BV21LV1NZQUZmV0N0TGJhQkUyaGIwdlVNNjVQL0dOcG1IeVpsYTRQSEtBTitqV3VQRHZ2R1M1eEFxNUVkUElTNEZnUVBUcnNlbWlYRlR6YnhqM0pUdTFtamhoc2Z1OG5SNWl1aHo0Z2xINTMrbHB6T3hsL1h3RzJXdllkb21EWXJtM1ZjK1piL0tFNXNOdHpGUWk3ZDBLbnF0V3NWSHFueTdyYlo5WXZSVjdxM2ZjNWNlNGQ4a0xDdU1PWUJwSkZEeHF5d2ZhbHBZNitOaGp4a2hwNy95aDlJdWdHNzV0UU1acjVNV0x1c2lwdUJHZ01TdXdUYnZKNkhKeUVlenFUQmRXUjM4SFM1ZDE1RU9vTERrWC95a0F0WTNmZVAwaTErZ3FTdnFOaWtjdDVmaWM2S3ZXakQiLCJtYWMiOiIzMzNjNGM0OWNmMWUyNTBmODczMmU3Yjg2N2RiNWVhMTY3NjFjMGNjNmJjZDE1NmEzYThlOWE0MTdjNjYzOWIxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:06:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRSK1IvSHZ5VS9uZGRuMmNvYXdUSVE9PSIsInZhbHVlIjoiT2dWOHpaUXZHcUc3Y3NvRkx5S01Hb0NOTGFTVXlpTmFEY2hXcFllMjVINGxvUnJicHhUNllESXROTTlDeHpIN0p1eU5CNzNJRk55b0RmRGZQYzFvSmhleGNFcndlK2l4UDhFUGo5bXpjT3hCakRybnh4WU1oQTFleXJQWkFNZDFQTnZZSDhjR2xINndBcVluZEpSa2N5Nkx4bGFDYWM1WklHVzMvOG0vNHZMcWlHQU1FdW1pTHpJK2crSEpIcEJWc2E3Wm1hVEVCcTBxVTBabmJ4OGY2bDhUa3k4Zm80T0VxZThaeEJ5RW4vNEkrL0UwdUFUaVQzUEhMaDFBR1lxaERmVWVjZGZudW9DTWRLQWd0Ni84czZqckZ5WWgvUHNsbzVjTXliTkh5L0F6M1o1RjBUT0w4ODlrYXJpSlBxcDNucVlKMmRiRnFpdDJVcTNhanVUTmh4ZkRPUWtNNEVocG5oOHRzRnFhaEloemtSclZUeWZzTVFGVkhDWVQ4SGNoQWFwbGVMQW8xY1k4ZUxmZy9sYzgzQml6S24reXM4OFUxSnZaM0k4RnR6VEZCdFUrWW1SdUthVjdnWGxmTWpreEQ1d3FtVmthR2Flbng3U1ZrUk5XZEVxeXJWbVNpbFN0bGRLcGRrdmxuekdnd0QzbXRqanJMcjNNQzl4ZCt3ejkiLCJtYWMiOiIzNWNmMTlmZDRlZDU1ZDRjN2U3MjgxZjA1YWQwNjM4MTA5ODc2MWRhMTU0NjFiYzQ3ZWExZGY0ZjI5NGIzZDY2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:06:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InR1UUdPUkthUmMvUXB5WW5vTHRtMXc9PSIsInZhbHVlIjoiWHAyU0c2KzNiNTF0NnRpNkg2VmU5UkRXOVQ3akYyejZPZGJjd04xSitYZ0JsSEM5Y1NCRUhDRjlUNWxsQnZBNENOZGRaUWhCV3Z1SVhVbnBHeVErcm9GNkptZTBaMlh1ckM1VHFvSzVCTkR5WkU5OVZQSmhGbjR2Y0hYTHFQVzBiS1ZWL2NVNzNTOTJrVkZkL28xUzA1RW9RZ3ZaOVZwdUdrUm83OFg0NzhqT0dRQmtQeWduSTAyOTdIZmJnRHhLdGh3bExJQlBtdk9BV21LV1NZQUZmV0N0TGJhQkUyaGIwdlVNNjVQL0dOcG1IeVpsYTRQSEtBTitqV3VQRHZ2R1M1eEFxNUVkUElTNEZnUVBUcnNlbWlYRlR6YnhqM0pUdTFtamhoc2Z1OG5SNWl1aHo0Z2xINTMrbHB6T3hsL1h3RzJXdllkb21EWXJtM1ZjK1piL0tFNXNOdHpGUWk3ZDBLbnF0V3NWSHFueTdyYlo5WXZSVjdxM2ZjNWNlNGQ4a0xDdU1PWUJwSkZEeHF5d2ZhbHBZNitOaGp4a2hwNy95aDlJdWdHNzV0UU1acjVNV0x1c2lwdUJHZ01TdXdUYnZKNkhKeUVlenFUQmRXUjM4SFM1ZDE1RU9vTERrWC95a0F0WTNmZVAwaTErZ3FTdnFOaWtjdDVmaWM2S3ZXakQiLCJtYWMiOiIzMzNjNGM0OWNmMWUyNTBmODczMmU3Yjg2N2RiNWVhMTY3NjFjMGNjNmJjZDE1NmEzYThlOWE0MTdjNjYzOWIxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:06:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1596143969\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}