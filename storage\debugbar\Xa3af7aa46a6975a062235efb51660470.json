{"__meta": {"id": "Xa3af7aa46a6975a062235efb51660470", "datetime": "2025-06-07 23:28:05", "utime": **********.14821, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749338884.281921, "end": **********.148237, "duration": 0.8663160800933838, "duration_str": "866ms", "measures": [{"label": "Booting", "start": 1749338884.281921, "relative_start": 0, "end": **********.047476, "relative_end": **********.047476, "duration": 0.7655551433563232, "duration_str": "766ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.047495, "relative_start": 0.7655739784240723, "end": **********.14824, "relative_end": 3.0994415283203125e-06, "duration": 0.10074520111083984, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45058456, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00571, "accumulated_duration_str": "5.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.101379, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.951}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.121615, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.951, "width_percent": 15.236}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.133487, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.187, "width_percent": 16.813}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/debug-product-creation\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1739221628 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1739221628\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1853223931 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1853223931\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-197885066 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-197885066\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-133719112 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/debug-product-creation</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749338863155%7C13%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFEWDlnQzBsZWFhSWpjZXFUUk5xY3c9PSIsInZhbHVlIjoicnAwSlVXVEtqVjQxWEFMd3dlZ3FSOTZTRUtzN0V2M1QwV05NZGMvUkdvNWw1ckpSOE9vRHpuMlJ6YytGcithR3pHZUVzMm9KMnExTFkzNWlycVFvcFlaUUkxYzRIdGl4b2NJZlNXaVg0dncyY0xVcjRvekZsU2M4bE1TMTJiY1pOcGRKRjdoL1N5aEJZelgwYkpGdk1TZWNDTktvU250VkRWbTVyWkhDVTRNOVlpSTFHSTM1UncvNDBRZ0hNRzlyY3Z4aGxVd2pRQkdLU0hSeERPWFl4YkcyOU1GeHgwdVNTb1B3UXJpOUE2QjlZVTRJMDJjSm55Vk12V3dNTWhNVnM2QWtreW9uUnE3Y1V6bzRtZGlpOGFORDhzbUc2akE3M0I2U3lqWWNsemJaTDUwakdyb0kyQ01hU09WaHgvZmRmaXVTVnVYOTkyVmRIYmJtczh4a3BTM05YY1RIMzlvWmlxYmRJSXkyUFk0aDJDa3dzemsyMTJSdDUxNnI2aWNHRUFNMFE0dnRScW1nSHF0OUpnNGtoejd4S2FrNHVIa3hydERmRDJTaURNTWVsSWFJb0R5Uno2V085YjJpT1BMdWE4aUhDdmwrN3FKYzhUcys1VmJncVlCMlZGMGJSOWc4akxjQXdxUm15c0ZrenhYZ3VUcWsyUk52Q29BMXdXR00iLCJtYWMiOiIwMzU3YTk3ZTlkMjBlMjY2M2NjYTM3YTg2YmM1YTM0YmE4OWIyNDY2ZDFiYmNiY2NlN2FjYmZhN2U3OGM3NzEwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjNBVXJkSU0yTlI4ZkEvWGhNWU5xZlE9PSIsInZhbHVlIjoiRXJqdjh0d293M3hxYThQaTlxZmpzTk9HUEFpVHZ4c2xuME8wcElocG5BWUV3d1hBOVVGa3NtZkFjNGV1bDgxM0NWZkZZOHQ5eDJNK0RiN2YvNkswVlBNR0VQeWRaZFphM3VPd2ZRV0FBTDgzOEpSODEvY0M0Uk5SUkFkY2RWaXk2aW5EdXhKcVBYZUpXckZtM3lFalp0RWR3MFgrR0NZNXg0NmJJY00rcCthL3JqTnorK0EwWXpDQXpUQTBzcE1jRUxOd2gzTUNlZld3Q0hQc2dwcHp4UVVONGZmUk9Cb0pBL1hGS1ozUURVUk5EZ2VTTytTVWtEZW4rdnJCaXcrYk9MSS95RE9UTCsyWkN5aUZmRDFyck8rdXo1V25zSy9aS3FUY3c2ZlJFS1B4NWJCQXFHdVFxQWhEU2pDNXBvczJ3b21yT21XbUtrcGRsT0Q5cHZEYTlCZzJkSmtZdmRJS2hHSkM1VC9EUXVxVzJZVzE0Sm5IMmdYekc1aGw1K0IzeUsxaWc2a2hSNjVBMjJWaFZxV3kvOVZKS29qY0pSNDBUbEFwc3JvR2xHcnlCbVV4V3RwK1hZd3J1WVJBUkFPN3hRSk1FY1ppMm51TklHUU1OSW95Q1lDMGlCbXZzMldpQ2ZKaDNWSXVZUlhHREtlVjdlajZQaTVzSllRcm8zQ1IiLCJtYWMiOiJlODRlZWEyODcwM2VhODljYzlmZWZkOWQ4YTE4MjdmYmJjYzU2OWVkMmQ5ZTU4YzQ4MzYyNjMwYTZmYzBlNGFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-133719112\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1029508907 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1029508907\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-62946966 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:28:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ims4aGlvdVlDbWNBMWhwc2cvR0tENkE9PSIsInZhbHVlIjoiZkZhTThkeDEvS0xzcVI5cWdxVjRYKzZrU2RHTWtPV1M3YlZ1cHNzVUpORnBEOFhyZkVpdlFPSGZXaS9KdE9tOWtMQjU1ZGI1NE1qUS9CaG5kTEFPM2dMcmR5b1FCS3pOdEg3aVNJcm9lYnBCTUpEeGdGaXVzTHgwTEU5OUFhZEozTlZWSFFwVFU4T0E2KzVpdmN0RVRzZTU1NnFsRjhkZkV5dDA4b0xTdTRSOGhaM1k4K1BXek1SblNqMFE2bXNjcDIxSjdnZG5uVEx2dWJKcCtuZE5mSjBRSnJJY0RVcGp3L1A4ZHNIcTNDUWpTclIxZmtyZ0syaTd6d2dKbUdIdW4zbitoWURIK1VuN3ArZmxDekEzMkN5N2xYZVlibEdab3hmS3pBM0ovczA0YXM0aUZpaDdxbU1KRlAzSm1VNm50aSt1ZGhxK3pJTEZIQlBKZEt3azJCa1UvMWFGaWs5RTFHOVV5M0NLMTRwOFFqRU1kZjFZNmlmRFA5NHNkdURDYWVJN0JtTmNVckFEVkpva0ZJQUZZbDVGalNxR3YrVlFpZFJTWXFXci9jb0lUOVBLQ05HUmtLeUQvRElMQlhHdkthNXNTK3dCTE9UM3doS2FPdzV5c3NVa0FxdWVRYm5lMnd2NldVSFJIQVZHY2svRDBEU2NVUVRUVVd4cnJhZUEiLCJtYWMiOiI2YWFhNzY1YWFmZTExMWM2ZWI2ZTIxMGI2NGNmMTQ4YjUwYjZmODM0MjAyNzI5Y2UxNDlkMzZhMzRmNGUzYTY4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:28:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InpqSHltbVNnYUYwdEFRdXpLTHN4N3c9PSIsInZhbHVlIjoiVkk0L1JaMWVrM1pzWG93WlhOVndIcHBPb3l3NmZrOUVzZWlzdkswRkJ5eHJXY2lHK2ZnUEZ3aGZCZy9vWHkvOVBPS25BWXJWc3NGVWVPa1ZjZTlUUi9DcHBhUFhJV1dLZis2aUIxRElaQnl3ZGs1OE9JVmdMN1h2ZXdJRG5SbmtQUS94dDB6My9JNEsxMUJ1QWZ1b0ZXZjliY1hzTSt2QXp0U1FjQzljNGl6aUJkc2phRkkzbHpmK1h6VHBhUlU3bXB3NjhSTlMxRng0T0g1cWNvZzdPUDBCRVgwUy9JU1hnRTZvb0tRY0lIZUNleHc3ODgvOUxqY3gyQVM5T1FmTjliZWlBbEVuSXlsd3BVOWtXMll2QThFWExjZHpNd2tUcVQ0QTlFWW9Oc1c5cnlRTGtCcHdwVzRRS1UxcWZVRnhPVEFzMjY5eC90T3NWd2RFa0hyTVZwajFxUVFzZ01nbXdpMkY3aEhPcnVFNWdIaEVINTlvT2RJMnJGd01ET2hSZUNMMlhyZ2xscTNrTWFhOWFJTFJaYVI3S2pTS25Pb1BXQ0loVGZTSXRGZkNVZzdVNGlSOGZvZGgwY01JZXhsK1hnM3NXVkxRTEhqSFA1Z1dUUGNSbE5rM2Ztb0VrQlE3c1Nrb1pHMCtGYXExdTJVc28vTHA2MXMwYWZ6SmFVZmQiLCJtYWMiOiIxODljMjRiYWI0Zjk3OGJmNmJkNmIxMzlkYjc4ZmVhY2RjMTVkOTk5NmRmZTM4YzAxNzhhMjhlNzVlOTEwYjM2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:28:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ims4aGlvdVlDbWNBMWhwc2cvR0tENkE9PSIsInZhbHVlIjoiZkZhTThkeDEvS0xzcVI5cWdxVjRYKzZrU2RHTWtPV1M3YlZ1cHNzVUpORnBEOFhyZkVpdlFPSGZXaS9KdE9tOWtMQjU1ZGI1NE1qUS9CaG5kTEFPM2dMcmR5b1FCS3pOdEg3aVNJcm9lYnBCTUpEeGdGaXVzTHgwTEU5OUFhZEozTlZWSFFwVFU4T0E2KzVpdmN0RVRzZTU1NnFsRjhkZkV5dDA4b0xTdTRSOGhaM1k4K1BXek1SblNqMFE2bXNjcDIxSjdnZG5uVEx2dWJKcCtuZE5mSjBRSnJJY0RVcGp3L1A4ZHNIcTNDUWpTclIxZmtyZ0syaTd6d2dKbUdIdW4zbitoWURIK1VuN3ArZmxDekEzMkN5N2xYZVlibEdab3hmS3pBM0ovczA0YXM0aUZpaDdxbU1KRlAzSm1VNm50aSt1ZGhxK3pJTEZIQlBKZEt3azJCa1UvMWFGaWs5RTFHOVV5M0NLMTRwOFFqRU1kZjFZNmlmRFA5NHNkdURDYWVJN0JtTmNVckFEVkpva0ZJQUZZbDVGalNxR3YrVlFpZFJTWXFXci9jb0lUOVBLQ05HUmtLeUQvRElMQlhHdkthNXNTK3dCTE9UM3doS2FPdzV5c3NVa0FxdWVRYm5lMnd2NldVSFJIQVZHY2svRDBEU2NVUVRUVVd4cnJhZUEiLCJtYWMiOiI2YWFhNzY1YWFmZTExMWM2ZWI2ZTIxMGI2NGNmMTQ4YjUwYjZmODM0MjAyNzI5Y2UxNDlkMzZhMzRmNGUzYTY4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:28:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InpqSHltbVNnYUYwdEFRdXpLTHN4N3c9PSIsInZhbHVlIjoiVkk0L1JaMWVrM1pzWG93WlhOVndIcHBPb3l3NmZrOUVzZWlzdkswRkJ5eHJXY2lHK2ZnUEZ3aGZCZy9vWHkvOVBPS25BWXJWc3NGVWVPa1ZjZTlUUi9DcHBhUFhJV1dLZis2aUIxRElaQnl3ZGs1OE9JVmdMN1h2ZXdJRG5SbmtQUS94dDB6My9JNEsxMUJ1QWZ1b0ZXZjliY1hzTSt2QXp0U1FjQzljNGl6aUJkc2phRkkzbHpmK1h6VHBhUlU3bXB3NjhSTlMxRng0T0g1cWNvZzdPUDBCRVgwUy9JU1hnRTZvb0tRY0lIZUNleHc3ODgvOUxqY3gyQVM5T1FmTjliZWlBbEVuSXlsd3BVOWtXMll2QThFWExjZHpNd2tUcVQ0QTlFWW9Oc1c5cnlRTGtCcHdwVzRRS1UxcWZVRnhPVEFzMjY5eC90T3NWd2RFa0hyTVZwajFxUVFzZ01nbXdpMkY3aEhPcnVFNWdIaEVINTlvT2RJMnJGd01ET2hSZUNMMlhyZ2xscTNrTWFhOWFJTFJaYVI3S2pTS25Pb1BXQ0loVGZTSXRGZkNVZzdVNGlSOGZvZGgwY01JZXhsK1hnM3NXVkxRTEhqSFA1Z1dUUGNSbE5rM2Ztb0VrQlE3c1Nrb1pHMCtGYXExdTJVc28vTHA2MXMwYWZ6SmFVZmQiLCJtYWMiOiIxODljMjRiYWI0Zjk3OGJmNmJkNmIxMzlkYjc4ZmVhY2RjMTVkOTk5NmRmZTM4YzAxNzhhMjhlNzVlOTEwYjM2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:28:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-62946966\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1506543798 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/debug-product-creation</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1506543798\", {\"maxDepth\":0})</script>\n"}}