{"__meta": {"id": "Xc0cea02fbe9987a4d1751b5e5acffe13", "datetime": "2025-06-08 00:08:03", "utime": **********.380639, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749341282.695219, "end": **********.380671, "duration": 0.6854519844055176, "duration_str": "685ms", "measures": [{"label": "Booting", "start": 1749341282.695219, "relative_start": 0, "end": **********.297888, "relative_end": **********.297888, "duration": 0.6026690006256104, "duration_str": "603ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.297902, "relative_start": 0.6026830673217773, "end": **********.380675, "relative_end": 4.0531158447265625e-06, "duration": 0.08277297019958496, "duration_str": "82.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45576216, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00537, "accumulated_duration_str": "5.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.33897, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 68.901}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.355758, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 68.901, "width_percent": 12.849}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3650901, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 81.75, "width_percent": 18.25}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-394065943 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-394065943\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-442745532 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-442745532\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-635352045 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-635352045\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1126573070 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341140061%7C1%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjVvWHgvTy81VDIvczVtMXpWWi81SXc9PSIsInZhbHVlIjoidi9ZTENGLzUxeXBibG1sUFVNVjVveFViNTNVeElGcFF1bmJDVnRyaEZXblh2WHFzK0Y2dnRzQllhN2RVa0RyTkZMTzBWeXl4N1Fob0FFWTlkQndnM1pSaTRzNEUxM3JPckNDSWNSb20vYTJwSjIzdWJ6VWQ2bDRDOGVNc3BBVTQ5SnFsUjBjelh1REtvOUtTNTNwTTFiUncyaDlRUW9ZeEh3RTA1NlNYb2V5V2VDV3ZBS1FMVE9BcnZBNzBWUHdtbDFBS3VZY3JmdUpTRFRDeTZ1bU9TdHVnZ0JmNWtCZlA1elBhMVZSY296bGJReGxWSm5yNG1PUGFKb0g4Mzc2UUpscktHTjMrQjNSWjZoRjlxYXREZXZQRlRTeEZ3VU9ITWM4eXJLRU05REEzOFZYdm51K3U1RFl5bDRRVTkxamNZaXdHUkNLQk8vR1krOFdDRUFtamhrN2ExdEJpVVNEM2trVmpMNmJpaW5TUVE1Y043TnQ0Z2N4TEI0WnNTbVFZc0xWSWJCK2p5NGJxRVMyaXJNOTl0WS9hR1lodzcyYnRoVGIvYlJjM1d3dmJ3Z2ZsNWdHeDZlemhuNmlxREJrTEo3eEVUNEM5enRFN1NqOGlxbHpYN295SkxqUW5pRlFDelgwZVNLZHkxZ2NPVTBNcTJIeHVjNUwrVGpGNHA0T00iLCJtYWMiOiJjMDQyNTQwOTI0ZDhiYTE2NWFkOTk2MjA1YzlkYTE5ZWQ0MmUyMjFlZmYwZmIzYjlhNGM3NjI2NTUzZjc3YjgwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkFzaWNDQ1VFa0RXa3o4VkluT3FIT1E9PSIsInZhbHVlIjoiM2JUbFVsYmpDcDMwNUQzSEdpeDJGdzU5YzNGc1hXNFA4L2tlK3Y1YzlmNW9UejN6V2M5ekpXdkxWNjZDSlZGQmJyRlRucWpiTWd2aWZVOWpiZldkbWlkZ1Nubi9IWkFiUy9oTUROVks4eXpOTlVCV0g3TnV0dXpPMDJVVVpVczI2VlBodWZjelU3QmdRL0lNSTM0QS9UbEFMSEdHbm1ycVcrQ0pPUW5oN1piajNNZVd2TnVveEozQitiUExCai9NVDJ4anNKTlZFV056eHFvM2xjc0xOYnFTQnVWRGVDUU10VGtUQXZDd3Jpb3NPR29BRDFuTWN2K3ZvSmIyMkhudmIyRVFGQWVGMTJwdkNlY0RGT0NxTGxjbXVpbEEzZTNZWW9zTnoyOVlMR1NvWG9pdTBRd05RNzdQb3pHN2tzRGxJT2x6Y015V1EvcncvOHU1UjcwYUpQM0xORmFaanU3akYzRGRTY3pzY2ZSTUhDdVVMcnZuZkxJRHR2eGpMQzN4bDNoRnBHejJmT0I2cmh2bVRJSHFhNzJLQzFFUzRldHZwS3FOQWlCTUU5aG93bHVLV3U4eU1Xdmc3VkkzQzVMYWhiQlYrRW1sOGJUbXNqZDl0OG9lQmVrTGsxRGs0ejA3WjVhU0VSVTJNNEczdHlFOWFSeForVkMvOWZRT09KcmoiLCJtYWMiOiI4MDgyOWM2NDE1M2VkMGM4OWUyMjQ1NGZhNzNkNTExOGQwOWFkOWJmMDNmOWY3NzZlMDdhOGY0ZWRjZmNmMDUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1126573070\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-233501028 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-233501028\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1879380459 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:08:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpBWUs4SHhQelRTYWJhUkhPeWN2eVE9PSIsInZhbHVlIjoidEg4d0RWNVpaTG1GRytnYU93cklPTCt6ejNHZjIwemhjMFBPVC83VWs5R3ZuZnZ5NUdmU0U5eHdjbGZiWjBvKzZrR3lOVkYyQkczcGdrSXZJMndzUCs5dmkyZDBvS25yODY5L08zZ3Z6cWJXNERwT2F6YnMxeFJDSkZmOUFVRkdhNGMrekpVcXRsS01DenJYRWRDYzRUOW1qZ3cwUGNhUnE3Vi9RdWZuOENDeEJpU29zQlh1eThyZGRQSTJzaStCazVlcXRocG1LR3k3aTJuRWM1d1RoSnY2TTN2em56TEZrTTVROXJYTHFGbEVTR0VtUDNSU2tMRFBaVStjMzVtaUFxL2MwT0t6SmQ4ZlVCNENiblpWWndSUE9tTGNhYXpkZzJRQTZPVFZUbTZYUzY4MnJBdWZpb3NZaDlSdklFN21rUk5ySTdqMk9ibjgvMG5MY2FNcjBvZ05lLzliQW1ReStTNlgyTndMc1B1QXprQkd6MHlSb3FUKzR3eWp3aUNqbWNIaVZCcHFtMmlacysxWnpCL3RMTkRYeHI2bFM4cklBL2J1UTZreUdXRlpvWnExV1BzS1FocUd0d3VRWjQ0U0VMREdwRHhnUmc0SjlVZCszSmJ0YnFpV2dNejVsdW54dTNMR1A4Mm41ZGcxNXVSTzlTb3RZM0ltRXBhRm9tMGUiLCJtYWMiOiIxMDBmODQ4NjM4ZDI3YzFmNThhNDNkYzAzNTkxODM1MjM1Njg5NTU0ZjcwZWJhMDg3NjY4YjFjNDU1YWViZGQ0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:08:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImNPTUhFeFhRcHlkWFpuckZXckNiK1E9PSIsInZhbHVlIjoibjljYTg0SkU0TU5FbkJkeUxwRGZWZmxxWlJjb2ZoamhPQWZ0NUxEbHAyRlBobVlYS3I2V0RBMDFWK1dEaWxLZk95ZThrV3IvWldtK0Z3a0N3TE9acjMwaG5rQlE1LzNYT2srQzBmL3pUeEwrQk02YmZKRytGeEJkKzZQeHFHKzA2MjFIZy9kUkY2Z21JNFBrSld0OTVuRUFndHhKcDBHK1pGNCtQY0l5cGYyVWlFRXJpV2c4RkZOcEV0djBxaE1wN2hzRzZjUmJSME15MTliZlNvR0Y3ZHJjZkpQeEpJeWFmdEoyMUZSSytqMklOdVNIU2ExcmdxN3VxMUtJZjk2QjlJalROQmVBZkE3MzBSN1hubERrRXYvMXRMckgvS2pCZklEK21TSVRkTUlyTGpmdzRuZnN6VjFZcnlpdGp3UkFEUTV4dmdIWnNKZ2RGdnpvUUVtNDFPZGxidHBBSmNTVnlSQU9ZT0VtWm5DdTJwMTVFRlE2L2orWHpIdlJnczBTN25yK1ExUi85WitadDB4SHRyVEhqMCt3TDhYaFlMUlJXQzVXOFNzdmFzMWdsUFpmM0dCWkphRTlTWkN0OFZQL3F3VzRDKytaZWFpcjd6TUMySXA5TDBlUm9MQzF1bXpEQmNUbEtJZTFrL1l4c0tDOXlyM0szV0E3NWx4YlhqNWsiLCJtYWMiOiI0ZTBlZTQxMDA1MzcxMmQwZTM0MTU1OWEzNjhkODg3NmUyMjI2MzBmYzdjNDdlOWJhYTkxMDRkODBhZDA4ZGY2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:08:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpBWUs4SHhQelRTYWJhUkhPeWN2eVE9PSIsInZhbHVlIjoidEg4d0RWNVpaTG1GRytnYU93cklPTCt6ejNHZjIwemhjMFBPVC83VWs5R3ZuZnZ5NUdmU0U5eHdjbGZiWjBvKzZrR3lOVkYyQkczcGdrSXZJMndzUCs5dmkyZDBvS25yODY5L08zZ3Z6cWJXNERwT2F6YnMxeFJDSkZmOUFVRkdhNGMrekpVcXRsS01DenJYRWRDYzRUOW1qZ3cwUGNhUnE3Vi9RdWZuOENDeEJpU29zQlh1eThyZGRQSTJzaStCazVlcXRocG1LR3k3aTJuRWM1d1RoSnY2TTN2em56TEZrTTVROXJYTHFGbEVTR0VtUDNSU2tMRFBaVStjMzVtaUFxL2MwT0t6SmQ4ZlVCNENiblpWWndSUE9tTGNhYXpkZzJRQTZPVFZUbTZYUzY4MnJBdWZpb3NZaDlSdklFN21rUk5ySTdqMk9ibjgvMG5MY2FNcjBvZ05lLzliQW1ReStTNlgyTndMc1B1QXprQkd6MHlSb3FUKzR3eWp3aUNqbWNIaVZCcHFtMmlacysxWnpCL3RMTkRYeHI2bFM4cklBL2J1UTZreUdXRlpvWnExV1BzS1FocUd0d3VRWjQ0U0VMREdwRHhnUmc0SjlVZCszSmJ0YnFpV2dNejVsdW54dTNMR1A4Mm41ZGcxNXVSTzlTb3RZM0ltRXBhRm9tMGUiLCJtYWMiOiIxMDBmODQ4NjM4ZDI3YzFmNThhNDNkYzAzNTkxODM1MjM1Njg5NTU0ZjcwZWJhMDg3NjY4YjFjNDU1YWViZGQ0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:08:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImNPTUhFeFhRcHlkWFpuckZXckNiK1E9PSIsInZhbHVlIjoibjljYTg0SkU0TU5FbkJkeUxwRGZWZmxxWlJjb2ZoamhPQWZ0NUxEbHAyRlBobVlYS3I2V0RBMDFWK1dEaWxLZk95ZThrV3IvWldtK0Z3a0N3TE9acjMwaG5rQlE1LzNYT2srQzBmL3pUeEwrQk02YmZKRytGeEJkKzZQeHFHKzA2MjFIZy9kUkY2Z21JNFBrSld0OTVuRUFndHhKcDBHK1pGNCtQY0l5cGYyVWlFRXJpV2c4RkZOcEV0djBxaE1wN2hzRzZjUmJSME15MTliZlNvR0Y3ZHJjZkpQeEpJeWFmdEoyMUZSSytqMklOdVNIU2ExcmdxN3VxMUtJZjk2QjlJalROQmVBZkE3MzBSN1hubERrRXYvMXRMckgvS2pCZklEK21TSVRkTUlyTGpmdzRuZnN6VjFZcnlpdGp3UkFEUTV4dmdIWnNKZ2RGdnpvUUVtNDFPZGxidHBBSmNTVnlSQU9ZT0VtWm5DdTJwMTVFRlE2L2orWHpIdlJnczBTN25yK1ExUi85WitadDB4SHRyVEhqMCt3TDhYaFlMUlJXQzVXOFNzdmFzMWdsUFpmM0dCWkphRTlTWkN0OFZQL3F3VzRDKytaZWFpcjd6TUMySXA5TDBlUm9MQzF1bXpEQmNUbEtJZTFrL1l4c0tDOXlyM0szV0E3NWx4YlhqNWsiLCJtYWMiOiI0ZTBlZTQxMDA1MzcxMmQwZTM0MTU1OWEzNjhkODg3NmUyMjI2MzBmYzdjNDdlOWJhYTkxMDRkODBhZDA4ZGY2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:08:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1879380459\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-752114852 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752114852\", {\"maxDepth\":0})</script>\n"}}