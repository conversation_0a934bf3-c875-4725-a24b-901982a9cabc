{"__meta": {"id": "Xff2696b58bf823cba3229d75f9377c7f", "datetime": "2025-06-30 15:34:38", "utime": **********.268347, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751297677.823353, "end": **********.268361, "duration": 0.4450080394744873, "duration_str": "445ms", "measures": [{"label": "Booting", "start": 1751297677.823353, "relative_start": 0, "end": **********.199039, "relative_end": **********.199039, "duration": 0.3756859302520752, "duration_str": "376ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.199048, "relative_start": 0.37569499015808105, "end": **********.268363, "relative_end": 1.9073486328125e-06, "duration": 0.06931495666503906, "duration_str": "69.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45348680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01682, "accumulated_duration_str": "16.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.235141, "duration": 0.01575, "duration_str": "15.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.639}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2593632, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.639, "width_percent": 3.805}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.262378, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.444, "width_percent": 2.556}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-730645961 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-730645961\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-729510102 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-729510102\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1256066293 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1256066293\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1037646803 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297675919%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFXdVVrM3pXQU1QUnRZU3lhbmNXT1E9PSIsInZhbHVlIjoicHAvRVk5R0UrUW5KQk9mQm9iTi9DUGNudkZtOEJlcnl0OEtUaFRGbGg0KzVVNlV5MzFMek9hUTI1aXRackNLbWdveFAvdWRNeUt0Y0pXekc5N0x6bzlTRndQMnFlbDNmS3lnMzI4dlN6c04zSWZ1aEVobEk1UTRFWWNqQmlRTHFTSWhmUURpb3VsbnREK3BoZHdvWmQzaTZ2VVoreWNrSThSa3FGVVJBQ0lwRjZjdDNSOTBKajlwcDA0RElRMjhqdmQzSnh5ZTVUTlUreGZKbnBIc2gxbVdQMldST1g4L1lBT0MrSTZMOW9Zd3JRVHBicm9NQVRSQ0VZTHh5Y28rek55YXoxWVFDRHpXYXg1bVVSZEw5Vk0wQTRNdUZUaWIzaTFuY3BrSkxpMHNNU3lMc2dkV0FYcVZaUkF5ZWNTKzBqL2RhOWtNNUlXZGxvaGdQb0U0M2NMWFFxRTRMK0FOQmxYb0tZdFg3Mk95STJsRGtvNlM5eUp1dXcyREJPbjI1ckZSVkJRelE1eDdLcnJhWFJxTkdQeHl4cDY1Tk4yNWwxcWdxSUlEWlZBTUJWQlVxT3UvRFpqeGlzQWRhbk02TFpjcTdtNzd6akREZmNscTNpb3FRMGcxbFhBdEdKeHVBcUh5dGdaVWNrTlZnT1oxa21lOHdtdkMyalZZeUErbDciLCJtYWMiOiIxMWIxYWY1ZDcyOGM0ZGZjNDFiNDU2NThmYzhkNGVhOTU0YjQ4ZWNhNjRkNGVhYTcyNTRjYjk1ZjA0NzNkMTlmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkYzbUxUbHRTQ2wwRUU2RzFzbjZEUHc9PSIsInZhbHVlIjoiQ2hJS0VsZWc3bU53YlY4ZTlsUEpFdUJkUzlmRW5DeVVUeEpOTGN3ZmxDMWM3Y2ZFd1lPZldTMzJMY2pHeHdvRVJieVJtd1ZLS2luWWZnbkxTSmlMMCtpM01zMVBES2M2R3JZZjJwYlpWaS9SSCtjaXd6Nk9QSnR1TWkrZzNMMXhYZUozVVd4NkhlbzBWajlkYjlPQzdIbEZkUXRSelBKenk1QWM0T1ZqV1ZqTm42WCszN1ZIMWExR0JUakx2NmdDSXFpRU40K3d4RDN4dDZkZDhjS3hYbVhrM0VqRUJOMXNjcERMY0MvdGhFNnhaK3BWU3lRRk82d1gvMVNFc21sanAxTmZCTU1WaStpMENsb2JVUmxmYVcyR2lPSEJIMm9OakI2dlY1b0pERGFha1lvMXNoeTEyekFodUJtWWxVYWVaenhQaGx2N1hQZ1NCbGt1VHp4UG9mQ0QvWE52ZjNxZDBuVTBGOG9KUjdjbTRZanZSY3RLbi9HcWZsVkJtM1BlTVFuc0xzc3Q3N2E5SHp6OVBLdzJEZ0ZKUU1oK2RHbm8rR0FFSloyWWlSb3ozcC9yV0ljdFBML05CT01uWXIxZFJGNEdLdHQ4eVorTHlubVJFSVJyM3kyOHVXU0FtSjkxaFlwa1FsSzFlNUhGckdCOWIyR21obVdhRjRZTDF2T0giLCJtYWMiOiIzN2Q2YzlkMzA0OTQ5MTliOTZmNzI5ZDIxYzFjYzFkMzJmZjExNDVkODIwN2EyNTg3MDdiNWMwMTkyYTNiZDhlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1037646803\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1363712908 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363712908\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-199566287 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVQMVlFRkRFdjROWnNtYU53U09RcGc9PSIsInZhbHVlIjoiYmFaZ0tqdDdJaGQ1RTFZUm5OK3FBclNheC9KY09TbTVWdVVxT093RW4wYWYvYm5VZ2p1ejRQSlNVSm5aZkV1ME1OdmUvSHJpdEhwVzc0R1BLZXV5dmQ3R3V4RnJPRmlIQ3Z6NlhpU3BYelFUNUFVVDRyRkRIOE04NzRIT1ZuOTJNeHlxTTB2T3B2RXEycUxPb0Y1OWUrRXV5VXhJWXFNUEFKcUJEaTZTQUtDamN3c3p5bHYrVzlIb0srQlIzcU1pVGVUMHNFNlhPTDkvcVJDMituZ0x2OHd5SExZS095Qlg1emNZQzBwUFVrMmx6RURIS1p4V1d2OXlEQlh1NElrV1VaRldVckpPTVZaVUlZellKSjlWSWI1WlRvZkF3cE9nb1JXNFZuVEdJbXRoRGN5eTcrL3U1RVFkaW52TzN6Rmo5bmxqZlF4N1N1UGk5OFZmM0ZicWFudjNuMlMzNWUwL0M3UWJCejc0ZXA2NjRRTHhIWnkrMERTWmRFaU1BaFY4Um1PbTR6WVVzZHZLUFl6YzFOcEtPREFxMzV5SkNyNjFoQ2pyU1lFN2Y2Y0pOQlBIVDRUUlZVZlhQMmpGVjR3S2oyNHRieU1lczVDOEhlZmxBM1djekl2WDljcUpKUnZrL0xrdlMyOEo3c2lIbzRUWno5K0JvR3YxZFFyTG45WHgiLCJtYWMiOiIxMjcyMDVmNzFlZDNhZGJhMmRjMzE3OTdlMTk0M2UzODYxMjhlM2RkMGE5MzJlNDk3YmZkZTBkODNhYjdmMmVjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImIzRUcyWExKU29ZWUhUcXMyb1R1cFE9PSIsInZhbHVlIjoiNHI2aVRybi9sT1h5dThWWUNMTDlkRHJISFJhYk1SK3dQUHBnWjJxNGpRMU0xVnhYZ3FETlpjaWFDeWpEMTc3QUJnVEFDeDB5TGtsTEtxUkJsL2JxM2ZnU3FkaGRqSlU4ckpON3BxK29xSlpaSFFGcU14Rm9GT0VhK2NkS3dNaXhTRytlcmMreGRScnd0WUZIRlVQem5CblVTNmxBNU00NzNzd2tBMlRPbWJpWHdaZXl6TjE4elBHY2xBdXlPSlRSekFNTTl4MGdrb3VpNktOcU5YdnhESlZEemlsVTNJb3pWRG14YjJwTzdxM2Zsa21SZmdtTS9uQ0NIeHZ2dUI5ZzQ2cWtBdTBocWp4NitEQWEyVlNmUWRDKzZHSk8weVQ1bG9GWjkvam1pdE54VXdHSXlOK2dMWkFqRjRxdFBuQklib0pQcVZJMERZR3JRbHRGUEVkNjdXbEtybFZ1UDg2SkJnRW85dGc2cHI5dEEwZy9ldDJSZ285dUc1MzRxMS9TVktOVkNrUXB4S2hKZ2MwQ3k4MmpKT0VLdUNqaDljTGJEZ2lVNHpib0FUd1BPOHpCY0FFSGdQRytxUTRDMVVJMDMvZS8wRVJ0eGhBeWs4RnJyQ016eGhObWMzVm91STBKMlJlYmxKN1RkbGFqYXVwUEZTRUFoU0RTeUJpQnIzUlQiLCJtYWMiOiI5OTZjYjNiOWM5YzQ1M2ZlODdiMmJmMjg3Mzg0ZDQyMTRkNmUwM2Y3NjA2MzFlY2VmY2M1MmI1ZDM0MzI0OGJiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVQMVlFRkRFdjROWnNtYU53U09RcGc9PSIsInZhbHVlIjoiYmFaZ0tqdDdJaGQ1RTFZUm5OK3FBclNheC9KY09TbTVWdVVxT093RW4wYWYvYm5VZ2p1ejRQSlNVSm5aZkV1ME1OdmUvSHJpdEhwVzc0R1BLZXV5dmQ3R3V4RnJPRmlIQ3Z6NlhpU3BYelFUNUFVVDRyRkRIOE04NzRIT1ZuOTJNeHlxTTB2T3B2RXEycUxPb0Y1OWUrRXV5VXhJWXFNUEFKcUJEaTZTQUtDamN3c3p5bHYrVzlIb0srQlIzcU1pVGVUMHNFNlhPTDkvcVJDMituZ0x2OHd5SExZS095Qlg1emNZQzBwUFVrMmx6RURIS1p4V1d2OXlEQlh1NElrV1VaRldVckpPTVZaVUlZellKSjlWSWI1WlRvZkF3cE9nb1JXNFZuVEdJbXRoRGN5eTcrL3U1RVFkaW52TzN6Rmo5bmxqZlF4N1N1UGk5OFZmM0ZicWFudjNuMlMzNWUwL0M3UWJCejc0ZXA2NjRRTHhIWnkrMERTWmRFaU1BaFY4Um1PbTR6WVVzZHZLUFl6YzFOcEtPREFxMzV5SkNyNjFoQ2pyU1lFN2Y2Y0pOQlBIVDRUUlZVZlhQMmpGVjR3S2oyNHRieU1lczVDOEhlZmxBM1djekl2WDljcUpKUnZrL0xrdlMyOEo3c2lIbzRUWno5K0JvR3YxZFFyTG45WHgiLCJtYWMiOiIxMjcyMDVmNzFlZDNhZGJhMmRjMzE3OTdlMTk0M2UzODYxMjhlM2RkMGE5MzJlNDk3YmZkZTBkODNhYjdmMmVjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImIzRUcyWExKU29ZWUhUcXMyb1R1cFE9PSIsInZhbHVlIjoiNHI2aVRybi9sT1h5dThWWUNMTDlkRHJISFJhYk1SK3dQUHBnWjJxNGpRMU0xVnhYZ3FETlpjaWFDeWpEMTc3QUJnVEFDeDB5TGtsTEtxUkJsL2JxM2ZnU3FkaGRqSlU4ckpON3BxK29xSlpaSFFGcU14Rm9GT0VhK2NkS3dNaXhTRytlcmMreGRScnd0WUZIRlVQem5CblVTNmxBNU00NzNzd2tBMlRPbWJpWHdaZXl6TjE4elBHY2xBdXlPSlRSekFNTTl4MGdrb3VpNktOcU5YdnhESlZEemlsVTNJb3pWRG14YjJwTzdxM2Zsa21SZmdtTS9uQ0NIeHZ2dUI5ZzQ2cWtBdTBocWp4NitEQWEyVlNmUWRDKzZHSk8weVQ1bG9GWjkvam1pdE54VXdHSXlOK2dMWkFqRjRxdFBuQklib0pQcVZJMERZR3JRbHRGUEVkNjdXbEtybFZ1UDg2SkJnRW85dGc2cHI5dEEwZy9ldDJSZ285dUc1MzRxMS9TVktOVkNrUXB4S2hKZ2MwQ3k4MmpKT0VLdUNqaDljTGJEZ2lVNHpib0FUd1BPOHpCY0FFSGdQRytxUTRDMVVJMDMvZS8wRVJ0eGhBeWs4RnJyQ016eGhObWMzVm91STBKMlJlYmxKN1RkbGFqYXVwUEZTRUFoU0RTeUJpQnIzUlQiLCJtYWMiOiI5OTZjYjNiOWM5YzQ1M2ZlODdiMmJmMjg3Mzg0ZDQyMTRkNmUwM2Y3NjA2MzFlY2VmY2M1MmI1ZDM0MzI0OGJiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-199566287\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-224458130 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-224458130\", {\"maxDepth\":0})</script>\n"}}