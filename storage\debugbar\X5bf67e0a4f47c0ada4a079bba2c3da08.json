{"__meta": {"id": "X5bf67e0a4f47c0ada4a079bba2c3da08", "datetime": "2025-06-30 14:55:52", "utime": **********.261113, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.74513, "end": **********.261134, "duration": 0.****************, "duration_str": "516ms", "measures": [{"label": "Booting", "start": **********.74513, "relative_start": 0, "end": **********.139727, "relative_end": **********.139727, "duration": 0.*****************, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.139737, "relative_start": 0.****************, "end": **********.261135, "relative_end": 1.1920928955078125e-06, "duration": 0.****************, "duration_str": "121ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02887, "accumulated_duration_str": "28.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.20173, "duration": 0.022879999999999998, "duration_str": "22.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.252}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.234192, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 79.252, "width_percent": 1.94}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2433841, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 81.192, "width_percent": 18.808}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=2uy8by%7C1751295328208%7C1%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFLa3oxU3NxZnJZSHRmTUg0dWpYVWc9PSIsInZhbHVlIjoiVXJNWHRVWmd5S3NLTE9EQklwTUtwSGVRSlVmelhUSkRiNUNsWnZSRy9XWCtkNW55NHlEeXVrcERiTEhtaGR4NnlBR1IvMkFFdjVuWmpZc2szWForM3VhaVl2alpEZXNKMWJpYUNqbUhTeWczV3MrOUNOdHhvS2UxZmR0TklGbGxzR2pQUVEwWlR0NVdEelhhQXlZMXZpZm5aZXRpMUUxTEFLQVFiMkllRnRMcitvK1ZCUys3bmZvU0QrMlB4OTNzWm1EbG5FTU5XNUNpZk1QWHUxNkpublMwWHdzbXpnOGFzMzlKdVpzenJvQXZCa3NKcVp5dUdRaEd3VWNGdVltSGNoR3Q3cjMvdkVoR2gxQXAwZFBxQ0diQnVoZDk0UFVNajRaS3JTd050bE5aL1E3TEttZGx2VFBKTGI2RERYc21OcUgrQnFlcXJQTkdhSmptYU1kdEVoWDBsb05Ibi8xYXQyZDZobFhtdjloQ1E2cUdBZE5qQWlQUmRTZU9YUkM4SmU3SFlVSVd1S1dKOU95VkFHQXVGWTRQV3JxV1NuaEltUXQ2cU1OSGFXaTRTMUFjVGtiaDNCOWp6bk5IVFNhU0RDakY0YWtwQ2FpblBTOGFJeGNCVmwraVYyN2g5UjcveFJ0RFlzQnN2WkdSUXB0Q2Z2Uk12OU5ZRFhVT2E1elMiLCJtYWMiOiJkODg2NDliM2I1NDA1OTFlMmVmY2RkNDcyN2JlMTJlZTdkNDNhN2ZiZDE2ZmUxYzkzMzBjOTA4MWI1NWY0OTgzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InNDcTY4STgzTEFiNnVwVDB6SVNueHc9PSIsInZhbHVlIjoiNXVsNnpNRy9YUktuYitkbkE2VHBnUm9PT045dXR5YjJQbFNRU2FYdmtSVGg3SUZtZi9adjE4ZmR2TkFjNWVHUHlBdFpNNWE0ZkZ2TzN5UGFZK0t6VmxGQVRBSDV4blBIR0F1d1dUMHB5QWhza0VBRWRxNFpJOTh6VldrSkVwYm5mOHNkeXo2cE01c1R6Ulo5ZjQrVHlkamYyN0RCWDhTdDR0RFpLV0VMZngrTVA1UG80NXJ1cFFaWlNyUkludTZPcXN6M1VOQlZOZEtNN085emNIRTVzaG1wZXZoek9HbnkxQmVhblpmdTFlemxHRnVmMzBpS2dKVkZDa05nZ1dvRG5sc3NLYUxYV0hQRDVkODgwdGxia29RRy8yVlU0anZhMExSdnI2QTc5TEpwYnRnUFcyMnVDUXZTeDRueGU4aUpMeHJiQW0yM3MwRDZ3cTZGaHpRanVyYU5SRTgvQkVCTjZMSjE4aGo5Yk5TejA3eFpsZUxaN0dLbTM3MmZnRVJURGw5VjJIcXVQdytPdlhYRHJWcVlLZXp2TDRRMFlDU25QOGk5aDBSNTcwTkJwODBhNEliZHBKb0ZXYjZRZDNRQ1V6c1p5bFMvc1RmbkRsS2pMN1pPZ0pWMERHME1FVGdOM0w1ZndqTSttUFFxL3dVSks0N2R6cTVLR3BWTXpTSjkiLCJtYWMiOiIxMjM4N2UwYzVkMjNkOTM2ZDE2ZDMyYzY0ZTU3M2U0YTc1ZjE0MmFiMDU5MThmMTgwZjg1MmQyZDRhOTFlYmE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1207409803 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rf3imG1lPKr2xfPRA9rNVzJMke6dPWK1Wb6HvZ8N</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1207409803\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1365077146 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:55:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ing0Tk9Va3F2SEx4alJtQURuWlhIcGc9PSIsInZhbHVlIjoiRjNTZm9VZnpEYjd2Zm5TcDkxZnlJcEJDck9rbmdKaGZyYStVMlBHQzZrZlJJamN0SGVKTzlKQlRncFdOQUVSamVpZkJhNFluUktHUG13QmtNMFVFeHE3Ui8vODlpdnAvOEk4aWw3eUFNTnJ5VFFscWlpckhrZ0NTcHVkQmxnZnc3RFNJSFRYOFNlTGkvZVk5WXJ1UGJ5d292RnpCcGRHUnpBcWFrYmdVYWQvNjI1RUJsd0F2V0dnNU95bUlQc0cxcjBJbmpwWUd0NERLWTNTa2R2dW54VUJyMmxoMUhIczVLcndnelVUTUM3NjJkUnp4R0dpQTYyWFBsalVJT09BQWgzbWJaczdlUHlmanN1RkhVRUtwcUlnTlJBWFV5QnRMMTVreXJUVXJTdU9pc1k4NXMwRUVjdWduU21oTytFbEJ5b1NWRTVXYTVzblZpZGlUanM5emR0ZzNNSkQ3ZVJ6SmV6WDJZOUlpVTk2UFlGaklZSGVZZmc3TWhVN0lFVnMzZDQyS214YmtUakFVdFROU1JYdHRTWFdPdzBQU3V2aFIwckErN1IwOWNTNW9YMWdYMmlFVlkrVjd6aU1xR1V1a2NzUTJEd2pKTkl5Z1ZjUDdqTHFWc29LZGZPeEtpOUU2ZVBSWTRYbHZZb29lUElMZmsrSzdMU05QdG5kYXJJYS8iLCJtYWMiOiI3YWZhYjFiMTM0NjY5N2E5MWQwYzkzZTNmYWQ3ZTRiNzM4ZmM4MTY0N2ZkZDU3YzgxZmYyMmY1ZmIzNjkxYjRmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:55:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNzUUFCTjArTEVWU2R2c2NwSzZidEE9PSIsInZhbHVlIjoiNE5UbFkxcHpzQ3htV3hORG5ZTU9MWGRpRFZqampjcE1odnRaZWxXN1JSYmc4T3AwMTZPNE1DUkNqVWV1SXE1MWtqTnNLK2dKVzlMdGJhbEtvbEFOUFpZczZzUTZYNnF1aW9XUkkxZWFKT0RDSnU2U3NTMG83YlVOQkp1bFVPSDQ4UnNINkE3MnNpVG5hSGxkZElJWnFJUm9WRU5SQnJndHQvUDVUWE0rWCtLc0NIN0c0eWxaSzRmcTJtajZqdHRZN04vRCtVWGhVdUtkeldtZTBYRVIxWldzM2RoRXRHbHZ3dzhWTm9DWHJwTERZa2gyTWJ6bkw0ZC96SjF1dXEyWFZTeUxoOUhoRlBCV05DRDNSOUVYdEN3emZodUZoZFhSbmF6ZmliZmxkS0pCdDU2WENXeDF5dDFIeEZqL2wrRFhGMndXZ2F5WldnR2E1bENveENQL25uajZGTVZ5a3FpNFlUSHp4SWxWYk5CZ0ZIVStLbDhmais1czZiUndMcUlaM2pUejN4a1ljeEgzcDkwNUE3TGRjNVdGOExHd0hVVzFYNU1HTytIVEFwcERWN20rQmlISVpIRDZacWxMWFEyYU5jdEZGUXEvRnZtNzZ3NC8xQXBNWmQxRGNXSXMzUzNSU1hCRGpDdU9PSXpHVFlDR3Jxc08rdkwvTnNwc0RqOW4iLCJtYWMiOiI2MmI2NWUyYjVjMTQ1NWNiNDU4NzYzN2Q1MTRkOTBmMDU2MmYwOWZjZDMxZTc3YzkzOTNkNGM4OGU5MjIyYTBkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:55:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ing0Tk9Va3F2SEx4alJtQURuWlhIcGc9PSIsInZhbHVlIjoiRjNTZm9VZnpEYjd2Zm5TcDkxZnlJcEJDck9rbmdKaGZyYStVMlBHQzZrZlJJamN0SGVKTzlKQlRncFdOQUVSamVpZkJhNFluUktHUG13QmtNMFVFeHE3Ui8vODlpdnAvOEk4aWw3eUFNTnJ5VFFscWlpckhrZ0NTcHVkQmxnZnc3RFNJSFRYOFNlTGkvZVk5WXJ1UGJ5d292RnpCcGRHUnpBcWFrYmdVYWQvNjI1RUJsd0F2V0dnNU95bUlQc0cxcjBJbmpwWUd0NERLWTNTa2R2dW54VUJyMmxoMUhIczVLcndnelVUTUM3NjJkUnp4R0dpQTYyWFBsalVJT09BQWgzbWJaczdlUHlmanN1RkhVRUtwcUlnTlJBWFV5QnRMMTVreXJUVXJTdU9pc1k4NXMwRUVjdWduU21oTytFbEJ5b1NWRTVXYTVzblZpZGlUanM5emR0ZzNNSkQ3ZVJ6SmV6WDJZOUlpVTk2UFlGaklZSGVZZmc3TWhVN0lFVnMzZDQyS214YmtUakFVdFROU1JYdHRTWFdPdzBQU3V2aFIwckErN1IwOWNTNW9YMWdYMmlFVlkrVjd6aU1xR1V1a2NzUTJEd2pKTkl5Z1ZjUDdqTHFWc29LZGZPeEtpOUU2ZVBSWTRYbHZZb29lUElMZmsrSzdMU05QdG5kYXJJYS8iLCJtYWMiOiI3YWZhYjFiMTM0NjY5N2E5MWQwYzkzZTNmYWQ3ZTRiNzM4ZmM4MTY0N2ZkZDU3YzgxZmYyMmY1ZmIzNjkxYjRmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:55:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNzUUFCTjArTEVWU2R2c2NwSzZidEE9PSIsInZhbHVlIjoiNE5UbFkxcHpzQ3htV3hORG5ZTU9MWGRpRFZqampjcE1odnRaZWxXN1JSYmc4T3AwMTZPNE1DUkNqVWV1SXE1MWtqTnNLK2dKVzlMdGJhbEtvbEFOUFpZczZzUTZYNnF1aW9XUkkxZWFKT0RDSnU2U3NTMG83YlVOQkp1bFVPSDQ4UnNINkE3MnNpVG5hSGxkZElJWnFJUm9WRU5SQnJndHQvUDVUWE0rWCtLc0NIN0c0eWxaSzRmcTJtajZqdHRZN04vRCtVWGhVdUtkeldtZTBYRVIxWldzM2RoRXRHbHZ3dzhWTm9DWHJwTERZa2gyTWJ6bkw0ZC96SjF1dXEyWFZTeUxoOUhoRlBCV05DRDNSOUVYdEN3emZodUZoZFhSbmF6ZmliZmxkS0pCdDU2WENXeDF5dDFIeEZqL2wrRFhGMndXZ2F5WldnR2E1bENveENQL25uajZGTVZ5a3FpNFlUSHp4SWxWYk5CZ0ZIVStLbDhmais1czZiUndMcUlaM2pUejN4a1ljeEgzcDkwNUE3TGRjNVdGOExHd0hVVzFYNU1HTytIVEFwcERWN20rQmlISVpIRDZacWxMWFEyYU5jdEZGUXEvRnZtNzZ3NC8xQXBNWmQxRGNXSXMzUzNSU1hCRGpDdU9PSXpHVFlDR3Jxc08rdkwvTnNwc0RqOW4iLCJtYWMiOiI2MmI2NWUyYjVjMTQ1NWNiNDU4NzYzN2Q1MTRkOTBmMDU2MmYwOWZjZDMxZTc3YzkzOTNkNGM4OGU5MjIyYTBkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:55:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365077146\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}