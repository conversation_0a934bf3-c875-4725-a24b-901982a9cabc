<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\AdSenseHost;

class AdUnit extends \Google\Model
{
  /**
   * @var string
   */
  public $code;
  protected $contentAdsSettingsType = AdUnitContentAdsSettings::class;
  protected $contentAdsSettingsDataType = '';
  protected $customStyleType = AdStyle::class;
  protected $customStyleDataType = '';
  /**
   * @var string
   */
  public $id;
  /**
   * @var string
   */
  public $kind;
  protected $mobileContentAdsSettingsType = AdUnitMobileContentAdsSettings::class;
  protected $mobileContentAdsSettingsDataType = '';
  /**
   * @var string
   */
  public $name;
  /**
   * @var string
   */
  public $status;

  /**
   * @param string
   */
  public function setCode($code)
  {
    $this->code = $code;
  }
  /**
   * @return string
   */
  public function getCode()
  {
    return $this->code;
  }
  /**
   * @param AdUnitContentAdsSettings
   */
  public function setContentAdsSettings(AdUnitContentAdsSettings $contentAdsSettings)
  {
    $this->contentAdsSettings = $contentAdsSettings;
  }
  /**
   * @return AdUnitContentAdsSettings
   */
  public function getContentAdsSettings()
  {
    return $this->contentAdsSettings;
  }
  /**
   * @param AdStyle
   */
  public function setCustomStyle(AdStyle $customStyle)
  {
    $this->customStyle = $customStyle;
  }
  /**
   * @return AdStyle
   */
  public function getCustomStyle()
  {
    return $this->customStyle;
  }
  /**
   * @param string
   */
  public function setId($id)
  {
    $this->id = $id;
  }
  /**
   * @return string
   */
  public function getId()
  {
    return $this->id;
  }
  /**
   * @param string
   */
  public function setKind($kind)
  {
    $this->kind = $kind;
  }
  /**
   * @return string
   */
  public function getKind()
  {
    return $this->kind;
  }
  /**
   * @param AdUnitMobileContentAdsSettings
   */
  public function setMobileContentAdsSettings(AdUnitMobileContentAdsSettings $mobileContentAdsSettings)
  {
    $this->mobileContentAdsSettings = $mobileContentAdsSettings;
  }
  /**
   * @return AdUnitMobileContentAdsSettings
   */
  public function getMobileContentAdsSettings()
  {
    return $this->mobileContentAdsSettings;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param string
   */
  public function setStatus($status)
  {
    $this->status = $status;
  }
  /**
   * @return string
   */
  public function getStatus()
  {
    return $this->status;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AdUnit::class, 'Google_Service_AdSenseHost_AdUnit');
