{"__meta": {"id": "Xa0d76fd8e22b30d3016902b291b6028e", "datetime": "2025-06-08 00:08:08", "utime": **********.116347, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749341287.425059, "end": **********.116369, "duration": 0.691309928894043, "duration_str": "691ms", "measures": [{"label": "Booting", "start": 1749341287.425059, "relative_start": 0, "end": 1749341287.955567, "relative_end": 1749341287.955567, "duration": 0.5305078029632568, "duration_str": "531ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749341287.955579, "relative_start": 0.530519962310791, "end": **********.116371, "relative_end": 1.9073486328125e-06, "duration": 0.16079187393188477, "duration_str": "161ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48128536, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01934, "accumulated_duration_str": "19.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.021656, "duration": 0.01351, "duration_str": "13.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 69.855}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.052089, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 69.855, "width_percent": 3.775}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.084114, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 73.63, "width_percent": 6.463}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.0891662, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 80.093, "width_percent": 6.101}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.098324, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 86.194, "width_percent": 9.566}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1040392, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 95.76, "width_percent": 4.24}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2130238017 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2130238017\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.096814, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1049431128 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1049431128\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1499694010 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1499694010\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-170170959 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-170170959\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1668603413 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InkxRWtZUE5kaTlKc2xqWnY3SGVtdHc9PSIsInZhbHVlIjoiVWRzdDdJV0RzdkpOWDg3L2treG5ZQ2pLNndNQ1UvZFBOcWxoOTRaY2RlY2xFb3NuV1duV3ppaW82NXNucmpKL1Y3RS9NU3NSdlZ2WkFianV4K1RNbi9XUFQ3U2o1ZWJ2Q2hObTRab3I1WWlMamtNNzZoWkRYMis3UUJSVGZXc1pveTVxOUZ6ZDlwM0l0WUNnYmw0ZGFsR2N2d01IRUtNVXJpaTN6SkVSU2I5bUNZQ3ljNVVvaFdabm5reHRkQWRTVHRPekVZS1JRRTR1SThSWUlnNmh6YWdlZUExU1N1dGhKTmFPWGVuWjNNMzJSME5TWE1YSWRlb2xZckdETWtCYUhEUWJHczNMc1M1azVJditQZjV1cnV3UDJ6VzZicUpaRi91d2RsVUpFVVlhVHFtTEJONUVTS3VWL25FY0VJbkh0YkFFQ2YwRmxDT0ZueUd3ZGs1akRva0t3ajdFdk9YQ2dtNzlaYmxPMzBUeU13Mm5RNjJuYWtRSW5QRXJlVkFVYTRzY2RaNWswdVJ2K1NUYUpjVUEyeEdKOGxpOW54ck1vcE5YdGJqOGJ6RmZMNkhZVXFoOXFYQmRjbldiVEdGK2t2dzVYaVBvS1B5a01oZDFHK3NhR2k3RzJtdWZYSUx2Z3RHSzhUUXdiS3lmOGNWaFhaZDhXMlJpR2xEdjlwTWwiLCJtYWMiOiIyZWU5NDE3ZWI2M2QxZWMzYjVhYzM2ODU5MmRmZWI4OWExZDAxZWY3M2E4OGZjYzhjZmZmOGFmMWRlNGY2MmE3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imt6QUIxdmJacW9GSGQ0SUM4WlhpTnc9PSIsInZhbHVlIjoib29wMmtOWmg2Q3VHU3MveTludUl4bGROQUxJc241cllMMXdsR2tEdXRmdzY0Qk8xU3FRcTI2eWpDRWE0ZlN5akhOcE41Y1BIbU9ySHk0Q3hMYmRoRDZoWXdGZUNpQllZSGVjaFgyNEI5cVN5Z2c1alc0UHk0R25OUXJQNHQwWDkvcVRtTVNvSzA5aXZ1dWhJbUlTOUQvNmpaajM5elpIZ1dtc29LRnNoc0ZoTXNCWUJ3MkUzRVJyOVlEVWdoV1lvaXVIb2JuTzNkeEZaOEdTMHZPVGVhc1gremNaanVyQ25pYmh2anMxbjVhNVFLaUcvcEp0SVhzbWw2NHF4L2lKSHkwMVdQYmZFUThRZDllU0drYi9NbjlQVmZQWXppaEVzVEp1bGlXK3Fka2VrUlp5bkhxdmh5RVcwbUV0MEZ0KzBXNlhNQ0IyaFk0R0Y1d0tIQi9mVVB6cEdqYkQ0cGl2Y3YwT0piWHdiam1XUjBkcklWVVpBY0lmdWpTeC93NTRmVEo5WGM0ckdyWTZWblZUbkVLcWxRSmg4WVNXOEsxZkdkZnFXQmxxdDh1UVo0Y0NMSyt6dDV2ZGJ1RTBTb0FScURZNDlkaDR2S2JSWVpXVTBkT1IwbVdqQVpZNGxYUEZiNlFwUndDZ3E2cGovQ0psdnViZkRBeHpuOFlZditUZUMiLCJtYWMiOiJkMjdlODdmMmY2YmY3YWU5NmFjODU1YzIyYmQzMmI5MzBmY2JlZjJhNWQ5YmIyOWE2MDMyZTVjODA4YmFmM2UxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1668603413\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2082141004 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2082141004\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2081277034 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:08:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im8rWXNLalphdjVBL2ZDMzg3Sy9NUWc9PSIsInZhbHVlIjoiM3JIRzliOUJqUEZUYms4N0lqTW1MT244WlhDTnNhZ2s2NVNaS2Z0ekkrTWhWMVE4SlVNLzN2dHE2WUZBY081Q0d0M010Zng1SGlPclIxN0lvMGV0QUJGc3dUa3RZQXdnZGN6Z3FjbXhOS2FhQjVGWENmOTF1WENFOWhrSG5NYUE5KzVsL25tMXFKbEM2OGRpcUFvdDB6WEo1ZGMrTEU3VEprYkpQODV0MHhlOXgyVVlmNXhZcG1jY1ZiT3RDVG5aaXF3M3BGSFhtTUN2M3hEZk5SNHljT0grVFBkbytwMVpPY1BNR1NBOFloYzhRN3RxczRnQXZRTCtkOEhacEZqK0JBeGNvbjdDYk54RzJ0aUZ2eTZPK3NQeS9IM2JWNHZHTE1DeVR1RHZKeVd1bk0xZHRWdzN2OGdvR2xtY3QyZ09qMFF5Z2ZWYnlsUVJMbjNNNC92NUtDZG1PbEpvYzNEc2hYTG5HQitwR0ZWK3ZVbzROY041Nis2SGxHQStRZXZRNEtyMldnYnhwVUFFQVlQeXFrSk5wZFFPZ09manl0UHFsMWtOUmorWjFuejEyZ0c5eWxVTTRTc0dFOTMyV3pCUmMybmhvT2dzMkpVSVoxalFBS2RHWWtkemRxdE55Ny90Qkc3UGF1NUo4ai8zQkZ4cnlyQzRPYzk4M213bzFsYWsiLCJtYWMiOiJjZmZhZTM0ZDNhNDRjN2M2NjQ0Y2Q2OTE3YzcwYWNmZWU1MzliOGUxNmQ2ZGRjOWQ1MzdkN2RkMDIxZmYwMWE5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:08:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlNjS3Zvc2dXV1BZaDZPeTdVUkNSeUE9PSIsInZhbHVlIjoiMFR1VmJnMFQvY0RwV2ZkUnUwZUcwZVd5dnc5TUNvazRwUFZncy9iZ0NQbmlzajduZTJRQllLWXBDYVNCaU9zOGM5TDRFUkUwb29IU0lBb2lFdlBGMS9NL3JGVUpXRTg5Z29HK2YvRGR2RmtIdSt6L0o2RnlkZ1RNNkNmSWlYQzlSK1p4VFMwa3htcXZKWVZHb1RrOVNSVEkvd0hZczg1R0N6VGF5anVKYTBqRUF0cEN0V3pua3Y2aGU5dVRLVkxXL1pMWWFpeWF2b2hJSkVQUWE3dHRCaTBwYi9MWlhJSkwxVDZETG16cUZYKzRkYjJKRGlvNkk5NG9zL2ducHo3cG9TYUpJTUc4cWNIL1RuMkUxSzFXQUtYQmdkaDVpdm92VEk2SzdidW93TXlKQm5SdEN4RmpJQUlPWnlmVmMwVUZoWEhGbFI0YjNBUWFBdUFlNnB0QnkrSEgzUFl5czZleDZraERhZDk4MTIxRWg2WGtpa1BpQkR2ektnUTU2OC9UMnQ5dFN3VVUvdmM4U2JmZkR4Y0ZIK1BKR2h2YlpFU0tIek1LNGhwb0dnSllzUVpzRkM3aHgvWTl2RDI3TGs4TVJQUWdTdXlyUko2aEJRVktZajRIU1pvN2l4NTJvYTNLL1p6d3VMMUp4ZHZKUmg1Vkk3WHJ5dEhYbWNJM0o4d0wiLCJtYWMiOiJjODZkYTEwNzc0MDVjMDc1NmNmM2UzYTcyYWNlMGE2OTcxNjBiYmFjY2FkOWMxODg4MWVkNWQ1M2U1MTBhMTFhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:08:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im8rWXNLalphdjVBL2ZDMzg3Sy9NUWc9PSIsInZhbHVlIjoiM3JIRzliOUJqUEZUYms4N0lqTW1MT244WlhDTnNhZ2s2NVNaS2Z0ekkrTWhWMVE4SlVNLzN2dHE2WUZBY081Q0d0M010Zng1SGlPclIxN0lvMGV0QUJGc3dUa3RZQXdnZGN6Z3FjbXhOS2FhQjVGWENmOTF1WENFOWhrSG5NYUE5KzVsL25tMXFKbEM2OGRpcUFvdDB6WEo1ZGMrTEU3VEprYkpQODV0MHhlOXgyVVlmNXhZcG1jY1ZiT3RDVG5aaXF3M3BGSFhtTUN2M3hEZk5SNHljT0grVFBkbytwMVpPY1BNR1NBOFloYzhRN3RxczRnQXZRTCtkOEhacEZqK0JBeGNvbjdDYk54RzJ0aUZ2eTZPK3NQeS9IM2JWNHZHTE1DeVR1RHZKeVd1bk0xZHRWdzN2OGdvR2xtY3QyZ09qMFF5Z2ZWYnlsUVJMbjNNNC92NUtDZG1PbEpvYzNEc2hYTG5HQitwR0ZWK3ZVbzROY041Nis2SGxHQStRZXZRNEtyMldnYnhwVUFFQVlQeXFrSk5wZFFPZ09manl0UHFsMWtOUmorWjFuejEyZ0c5eWxVTTRTc0dFOTMyV3pCUmMybmhvT2dzMkpVSVoxalFBS2RHWWtkemRxdE55Ny90Qkc3UGF1NUo4ai8zQkZ4cnlyQzRPYzk4M213bzFsYWsiLCJtYWMiOiJjZmZhZTM0ZDNhNDRjN2M2NjQ0Y2Q2OTE3YzcwYWNmZWU1MzliOGUxNmQ2ZGRjOWQ1MzdkN2RkMDIxZmYwMWE5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:08:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlNjS3Zvc2dXV1BZaDZPeTdVUkNSeUE9PSIsInZhbHVlIjoiMFR1VmJnMFQvY0RwV2ZkUnUwZUcwZVd5dnc5TUNvazRwUFZncy9iZ0NQbmlzajduZTJRQllLWXBDYVNCaU9zOGM5TDRFUkUwb29IU0lBb2lFdlBGMS9NL3JGVUpXRTg5Z29HK2YvRGR2RmtIdSt6L0o2RnlkZ1RNNkNmSWlYQzlSK1p4VFMwa3htcXZKWVZHb1RrOVNSVEkvd0hZczg1R0N6VGF5anVKYTBqRUF0cEN0V3pua3Y2aGU5dVRLVkxXL1pMWWFpeWF2b2hJSkVQUWE3dHRCaTBwYi9MWlhJSkwxVDZETG16cUZYKzRkYjJKRGlvNkk5NG9zL2ducHo3cG9TYUpJTUc4cWNIL1RuMkUxSzFXQUtYQmdkaDVpdm92VEk2SzdidW93TXlKQm5SdEN4RmpJQUlPWnlmVmMwVUZoWEhGbFI0YjNBUWFBdUFlNnB0QnkrSEgzUFl5czZleDZraERhZDk4MTIxRWg2WGtpa1BpQkR2ektnUTU2OC9UMnQ5dFN3VVUvdmM4U2JmZkR4Y0ZIK1BKR2h2YlpFU0tIek1LNGhwb0dnSllzUVpzRkM3aHgvWTl2RDI3TGs4TVJQUWdTdXlyUko2aEJRVktZajRIU1pvN2l4NTJvYTNLL1p6d3VMMUp4ZHZKUmg1Vkk3WHJ5dEhYbWNJM0o4d0wiLCJtYWMiOiJjODZkYTEwNzc0MDVjMDc1NmNmM2UzYTcyYWNlMGE2OTcxNjBiYmFjY2FkOWMxODg4MWVkNWQ1M2U1MTBhMTFhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:08:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081277034\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1756169226 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1756169226\", {\"maxDepth\":0})</script>\n"}}