{"__meta": {"id": "Xd3bc2bee5f85dc9b342eafa06647a3b2", "datetime": "2025-06-08 15:29:44", "utime": **********.488658, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396583.899114, "end": **********.488696, "duration": 0.5895822048187256, "duration_str": "590ms", "measures": [{"label": "Booting", "start": 1749396583.899114, "relative_start": 0, "end": **********.418915, "relative_end": **********.418915, "duration": 0.519801139831543, "duration_str": "520ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.418925, "relative_start": 0.5198111534118652, "end": **********.488699, "relative_end": 2.86102294921875e-06, "duration": 0.06977391242980957, "duration_str": "69.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43495664, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01758, "accumulated_duration_str": "17.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4577029, "duration": 0.01758, "duration_str": "17.58ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wWkRFw77ieFixKV8JTSg4Jm7iJrLf6hsqY7pEPcp", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1892519527 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1892519527\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1068841433 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1068841433\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1723304062 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InAveC9KdDBBZGp5SFZTWThTaVNLaEE9PSIsInZhbHVlIjoiMmxEdlZiMlhLMkhZeDZPOGtUOEk2UVl3T2MycWNhd1JqVy9pRTdraVorcFpTK01KU2VSZDg4Tnd2WG1ZVVBiOTZwSS9iTDNRZDdLcDZrSHRhdzUvTUJKOE9kQlR1YUdITEtWb0NDME8vVTV1OGhSekQ3WUgrOUlrQjlFaUlKNTlpZWc3QnZnQ0JpbHluOWw0RUtRZHByRmVmeU1YaDRCSTZZL0lOUjFzMG9Bam5YR2VYNUpVdXNNa0F4cVhQdTd0eGRNdDJvZXg3QWtPTEFCREVhNTAwTEZyZWpwOWF4ZEZPQUNPVmc1djhXR1J2c1FSTWVneWk3aUFSajdlU2VjakdmMFczZTUvenlTNmlQektkcmxoS0l2ZWFzRmJmZmVrNG9XQTRUQ0FrVDZEUDg3N0xzc3ZxMVdhbUtObXRmMDNlTXZxNTJ6TjBvTkgvYUZpN1FSMnp5ZEN4Q3VYS1ZNU3F1UEV1MHdVU3JNTllYQXVuUlYyV2lNRE9aUEhlRGtvUkhBQ09jV1oza2lvRjhQYTZxVUJjaTBIQmovdE9nODBqa2xOVGtCWVQ3RVRVWWp4eUU2TC9TVEJlNFRkOFZQTDVTWWJlSlpiUzFTcnZXcUFxSGlVTXlyVU11b1pkaGcyWHBRSk1nU3IxZ29ieW5zK3FLM1grTkdlTVI0UWk4dVUiLCJtYWMiOiI2MzVjZDJmNWQwYWJkNjZlZjY4MjE2N2U4NzRhMTc4NWUwMGI0NDhmY2FjZGU4YzdkMTc2ODY0YWQ1Y2E4NmRhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IitGZHZPb1Vxd0dDZ1kwbG9rZXVOUnc9PSIsInZhbHVlIjoiVVdFZkhZejZmYmlITDlKYlhsRlM0T1FYWGh4ZmlJbkh5aXVXYjhua2hkZXpqOG1OYm40Nld0amwwdDJnREJ1WnpFRXAvSUlRTE9RSzJVUndkK282SHJwbDExajQ3bk9VZlFMbjE2c3hzbHdGV3VWWWJObTNvNURHRWgxblJ2YTN2Q0kvWnB6NHVTWmg0TG5kaGdaQUZSV2RVSmNLUWZMQVI4K205L2NuSUdDV2E5ZE1XcWRiMnhtNVIrRE1IRmxMSFBIUWJLUEc2Z0NldlJuZ1hha0tQU3lNaFUyS3cwWXRWMzlqa1haVlI4SDhtRDZWTnlDY21TcElobWd2ZjdzUUViWHMrVmR5VUV2cHgwNThvMERJYVVoaGxVQW5obzBxaUhvN2poK3plZ1loNG5xVnJmbDlLRng4emNyRFR0RTMvWHBqYVBaU295Y05HdytDa0QrRFlpRGhsb3JSV3pXK3pUeDBKd3ZIV0dEVmtKMlcyNVBKbDk2ZWtHVkR3aDhVbzVjZmo3QVpla3BoMjFIdFZIK1RFcDlWZDFBdFhDSFBrYXlibjhvUnI3S2s0OWZqRG1Md1BxR0tINUNvZ2NNdkJzdkNRYi9WMGp3Q0JOekpBWDR4c0NNYk5Sa0JrRWtYa3k5L0ZrMW1WTUg4aHNQenRYR01FRTY5b05rOCtPc1QiLCJtYWMiOiIyYjIzZDE4ZTZhMTY4YTAwZDg3ZDgyMTJlMzMyYzllMTUxYmFiZWNjYTljMzdhM2U2NTIzODhhOGViMTU1MWM1IiwidGFnIjoiIn0%3D; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749396259916%7C1%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723304062\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-684826356 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aRVmhJY8zo7iU33kIiodnph2qXgt57kAdeMzyTJW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9Hl42KK3LIJZIc1GjeFvdAvqwA62mqMM6ouBBVUW</span>\"\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-684826356\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-408439709 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:29:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVrYmZsemd0dUNyeWRHMCtCTDA5UWc9PSIsInZhbHVlIjoibVF0NHhQWVdzZy8rWDlHWDBaU2htT0MzTVFhdnROcGx2MFJGQ3JIa004cHNrQjBZR2o2Zms3OVROTVlaL2pGVk40UVc1enU2M05xVHhIN3FMUEIvVTFyemlWWExYaGJOSUkwUmZsTTBMRlM2a2RnN3IzQjFzUzl0MSt2UXZjWkRKeTRCRk5odXJmYTlFTlU2NG5BYm43K205dGJBUVVXSWdmRmpVWGVlOVRsZEg1QjVCYnY4SVFSQWQrUEQzWE1oZitzYVNRdWZKOGhydEczVk00RElWYjZSNkoxVXZwNDg1TXRGVE9kN28wSG5mS0pCYWsyajJMSEtkWnYxUk1qWGdtUGtDQlovY0tUZG9ZaSsvVHFPcVY4NjdBRDVwZE1NVzBzenZsS05RVkYxYUlrMjl1ZFlBazFrUFVBeVIyMlJyR2JQYjkxU1VhYWRCNkJWYlFFLzBqc0pSU2NnZHhueWFsdm9hMERUWVAwNVI2aTdldTQyZlZIME50K2svU1JXQXk2ZGl2VjJDbWEwMEY3YnZqVFlxei8wcndRWStLc1FPYU93WWZienNuNGtRN0xNdmxYWmR5K2ZtT2hjTzZ3c1RkZk50UnJPOUgranczRWxmOFFEZmRDemwzb3lDMGh1eXRuUjFQM0VTQU9rZk5kM3JvS3hCck9GYkl3VXZXalQiLCJtYWMiOiIyNDY3NDE0ZDBjNjdhMTUwOGQ0NmNhYThiNDE4MDRjNjE3YmE3M2I3YzY5Yjc3OGQ3Mjc1NjgwMzg0MGEzOWJmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjJHbDNtcGh5NlhRT0FRM1U5NS9YTlE9PSIsInZhbHVlIjoiNEZGbGNhZWFtbUxicEEzc3o1c2tFbU84ckZOQ0lqdHJISUF5V2ZKL2FSd1R2TDFaVUxhY0JiNFNZemhRUDcySHQ5ZzlCbmRaT2pIamh3V09hTFlTVkVmZXYvOGpvV2tLZ2g3QlI0QnI1NG1Ra0JPNVU3QkRpRVBxbmlsWHcxSjFZbmtzVUtpM1ZudkFmQ0ZoZHVlK1RyNTMyeVdDbkZuTEcwazEvVTFYSFdjcXhOaFA1clJ4VFVXOTRZSVVSc0ZOUUlocjI3SjlwL0ZBcVRWOUZSWVhhMXZZSy9lbkZ2OHR4YUJGQmpuak9QVitIekh3WHlQOWlkVXRKQUJ2dlhJQ3RaSjIxc1hnMllSQmZadGt6VTFlVlptQ1NTQVNtOXdwOVJyUmR4VEhhV3l3ZFN4ajJDckw0d2J6ZkNFdG5DbnNRVWtFYlNCVm1DVmhtZkllbitoV1JhWDZUa1dYVTQvaXN2RDFxbEV5OUZackhxR2FrK0RaR2NFNTJsL1ZCZ1p0S21IUlVIbjRhZUdVd3Y4RUh5V2FNN3ExUEVLMUZkVGxOZWJCYjZSUWlsdFJuaEd5UnZQSFk1MXo5UG55WjJ5eTlCRVZhVS9YdkxTN0hvaHBHblBLWjJNc2ZQWFlEZXo1RFgxaXV6SlFZK2hZRDdXcDFNWVEzMnV4b0IwUERuS2siLCJtYWMiOiI2MWM2NGUzOWZiYjEwM2M1ZDRmY2Q3MTRmYWIxZWM4ZjE5YzQ0NDNjZGUwNjliMmVhMjE5NzFkNDY4NjZjZGU2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVrYmZsemd0dUNyeWRHMCtCTDA5UWc9PSIsInZhbHVlIjoibVF0NHhQWVdzZy8rWDlHWDBaU2htT0MzTVFhdnROcGx2MFJGQ3JIa004cHNrQjBZR2o2Zms3OVROTVlaL2pGVk40UVc1enU2M05xVHhIN3FMUEIvVTFyemlWWExYaGJOSUkwUmZsTTBMRlM2a2RnN3IzQjFzUzl0MSt2UXZjWkRKeTRCRk5odXJmYTlFTlU2NG5BYm43K205dGJBUVVXSWdmRmpVWGVlOVRsZEg1QjVCYnY4SVFSQWQrUEQzWE1oZitzYVNRdWZKOGhydEczVk00RElWYjZSNkoxVXZwNDg1TXRGVE9kN28wSG5mS0pCYWsyajJMSEtkWnYxUk1qWGdtUGtDQlovY0tUZG9ZaSsvVHFPcVY4NjdBRDVwZE1NVzBzenZsS05RVkYxYUlrMjl1ZFlBazFrUFVBeVIyMlJyR2JQYjkxU1VhYWRCNkJWYlFFLzBqc0pSU2NnZHhueWFsdm9hMERUWVAwNVI2aTdldTQyZlZIME50K2svU1JXQXk2ZGl2VjJDbWEwMEY3YnZqVFlxei8wcndRWStLc1FPYU93WWZienNuNGtRN0xNdmxYWmR5K2ZtT2hjTzZ3c1RkZk50UnJPOUgranczRWxmOFFEZmRDemwzb3lDMGh1eXRuUjFQM0VTQU9rZk5kM3JvS3hCck9GYkl3VXZXalQiLCJtYWMiOiIyNDY3NDE0ZDBjNjdhMTUwOGQ0NmNhYThiNDE4MDRjNjE3YmE3M2I3YzY5Yjc3OGQ3Mjc1NjgwMzg0MGEzOWJmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjJHbDNtcGh5NlhRT0FRM1U5NS9YTlE9PSIsInZhbHVlIjoiNEZGbGNhZWFtbUxicEEzc3o1c2tFbU84ckZOQ0lqdHJISUF5V2ZKL2FSd1R2TDFaVUxhY0JiNFNZemhRUDcySHQ5ZzlCbmRaT2pIamh3V09hTFlTVkVmZXYvOGpvV2tLZ2g3QlI0QnI1NG1Ra0JPNVU3QkRpRVBxbmlsWHcxSjFZbmtzVUtpM1ZudkFmQ0ZoZHVlK1RyNTMyeVdDbkZuTEcwazEvVTFYSFdjcXhOaFA1clJ4VFVXOTRZSVVSc0ZOUUlocjI3SjlwL0ZBcVRWOUZSWVhhMXZZSy9lbkZ2OHR4YUJGQmpuak9QVitIekh3WHlQOWlkVXRKQUJ2dlhJQ3RaSjIxc1hnMllSQmZadGt6VTFlVlptQ1NTQVNtOXdwOVJyUmR4VEhhV3l3ZFN4ajJDckw0d2J6ZkNFdG5DbnNRVWtFYlNCVm1DVmhtZkllbitoV1JhWDZUa1dYVTQvaXN2RDFxbEV5OUZackhxR2FrK0RaR2NFNTJsL1ZCZ1p0S21IUlVIbjRhZUdVd3Y4RUh5V2FNN3ExUEVLMUZkVGxOZWJCYjZSUWlsdFJuaEd5UnZQSFk1MXo5UG55WjJ5eTlCRVZhVS9YdkxTN0hvaHBHblBLWjJNc2ZQWFlEZXo1RFgxaXV6SlFZK2hZRDdXcDFNWVEzMnV4b0IwUERuS2siLCJtYWMiOiI2MWM2NGUzOWZiYjEwM2M1ZDRmY2Q3MTRmYWIxZWM4ZjE5YzQ0NDNjZGUwNjliMmVhMjE5NzFkNDY4NjZjZGU2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-408439709\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1445406394 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wWkRFw77ieFixKV8JTSg4Jm7iJrLf6hsqY7pEPcp</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1445406394\", {\"maxDepth\":0})</script>\n"}}