{"__meta": {"id": "Xa15d30aab64e0638847b24641b0e4b08", "datetime": "2025-06-08 00:29:20", "utime": **********.368177, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342559.29135, "end": **********.36824, "duration": 1.076890230178833, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": 1749342559.29135, "relative_start": 0, "end": **********.239669, "relative_end": **********.239669, "duration": 0.9483191967010498, "duration_str": "948ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.239687, "relative_start": 0.9483370780944824, "end": **********.368247, "relative_end": 6.9141387939453125e-06, "duration": 0.12856006622314453, "duration_str": "129ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45039840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.007089999999999999, "accumulated_duration_str": "7.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.304841, "duration": 0.004889999999999999, "duration_str": "4.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 68.97}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.330492, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 68.97, "width_percent": 15.656}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.346152, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.626, "width_percent": 15.374}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1936358451 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1936358451\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-26996388 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-26996388\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1552898117 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552898117\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-894159226 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749342447060%7C32%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVZQWdyT2N4NU44TkFHb01YY0ttWmc9PSIsInZhbHVlIjoiM0FlMFU4cEZDbmFjaW9jbS9zUzl1VXM1RHc5MVJ6dFliWkNpL0lHam83SFcwZmhPeFlBL0JCeEE2QVRJcVovd21iaUJKaGxuZ2s5alE3QU1vRmZTSmczSUFNOHE2dXVMUlZtQ3I4eGRpS1N5MXZ6Yk5HRlNJNklNbHJtc1Y4RVl1K1NJM0NFVlAweUpFcHhHSDJ0aHplWDJucjNjd0h1K2I5aDJXWGNtWk5RU2hvN3pCbDdxR2VOSmlSa1RYOUkvZFBjcUhSV29jVW9pZXY0R2ZGK1ZWYzA0S2FHVE5kcjh5dXhuem9ZdkNmRzdZK2l3ZGtKQ3E3d2s0UlVYR2NBbklRM0xUaWZKSStHQTBEN1RINXRNVUQzQXNFaWVnYXpnVGNJUUUyZy9kWlFFZnJWbVlTT3BES2h6NkxTSDhXR0VOZ2ZrOGVZaWE1YXRURDBQNU84STljQ1BlQ0EzZlVLaU9qYzVQQ01WSWM2Q2o4NnpDaytPTitabURJdHFvOVE1UEFiMXY4R0wzMWRFeCt0aUgweWpCUEVTTGx2aUg1RThlUjNrbkRsU2t1RlVMRDVObEFmYlBEZEtjZElMcFhydWRKa3pWUXQzL3JiK09oU2FyWVRVb3JXUXQ4OXRjK0RyTGpFcTlMa1M0T21PRmxqYWRCeVJVNEZnS3NEZUhndUciLCJtYWMiOiJhNDVjYjUwZDA4MTJlOTRhMTBlZGRmOGRkN2ZiZjhmMjM1ZTMzNTkzODBkZDc5ZmI3MWE3OTg3NmY5MWRhMTVkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ii84QlBXQkIyWTFES1FkNFlFd2JCWnc9PSIsInZhbHVlIjoiYjBpTHhzcHRNNTRRMWtTVXp4a0R5Vm1kaHVCeXNGUGErN0d3clE4NFdJa1ROZ0ViWG50Ti9vUHU2dlJ0V2oxaWhUeGFPVWdHWGNLdFY5ZHhlOFlKZTNTOWdLRkFBaGR6a3lhOG10MklWYXVBVHJ5MEthdElzeVIrNlZwMU1veXZ2U1Z1Ym95TFlScW9CU3NoMUExcyt3bUFaVmMyVnF2am8wMVNGVGlKNDNEYXg5Y3gyemxtdVhQNzlYSHN6WlZFUjRyc3VZRERwZlFKb09ucVFMRXRScjRmSGRMZzJ6WGFoMko0QnRicjVsTHFRUVFMTVkzUDFxcHBxVkJJVFJsRjArUGdVVjJNVHV1U2lxNDkyaE1tT2hRUVhGWWtvSitjZjk3aFVsOGF1VmM4SmhhdjNDUU5PeTNwNTdrLzFHK1VYM3dDZnArb0pma2x5WHRGTVdKVjBZQy81SzY5bUtZUHp4a25NWitIRGppdVRneVVGRWM2dEh1dGd1eHQ0U0RBTzg5a0c0eUlocFB0bU4vTk80aFdjSEJkSC9EbnF2NlFBN3MrOEYvbUJWMkE0QThMcHdJTFh5azZQK0ZIVldmdzlhZmFlQ1B3QVlrbW5ZS1RBUEpORmVxVXgzWTB2V1V5U0k2VVloV3U3UFExZlpIa3JVQm5GaWI2ZzBKUjBLcHQiLCJtYWMiOiJhOGM0MDQzNTg1ZDdlM2U4ZmM3MTVhM2RmZjNjZTNlZjQ2MTEwNDE5MzY3YzFkOWQwYzlhYTUxMjA0NzRjODhlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-894159226\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1179545667 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1179545667\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:29:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlprZi92allHcVhsWkNyc2tNUnFoS3c9PSIsInZhbHVlIjoiMUgrNVhNWGdXcWYzamRQejVkLzJGVFZlUXBPY0k3UG9Eb2dXczJLR3BQZThIQndFcTZNZk1GN0g0eFZqUHIzN0llcjJqUFVwSElTbkprNnNya1ZlMVg2SU9HUXhldVE1M3B5SE96MmpkRDl1NEdGNUQ5Yk9WeHhTM0NJTG9TK2VkcEluZm5QNWtaVEZBK1lHZ1NxS0JDTGxoMllidEl6ZU9PN0V2ckNOalI3N1FITTdzZzRvazhjRnZDK3BFSGNsVUtsVW9UeWppb0dHTmZNT0lxcFQ2SVd0Wk5aQ09sN3lrbGZTVzZZZ1U5MlJ1TS9OYnQzVEl5bUtxM1NTQmUxSC9ORXorbWtNVE41VW00RGRsZHpIcmZGNk5WVFd2eE5tamhzYXNZUmpsZVV3UXNtWEdoRGhwWDkrZXM2aGMwOC9NSDhqQktGc3ZUNnZDOWV2eEsrYkhNRGR5bm1FV0cxdk5TQUlQMy8yaW5xTXgyVW1RTWdnSGlqQTUvS2hRSDZwLzgrV2JMNXRHS3k2SU01VHlDTk9JREJ3SmYxWWp4OCs0Snp2M0s2MHgyVjRJUGhHV1Z2RjZaeE95TVJ5ekhoQjRGNWc1Q0xjNlJ3RUh3S0JMUjJvYWRDaDVxbys4NFMyVjlNUVlMd0xxczhpL1MxNEFyLzhocXFCTVRBV2R2MXEiLCJtYWMiOiI5M2Q0MDgwYzE1ODljOGIzN2Y3MTRlNWIzYzNkZmUwZTBkNWZiOGM0M2JlNzk4ZTQxM2I0ZmFhMjZkOTkyMjhmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:29:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjRrMnUxOHo0RW52V0VaY0dBNHlhTWc9PSIsInZhbHVlIjoiOTU1ZlBZeGYyWlc5S3I3bUVib2ZLaEFqUEswQ2didHBHODE5RlNUTmRhZldoYUlUL3NEaVJHUXM5cUIyL05KMHRXMkk2dHlPWjV6UWxuWmFMM0FiN1NGaDQzR2hQbmQrWEpHOUR3YnFZcWFVZVRZTy9yWklpTncyMGpjVnJNYm1iNDhaUmlFalJmNVJicmRDMjFHUEpXejFJd21aL0NnRkVaTkRubzFZUDJiQXBnOWZDVEwwQi9zMmJJN0JHUVlmWkE4R3p2QXRwSHdzYXlxUWpFTXVnU1NjQ25nQ04yQmdMSnhHZ2xnY0JoVWpSU2M1SHIwSjkrbHJIT1hzc0RlRVZmWVZtWHUwUEhvczMveS9oWXhyNGg5NjhqdVEzRmNzNjhRZUcwUHA1dmhMQ0xqYzdKK09VUUdMOXJEcUZaNUh0bThOQVFsOVJXQjA0L05XcHNEM2tMYmxyZldsUW1wVWdKMzdUNmI3TGR3L3RRZEJEK2lHUjlLa0pnbGZYUysvaXNsSVh5RVNaMFlEZ3hYM0owODhvUWlENUVjbG8vbllNN2FmSDY3TlU2Mk9hMlFUOUY5eGpXK1hVR2Flb3J1anpieDB3QUs2cVh2YmJKUVpxY25BWmdHVit1RTllSzIvNmsyODJvd29WN25UM3RIbHVmRm4yNjhlaXVGMmlOR0QiLCJtYWMiOiJiZDlmNDJjOGE5OGVhYTQxNGFmOTQ5MGMxNzYzZjBiOTEwYzZkODJlNzcxMTk4YzQyMzRlZTczOTZjYTQ0NWFhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:29:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlprZi92allHcVhsWkNyc2tNUnFoS3c9PSIsInZhbHVlIjoiMUgrNVhNWGdXcWYzamRQejVkLzJGVFZlUXBPY0k3UG9Eb2dXczJLR3BQZThIQndFcTZNZk1GN0g0eFZqUHIzN0llcjJqUFVwSElTbkprNnNya1ZlMVg2SU9HUXhldVE1M3B5SE96MmpkRDl1NEdGNUQ5Yk9WeHhTM0NJTG9TK2VkcEluZm5QNWtaVEZBK1lHZ1NxS0JDTGxoMllidEl6ZU9PN0V2ckNOalI3N1FITTdzZzRvazhjRnZDK3BFSGNsVUtsVW9UeWppb0dHTmZNT0lxcFQ2SVd0Wk5aQ09sN3lrbGZTVzZZZ1U5MlJ1TS9OYnQzVEl5bUtxM1NTQmUxSC9ORXorbWtNVE41VW00RGRsZHpIcmZGNk5WVFd2eE5tamhzYXNZUmpsZVV3UXNtWEdoRGhwWDkrZXM2aGMwOC9NSDhqQktGc3ZUNnZDOWV2eEsrYkhNRGR5bm1FV0cxdk5TQUlQMy8yaW5xTXgyVW1RTWdnSGlqQTUvS2hRSDZwLzgrV2JMNXRHS3k2SU01VHlDTk9JREJ3SmYxWWp4OCs0Snp2M0s2MHgyVjRJUGhHV1Z2RjZaeE95TVJ5ekhoQjRGNWc1Q0xjNlJ3RUh3S0JMUjJvYWRDaDVxbys4NFMyVjlNUVlMd0xxczhpL1MxNEFyLzhocXFCTVRBV2R2MXEiLCJtYWMiOiI5M2Q0MDgwYzE1ODljOGIzN2Y3MTRlNWIzYzNkZmUwZTBkNWZiOGM0M2JlNzk4ZTQxM2I0ZmFhMjZkOTkyMjhmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:29:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjRrMnUxOHo0RW52V0VaY0dBNHlhTWc9PSIsInZhbHVlIjoiOTU1ZlBZeGYyWlc5S3I3bUVib2ZLaEFqUEswQ2didHBHODE5RlNUTmRhZldoYUlUL3NEaVJHUXM5cUIyL05KMHRXMkk2dHlPWjV6UWxuWmFMM0FiN1NGaDQzR2hQbmQrWEpHOUR3YnFZcWFVZVRZTy9yWklpTncyMGpjVnJNYm1iNDhaUmlFalJmNVJicmRDMjFHUEpXejFJd21aL0NnRkVaTkRubzFZUDJiQXBnOWZDVEwwQi9zMmJJN0JHUVlmWkE4R3p2QXRwSHdzYXlxUWpFTXVnU1NjQ25nQ04yQmdMSnhHZ2xnY0JoVWpSU2M1SHIwSjkrbHJIT1hzc0RlRVZmWVZtWHUwUEhvczMveS9oWXhyNGg5NjhqdVEzRmNzNjhRZUcwUHA1dmhMQ0xqYzdKK09VUUdMOXJEcUZaNUh0bThOQVFsOVJXQjA0L05XcHNEM2tMYmxyZldsUW1wVWdKMzdUNmI3TGR3L3RRZEJEK2lHUjlLa0pnbGZYUysvaXNsSVh5RVNaMFlEZ3hYM0owODhvUWlENUVjbG8vbllNN2FmSDY3TlU2Mk9hMlFUOUY5eGpXK1hVR2Flb3J1anpieDB3QUs2cVh2YmJKUVpxY25BWmdHVit1RTllSzIvNmsyODJvd29WN25UM3RIbHVmRm4yNjhlaXVGMmlOR0QiLCJtYWMiOiJiZDlmNDJjOGE5OGVhYTQxNGFmOTQ5MGMxNzYzZjBiOTEwYzZkODJlNzcxMTk4YzQyMzRlZTczOTZjYTQ0NWFhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:29:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}