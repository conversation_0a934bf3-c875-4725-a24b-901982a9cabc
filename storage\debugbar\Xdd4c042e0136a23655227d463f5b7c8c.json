{"__meta": {"id": "Xdd4c042e0136a23655227d463f5b7c8c", "datetime": "2025-06-30 15:34:38", "utime": **********.308348, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751297677.823353, "end": **********.308364, "duration": 0.48501086235046387, "duration_str": "485ms", "measures": [{"label": "Booting", "start": 1751297677.823353, "relative_start": 0, "end": **********.222843, "relative_end": **********.222843, "duration": 0.3994898796081543, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.222852, "relative_start": 0.39949893951416016, "end": **********.308365, "relative_end": 1.1920928955078125e-06, "duration": 0.08551311492919922, "duration_str": "85.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45556488, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022549999999999997, "accumulated_duration_str": "22.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2597468, "duration": 0.02131, "duration_str": "21.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.501}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.291099, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.501, "width_percent": 2.395}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.298249, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.896, "width_percent": 3.104}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-458014352 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-458014352\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-673517383 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-673517383\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1689096812 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689096812\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1563515151 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297675919%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFXdVVrM3pXQU1QUnRZU3lhbmNXT1E9PSIsInZhbHVlIjoicHAvRVk5R0UrUW5KQk9mQm9iTi9DUGNudkZtOEJlcnl0OEtUaFRGbGg0KzVVNlV5MzFMek9hUTI1aXRackNLbWdveFAvdWRNeUt0Y0pXekc5N0x6bzlTRndQMnFlbDNmS3lnMzI4dlN6c04zSWZ1aEVobEk1UTRFWWNqQmlRTHFTSWhmUURpb3VsbnREK3BoZHdvWmQzaTZ2VVoreWNrSThSa3FGVVJBQ0lwRjZjdDNSOTBKajlwcDA0RElRMjhqdmQzSnh5ZTVUTlUreGZKbnBIc2gxbVdQMldST1g4L1lBT0MrSTZMOW9Zd3JRVHBicm9NQVRSQ0VZTHh5Y28rek55YXoxWVFDRHpXYXg1bVVSZEw5Vk0wQTRNdUZUaWIzaTFuY3BrSkxpMHNNU3lMc2dkV0FYcVZaUkF5ZWNTKzBqL2RhOWtNNUlXZGxvaGdQb0U0M2NMWFFxRTRMK0FOQmxYb0tZdFg3Mk95STJsRGtvNlM5eUp1dXcyREJPbjI1ckZSVkJRelE1eDdLcnJhWFJxTkdQeHl4cDY1Tk4yNWwxcWdxSUlEWlZBTUJWQlVxT3UvRFpqeGlzQWRhbk02TFpjcTdtNzd6akREZmNscTNpb3FRMGcxbFhBdEdKeHVBcUh5dGdaVWNrTlZnT1oxa21lOHdtdkMyalZZeUErbDciLCJtYWMiOiIxMWIxYWY1ZDcyOGM0ZGZjNDFiNDU2NThmYzhkNGVhOTU0YjQ4ZWNhNjRkNGVhYTcyNTRjYjk1ZjA0NzNkMTlmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkYzbUxUbHRTQ2wwRUU2RzFzbjZEUHc9PSIsInZhbHVlIjoiQ2hJS0VsZWc3bU53YlY4ZTlsUEpFdUJkUzlmRW5DeVVUeEpOTGN3ZmxDMWM3Y2ZFd1lPZldTMzJMY2pHeHdvRVJieVJtd1ZLS2luWWZnbkxTSmlMMCtpM01zMVBES2M2R3JZZjJwYlpWaS9SSCtjaXd6Nk9QSnR1TWkrZzNMMXhYZUozVVd4NkhlbzBWajlkYjlPQzdIbEZkUXRSelBKenk1QWM0T1ZqV1ZqTm42WCszN1ZIMWExR0JUakx2NmdDSXFpRU40K3d4RDN4dDZkZDhjS3hYbVhrM0VqRUJOMXNjcERMY0MvdGhFNnhaK3BWU3lRRk82d1gvMVNFc21sanAxTmZCTU1WaStpMENsb2JVUmxmYVcyR2lPSEJIMm9OakI2dlY1b0pERGFha1lvMXNoeTEyekFodUJtWWxVYWVaenhQaGx2N1hQZ1NCbGt1VHp4UG9mQ0QvWE52ZjNxZDBuVTBGOG9KUjdjbTRZanZSY3RLbi9HcWZsVkJtM1BlTVFuc0xzc3Q3N2E5SHp6OVBLdzJEZ0ZKUU1oK2RHbm8rR0FFSloyWWlSb3ozcC9yV0ljdFBML05CT01uWXIxZFJGNEdLdHQ4eVorTHlubVJFSVJyM3kyOHVXU0FtSjkxaFlwa1FsSzFlNUhGckdCOWIyR21obVdhRjRZTDF2T0giLCJtYWMiOiIzN2Q2YzlkMzA0OTQ5MTliOTZmNzI5ZDIxYzFjYzFkMzJmZjExNDVkODIwN2EyNTg3MDdiNWMwMTkyYTNiZDhlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1563515151\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1256825137 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1256825137\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1439108209 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVvVllnTFdhZnRHT3VCY1BIeW9nSHc9PSIsInZhbHVlIjoiditJVmkrMHhJb0MyaXo5aU14Sk9Ea1ZScFRoM2FTN3lIVGNIMld5bmRZNTk5Wm50U1lGYlJrZGlreXJYUmlPanV3ZldHdDlxNmFnY1BOMWRmc3JhNFFld0RhVGJNSENMNDRJbU1hNnRCQXI1eHNpYmd4bi9rc2NXbDJmNWppOW5tL0RreGlKSFB5eGlVZkRITU5rZTNIZDhmZWpnSVgwTG5YblJwWVpWTWYzUnV0UjBlTFV4N3M1M29Vd2xtMzVzNHgxRnl5SDNOU2dZMmM0ZVFzMXY1WjcrOC9HMENURnEvVlJuZWloMlVjTkw5YTJlMXZHTmZhZXVxOHMrVjIxOUxnSWJESVZmazRzWHd0Yi9kdGRZZC9VRmYvalMxZXZKcDdnZFRWQjU3L2FZeW1mT2FyZlRZZ08wUUp0VGUzWFdmdmsvV1RsT2tNWWxSdFlYNnJNUjN0OFV5T0kwZGkzeWJod21xNWkvQUlWeDhmd3NZUWowM0RtN3N6R2d0OG5nZlN4TXU3b0ZYMS9hWmg0YXE3eWxSSElETllLVHovN2tBR0lPMVlHYkxIR2JOUCtHSlFSOEQvaWxRVkRpLzhZM2F3dmJWYTc4L2hoZzdJd0dKN3ZYL2xzWHgveDRiUzNnSWpEK2UyYWoyM0luV01WRGtwNFZIaWZtRzdVQXo2RHAiLCJtYWMiOiI4MjQwM2Q4YzEyMGY4YmNiMDE0MTVmZjJhMGU2OGVjYzZmM2RjNjNlMWRjOWE5YmUzZTVmMjM2MDY5YmIxODY5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii9PT2dTN0lxRDBWQlJRN2FWUlRCU2c9PSIsInZhbHVlIjoiOTY0QW84WjEyVGRuUEhGbkhIRGpFMkJBaHRQd0JndjMxR2FaUlg5L2tvZDJCU2NzVTJMTHp5OU1nWldoTGxRc2ZNRkgxRXBzK2Y4Qis3bXJycjlBM1dSYm40TUV5QzRzZ0laQ09kK1o0WmlTV01iYzV2ZTFzODVwOFNpZ0xBMmVaUlpGU1JleVdERWtiaGNwK2RKMWQxWkZiZ3pUL2RtK08vclErekJtVzJ1MnR1MnNCM1pPQmI3bjRZTUJpanpzNjNKR0htK1NrSkxpWDZZNGJsNEJlbCsxamNQdTR0S281ZmFQbTVBQmx0YkFpUU9udjFmMHhnQUFsRmg4c2t5Y0swTXpFMHBBa2wwb2hLSEhsMFg4SFdDeVh0OG1VazVGUGhCdzFNK1ViU2htaVF4WnVkQ0VOUVJDOEpZNFh1MFA3NkFtaEFyUnFzV3BsYnVmREMwRTFTOU5DQXFhZzRCQkJkTTBMWWdqVzhhQWxWcDE5OWRpemJkOElCWDJrbHFDTzRPUXJoaWlnSWtsRTlBczNzRC8veTFaVUp1VDNJam1ocktOcmNPeU1Wc2dWVDFENXhtOW1RU25SK1NXc2gxa2w3QzErb1hOL1dBaExFZGg3ajYvRi9pY3R1V2sxZldFbkRwM0xmc0F1Yng0dVVER3FINUF5WGNIb212REZLdmoiLCJtYWMiOiI0NjVmYWNiNDVmMmQyMDUzMGZiY2M5NGRiYjgzMDVjYjIwOWI0ODhiZTU3MDQ1OTEwOWViM2ExZmM2ZDNiZGQzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVvVllnTFdhZnRHT3VCY1BIeW9nSHc9PSIsInZhbHVlIjoiditJVmkrMHhJb0MyaXo5aU14Sk9Ea1ZScFRoM2FTN3lIVGNIMld5bmRZNTk5Wm50U1lGYlJrZGlreXJYUmlPanV3ZldHdDlxNmFnY1BOMWRmc3JhNFFld0RhVGJNSENMNDRJbU1hNnRCQXI1eHNpYmd4bi9rc2NXbDJmNWppOW5tL0RreGlKSFB5eGlVZkRITU5rZTNIZDhmZWpnSVgwTG5YblJwWVpWTWYzUnV0UjBlTFV4N3M1M29Vd2xtMzVzNHgxRnl5SDNOU2dZMmM0ZVFzMXY1WjcrOC9HMENURnEvVlJuZWloMlVjTkw5YTJlMXZHTmZhZXVxOHMrVjIxOUxnSWJESVZmazRzWHd0Yi9kdGRZZC9VRmYvalMxZXZKcDdnZFRWQjU3L2FZeW1mT2FyZlRZZ08wUUp0VGUzWFdmdmsvV1RsT2tNWWxSdFlYNnJNUjN0OFV5T0kwZGkzeWJod21xNWkvQUlWeDhmd3NZUWowM0RtN3N6R2d0OG5nZlN4TXU3b0ZYMS9hWmg0YXE3eWxSSElETllLVHovN2tBR0lPMVlHYkxIR2JOUCtHSlFSOEQvaWxRVkRpLzhZM2F3dmJWYTc4L2hoZzdJd0dKN3ZYL2xzWHgveDRiUzNnSWpEK2UyYWoyM0luV01WRGtwNFZIaWZtRzdVQXo2RHAiLCJtYWMiOiI4MjQwM2Q4YzEyMGY4YmNiMDE0MTVmZjJhMGU2OGVjYzZmM2RjNjNlMWRjOWE5YmUzZTVmMjM2MDY5YmIxODY5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii9PT2dTN0lxRDBWQlJRN2FWUlRCU2c9PSIsInZhbHVlIjoiOTY0QW84WjEyVGRuUEhGbkhIRGpFMkJBaHRQd0JndjMxR2FaUlg5L2tvZDJCU2NzVTJMTHp5OU1nWldoTGxRc2ZNRkgxRXBzK2Y4Qis3bXJycjlBM1dSYm40TUV5QzRzZ0laQ09kK1o0WmlTV01iYzV2ZTFzODVwOFNpZ0xBMmVaUlpGU1JleVdERWtiaGNwK2RKMWQxWkZiZ3pUL2RtK08vclErekJtVzJ1MnR1MnNCM1pPQmI3bjRZTUJpanpzNjNKR0htK1NrSkxpWDZZNGJsNEJlbCsxamNQdTR0S281ZmFQbTVBQmx0YkFpUU9udjFmMHhnQUFsRmg4c2t5Y0swTXpFMHBBa2wwb2hLSEhsMFg4SFdDeVh0OG1VazVGUGhCdzFNK1ViU2htaVF4WnVkQ0VOUVJDOEpZNFh1MFA3NkFtaEFyUnFzV3BsYnVmREMwRTFTOU5DQXFhZzRCQkJkTTBMWWdqVzhhQWxWcDE5OWRpemJkOElCWDJrbHFDTzRPUXJoaWlnSWtsRTlBczNzRC8veTFaVUp1VDNJam1ocktOcmNPeU1Wc2dWVDFENXhtOW1RU25SK1NXc2gxa2w3QzErb1hOL1dBaExFZGg3ajYvRi9pY3R1V2sxZldFbkRwM0xmc0F1Yng0dVVER3FINUF5WGNIb212REZLdmoiLCJtYWMiOiI0NjVmYWNiNDVmMmQyMDUzMGZiY2M5NGRiYjgzMDVjYjIwOWI0ODhiZTU3MDQ1OTEwOWViM2ExZmM2ZDNiZGQzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439108209\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-325984688 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-325984688\", {\"maxDepth\":0})</script>\n"}}