<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\ProductService;
use App\Models\ProductServiceCategory;
use App\Models\WarehouseProduct;
use App\Models\warehouse;
use App\Models\Pos;
use App\Models\PosProduct;
use App\Models\ReceiptOrder;
use App\Models\ReceiptOrderProduct;
use Carbon\Carbon;

class ProductAnalyticsController extends Controller
{
    /**
     * عرض الصفحة الرئيسية لتحليل أداء المنتجات
     */
    public function index(Request $request)
    {
        // التحقق من الصلاحيات
        if (!Auth::user()->can('show financial record')) {
            return redirect()->back()->with('error', __('You are not authorized to perform this action'));
        }

        // جلب البيانات الأساسية للعرض
        $warehouses = warehouse::where('created_by', Auth::user()->creatorId())->get();
        $categories = ProductServiceCategory::where('created_by', Auth::user()->creatorId())->get();
        
        // جلب بيانات المنتجات الأساسية
        $warehouseId = $request->get('warehouse_id');
        $categoryId = $request->get('category_id');
        $dateFrom = $request->get('date_from', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', Carbon::now()->endOfMonth()->format('Y-m-d'));
        
        $productData = $this->getProductOverview($warehouseId, $categoryId, $dateFrom, $dateTo);

        return view('financial_operations.product_analytics.index', compact(
            'warehouses',
            'categories',
            'productData',
            'warehouseId',
            'categoryId',
            'dateFrom',
            'dateTo'
        ));
    }

    /**
     * جلب نظرة عامة على أداء المنتجات
     */
    public function getProductOverview($warehouseId = null, $categoryId = null, $dateFrom = null, $dateTo = null)
    {
        try {
            $creatorId = Auth::user()->creatorId();

            // إحصائيات عامة للمنتجات
            $totalProducts = ProductService::where('created_by', $creatorId)
                ->when($categoryId, function($q) use ($categoryId) {
                    return $q->where('category_id', $categoryId);
                })
                ->count();

            // إجمالي قيمة المخزون من warehouse_products
            $stockValueQuery = DB::table('warehouse_products as wp')
                ->join('product_services as ps', 'wp.product_id', '=', 'ps.id')
                ->where('ps.created_by', $creatorId)
                ->when($warehouseId, function($q) use ($warehouseId) {
                    return $q->where('wp.warehouse_id', $warehouseId);
                })
                ->when($categoryId, function($q) use ($categoryId) {
                    return $q->where('ps.category_id', $categoryId);
                });

            $totalStockValue = $stockValueQuery->sum(DB::raw('ps.purchase_price * wp.quantity'));

            // المنتجات النشطة (لها مبيعات في الفترة) - من كلا النظامين
            $activeProductsFromPos = DB::table('pos_products as pp')
                ->join('pos as p', 'pp.pos_id', '=', 'p.id')
                ->join('product_services as ps', 'pp.product_id', '=', 'ps.id')
                ->where('ps.created_by', $creatorId)
                ->whereBetween('p.pos_date', [$dateFrom, $dateTo])
                ->when($warehouseId, function($q) use ($warehouseId) {
                    return $q->where('p.warehouse_id', $warehouseId);
                })
                ->when($categoryId, function($q) use ($categoryId) {
                    return $q->where('ps.category_id', $categoryId);
                })
                ->distinct('ps.id')
                ->pluck('ps.id');

            // التحقق من وجود pos_v2_products
            $activeProductsFromPosV2 = collect();
            if (DB::getSchemaBuilder()->hasTable('pos_v2_products')) {
                $activeProductsFromPosV2 = DB::table('pos_v2_products as pp')
                    ->join('pos_v2 as p', 'pp.pos_id', '=', 'p.id')
                    ->join('product_services as ps', 'pp.product_id', '=', 'ps.id')
                    ->where('ps.created_by', $creatorId)
                    ->whereBetween('p.pos_date', [$dateFrom, $dateTo])
                    ->when($warehouseId, function($q) use ($warehouseId) {
                        return $q->where('p.warehouse_id', $warehouseId);
                    })
                    ->when($categoryId, function($q) use ($categoryId) {
                        return $q->where('ps.category_id', $categoryId);
                    })
                    ->distinct('ps.id')
                    ->pluck('ps.id');
            }

            $activeProducts = $activeProductsFromPos->merge($activeProductsFromPosV2)->unique()->count();

            // المنتجات الراكدة (بدون مبيعات)
            $stagnantProducts = $totalProducts - $activeProducts;

            // المنتجات منتهية الصلاحية قريباً (خلال 30 يوم)
            $expiringProducts = ProductService::where('created_by', $creatorId)
                ->whereNotNull('expiry_date')
                ->where('expiry_date', '<=', Carbon::now()->addDays(30))
                ->when($categoryId, function($q) use ($categoryId) {
                    return $q->where('category_id', $categoryId);
                })
                ->count();

            // إجمالي المبيعات في الفترة من كلا النظامين
            $salesFromPos = DB::table('pos_products as pp')
                ->join('pos as p', 'pp.pos_id', '=', 'p.id')
                ->join('product_services as ps', 'pp.product_id', '=', 'ps.id')
                ->where('ps.created_by', $creatorId)
                ->whereBetween('p.pos_date', [$dateFrom, $dateTo])
                ->when($warehouseId, function($q) use ($warehouseId) {
                    return $q->where('p.warehouse_id', $warehouseId);
                })
                ->when($categoryId, function($q) use ($categoryId) {
                    return $q->where('ps.category_id', $categoryId);
                })
                ->sum(DB::raw('pp.quantity * pp.price'));

            $salesFromPosV2 = 0;
            if (DB::getSchemaBuilder()->hasTable('pos_v2_products')) {
                $salesFromPosV2 = DB::table('pos_v2_products as pp')
                    ->join('pos_v2 as p', 'pp.pos_id', '=', 'p.id')
                    ->join('product_services as ps', 'pp.product_id', '=', 'ps.id')
                    ->where('ps.created_by', $creatorId)
                    ->whereBetween('p.pos_date', [$dateFrom, $dateTo])
                    ->when($warehouseId, function($q) use ($warehouseId) {
                        return $q->where('p.warehouse_id', $warehouseId);
                    })
                    ->when($categoryId, function($q) use ($categoryId) {
                        return $q->where('ps.category_id', $categoryId);
                    })
                    ->sum(DB::raw('pp.quantity * pp.price'));
            }

            $totalSalesRevenue = $salesFromPos + $salesFromPosV2;

            // متوسط دوران المخزون
            $avgTurnoverRatio = $totalStockValue > 0 ? round($totalSalesRevenue / $totalStockValue, 2) : 0;

            return [
                'total_products' => $totalProducts,
                'total_stock_value' => round($totalStockValue, 2),
                'active_products' => $activeProducts,
                'stagnant_products' => $stagnantProducts,
                'expiring_products' => $expiringProducts,
                'total_sales_revenue' => round($totalSalesRevenue, 2),
                'avg_turnover_ratio' => $avgTurnoverRatio,
                'active_percentage' => $totalProducts > 0 ? round(($activeProducts / $totalProducts) * 100, 1) : 0,
                'debug_info' => [
                    'creator_id' => $creatorId,
                    'warehouse_id' => $warehouseId,
                    'category_id' => $categoryId,
                    'date_from' => $dateFrom,
                    'date_to' => $dateTo,
                    'stock_value_query_count' => $stockValueQuery->count(),
                    'sales_from_pos' => $salesFromPos,
                    'sales_from_pos_v2' => $salesFromPosV2
                ]
            ];

        } catch (\Exception $e) {
            return [
                'total_products' => 0,
                'total_stock_value' => 0,
                'active_products' => 0,
                'stagnant_products' => 0,
                'expiring_products' => 0,
                'total_sales_revenue' => 0,
                'avg_turnover_ratio' => 0,
                'active_percentage' => 0,
                'error' => $e->getMessage(),
                'debug_info' => [
                    'creator_id' => $creatorId,
                    'error_details' => $e->getTraceAsString()
                ]
            ];
        }
    }

    /**
     * جلب أفضل المنتجات مبيعاً
     */
    public function getTopSellingProducts(Request $request)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $warehouseId = $request->get('warehouse_id');
            $categoryId = $request->get('category_id');
            $dateFrom = $request->get('date_from', Carbon::now()->startOfMonth()->format('Y-m-d'));
            $dateTo = $request->get('date_to', Carbon::now()->endOfMonth()->format('Y-m-d'));

            // جمع البيانات من pos_products
            $salesFromPos = DB::table('product_services as ps')
                ->join('pos_products as pp', 'ps.id', '=', 'pp.product_id')
                ->join('pos as p', 'pp.pos_id', '=', 'p.id')
                ->where('ps.created_by', $creatorId)
                ->whereBetween('p.pos_date', [$dateFrom, $dateTo])
                ->when($warehouseId, function($q) use ($warehouseId) {
                    return $q->where('p.warehouse_id', $warehouseId);
                })
                ->when($categoryId, function($q) use ($categoryId) {
                    return $q->where('ps.category_id', $categoryId);
                })
                ->select(
                    'ps.id',
                    DB::raw('SUM(pp.quantity) as total_sold'),
                    DB::raw('SUM(pp.quantity * pp.price) as total_revenue'),
                    DB::raw('COUNT(DISTINCT pp.pos_id) as order_count'),
                    DB::raw('AVG(pp.price) as avg_selling_price')
                )
                ->groupBy('ps.id')
                ->get()
                ->keyBy('id');

            // جمع البيانات من pos_v2_products إذا كان الجدول موجود
            $salesFromPosV2 = collect();
            if (DB::getSchemaBuilder()->hasTable('pos_v2_products')) {
                $salesFromPosV2 = DB::table('product_services as ps')
                    ->join('pos_v2_products as pp', 'ps.id', '=', 'pp.product_id')
                    ->join('pos_v2 as p', 'pp.pos_id', '=', 'p.id')
                    ->where('ps.created_by', $creatorId)
                    ->whereBetween('p.pos_date', [$dateFrom, $dateTo])
                    ->when($warehouseId, function($q) use ($warehouseId) {
                        return $q->where('p.warehouse_id', $warehouseId);
                    })
                    ->when($categoryId, function($q) use ($categoryId) {
                        return $q->where('ps.category_id', $categoryId);
                    })
                    ->select(
                        'ps.id',
                        DB::raw('SUM(pp.quantity) as total_sold'),
                        DB::raw('SUM(pp.quantity * pp.price) as total_revenue'),
                        DB::raw('COUNT(DISTINCT pp.pos_id) as order_count'),
                        DB::raw('AVG(pp.price) as avg_selling_price')
                    )
                    ->groupBy('ps.id')
                    ->get()
                    ->keyBy('id');
            }

            // دمج البيانات من كلا المصدرين
            $combinedSales = $salesFromPos;
            $salesFromPosV2->each(function($item, $id) use (&$combinedSales) {
                if (isset($combinedSales[$id])) {
                    $combinedSales[$id]->total_sold += $item->total_sold;
                    $combinedSales[$id]->total_revenue += $item->total_revenue;
                    $combinedSales[$id]->order_count += $item->order_count;
                    $combinedSales[$id]->avg_selling_price =
                        ($combinedSales[$id]->avg_selling_price + $item->avg_selling_price) / 2;
                } else {
                    $combinedSales[$id] = $item;
                }
            });

            // ترتيب حسب الكمية المباعة وأخذ جميع المنتجات
            $topProductIds = $combinedSales->sortByDesc('total_sold')->pluck('id');

            if ($topProductIds->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'top_products' => [],
                        'period' => [
                            'from' => $dateFrom,
                            'to' => $dateTo,
                            'warehouse_id' => $warehouseId,
                            'category_id' => $categoryId
                        ]
                    ]
                ]);
            }

            // جلب تفاصيل المنتجات مع المخزون
            $topProducts = DB::table('product_services as ps')
                ->leftJoin('product_service_categories as psc', 'ps.category_id', '=', 'psc.id')
                ->leftJoin('product_service_units as psu', 'ps.unit_id', '=', 'psu.id')
                ->leftJoin('warehouse_products as wp', function($join) use ($warehouseId) {
                    $join->on('ps.id', '=', 'wp.product_id');
                    if ($warehouseId) {
                        $join->where('wp.warehouse_id', $warehouseId);
                    }
                })
                ->whereIn('ps.id', $topProductIds)
                ->where('ps.created_by', $creatorId)
                ->select(
                    'ps.id',
                    'ps.name',
                    'ps.sku',
                    'ps.sale_price',
                    'ps.purchase_price',
                    'psc.name as category_name',
                    'psu.name as unit_name',
                    DB::raw('COALESCE(SUM(wp.quantity), 0) as stock_quantity')
                )
                ->groupBy('ps.id', 'ps.name', 'ps.sku', 'ps.sale_price', 'ps.purchase_price', 'psc.name', 'psu.name')
                ->get()
                ->map(function($product) use ($combinedSales) {
                    $salesData = $combinedSales[$product->id];
                    $profitMargin = $salesData->avg_selling_price > 0 && $product->purchase_price > 0
                        ? round((($salesData->avg_selling_price - $product->purchase_price) / $salesData->avg_selling_price) * 100, 1)
                        : 0;

                    return (object)[
                        'id' => $product->id,
                        'name' => $product->name,
                        'sku' => $product->sku,
                        'sale_price' => round($product->sale_price, 2),
                        'purchase_price' => round($product->purchase_price, 2),
                        'category_name' => $product->category_name ?? 'غير محدد',
                        'unit_name' => $product->unit_name ?? 'قطعة',
                        'stock_quantity' => $product->stock_quantity,
                        'total_sold' => $salesData->total_sold,
                        'total_revenue' => round($salesData->total_revenue, 2),
                        'order_count' => $salesData->order_count,
                        'avg_selling_price' => round($salesData->avg_selling_price, 2),
                        'profit_margin' => $profitMargin
                    ];
                })
                ->sortByDesc('total_sold')
                ->values();

            return response()->json([
                'success' => true,
                'data' => [
                    'top_products' => $topProducts,
                    'period' => [
                        'from' => $dateFrom,
                        'to' => $dateTo,
                        'warehouse_id' => $warehouseId,
                        'category_id' => $categoryId
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب بيانات أفضل المنتجات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب تحليل الفئات
     */
    public function getCategoryAnalysis(Request $request)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $warehouseId = $request->get('warehouse_id');
            $dateFrom = $request->get('date_from', Carbon::now()->startOfMonth()->format('Y-m-d'));
            $dateTo = $request->get('date_to', Carbon::now()->endOfMonth()->format('Y-m-d'));

            $categoryAnalysis = DB::table('product_service_categories as psc')
                ->leftJoin('product_services as ps', 'psc.id', '=', 'ps.category_id')
                ->leftJoin('pos_products as pp', 'ps.id', '=', 'pp.product_id')
                ->leftJoin('pos as p', 'pp.pos_id', '=', 'p.id')
                ->leftJoin('warehouse_products as wp', 'ps.id', '=', 'wp.product_id')
                ->where('psc.created_by', $creatorId)
                ->when($warehouseId, function($q) use ($warehouseId) {
                    return $q->where(function($subQ) use ($warehouseId) {
                        $subQ->where('p.warehouse_id', $warehouseId)
                             ->orWhere('wp.warehouse_id', $warehouseId)
                             ->orWhereNull('p.warehouse_id');
                    });
                })
                ->when($dateFrom && $dateTo, function($q) use ($dateFrom, $dateTo) {
                    return $q->where(function($subQ) use ($dateFrom, $dateTo) {
                        $subQ->whereBetween('p.pos_date', [$dateFrom, $dateTo])
                             ->orWhereNull('p.pos_date');
                    });
                })
                ->select(
                    'psc.id',
                    'psc.name',
                    'psc.color',
                    DB::raw('COUNT(DISTINCT ps.id) as total_products'),
                    DB::raw('COALESCE(SUM(pp.quantity), 0) as total_sold'),
                    DB::raw('COALESCE(SUM(pp.quantity * pp.price), 0) as total_revenue'),
                    DB::raw('COALESCE(SUM(wp.quantity), 0) as total_stock'),
                    DB::raw('COALESCE(SUM(ps.purchase_price * wp.quantity), 0) as stock_value')
                )
                ->groupBy('psc.id', 'psc.name', 'psc.color')
                ->orderBy('total_revenue', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'category_analysis' => $categoryAnalysis,
                    'period' => [
                        'from' => $dateFrom,
                        'to' => $dateTo,
                        'warehouse_id' => $warehouseId
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب تحليل الفئات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب تحليل دوران المخزون
     */
    public function getInventoryTurnover(Request $request)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $warehouseId = $request->get('warehouse_id');
            $categoryId = $request->get('category_id');
            $dateFrom = $request->get('date_from', Carbon::now()->startOfMonth()->format('Y-m-d'));
            $dateTo = $request->get('date_to', Carbon::now()->endOfMonth()->format('Y-m-d'));

            $inventoryTurnover = DB::table('product_services as ps')
                ->leftJoin('warehouse_products as wp', 'ps.id', '=', 'wp.product_id')
                ->leftJoin('product_service_categories as psc', 'ps.category_id', '=', 'psc.id')
                ->leftJoin('product_service_units as psu', 'ps.unit_id', '=', 'psu.id')
                ->leftJoin(DB::raw('(
                    SELECT
                        pp.product_id,
                        SUM(pp.quantity) as total_sold,
                        SUM(pp.quantity * pp.price) as total_revenue
                    FROM pos_products pp
                    JOIN pos p ON pp.pos_id = p.id
                    WHERE p.created_by = ' . $creatorId . '
                        AND p.pos_date BETWEEN "' . $dateFrom . '" AND "' . $dateTo . '"
                        ' . ($warehouseId ? 'AND p.warehouse_id = ' . $warehouseId : '') . '
                    GROUP BY pp.product_id
                ) as sales'), 'ps.id', '=', 'sales.product_id')
                ->where('ps.created_by', $creatorId)
                ->when($warehouseId, function($q) use ($warehouseId) {
                    return $q->where('wp.warehouse_id', $warehouseId);
                })
                ->when($categoryId, function($q) use ($categoryId) {
                    return $q->where('ps.category_id', $categoryId);
                })
                ->select(
                    'ps.id',
                    'ps.name',
                    'ps.sku',
                    'ps.sale_price',
                    'ps.purchase_price',
                    'psc.name as category_name',
                    'psu.name as unit_name',
                    'wp.quantity as current_stock',
                    DB::raw('COALESCE(sales.total_sold, 0) as total_sold'),
                    DB::raw('COALESCE(sales.total_revenue, 0) as total_revenue'),
                    DB::raw('ps.purchase_price * wp.quantity as stock_value'),
                    DB::raw('CASE
                        WHEN wp.quantity > 0 THEN COALESCE(sales.total_sold, 0) / wp.quantity
                        ELSE 0
                    END as turnover_ratio'),
                    DB::raw('CASE
                        WHEN COALESCE(sales.total_sold, 0) > 0 THEN wp.quantity / COALESCE(sales.total_sold, 0) * 30
                        ELSE 999
                    END as days_of_supply')
                )
                ->orderBy('turnover_ratio', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'inventory_turnover' => $inventoryTurnover,
                    'period' => [
                        'from' => $dateFrom,
                        'to' => $dateTo,
                        'warehouse_id' => $warehouseId,
                        'category_id' => $categoryId
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب تحليل دوران المخزون: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب المنتجات الراكدة
     */
    public function getStagnantProducts(Request $request)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $warehouseId = $request->get('warehouse_id');
            $categoryId = $request->get('category_id');
            $dateFrom = $request->get('date_from', Carbon::now()->startOfMonth()->format('Y-m-d'));
            $dateTo = $request->get('date_to', Carbon::now()->endOfMonth()->format('Y-m-d'));

            $stagnantProducts = DB::table('product_services as ps')
                ->join('warehouse_products as wp', 'ps.id', '=', 'wp.product_id')
                ->leftJoin('product_service_categories as psc', 'ps.category_id', '=', 'psc.id')
                ->leftJoin('product_service_units as psu', 'ps.unit_id', '=', 'psu.id')
                ->leftJoin(DB::raw('(
                    SELECT DISTINCT pp.product_id
                    FROM pos_products pp
                    JOIN pos p ON pp.pos_id = p.id
                    WHERE p.created_by = ' . $creatorId . '
                        AND p.pos_date BETWEEN "' . $dateFrom . '" AND "' . $dateTo . '"
                        ' . ($warehouseId ? 'AND p.warehouse_id = ' . $warehouseId : '') . '
                ) as sales'), 'ps.id', '=', 'sales.product_id')
                ->where('ps.created_by', $creatorId)
                ->where('wp.quantity', '>', 0)
                ->whereNull('sales.product_id')
                ->when($warehouseId, function($q) use ($warehouseId) {
                    return $q->where('wp.warehouse_id', $warehouseId);
                })
                ->when($categoryId, function($q) use ($categoryId) {
                    return $q->where('ps.category_id', $categoryId);
                })
                ->select(
                    'ps.id',
                    'ps.name',
                    'ps.sku',
                    'ps.sale_price',
                    'ps.purchase_price',
                    'psc.name as category_name',
                    'psu.name as unit_name',
                    'wp.quantity as current_stock',
                    DB::raw('ps.purchase_price * wp.quantity as stock_value'),
                    DB::raw('DATEDIFF(NOW(), ps.updated_at) as days_since_update'),
                    'ps.expiry_date',
                    DB::raw('CASE
                        WHEN ps.expiry_date IS NOT NULL THEN DATEDIFF(ps.expiry_date, NOW())
                        ELSE NULL
                    END as days_to_expiry')
                )
                ->orderBy('stock_value', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'stagnant_products' => $stagnantProducts,
                    'period' => [
                        'from' => $dateFrom,
                        'to' => $dateTo,
                        'warehouse_id' => $warehouseId,
                        'category_id' => $categoryId
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب المنتجات الراكدة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب تحليل أوامر الاستلام
     */
    public function getReceiptOrdersAnalysis(Request $request)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $warehouseId = $request->get('warehouse_id');
            $categoryId = $request->get('category_id');
            $dateFrom = $request->get('date_from', Carbon::now()->startOfMonth()->format('Y-m-d'));
            $dateTo = $request->get('date_to', Carbon::now()->endOfMonth()->format('Y-m-d'));

            // التحقق من وجود جدول أوامر الاستلام
            if (!DB::getSchemaBuilder()->hasTable('receipt_orders') ||
                !DB::getSchemaBuilder()->hasTable('receipt_order_products')) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'receipt_analysis' => [],
                        'message' => 'جداول أوامر الاستلام غير متوفرة'
                    ]
                ]);
            }

            $receiptAnalysis = DB::table('receipt_order_products as rop')
                ->join('receipt_orders as ro', 'rop.receipt_order_id', '=', 'ro.id')
                ->join('product_services as ps', 'rop.product_id', '=', 'ps.id')
                ->leftJoin('product_service_categories as psc', 'ps.category_id', '=', 'psc.id')
                ->leftJoin('product_service_units as psu', 'ps.unit_id', '=', 'psu.id')
                ->where('ps.created_by', $creatorId)
                ->whereBetween('ro.invoice_date', [$dateFrom, $dateTo])
                ->when($warehouseId, function($q) use ($warehouseId) {
                    return $q->where('ro.warehouse_id', $warehouseId);
                })
                ->when($categoryId, function($q) use ($categoryId) {
                    return $q->where('ps.category_id', $categoryId);
                })
                ->select(
                    'ps.id',
                    'ps.name',
                    'ps.sku',
                    'ps.purchase_price',
                    'psc.name as category_name',
                    'psu.name as unit_name',
                    'ro.order_type',
                    DB::raw('SUM(rop.quantity) as total_received'),
                    DB::raw('SUM(rop.total_cost) as total_cost'),
                    DB::raw('AVG(rop.unit_cost) as avg_unit_cost'),
                    DB::raw('COUNT(DISTINCT ro.id) as receipt_count')
                )
                ->groupBy('ps.id', 'ps.name', 'ps.sku', 'ps.purchase_price', 'psc.name', 'psu.name', 'ro.order_type')
                ->orderBy('total_received', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'receipt_analysis' => $receiptAnalysis,
                    'period' => [
                        'from' => $dateFrom,
                        'to' => $dateTo,
                        'warehouse_id' => $warehouseId,
                        'category_id' => $categoryId
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب بيانات أوامر الاستلام: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب المنتجات القريبة من انتهاء الصلاحية
     */
    public function getExpiringProducts(Request $request)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $warehouseId = $request->get('warehouse_id');
            $categoryId = $request->get('category_id');
            $daysThreshold = $request->get('days_threshold', 30);

            $expiringProducts = DB::table('product_services as ps')
                ->join('warehouse_products as wp', 'ps.id', '=', 'wp.product_id')
                ->leftJoin('product_service_categories as psc', 'ps.category_id', '=', 'psc.id')
                ->leftJoin('product_service_units as psu', 'ps.unit_id', '=', 'psu.id')
                ->where('ps.created_by', $creatorId)
                ->whereNotNull('ps.expiry_date')
                ->where('ps.expiry_date', '<=', Carbon::now()->addDays($daysThreshold))
                ->where('wp.quantity', '>', 0)
                ->when($warehouseId, function($q) use ($warehouseId) {
                    return $q->where('wp.warehouse_id', $warehouseId);
                })
                ->when($categoryId, function($q) use ($categoryId) {
                    return $q->where('ps.category_id', $categoryId);
                })
                ->select(
                    'ps.id',
                    'ps.name',
                    'ps.sku',
                    'ps.sale_price',
                    'ps.purchase_price',
                    'ps.expiry_date',
                    'psc.name as category_name',
                    'psu.name as unit_name',
                    'wp.quantity as current_stock',
                    DB::raw('ps.purchase_price * wp.quantity as potential_loss'),
                    DB::raw('DATEDIFF(ps.expiry_date, NOW()) as days_to_expiry'),
                    DB::raw('CASE
                        WHEN DATEDIFF(ps.expiry_date, NOW()) <= 0 THEN "منتهي الصلاحية"
                        WHEN DATEDIFF(ps.expiry_date, NOW()) <= 7 THEN "خطر عالي"
                        WHEN DATEDIFF(ps.expiry_date, NOW()) <= 15 THEN "خطر متوسط"
                        ELSE "تحذير"
                    END as risk_level')
                )
                ->orderBy('days_to_expiry', 'asc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'expiring_products' => $expiringProducts,
                    'days_threshold' => $daysThreshold,
                    'filters' => [
                        'warehouse_id' => $warehouseId,
                        'category_id' => $categoryId
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب المنتجات منتهية الصلاحية: ' . $e->getMessage()
            ], 500);
        }
    }
}
