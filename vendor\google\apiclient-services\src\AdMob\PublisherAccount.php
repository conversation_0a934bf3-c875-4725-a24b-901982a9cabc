<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\AdMob;

class PublisherAccount extends \Google\Model
{
  /**
   * @var string
   */
  public $currencyCode;
  /**
   * @var string
   */
  public $name;
  /**
   * @var string
   */
  public $publisherId;
  /**
   * @var string
   */
  public $reportingTimeZone;

  /**
   * @param string
   */
  public function setCurrencyCode($currencyCode)
  {
    $this->currencyCode = $currencyCode;
  }
  /**
   * @return string
   */
  public function getCurrencyCode()
  {
    return $this->currencyCode;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param string
   */
  public function setPublisherId($publisherId)
  {
    $this->publisherId = $publisherId;
  }
  /**
   * @return string
   */
  public function getPublisherId()
  {
    return $this->publisherId;
  }
  /**
   * @param string
   */
  public function setReportingTimeZone($reportingTimeZone)
  {
    $this->reportingTimeZone = $reportingTimeZone;
  }
  /**
   * @return string
   */
  public function getReportingTimeZone()
  {
    return $this->reportingTimeZone;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(PublisherAccount::class, 'Google_Service_AdMob_PublisherAccount');
