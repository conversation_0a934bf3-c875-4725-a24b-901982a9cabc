<?php

namespace App\Http\Controllers;

use App\Models\warehouse;
use App\Models\WarehouseTransfer;
use App\Models\Purchase;
use App\Models\PurchaseProduct;
use App\Models\User;
use App\Models\ProductService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BranchInventoryController extends Controller
{
    /**
     * Display a listing of branch inventory operations.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (Auth::user()->can('manage warehouse') || Auth::user()->can('show warehouse')) {
            $user = Auth::user();
            
            // Get warehouse transfers (نقل بضاعة)
            $transfers = WarehouseTransfer::with(['product', 'fromWarehouse', 'toWarehouse'])
                ->where('created_by', $user->creatorId())
                ->select(
                    'id as operation_id',
                    'to_warehouse as warehouse_id',
                    'product_id',
                    'quantity',
                    'created_by',
                    'date as operation_date',
                    DB::raw("'نقل بضاعة' as operation_type"),
                    'created_at'
                )
                ->get();

            // Get purchase receipts (استلام بضاعة)
            $purchases = Purchase::with(['warehouse', 'vender'])
                ->where('created_by', $user->creatorId())
                ->select(
                    'id as operation_id',
                    'warehouse_id',
                    DB::raw('NULL as product_id'),
                    DB::raw('0 as quantity'),
                    'created_by',
                    'purchase_date as operation_date',
                    DB::raw("'استلام بضاعة' as operation_type"),
                    'created_at'
                )
                ->get();

            // Calculate product count for purchases
            foreach ($purchases as $purchase) {
                $productCount = PurchaseProduct::where('purchase_id', $purchase->operation_id)->count();
                $purchase->quantity = $productCount;
            }

            // Combine and sort operations
            $operations = collect()
                ->merge($transfers)
                ->merge($purchases)
                ->sortByDesc('created_at')
                ->values();

            // Get warehouses for filtering
            $warehouses = warehouse::where('created_by', $user->creatorId())->get();

            return view('branch_inventory.index', compact('operations', 'warehouses'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Get operation details via AJAX
     */
    public function getOperationDetails(Request $request)
    {
        $operationType = $request->operation_type;
        $operationId = $request->operation_id;

        if ($operationType === 'نقل بضاعة') {
            $transfer = WarehouseTransfer::with(['product', 'fromWarehouse', 'toWarehouse'])
                ->find($operationId);
            
            if ($transfer) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'type' => 'نقل بضاعة',
                        'from_warehouse' => $transfer->fromWarehouse->name ?? 'غير محدد',
                        'to_warehouse' => $transfer->toWarehouse->name ?? 'غير محدد',
                        'product' => $transfer->product->name ?? 'غير محدد',
                        'quantity' => $transfer->quantity,
                        'date' => $transfer->date,
                    ]
                ]);
            }
        } elseif ($operationType === 'استلام بضاعة') {
            $purchase = Purchase::with(['warehouse', 'vender', 'purchaseProducts.product'])
                ->find($operationId);
            
            if ($purchase) {
                $products = $purchase->purchaseProducts->map(function ($item) {
                    return [
                        'name' => $item->product->name ?? 'غير محدد',
                        'quantity' => $item->quantity,
                        'price' => $item->price,
                    ];
                });

                return response()->json([
                    'success' => true,
                    'data' => [
                        'type' => 'استلام بضاعة',
                        'warehouse' => $purchase->warehouse->name ?? 'غير محدد',
                        'vendor' => $purchase->vender->name ?? 'غير محدد',
                        'products' => $products,
                        'total_amount' => $purchase->getTotal(),
                        'date' => $purchase->purchase_date,
                    ]
                ]);
            }
        }

        return response()->json([
            'success' => false,
            'message' => 'العملية غير موجودة'
        ]);
    }

    /**
     * Filter operations by warehouse
     */
    public function filterByWarehouse(Request $request)
    {
        $warehouseId = $request->warehouse_id;
        $user = Auth::user();

        $query = collect();

        if ($warehouseId && $warehouseId !== 'all') {
            // Filter transfers
            $transfers = WarehouseTransfer::with(['product', 'fromWarehouse', 'toWarehouse'])
                ->where('created_by', $user->creatorId())
                ->where('to_warehouse', $warehouseId)
                ->select(
                    'id as operation_id',
                    'to_warehouse as warehouse_id',
                    'product_id',
                    'quantity',
                    'created_by',
                    'date as operation_date',
                    DB::raw("'نقل بضاعة' as operation_type"),
                    'created_at'
                )
                ->get();

            // Filter purchases
            $purchases = Purchase::with(['warehouse', 'vender'])
                ->where('created_by', $user->creatorId())
                ->where('warehouse_id', $warehouseId)
                ->select(
                    'id as operation_id',
                    'warehouse_id',
                    DB::raw('NULL as product_id'),
                    DB::raw('0 as quantity'),
                    'created_by',
                    'purchase_date as operation_date',
                    DB::raw("'استلام بضاعة' as operation_type"),
                    'created_at'
                )
                ->get();

            // Calculate product count for purchases
            foreach ($purchases as $purchase) {
                $productCount = PurchaseProduct::where('purchase_id', $purchase->operation_id)->count();
                $purchase->quantity = $productCount;
            }

            $query = collect()
                ->merge($transfers)
                ->merge($purchases);
        } else {
            // Return all operations (same as index method)
            return $this->index();
        }

        $operations = $query->sortByDesc('created_at')->values();
        $warehouses = warehouse::where('created_by', $user->creatorId())->get();

        return view('branch_inventory.index', compact('operations', 'warehouses'));
    }
}
