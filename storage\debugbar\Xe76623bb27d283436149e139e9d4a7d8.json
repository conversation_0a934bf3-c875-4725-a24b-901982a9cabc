{"__meta": {"id": "Xe76623bb27d283436149e139e9d4a7d8", "datetime": "2025-06-07 22:17:41", "utime": **********.513764, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334659.904094, "end": **********.513816, "duration": 1.6097221374511719, "duration_str": "1.61s", "measures": [{"label": "Booting", "start": 1749334659.904094, "relative_start": 0, "end": **********.362125, "relative_end": **********.362125, "duration": 1.4580309391021729, "duration_str": "1.46s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.362154, "relative_start": 1.4580600261688232, "end": **********.513822, "relative_end": 5.9604644775390625e-06, "duration": 0.15166807174682617, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43405864, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00662, "accumulated_duration_str": "6.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.480412, "duration": 0.00662, "duration_str": "6.62ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aeg571Q3ThqeNMfiIAc14ULZrTrG6DjnVuualNlJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2071105071 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2071105071\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1410129215 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1410129215\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1009344626 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IkJCREYvMmJDWXdqU1pxd3hHRGY3T3c9PSIsInZhbHVlIjoiek5nMHNYUW5DQzU1RUc1MHl2dnlIQ2ZDbzVIaElaVTM5Z3VaUDduYTJvZW5hWTFSck1YNWNyL3dlN1ZWTW1qVjRyNEZ3R3lFczV6YktNK2t4Um9RSU1BeVI5UHFHU3hER3FtMFlXejdrZzExN3dPV3BzRVNlSi9qRk5NckYrOVJ6bE9Md3ZPSHdIQnltbmcvVXlCMmFyZXovSmJHSStFMkpseTFpSC9xRGo3Sm8vK3hrSHR3L0JpNUNBVGs0S3BDaHJHZW5JSmpLY2FIVyt5L2QrOXRjY0RLYlpBOXlpYkZWVERaaUVwR2VJVTdQeXpTT1hReXFWSDduTmFNK2dNcVZZOVVhaVcvK0hPU3VMdXc0Vk1jSUZ6aXd0WFJVSHY5U3cxeWlMR0tveThZZXV4c1FIUTBxU1dvUjZ3OUpRMDRWRWFQWTRReE5DYnhEVm5Qb2NtYXppUEg1WDl1TmpzZnJPcmZsc3A2ZWFTdFJwbG9aRlBSWC85dU0rOUt6MXRZNGxSa0hKZkZaK3ZraG5GWXZNbmZvc3ZKYzV0V01mMkZMVk9FQm04dGtGbnV5cWRqRXRCektQMkdTWVloOElDT3dzQ3dOdGZ4Qy8zbzBGTGh2bTRrM1JCc0cvVWNyNEYwS2h5MCtSV2UycXdSRnJpMCtYd1kzeUhwNGxwNWovT3IiLCJtYWMiOiIwMGNiNDdmOWFhNWNjNDg2ZmM4Yjg1ZDlhMTk5YjU2ZmYwYjQ5MWU3NDA3ZjYxMmE0MjQwN2JiNjYwYmQ5ZTMzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InRNYVh1U0xHUVNBWkFMdmx0Y3ljV2c9PSIsInZhbHVlIjoiZ3NKc0txcll2OW1RUTFSNjIwWVhpbzBMWXoxQmZ5ZUxlOS90a3dSMWoyQlhHT2trVnI4NzlocDRnYWx5OGRFeWx4dGhMYzJUTG9xSFZnRmw2WVE4YzlER0NwM3ROd1lmbEtLTzdyNklMNjd2bzNnUUNid3pibVlhSGxBNWF5YXRFVzVOQWxDV1VFbW1mOVdVWUFjYW9BaFcwU2FENTJHNzAvcGFraU1KQ1JOZCs2Nk9ZYTM4RitVQUgzY2hBc05UMVJMNEpjREtHaFNtV2txQVZ3dEpHTGVwTGFnaDhsNnRwdVNQejJ3eUVpMlEyWGg5N1QrdmpJR0o2cTNCaEtWdG5jUU9aeXF2RlpVM1Jka25QU3JDcnZJZ0I2aG5OMzhRQWR1ZmU0OFBOYklZNlI5d2pmSEc2NDQzay9HREx3YzVnUEh6dGIzajBPc0FIY1Q5TzhoK2ZLNEc5WjZWUXRFbTkrSGFBWUZsN1J3d0hheStVQlJ5bDdNMnJmbVRSV1F0bXNRR3ExRTdXNTU0T3NScE93N3MrRDIrVGVoMDNQUWxkdTI0WWdDWnpFdklKL2RuMjFTSGRickhZaXRkeXhOR2dXVjJsTVRlWndlRjBUellmUUJsRzVzL1ExU1ZTNTRBUmkwTTBtN1Z5bnFaUTVpVFVDZGg2QVpRNEo5UDY2S28iLCJtYWMiOiJmNjE0ZDNmZTZmZmIyOTFkMWZmZmRkZThjNmVlYzY0OTVlYWFjNDdlNzIzYzhlODhhYzJjMWVlNzc2ZGE0MTM2IiwidGFnIjoiIn0%3D; _clsk=ij6lzq%7C1749334646194%7C1%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1009344626\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-34177714 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aeg571Q3ThqeNMfiIAc14ULZrTrG6DjnVuualNlJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JGbRg3rX9CZC3OYjDIftJuBr6aHnx3C6fHbYY9Rw</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34177714\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1148055983 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:17:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxEdjFiZ1d6RVhDRy9aV09YbVFJT0E9PSIsInZhbHVlIjoic3FqaUorVzlvTG5FTWNBQXB6NkxJK3dWUzVTV1JSV3VTRjZYMms2REZoZUJTQUlZT284TXU0THpPTEtzRUcrTlA4Zm92WG9rUGxkcThTNEJEeHZTZExiZ2M2REFoM2NCTnN1QXQrZlZaVVlmK2laUkRnZzhpZG5aZnJRWTFkVWtHRTlTdEkxNDd2Z1BUcEp1K0VoRGpEWm4yV1Q0ZDZIaEJ5OG5IR1VKM21ZWElqUHNGUVRnSEdkM0MvUFBkaUsrMDNSMVgwMldXREdHYUhURHh2djZHcXdXNUxwUm56UEUzUjRkamZ3VDRKYnNrajZYRVR5c0VTSTl0NFd2UFdaelNIaWxoaU8xbWJpWUc5WVJSWFFDMUpIalBwL2lISW1rc3Mydk9sRFNvNVM0TEFJcDJmVXBYalJFdzFjdEs2YVduektMUFhXSWN5L084cC9FRnZEaEdUU0xJODFRR2YvMTlXOEQ2R1BtZkZONmw4M0xJdm04UkRYanVNY2xkTzJ4LzlYbzVHM0FiMDRDY21TRGZveG1xM2VFN2RPTEZGQ29yamsybmNjbi8zVkp1SGlaeTh1WTJyRlFEbUJYZU9XczdpZmlvQXNBMnVCbnllNXM1QnRSTUFDeFUwS0RMZ2xBaXVlTlhTeVZNeWxQUHVlT2tUWmhTN2sxZXRxc040ek4iLCJtYWMiOiIyN2NhMmE5ZTQ5NGJmNGMyODQ3ZDQyNjQ0MTMzNTVhMTkwMTc0NWEwYTk4M2E0MTYzNDExMjE3ZjlhYThlNmQwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:17:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImVXeFB0TkFVUHlvN1A1Sk94Y2VsV3c9PSIsInZhbHVlIjoiVXdBaGEySlpya0xKRldHN2RicE01cC9KaE12aVRkNjhSemloL1d2a2J6dDhXVWlpWWhjSnA1RWxsbDhDZGNFN0hCdWY2SjhDZHZaWHBBT1ZnZDVrVUVjaU0xRGk3dWZaRVdvUEJBTy90U3graVA1TjJlUUxCekNiT3daaWtxWG5uaG1sMnB1c200aU1VTDRDNjk3ZlExd1JvZGlSMVluMy8rMnlNQm5nQ1V1NkZYLzM2aDY2cjJhWnBZMUlUK1JrcUluSW94WlV1ZzRzenNWODE4SXNYM2tjZWZ5MVQ0YzY5d3d0d0VDVTZQWFNEaFExdURGNERjWDdiUGtKWHNVTDBWY0t3N2FTV2czQjROWXMyZ2Y4OGNzUk5ERTRNcHBLdWtGb2k5MDYrUWZmZFAyVlA3SnVoT2lsWEF4azc4RWJQeC9Ca2JKSGc2RVhGOHI2am5wWHQ2R3NNbXJEOVI1MlpYYTM4TnowbTgveDE4eFNlZ25jSERteVZrWGNVT1dKRHF4SkRLd1Rxb2h4TDhDRTVtTzVHOFFsS0ZMQnNPdmthWks5VTRlSllvU3VPWDBkMHJEMW94cEZjQVVLTHpianBjdExndnZWSndkK0NQMjF5OEhObThDT3Ira0hES2tpZXB0Tm1nSkt2OFY5TWFYejRROVdNZ2FuQldiVGZQWDgiLCJtYWMiOiJkYTE2NDkzZDFjNDE5NzkzN2MxMWJlNDcxYzJjMGVkNTlhMjIwNTA1MDIxZTdmZmJiMTBiMjllNGIxYjRlOTlmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:17:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxEdjFiZ1d6RVhDRy9aV09YbVFJT0E9PSIsInZhbHVlIjoic3FqaUorVzlvTG5FTWNBQXB6NkxJK3dWUzVTV1JSV3VTRjZYMms2REZoZUJTQUlZT284TXU0THpPTEtzRUcrTlA4Zm92WG9rUGxkcThTNEJEeHZTZExiZ2M2REFoM2NCTnN1QXQrZlZaVVlmK2laUkRnZzhpZG5aZnJRWTFkVWtHRTlTdEkxNDd2Z1BUcEp1K0VoRGpEWm4yV1Q0ZDZIaEJ5OG5IR1VKM21ZWElqUHNGUVRnSEdkM0MvUFBkaUsrMDNSMVgwMldXREdHYUhURHh2djZHcXdXNUxwUm56UEUzUjRkamZ3VDRKYnNrajZYRVR5c0VTSTl0NFd2UFdaelNIaWxoaU8xbWJpWUc5WVJSWFFDMUpIalBwL2lISW1rc3Mydk9sRFNvNVM0TEFJcDJmVXBYalJFdzFjdEs2YVduektMUFhXSWN5L084cC9FRnZEaEdUU0xJODFRR2YvMTlXOEQ2R1BtZkZONmw4M0xJdm04UkRYanVNY2xkTzJ4LzlYbzVHM0FiMDRDY21TRGZveG1xM2VFN2RPTEZGQ29yamsybmNjbi8zVkp1SGlaeTh1WTJyRlFEbUJYZU9XczdpZmlvQXNBMnVCbnllNXM1QnRSTUFDeFUwS0RMZ2xBaXVlTlhTeVZNeWxQUHVlT2tUWmhTN2sxZXRxc040ek4iLCJtYWMiOiIyN2NhMmE5ZTQ5NGJmNGMyODQ3ZDQyNjQ0MTMzNTVhMTkwMTc0NWEwYTk4M2E0MTYzNDExMjE3ZjlhYThlNmQwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:17:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImVXeFB0TkFVUHlvN1A1Sk94Y2VsV3c9PSIsInZhbHVlIjoiVXdBaGEySlpya0xKRldHN2RicE01cC9KaE12aVRkNjhSemloL1d2a2J6dDhXVWlpWWhjSnA1RWxsbDhDZGNFN0hCdWY2SjhDZHZaWHBBT1ZnZDVrVUVjaU0xRGk3dWZaRVdvUEJBTy90U3graVA1TjJlUUxCekNiT3daaWtxWG5uaG1sMnB1c200aU1VTDRDNjk3ZlExd1JvZGlSMVluMy8rMnlNQm5nQ1V1NkZYLzM2aDY2cjJhWnBZMUlUK1JrcUluSW94WlV1ZzRzenNWODE4SXNYM2tjZWZ5MVQ0YzY5d3d0d0VDVTZQWFNEaFExdURGNERjWDdiUGtKWHNVTDBWY0t3N2FTV2czQjROWXMyZ2Y4OGNzUk5ERTRNcHBLdWtGb2k5MDYrUWZmZFAyVlA3SnVoT2lsWEF4azc4RWJQeC9Ca2JKSGc2RVhGOHI2am5wWHQ2R3NNbXJEOVI1MlpYYTM4TnowbTgveDE4eFNlZ25jSERteVZrWGNVT1dKRHF4SkRLd1Rxb2h4TDhDRTVtTzVHOFFsS0ZMQnNPdmthWks5VTRlSllvU3VPWDBkMHJEMW94cEZjQVVLTHpianBjdExndnZWSndkK0NQMjF5OEhObThDT3Ira0hES2tpZXB0Tm1nSkt2OFY5TWFYejRROVdNZ2FuQldiVGZQWDgiLCJtYWMiOiJkYTE2NDkzZDFjNDE5NzkzN2MxMWJlNDcxYzJjMGVkNTlhMjIwNTA1MDIxZTdmZmJiMTBiMjllNGIxYjRlOTlmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:17:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1148055983\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-641757717 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aeg571Q3ThqeNMfiIAc14ULZrTrG6DjnVuualNlJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-641757717\", {\"maxDepth\":0})</script>\n"}}