{"__meta": {"id": "X1950cbb5ecf433c7e6d6a11f54466b19", "datetime": "2025-06-30 15:34:37", "utime": **********.246862, "method": "POST", "uri": "/pos-financial-record/closing-shift", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751297676.817153, "end": **********.246883, "duration": 0.4297299385070801, "duration_str": "430ms", "measures": [{"label": "Booting", "start": 1751297676.817153, "relative_start": 0, "end": **********.154891, "relative_end": **********.154891, "duration": 0.337738037109375, "duration_str": "338ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.154899, "relative_start": 0.33774590492248535, "end": **********.246885, "relative_end": 2.1457672119140625e-06, "duration": 0.09198617935180664, "duration_str": "91.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50842024, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record/closing-shift", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@closeShift", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.closing.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=169\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:169-188</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.029009999999999998, "accumulated_duration_str": "29.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.183525, "duration": 0.022269999999999998, "duration_str": "22.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 76.767}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.213683, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 76.767, "width_percent": 1.275}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-30 15:34:37', `closed_by` = 22, `shifts`.`updated_at` = '2025-06-30 15:34:37' where `id` = '44' and `shifts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2025-06-30 15:34:37", "22", "2025-06-30 15:34:37", "44"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 176}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2243862, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:176", "source": "app/Http/Controllers/FinancialRecordController.php:176", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=176", "ajax": false, "filename": "FinancialRecordController.php", "line": "176"}, "connection": "kdmkjkqknb", "start_percent": 78.042, "width_percent": 10.617}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-30 15:34:37' where `id` = 22", "type": "query", "params": [], "bindings": ["1", "2025-06-30 15:34:37", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.229386, "duration": 0.00329, "duration_str": "3.29ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:183", "source": "app/Http/Controllers/FinancialRecordController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=183", "ajax": false, "filename": "FinancialRecordController.php", "line": "183"}, "connection": "kdmkjkqknb", "start_percent": 88.659, "width_percent": 11.341}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "success": "Shift Closed Successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record/closing-shift", "status_code": "<pre class=sf-dump id=sf-dump-1923592412 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1923592412\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1136104084 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1136104084\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-167445952 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>shift_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">44</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167445952\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-440855494 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297675919%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJGT0pQc2M0bzUvemJ5akt3aDduZ0E9PSIsInZhbHVlIjoiKzBSMnI3NFNQLzc4a205eUhKU3psbDdjaDJ2eXdVb1VyK1drKzRGdGUxRU1DT0VkNkcya1RNUmgzTW1IU3JiSU0xeGVpOFgrUlc3dkxTNGtTaERQakE3Y3RpNVQ2SWJrMjNKUTVlVVpLZ1ZldFNJNk5nWmxSdU1lbzBzR0kyVmZZMkY4cVVBbGRKSFoxYUJ5L041cXZ5eUdhdGtQRFhGRGtBVmpLNVZmRG5URHhUNHZ5cTJWYzFiWWRXNkQySkVZVTdnajNzSkF3ZklhZVN4bXN1elRxSDZaTkJXQjlIS0dVbG4wL1c0ditmaXZMNmZYbER6bkdMNVdJOTJ6V2wzakxqS2VlTTA1Q3ZieVRhMUEycnk1QUg2M3FTR2tpelFHOWNEME1UUlNVM1ZEQU4wOXBWZmc0YXJRQjFqM2xoTStkeWtBMUhDVFlqUEI1cG9TUEdHMEtTUGZGbkFEd1JaK0xEdUkzSDIzWkJ6V0l2ZlJzbG9vNnpMMDJoeitYZGUxWmlWTlRSUkJzQVFLUzl1c3ZFa2ZJOHJ6MVVNOFpBMENSTjhwMEIvWU0wRG9ENUxBc0RSYmtGTVVXTGUxMndKQ3krTHBxWHZzMW12ZTJ6VW42VW1CcGM4RTQ0MFc4dW1kRlB0S2RySVZJam94Rmx6YnBZV3BGd3dCc0owQmFKTisiLCJtYWMiOiJhYzQyN2ZhMTYxOGNhODFkNWNkMTQzNDJhYTNiM2ZmMmFiMzA3MzY3OGU4MzMwYTM0MjE3M2I4ZThmYzBjYjAzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ikw0K2NpZUlUMythanpheVJvZ1VrYlE9PSIsInZhbHVlIjoiUERTNTM0RjZZS3JUNnVjN1ZRazFOaE9JVEpxSExjOW9ac1BZYkpHbWJWbEJJeVp4MVNWb2xmMUtmaUEwc2wyUVhVZmhwVlBuNFFuaEdZdXpRd1pCUDVmTE5lZSs3SFBQS09zVWVKd28xVTh6U2FIbTV1TXBnQ1hZRmpQdS9iWUVQLzErdU4rU2hYSUhCM09PVEdpdlJWRGsvUTNiN2wySC9JVFJLdS9ZU1VybWFzc3plcFh1NUIwT2RrbnFqbi9OSDgyTUMyZzE4dGg2b2JSb2RLR0VNdzVIQzNXb1pETTVkK0JodzVTQnlzSUJGRW9yRVZlUU41azJhMGZ4WnIvMHIrTU55alpLYmxWdmlxaHg0eXdpNzBPVjRkV3c2eVNTNDhvb1ZjaDJBeXY2TU10bWVaZTZTekdyZ3FHQ2paMEhVZmtld1EzMkh1aUxRZlRlSEQrNjZWVWhkQW8vK1lYTHNRTzAyVEV5eGRWejZMYTRHeWF0R3F1YjY3TjNTaEdtclhEb3Z1WE8yNjhFOXJLcXIxYjNaTTAvdU5FTVZGT3N3YzlTY2dINmtTTDJHREt4cVJxRHkvNW1NblhSKzVhZVNERXNoUE9iYUk4VGFLQ01SU25IZ0E0ZTV1NTZ0UXdvcUxtSG5QOFNhckh1dlNoQU9KK3pzTGk5UjFhYldyRjIiLCJtYWMiOiJiMjAwMTFlNTQ2NDNjODljMjMxOTI4MWZkMTZiNWNjYWQ3ZmM0MGU0NTlhNjA0OWE4MDE4MjM5NTlhN2U2Y2FkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-440855494\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1900567075 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1900567075\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-589492333 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZFTkxtYk5JREpTNm5qRWJhYm1telE9PSIsInZhbHVlIjoiSjdNY1liT3FwMXRVMVdDRVZSd0twLzdnS3FBakZmTnVVK3ZvNURZeHdTd3p3TnhHUUJYN1ljYjNaSEwvcTFWWHQ4WXFSOGp4b0h6SFFIb2RJanMyQ1R2SkxIejVzUXQ4aFNtVTFUR1lydXkrS1hOS1ltd3pCQnhxaFFndExlNStWd2F2QWh4Y2tRbUpiYXU5cFVxcXVMMnRxSkZSd2lzK0NGMWsvQnBZWHFjeG9jcWc2aGQ0MTlHZjZvUWV3YVlNQXFQOVNSeGRnUEVFbk1EVUNrbnYrakJWNnY4anJpMFA1NDlQM1hTVHZzdHNhQ28xUENzTDY3Z2Vqckh3Mjh1NzY0TWpUT3ZQS2Z1cTdQOXorU3lvUC9tY0FXYldwdXQ2Q2RTejcrOG8vMzYvY1Q2OU1LZWFLdnhNSFJ3NEN2NDRtNVBKQ3p3VFJmcjV2TG4xNUhmSXFkS2EzcCtQVWZPT3B3MFRsL1hWZlJZTzJGVVZlY1lMSEVSYjhVYUUyeVBkam9LMWZKM2VYZWpMQkdpaDlBMVVrR1g0SjZwdkgxeEZzYlcvc3l4YkM0OUpVb0pXc2grUjBmT1dtelVWOUhSZVJjMTIzaTVNS2xRN3l1a3VxV3FtUUs0SzBSczR6b3NMdndpWFBTVUp3aW41Y1VNc041Q1F5eEpVVFZJYVV3ZHYiLCJtYWMiOiI1Y2U1ZmFkYmM2Mzk0Y2I1NmY1Yjk2ZjQyOGY4ZjVkMjJiNGNjYTYwZDdlZGY3MmFiOWMyODFkOTBlODFiZGM5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Imo1cGd4VmQzUFVESzVvQVhYTExJa3c9PSIsInZhbHVlIjoiWEhMRURBTUVIaFAvVEpJK2JEKzhVSTlBa2txeno4bHhqN1hNd3ptcHdsS2FxS1FPWDZ6a3puMmtMS2pMaGo4ODlReW5kVkhoWlprN054VmZvY0RBV1Z0ekNUcXlucmFSZXRPdU5tUENjNjNxb0Nka0o2aHJ0S1A0Ui9RUDZPdVFLbmk4ZmwyMzhIWjFUOUZlcitodEs4YndLYWlLS1phV0Y1cHBOenVObDhCREVMVXE4S0YxYzA1VGNKRnArMXV6MG8zcmVaRzYyQUVtNUVzd1JLdVBmTGRVY1IzVlpDazNnMWR1SnBIQmhvSStHT2RIWTNjOUhBc2g3c2lnNnBIYVcxMythWWdqVFlqMGUzYzZDK2syc0h5RElaYWliRkM1V0tFcVNhV3BMMmpieHdjNnVVS09SbGp5ekllTmhyMDJBSXpqU3BDUERWZjhrUnJoUGNVTEJSNjJ4c0JFQ1crNjF5UnJmeGFHUXZPckxlTnFpWWNNNTJDVHRidjE2VE5PYnNzVzZqZnVZRFpZYVJoTjZHYzF1KzhBaDM4b1VoYW4rMzJ0N2J4VVhnK0NFM2xRYWZ5Vm5BczhxaHFQRWExdGQxR1kzeGo0TDRLUE5GM2ptSE5tdW0wYm5HUWlFdmJyZEJMUFZiS3VPaHVpcmYwVFMzc05ORXhUVnJPUFVCSzUiLCJtYWMiOiJiYmJmNjJkNjQyMDQ1YzY0MjgxYWUzZjNjMWZjZTMxZDNlMGUzMzFiNWY5YmYzNmFhOTgxN2Y5NjZjMWUxZjllIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZFTkxtYk5JREpTNm5qRWJhYm1telE9PSIsInZhbHVlIjoiSjdNY1liT3FwMXRVMVdDRVZSd0twLzdnS3FBakZmTnVVK3ZvNURZeHdTd3p3TnhHUUJYN1ljYjNaSEwvcTFWWHQ4WXFSOGp4b0h6SFFIb2RJanMyQ1R2SkxIejVzUXQ4aFNtVTFUR1lydXkrS1hOS1ltd3pCQnhxaFFndExlNStWd2F2QWh4Y2tRbUpiYXU5cFVxcXVMMnRxSkZSd2lzK0NGMWsvQnBZWHFjeG9jcWc2aGQ0MTlHZjZvUWV3YVlNQXFQOVNSeGRnUEVFbk1EVUNrbnYrakJWNnY4anJpMFA1NDlQM1hTVHZzdHNhQ28xUENzTDY3Z2Vqckh3Mjh1NzY0TWpUT3ZQS2Z1cTdQOXorU3lvUC9tY0FXYldwdXQ2Q2RTejcrOG8vMzYvY1Q2OU1LZWFLdnhNSFJ3NEN2NDRtNVBKQ3p3VFJmcjV2TG4xNUhmSXFkS2EzcCtQVWZPT3B3MFRsL1hWZlJZTzJGVVZlY1lMSEVSYjhVYUUyeVBkam9LMWZKM2VYZWpMQkdpaDlBMVVrR1g0SjZwdkgxeEZzYlcvc3l4YkM0OUpVb0pXc2grUjBmT1dtelVWOUhSZVJjMTIzaTVNS2xRN3l1a3VxV3FtUUs0SzBSczR6b3NMdndpWFBTVUp3aW41Y1VNc041Q1F5eEpVVFZJYVV3ZHYiLCJtYWMiOiI1Y2U1ZmFkYmM2Mzk0Y2I1NmY1Yjk2ZjQyOGY4ZjVkMjJiNGNjYTYwZDdlZGY3MmFiOWMyODFkOTBlODFiZGM5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Imo1cGd4VmQzUFVESzVvQVhYTExJa3c9PSIsInZhbHVlIjoiWEhMRURBTUVIaFAvVEpJK2JEKzhVSTlBa2txeno4bHhqN1hNd3ptcHdsS2FxS1FPWDZ6a3puMmtMS2pMaGo4ODlReW5kVkhoWlprN054VmZvY0RBV1Z0ekNUcXlucmFSZXRPdU5tUENjNjNxb0Nka0o2aHJ0S1A0Ui9RUDZPdVFLbmk4ZmwyMzhIWjFUOUZlcitodEs4YndLYWlLS1phV0Y1cHBOenVObDhCREVMVXE4S0YxYzA1VGNKRnArMXV6MG8zcmVaRzYyQUVtNUVzd1JLdVBmTGRVY1IzVlpDazNnMWR1SnBIQmhvSStHT2RIWTNjOUhBc2g3c2lnNnBIYVcxMythWWdqVFlqMGUzYzZDK2syc0h5RElaYWliRkM1V0tFcVNhV3BMMmpieHdjNnVVS09SbGp5ekllTmhyMDJBSXpqU3BDUERWZjhrUnJoUGNVTEJSNjJ4c0JFQ1crNjF5UnJmeGFHUXZPckxlTnFpWWNNNTJDVHRidjE2VE5PYnNzVzZqZnVZRFpZYVJoTjZHYzF1KzhBaDM4b1VoYW4rMzJ0N2J4VVhnK0NFM2xRYWZ5Vm5BczhxaHFQRWExdGQxR1kzeGo0TDRLUE5GM2ptSE5tdW0wYm5HUWlFdmJyZEJMUFZiS3VPaHVpcmYwVFMzc05ORXhUVnJPUFVCSzUiLCJtYWMiOiJiYmJmNjJkNjQyMDQ1YzY0MjgxYWUzZjNjMWZjZTMxZDNlMGUzMzFiNWY5YmYzNmFhOTgxN2Y5NjZjMWUxZjllIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-589492333\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-167531384 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Shift Closed Successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167531384\", {\"maxDepth\":0})</script>\n"}}