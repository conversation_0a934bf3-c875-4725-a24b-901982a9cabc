<?php

namespace App\Http\Controllers;

use App\Models\ProductService;
use App\Models\warehouse;
use App\Models\WarehouseProduct;
use App\Models\ProductExpiryDate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ProductExpiryController extends Controller
{
    /**
     * Display a listing of products with expiry dates.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        if (Auth::user()->can('show product expiry') || Auth::user()->hasRole('Cashier')) {
            // Check if user has a specific warehouse assigned
            $user = Auth::user();

            if ($user->warehouse_id) {
                // User has a specific warehouse assigned, only show that warehouse
                $warehouses = warehouse::where('id', $user->warehouse_id)
                    ->where('created_by', '=', $user->creatorId())
                    ->get();

                // If user has a warehouse assigned, default to that warehouse
                $selectedWarehouse = $request->warehouse_id ?? $user->warehouse_id;

                // If user tries to access a warehouse they don't have permission for, redirect to their assigned warehouse
                if ($selectedWarehouse != 'all' && $selectedWarehouse != $user->warehouse_id) {
                    return redirect()->route('product.expiry.index', ['warehouse_id' => $user->warehouse_id])
                        ->with('error', __('You only have access to your assigned warehouse.'));
                }
            } else {
                // User doesn't have a specific warehouse, show all warehouses
                $warehouses = warehouse::where('created_by', '=', $user->creatorId())->get();

                // Get selected warehouse or default to all
                $selectedWarehouse = $request->warehouse_id ?? 'all';
            }

            // Obtener productos que aparecen en POS (con cantidad > 0 en warehouse_products)
            if ($selectedWarehouse != 'all') {
                // Obtener productos del almacén específico con cantidad > 0
                $warehouseProducts = WarehouseProduct::where('warehouse_id', $selectedWarehouse)
                    ->where('quantity', '>', 0)
                    ->with(['product', 'warehouse'])
                    ->get();

                // Filtrar solo productos (no servicios) y que pertenezcan al usuario actual
                $warehouseProducts = $warehouseProducts->filter(function($wp) {
                    return $wp->product &&
                           $wp->product->type == 'product' &&
                           $wp->product->created_by == \Auth::user()->creatorId();
                });

                $products = collect();

                // Procesar cada producto del almacén
                foreach ($warehouseProducts as $warehouseProduct) {
                    // Crear una copia del producto con información del almacén
                    $product = clone $warehouseProduct->product;
                    $product->warehouse_id = $warehouseProduct->warehouse_id;
                    $product->warehouse_name = $warehouseProduct->warehouse ? $warehouseProduct->warehouse->name : '';
                    $product->warehouse_quantity = $warehouseProduct->quantity;

                    // Obtener o crear registro de fecha de caducidad
                    $defaultExpiryDate = $product->expiry_date ?? date('Y-m-d', strtotime('+1 year'));

                    $expiryDate = ProductExpiryDate::firstOrCreate(
                        [
                            'product_id' => $product->id,
                            'warehouse_id' => $selectedWarehouse,
                        ],
                        [
                            'expiry_date' => $defaultExpiryDate,
                            'created_by' => \Auth::user()->creatorId(),
                        ]
                    );

                    $product->warehouse_expiry_date = $expiryDate->expiry_date;

                    // Calcular días restantes hasta la caducidad
                    if ($expiryDate->expiry_date) {
                        $today = date('Y-m-d');
                        $expiryTimestamp = strtotime($expiryDate->expiry_date);
                        $todayTimestamp = strtotime($today);
                        $daysRemaining = floor(($expiryTimestamp - $todayTimestamp) / (60 * 60 * 24));
                        $product->days_remaining = $daysRemaining;

                        // Determinar estado de caducidad
                        if ($daysRemaining < 0) {
                            $product->expiry_status = __('منتهي الصلاحية');
                            $product->expiry_badge_class = 'badge bg-danger';
                        } elseif ($daysRemaining <= 60) {
                            $product->expiry_status = __('ينتهي قريباً');
                            $product->expiry_badge_class = 'badge bg-warning';
                        } else {
                            $product->expiry_status = __('صالح');
                            $product->expiry_badge_class = 'badge bg-success';
                        }
                    } else {
                        $product->days_remaining = null;
                        $product->expiry_status = __('غير محدد');
                        $product->expiry_badge_class = 'badge bg-secondary';
                    }

                    $products->push($product);
                }
            } else {
                // Obtener todos los productos de almacén con cantidad > 0
                $warehouseProducts = WarehouseProduct::where('quantity', '>', 0)
                    ->with(['product', 'warehouse'])
                    ->get();

                // Filtrar solo productos (no servicios) y que pertenezcan al usuario actual
                $warehouseProducts = $warehouseProducts->filter(function($wp) {
                    return $wp->product &&
                           $wp->product->type == 'product' &&
                           $wp->product->created_by == \Auth::user()->creatorId();
                });

                $products = collect();

                // Procesar cada producto del almacén
                foreach ($warehouseProducts as $warehouseProduct) {
                    // Crear una copia del producto con información del almacén
                    $product = clone $warehouseProduct->product;
                    $product->warehouse_id = $warehouseProduct->warehouse_id;
                    $product->warehouse_name = $warehouseProduct->warehouse ? $warehouseProduct->warehouse->name : '';
                    $product->warehouse_quantity = $warehouseProduct->quantity;

                    // Obtener fecha de caducidad para este producto en este almacén
                    $expiryDate = ProductExpiryDate::where('product_id', $product->id)
                        ->where('warehouse_id', $warehouseProduct->warehouse_id)
                        ->first();

                    if ($expiryDate) {
                        $product->warehouse_expiry_date = $expiryDate->expiry_date;

                        // Calcular días restantes hasta la caducidad
                        if ($expiryDate->expiry_date) {
                            $today = date('Y-m-d');
                            $expiryTimestamp = strtotime($expiryDate->expiry_date);
                            $todayTimestamp = strtotime($today);
                            $daysRemaining = floor(($expiryTimestamp - $todayTimestamp) / (60 * 60 * 24));
                            $product->days_remaining = $daysRemaining;

                            // Determinar estado de caducidad
                            if ($daysRemaining < 0) {
                                $product->expiry_status = __('منتهي الصلاحية');
                                $product->expiry_badge_class = 'badge bg-danger';
                            } elseif ($daysRemaining <= 60) {
                                $product->expiry_status = __('ينتهي قريباً');
                                $product->expiry_badge_class = 'badge bg-warning';
                            } else {
                                $product->expiry_status = __('صالح');
                                $product->expiry_badge_class = 'badge bg-success';
                            }
                        } else {
                            $product->days_remaining = null;
                            $product->expiry_status = __('غير محدد');
                            $product->expiry_badge_class = 'badge bg-secondary';
                        }
                    } else {
                        // Si no hay fecha de caducidad específica para este almacén, usar la fecha general del producto
                        $product->warehouse_expiry_date = $product->expiry_date;

                        // Calcular días restantes hasta la caducidad
                        if ($product->expiry_date) {
                            $today = date('Y-m-d');
                            $expiryTimestamp = strtotime($product->expiry_date);
                            $todayTimestamp = strtotime($today);
                            $daysRemaining = floor(($expiryTimestamp - $todayTimestamp) / (60 * 60 * 24));
                            $product->days_remaining = $daysRemaining;

                            // Determinar estado de caducidad
                            if ($daysRemaining < 0) {
                                $product->expiry_status = __('منتهي الصلاحية');
                                $product->expiry_badge_class = 'badge bg-danger';
                            } elseif ($daysRemaining <= 60) {
                                $product->expiry_status = __('ينتهي قريباً');
                                $product->expiry_badge_class = 'badge bg-warning';
                            } else {
                                $product->expiry_status = __('صالح');
                                $product->expiry_badge_class = 'badge bg-success';
                            }
                        } else {
                            $product->days_remaining = null;
                            $product->expiry_status = __('غير محدد');
                            $product->expiry_badge_class = 'badge bg-secondary';
                        }
                    }

                    $products->push($product);
                }
            }

            // Filtrar por estado de caducidad si se proporciona
            if ($request->has('status')) {
                $status = $request->status;
                $today = date('Y-m-d');

                if ($status === 'expired') {
                    $products = $products->filter(function($product) use ($today) {
                        return $product->warehouse_expiry_date && $product->warehouse_expiry_date < $today;
                    });
                } elseif ($status === 'expiring_soon') {
                    $sixtyDaysLater = date('Y-m-d', strtotime('+60 days'));
                    $products = $products->filter(function($product) use ($today, $sixtyDaysLater) {
                        return $product->warehouse_expiry_date &&
                               $product->warehouse_expiry_date >= $today &&
                               $product->warehouse_expiry_date <= $sixtyDaysLater;
                    });
                } elseif ($status === 'valid') {
                    $products = $products->filter(function($product) use ($today) {
                        return $product->warehouse_expiry_date && $product->warehouse_expiry_date >= $today;
                    });
                }
            }

            return view('product_expiry.index', compact('products', 'warehouses', 'selectedWarehouse'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Show the form for editing the expiry date of a product.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id, Request $request)
    {
        if (Auth::user()->can('edit product expiry') || Auth::user()->hasRole('Cashier')) {
            $product = ProductService::find($id);
            $user = Auth::user();

            if ($product->created_by == $user->creatorId()) {
                // Get warehouse information if provided
                $warehouseId = $request->warehouse_id ?? null;

                // Check if user has a specific warehouse assigned
                if ($user->warehouse_id && $warehouseId != $user->warehouse_id) {
                    // If user tries to access a product in a warehouse they don't have permission for
                    return redirect()->route('product.expiry.index')
                        ->with('error', __('You only have access to your assigned warehouse.'));
                }

                $warehouse = null;
                $expiryDate = null;

                if ($warehouseId && $warehouseId != 'all') {
                    $warehouse = warehouse::find($warehouseId);

                    // Get quantity from warehouse_products table directly
                    $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouseId)
                        ->where('product_id', $product->id)
                        ->first();

                    // Get warehouse name
                    $product->warehouse_name = $warehouse ? $warehouse->name : '';

                    // Get quantity using direct approach
                    $directQuantity = $warehouseProduct ? $warehouseProduct->quantity : 0;

                    // Use the direct approach
                    $product->warehouse_quantity = $directQuantity;

                    // Log for debugging
                    \Log::info('Edit - Product ID: ' . $product->id . ', Name: ' . $product->name . ', Warehouse ID: ' . $warehouseId . ', Quantity: ' . $product->warehouse_quantity);

                    // Get or create expiry date record for this product in this warehouse
                    // Asegurarse de que expiry_date no sea null
                    $defaultExpiryDate = $product->expiry_date ?? date('Y-m-d', strtotime('+1 year'));

                    $expiryDate = ProductExpiryDate::firstOrCreate(
                        [
                            'product_id' => $product->id,
                            'warehouse_id' => $warehouseId,
                        ],
                        [
                            'expiry_date' => $defaultExpiryDate,
                            'created_by' => \Auth::user()->creatorId(),
                        ]
                    );
                }

                return view('product_expiry.edit', compact('product', 'warehouse', 'warehouseId', 'expiryDate'));
            } else {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Update the expiry date of a product.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        if (Auth::user()->can('edit product expiry') || Auth::user()->hasRole('Cashier')) {
            $product = ProductService::find($id);

            if ($product->created_by == \Auth::user()->creatorId()) {
                $request->validate([
                    'expiry_date' => 'required|date',
                ]);

                // Asegurarse de que la fecha de caducidad no sea nula
                if (!$request->expiry_date) {
                    return redirect()->back()->with('error', __('تاريخ الصلاحية مطلوب.'))->withInput();
                }

                // Update expiry date based on warehouse selection
                if ($request->has('warehouse_id') && $request->warehouse_id != 'all') {
                    // Update or create expiry date for this product in this warehouse
                    ProductExpiryDate::updateOrCreate(
                        [
                            'product_id' => $product->id,
                            'warehouse_id' => $request->warehouse_id,
                        ],
                        [
                            'expiry_date' => $request->expiry_date,
                            'created_by' => \Auth::user()->creatorId(),
                        ]
                    );
                } else {
                    // Update the general expiry date in the product table
                    $product->expiry_date = $request->expiry_date;
                    $product->save();
                }

                // Redirect back to the index page with the warehouse filter if it was set
                $redirectParams = [];
                if ($request->has('warehouse_id')) {
                    $redirectParams['warehouse_id'] = $request->warehouse_id;
                }

                return redirect()->route('product.expiry.index', $redirectParams)
                    ->with('success', __('تم تحديث تاريخ الصلاحية بنجاح.'));
            } else {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Show product details with expiry information.
     *
     * @param  int  $id
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function show($id, Request $request)
    {
        if (Auth::user()->can('show product expiry') || Auth::user()->hasRole('Cashier')) {
            $product = ProductService::with(['category', 'unit'])->find($id);
            $user = Auth::user();

            if ($product && $product->created_by == $user->creatorId()) {
                // Get warehouse information if provided
                $warehouseId = $request->warehouse_id ?? null;

                // Check if user has a specific warehouse assigned
                if ($user->warehouse_id && $warehouseId != $user->warehouse_id && $warehouseId != 'all') {
                    // If user tries to access a product in a warehouse they don't have permission for
                    return redirect()->route('product.expiry.index')
                        ->with('error', __('You only have access to your assigned warehouse.'));
                }

                $warehouse = null;

                if ($warehouseId && $warehouseId != 'all') {
                    $warehouse = warehouse::find($warehouseId);

                    // Get quantity from warehouse_products table directly
                    $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouseId)
                        ->where('product_id', $product->id)
                        ->first();

                    // Get warehouse name
                    $product->warehouse_name = $warehouse ? $warehouse->name : '';

                    // Get quantity using direct approach
                    $directQuantity = $warehouseProduct ? $warehouseProduct->quantity : 0;

                    // Use the direct approach
                    $product->warehouse_quantity = $directQuantity;

                    // Log for debugging
                    \Log::info('Show - Product ID: ' . $product->id . ', Name: ' . $product->name . ', Warehouse ID: ' . $warehouseId . ', Quantity: ' . $product->warehouse_quantity);

                    // Get expiry date for this product in this warehouse
                    $expiryDate = ProductExpiryDate::where('product_id', $product->id)
                        ->where('warehouse_id', $warehouseId)
                        ->first();

                    if ($expiryDate) {
                        $product->warehouse_expiry_date = $expiryDate->expiry_date;

                        // Calcular días restantes hasta la caducidad
                        if ($expiryDate->expiry_date) {
                            $today = date('Y-m-d');
                            $expiryTimestamp = strtotime($expiryDate->expiry_date);
                            $todayTimestamp = strtotime($today);
                            $daysRemaining = floor(($expiryTimestamp - $todayTimestamp) / (60 * 60 * 24));
                            $product->days_remaining = $daysRemaining;
                        } else {
                            $product->days_remaining = null;
                        }

                        $status = $expiryDate->getExpiryStatus();
                        $product->expiry_status = $status['status'];
                        $product->expiry_badge_class = $status['badge_class'];
                    } else {
                        $product->warehouse_expiry_date = null;
                        $product->days_remaining = null;
                        $product->expiry_status = __('غير محدد');
                        $product->expiry_badge_class = 'badge bg-secondary';
                    }
                } else {
                    $product->warehouse_quantity = 'N/A';
                    $product->warehouse_expiry_date = $product->expiry_date;

                    // Get expiry status and calculate days remaining
                    if ($product->expiry_date) {
                        $today = date('Y-m-d');
                        $expiryTimestamp = strtotime($product->expiry_date);
                        $todayTimestamp = strtotime($today);
                        $daysRemaining = floor(($expiryTimestamp - $todayTimestamp) / (60 * 60 * 24));
                        $product->days_remaining = $daysRemaining;

                        if ($daysRemaining < 0) {
                            $product->expiry_status = __('منتهي الصلاحية');
                            $product->expiry_badge_class = 'badge bg-danger';
                        } elseif ($daysRemaining <= 60) {
                            $product->expiry_status = __('ينتهي قريباً');
                            $product->expiry_badge_class = 'badge bg-warning';
                        } else {
                            $product->expiry_status = __('صالح');
                            $product->expiry_badge_class = 'badge bg-success';
                        }
                    } else {
                        $product->days_remaining = null;
                        $product->expiry_status = __('غير محدد');
                        $product->expiry_badge_class = 'badge bg-secondary';
                    }
                }

                // Get warehouses where this product exists, filtered by user permissions
                $warehouseProductsQuery = WarehouseProduct::where('product_id', $product->id)
                    ->with('warehouse');

                // If user has a specific warehouse assigned, only show that warehouse
                if ($user->warehouse_id) {
                    $warehouseProductsQuery->where('warehouse_id', $user->warehouse_id);
                }

                $productWarehouses = $warehouseProductsQuery->get();

                // Get expiry dates for warehouses, filtered by user permissions
                $expiryDatesQuery = ProductExpiryDate::where('product_id', $product->id)
                    ->with('warehouse');

                // If user has a specific warehouse assigned, only show that warehouse
                if ($user->warehouse_id) {
                    $expiryDatesQuery->where('warehouse_id', $user->warehouse_id);
                }

                $expiryDates = $expiryDatesQuery->get();

                return view('product_expiry.show', compact('product', 'warehouse', 'warehouseId', 'productWarehouses', 'expiryDates'));
            } else {
                return redirect()->back()->with('error', __('Product not found.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }
}
