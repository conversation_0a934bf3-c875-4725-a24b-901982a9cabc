{"__meta": {"id": "Xbb727471f66b1b449be92f4ebfc445e1", "datetime": "2025-06-30 15:44:07", "utime": **********.431037, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751298246.929004, "end": **********.431055, "duration": 0.5020511150360107, "duration_str": "502ms", "measures": [{"label": "Booting", "start": 1751298246.929004, "relative_start": 0, "end": **********.36487, "relative_end": **********.36487, "duration": 0.435866117477417, "duration_str": "436ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.364879, "relative_start": 0.43587493896484375, "end": **********.431057, "relative_end": 1.9073486328125e-06, "duration": 0.0661780834197998, "duration_str": "66.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45554352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0033100000000000004, "accumulated_duration_str": "3.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3977652, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.674}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4105651, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.674, "width_percent": 15.408}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.418585, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.082, "width_percent": 16.918}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-676673380 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297684545%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjIxeHhUd1FGL3ROelJjWDBYcTFNNnc9PSIsInZhbHVlIjoiRGJodGJ0cHNiMEpGQXZ0UlQyeEVtZ1FZeFZPVHh6SUFUVS9rcGVXcmZpdDZWLzB5eHVmM25qckt3aGRIM3d1RlpHWVhSNmtDVjBybEs0MTNMbjdWNCsya2tBVWZ5VWw0U1hBa0FqRGlkWmRWOFVPTXQ0ejB3Z20yS3dieXoxT056QnZROU5YeFA2REZDb1VkWWoyWVBwY1FXd0hhSkU2ZGJZNUliTVU5QjRIUmxjVXhncjRDWmdDT1lWZGVXNVkyemhhRmdmUnN4MzgrSkZKU2RiRm40NDZ2Y3RvY2FmV1orWjk3OFEvQjg1NTRSanBNSDF6ZDMzVVJ3MDFHZk1sR05lcWUzbElJeThxenR6U1Z5ZWltMFhzNFFuWXU0blNVaVpoSEdCUmY3UWZNVlJoYmM5MUJ6RzJXZ3BYMmhJVGJRSHRYTndVR3dkV0dMaU85MVZ1MkJqYmM2Y0pMTGlYUTZwRndRUnRJNk4rRVYzMmZSSWk0UUJGcEZBUU85ekNVZ3B5VS93eFVOZWIxQTVOaXpFVlE5SEQ4dlZWNWFaMHJIZXR2OGd4ZDdoUGs2YTQvTGIxQnhNMWVMV2Y2K1F5eFRSVjdJcmdpU2tVM2lleFRmQkZsbmdiRnhqOEt4SDB2S2MrUE5PQVZMZjJaQlFCMC91ZXJvTURkNDg4bTBhUUgiLCJtYWMiOiI2OTQzNmVmZjg4Mzc3ZjQ1OTlhOGY0OGIzMjA3MzAxZmIwYjM3NTE4NTdhYTIwZjJmZmViYmY0ZTdhODYwOTI1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlY1T1A4KzVva3NNSDJqWDEzbDVFOFE9PSIsInZhbHVlIjoiUmtHWExoWDBkc2NvUTVqUFoyUHdiekhHOGkwS2s1dng1UGxBYXFZM3lEbWp1cVBrZlR4Y1Y3U0lnYWxDYVE1UmxUWmRIVU8rcTFwZGhkRHIyWmdTdnlZZ3NsMUl3VjhHZW9FTlNkN3p1ZmthclVHaVk1ZTJmczA1Rm93WnV6ZWF2MDlwU1pMZHE2N0pJYStDcGhmUHdTVU45dnd6RjJLWW5KSlZveU5nbkxKSGM0QzBjSWx0ZXFZVGhQMGdIMGY1ZUtoaG84djVnYjhiQmVMcWw2NjhoNy9nTVVZVXF6bURKZUhmQ00zUE5DMklGVERiVi9hY2NpR2dnMDlsYjFGWWJEVFpHSUNoMXVlVzJuL2pDQzNWKzBPeVY2UHBPYStnVFBTcVhOWmkwd1dkVjBrOGtXajdjY1Yrd3J1VXVDdzBod3lTNEFnTUVlRHdmUDdMRWtVSTJ4MzQzZDJSUmU2enY5L05Ta05ScElHNnIvT0VOWFhDdXFFKzRrTzJiOEVmdStTS3pxUVJiUFpab1hvd1ZaVTlJZmpaZTNteENvdjJ2WFM0RjhxdTFucmszdno5c1RTSWdZUUZmRmxqbzEydmd6RVBqTUxOMnlVbWk3ck4yMGJzbmtCOHN2aitUbG56T1M0eUdWMHVhejZ2bm8xbVhqYlVGZm5oUkFOZjNobmwiLCJtYWMiOiI5ZDBkMTQ5YjAyNWVkYjQwMmRlYTcxYTgyNzQzYWYxZjRhOWQ3ZmVmMTZhYmUwMTA4NTMxM2YyNGU3YTMwZTM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-676673380\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-649652125 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-649652125\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-138409861 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:44:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1hUUFJYVpiSzYrenMyTm85d2Y2NEE9PSIsInZhbHVlIjoiZW5jVHdMMlE3a2tleGtyeUpHa2RuR3dhT3EwMjg5ck1TbVFKeFN5QUszSTlXUnVMRm41QUZKVUxFbFRXTzBxR1dhMUxpeStnc3IzV0Nka0hiMUhLZlRFem9oOFJZWkFsT1d1T2pjdjNSVEJzTEVPaDcwaGI0d3I4aThLbnkzbXZtV2s3ZVhyYzY5eHg3RjBJV211eXM2cE9xU2pZV1BKQWtpeExYbk5rQjUxVFNpWDlEL25vc3E0SW5yMVFtaGV3QjhKREFtajlRbGtMRTcyc3VsMmo2RzNHYklQSHhBT0JQcGdScXVmZFBzdmNkdXAwVzR6NVE4M3VsZndyZWJCMkNiYlJNa2FEK2toU3hQcFFQZ0RCQ052Y1lXRmxubjRtWEZtd1VYWWYzNEZSeEpValBxYzB0RTdHaG1iNDJoeXhVM2JJeE5lenZVZHVybnkyVlpqdnNqRWVQUUFIVWNoNWRiMEd1Z2dLUHMrWHFuN1JYUHI2dlc3TWRTSFpTL0hNdG1lVlVGUDNPYyt6VzJwZ3BoZERDNzRWOXVHaDZhVkZmVGwwMXhqUGd2anJ1OVBmSTB6dXRGWmZ4TG10azFVV0FpbUsvUjcySXN6Sk94VzIrTzQzelZ1RW5lVUFIanFQWk9lS1oweGM5TGZjdDBVcmRienY5L1VpRG9LZjRUbkoiLCJtYWMiOiI0YjI3N2VkMWUzODAxNDIwZjM2NGRkNTcwNzhiMjczZWYwN2UyMjhjNjFjZWE1Zjg4OTc5MzE1NDJjNzZlYzJiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBQTlJ4OUt1TVVUSFp4UnkyY2l1TkE9PSIsInZhbHVlIjoiNUdHdWNqdEI0WXgxL09raVB3NkVRQzdteDMraHJYKzJoMkhYSUdHcyt0eVFNMm9wZkVmdTE4SGtPUUNCek1Rd2ZFeDI4M004SDQvcVRTbkFiUlBEeXpJaVZCWEU3WjdKU2E5RGliYkppTUc5OEhTKy9MMDJyRkF5ck5IbmZnRWFlcjNxbndKUEdPZkxyMkEwK2tGSGYvV2lzWXc2WVBveGZIbnEwSTkvcVE3NmU5bzRNQlMxdlZoUk9hMEtBc29CSkxXUlJNZlg4RngyMGQ1TWJGakhucWhGbWZlcWpxVFpzYlhESWhqQThLcEhmejFibklsQ0c4RGY3SzJxemNaYXFtT0xHZWcrSzA2Q243V1ZZTmdmQXNDUWw5UlU5RnpKQkswOFpjTGxjTEcxaUtWTmE2TkVONnhualNQbzl2ZDEvUG1kdURwMTdNd0RyeGUzY2Q2a3NZRXh3ZitWL25TWWdIUlRwbEhwdEdSemtiUGVRZkRyazZ5STlES1pYYUJuN2lDVzJ0NnFkMmhVZjBEV3IzTUVVbTdDR0N0RzBqbGZoRzFZd2hwN0twQUNndXhrcFZuL2tDVTRiZTBuTGJMTE1PTmYzZW0vZGtFaXRJVVJXY2psR1FNT1JLQTh0WEdnTVZscVh5c0p0TEJJdVFYaDQxaTlvbFhFd0YxV2pPWUgiLCJtYWMiOiJiOTc2YTVmYTk1MDNkM2VlY2U0YzlmOGJiN2Q3Y2YwYmIxOTM1NGQzOTcxOGI0ZjY0ZDZkMTZiYTZmZDc0ZDRhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1hUUFJYVpiSzYrenMyTm85d2Y2NEE9PSIsInZhbHVlIjoiZW5jVHdMMlE3a2tleGtyeUpHa2RuR3dhT3EwMjg5ck1TbVFKeFN5QUszSTlXUnVMRm41QUZKVUxFbFRXTzBxR1dhMUxpeStnc3IzV0Nka0hiMUhLZlRFem9oOFJZWkFsT1d1T2pjdjNSVEJzTEVPaDcwaGI0d3I4aThLbnkzbXZtV2s3ZVhyYzY5eHg3RjBJV211eXM2cE9xU2pZV1BKQWtpeExYbk5rQjUxVFNpWDlEL25vc3E0SW5yMVFtaGV3QjhKREFtajlRbGtMRTcyc3VsMmo2RzNHYklQSHhBT0JQcGdScXVmZFBzdmNkdXAwVzR6NVE4M3VsZndyZWJCMkNiYlJNa2FEK2toU3hQcFFQZ0RCQ052Y1lXRmxubjRtWEZtd1VYWWYzNEZSeEpValBxYzB0RTdHaG1iNDJoeXhVM2JJeE5lenZVZHVybnkyVlpqdnNqRWVQUUFIVWNoNWRiMEd1Z2dLUHMrWHFuN1JYUHI2dlc3TWRTSFpTL0hNdG1lVlVGUDNPYyt6VzJwZ3BoZERDNzRWOXVHaDZhVkZmVGwwMXhqUGd2anJ1OVBmSTB6dXRGWmZ4TG10azFVV0FpbUsvUjcySXN6Sk94VzIrTzQzelZ1RW5lVUFIanFQWk9lS1oweGM5TGZjdDBVcmRienY5L1VpRG9LZjRUbkoiLCJtYWMiOiI0YjI3N2VkMWUzODAxNDIwZjM2NGRkNTcwNzhiMjczZWYwN2UyMjhjNjFjZWE1Zjg4OTc5MzE1NDJjNzZlYzJiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBQTlJ4OUt1TVVUSFp4UnkyY2l1TkE9PSIsInZhbHVlIjoiNUdHdWNqdEI0WXgxL09raVB3NkVRQzdteDMraHJYKzJoMkhYSUdHcyt0eVFNMm9wZkVmdTE4SGtPUUNCek1Rd2ZFeDI4M004SDQvcVRTbkFiUlBEeXpJaVZCWEU3WjdKU2E5RGliYkppTUc5OEhTKy9MMDJyRkF5ck5IbmZnRWFlcjNxbndKUEdPZkxyMkEwK2tGSGYvV2lzWXc2WVBveGZIbnEwSTkvcVE3NmU5bzRNQlMxdlZoUk9hMEtBc29CSkxXUlJNZlg4RngyMGQ1TWJGakhucWhGbWZlcWpxVFpzYlhESWhqQThLcEhmejFibklsQ0c4RGY3SzJxemNaYXFtT0xHZWcrSzA2Q243V1ZZTmdmQXNDUWw5UlU5RnpKQkswOFpjTGxjTEcxaUtWTmE2TkVONnhualNQbzl2ZDEvUG1kdURwMTdNd0RyeGUzY2Q2a3NZRXh3ZitWL25TWWdIUlRwbEhwdEdSemtiUGVRZkRyazZ5STlES1pYYUJuN2lDVzJ0NnFkMmhVZjBEV3IzTUVVbTdDR0N0RzBqbGZoRzFZd2hwN0twQUNndXhrcFZuL2tDVTRiZTBuTGJMTE1PTmYzZW0vZGtFaXRJVVJXY2psR1FNT1JLQTh0WEdnTVZscVh5c0p0TEJJdVFYaDQxaTlvbFhFd0YxV2pPWUgiLCJtYWMiOiJiOTc2YTVmYTk1MDNkM2VlY2U0YzlmOGJiN2Q3Y2YwYmIxOTM1NGQzOTcxOGI0ZjY0ZDZkMTZiYTZmZDc0ZDRhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-138409861\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-6******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6********\", {\"maxDepth\":0})</script>\n"}}