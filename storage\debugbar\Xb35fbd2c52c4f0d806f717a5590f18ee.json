{"__meta": {"id": "Xb35fbd2c52c4f0d806f717a5590f18ee", "datetime": "2025-06-30 14:56:50", "utime": **********.73974, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.211021, "end": **********.739763, "duration": 0.5287420749664307, "duration_str": "529ms", "measures": [{"label": "Booting", "start": **********.211021, "relative_start": 0, "end": **********.600987, "relative_end": **********.600987, "duration": 0.3899660110473633, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.600996, "relative_start": 0.38997507095336914, "end": **********.739766, "relative_end": 2.86102294921875e-06, "duration": 0.13876986503601074, "duration_str": "139ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43762256, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02374, "accumulated_duration_str": "23.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.634088, "duration": 0.02323, "duration_str": "23.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.852}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.660554, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "kdmkjkqknb", "start_percent": 97.852, "width_percent": 2.148}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1218696139 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1218696139\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2072535490 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2072535490\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1938771579 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=2uy8by%7C1751295408578%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imp2TUM1M283eHVWRWQzazdPaU0zMXc9PSIsInZhbHVlIjoiYnllUmZzdHVBc2ZOS3E4SGFHamV1QVord2VGNjB2K3MzdTY5ZzYyUVZRQVFpcUs3MUE0UlY4Z2JJOWdsQU9LRTFSV2R2L2pucHRqM0J1V0RKUjVIa0N3TlM3aXJ0NkNCQi82STB2MG53RE1SdkVQYk8wSGYzUzk4aEJEM1c2R25lYWJvL0RNM1pHVy8yaXVtdnE3Z0Z6Mml6RXV3VUxPY2JXWUt3eUN3aG9lNlY1L0tmbDEvajI1a1RWMlQyaUdGUVljalA0ZmFoQmVCZGxkYUdJR2poMEI0TGcyZ1hOQStXdTJyQk9zQmVFS2wvaGowVGU1aHU0TnM2NWdlWkdsN3Nlc1F3TzQrQ29iZkVkd2xHNndwOURqbThER1I0MXFPeTIrUmVTcDBtTjZsaHFjNXY0K3Z0SFNmajhMOEprdmJvekMrM1ZzZGc1cHROWHVRZ29vcFRvT2htdjZyMEgvLzhFdlF6SjhJWmJ5eWtqWjc4M0x1Vm5tdHlabm5WTkM2TzF3TXB1WWtXUVZxUlZESWNzOUM5MWtsZCtiMng4ZTNhUE9tVXI5TkFIbG1CaUdGZEQ4NDEzZVNxbVJ0ek11cDd1Snp6ZHJyK3c2T09ZN3hmdzZaeTY2OHZmRGpFMkNWcncwaVYzSHJpcFJaZ0h5QkdYQ2l4dHVweVNmZmtuMVIiLCJtYWMiOiI5YWY3YjE5OGEwZjk5ZWQyYTljMDNmNmMzNTI5YTU2ZGM4ODUyN2RkOWNmOTBjY2YzNmJiNzM3NzBlZTU0NjRlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlFQU1VsWFhXb29KaFdLM055bU5rZUE9PSIsInZhbHVlIjoiWXdvZ3lpWWJtMU1ycU1Qdko0RUZYWHQ4V0F5N01LbjU3SnhuRHkwTHBjbWcyT0lsRGErOTVJd09GQ0pPZlZhejZMY2s0RWxza2o3dGU5ekZKOEpRS3ducE1xU3NPWlgxeENldVBqZWNJQ3ZDRXdlK2JBbjhPZHlBRUkveEdGNWJFc0lhWFEzb2c1K3JhMXZ0SDFXVTFmMGRzRzlYSC9DQU5uRk4wL0JjUXpHS25lOXI2bjFNZUJxUEtPbHRNOEM3eFBuWW5uVlVZUDNKanBmTmVEQXFZYlRPQW9YMDIvbFB0RFVLYTlJZkNXMlNSaWVEL043bFc1ZDQrYXI3RFN0QmhYdGkxZUpvOTFaOGxkYjB0bEhtbEQ3V0xab1VaemFSa1IyZ2xBYlJRN2NMRElLcVZKbGR4eEpLM0VOOHdLQW9Mc2lLMTg4dk0rSFBVZ2JJVWtyWDB2SE5OZlNxdEdFNkZQWGduOWk0WENtRmVURU95dXUyWmpMV3ZMSTlRL0VzMlQrQy85R21zY1JQYnpPZ0JLZ2M4NlFzN2ZwOTJ6bEU0VzNpR21qVGJBRVptNlVZNGhDb3ZHMXgvT3Z1RG5HMnJOQ3VyTjM1UzRSUFFsQXpteExTazFXMmhCL0xFWm05VTI4disxeElOb3k4U0Nmci9TRmhzWkZwR0lKWFNSaGsiLCJtYWMiOiI5M2ZkNDU5NWNlZjg1MzQzMzM3ZDk1MjllZWM3Y2RjZDc2YzlmMDg0ZTAxNjhjNzE3M2NjOTZiODVmNTA1MzlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938771579\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rf3imG1lPKr2xfPRA9rNVzJMke6dPWK1Wb6HvZ8N</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-600348362 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:56:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJGSlVXVEoyWEhGK0xiQ0xRSTFYbVE9PSIsInZhbHVlIjoiTDFGdFYzc3U4RGV5Y3pSK1JXMm1WMDk0NkpGcDhEWEdhUUdZWjlYV09nMDl0Q3NHaVdoUzN2b1B2VmFRalY5UWpLSHFCaDVVNlhhUTlEVitOT3RwYlFjZk53MlRzVGVxWDFKZHp1S3RPSlVpUVMyRk9BOUxhVC9kT1NyUnczMVZBbDVFNjZYMS9Fd2ltNExOMHVKanJTRDZXcENKbTJlMEtWenF1TnRtQ3R0bHRqRHUrbGI2RXlXWlYyS3dkSkp4QTRuWkpYcWdBcFdtamNTNHhSbDR5bWFwZnh3RUV2QTRaQ0o1Q0lKNVNQMk03UWFCZUsyMVN6dG5DLzA3THZHYXFYL1J2M2EybU9YWmZxYnFRT2ZyaityVGVtVllHVmRpbDFWOGdBSjFrTXptdE9zaG5Fbi9RWkVnSXNndDNYbGk1MG8rc3ppSEFPNVhURitPSVhFS3dZRnpxZFZTcHc1L1BaK2xzQk1YeW1uNEhGVjI3aEErUmNyOG9Jakl0UmJlSUtNWkRpdzFLaHc0QUJraGU5MVhNVTNKRGluUy9WNUFtNWRXdkpqbU5QNnhyTFlmK1dNRUt5TzJ1SStoejJEVlNqdTFsR1hnNkZ1QzdZaTB3OGlwSkY3N09IWXJBQ2MyL200NlgwZ1JGTkFZZUtuNHNjNVdxcFpDWUlMbFJubWQiLCJtYWMiOiIzYjQ5ZWRmY2YyOTRiZDVmMzY3MDhlZTdmYTQ5M2JlNDk2YTRmODJmMDlkZjkzYzhmMmEwYzg4YWI3YTkwNDQ4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:56:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Iis4cGs4R1dhNEhVZGhYQ0R1b2N0U3c9PSIsInZhbHVlIjoiVEgrL2pwWVN3YU52ZzJJQXk2Um02TElhY3Z2UTA5ZW5oUURYendGQWR2WW1sOFI4YS9adUllM29PZnU3VnNhU1RNRGVxTitTTElaNzRLd1loRE9oR0tsUjcyMUEwWGFKM1JnUCt5THJhT0V5QlNXVU10Z0JJVTZSQ0JyTmpFUkUxU1NLRG1TU1N1dDd0ZmdUTkZtQjN6VDBlOUVuS3JNT0RuamdEZmtYWVNmY3hWZ2NIUXU3Vm9HWEpSQmdyVyszbUtoNElqRGc5cE5TN3VWVU1NTDNadnU0M005VEQ1TG56aWVmUWFSV2hVUVUxKzFwTS83eGFOOHpNSDQxeE9DOWV4ODJqd0l4dElmUmpkd1hmZE5uTlh2dmk0Y2I1YTNQZzk4L2ZQWHY0TWNGNG1Oc25zNG5JK0JHOUdPeHdxM2VEVGNCZzhKRVhXbGFESjU3WWZZSXhCUVdtcTIzU3hYeWcyNkRBRTVGSmtVZ0F5OGxabVBxNXZvK2RjWm5qZ2RHQmVKbFBCODJkenFlVEFtZ1JDZ1RQSGJndFBQcjMvWm5QbC9yUFR5OVZPNERGVlpneGFSdXRzWnZrNmEvaUxtTFprWUsyL0c5KzArYllPUmVJUmR6SVcyc1d1TDlvY29GajZJSk5Bcjl2d2FXTnFuU1lzb3cwM2M4dzBORyszeWMiLCJtYWMiOiJiYzgzYmZkYWZmYjY2N2Q3NzFiZTI2MThiYjlkZDBhZWRmMmZjMWE5MmY0MTY4ZGQ2MTYzMmFlMzhmZjM0NjEzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:56:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJGSlVXVEoyWEhGK0xiQ0xRSTFYbVE9PSIsInZhbHVlIjoiTDFGdFYzc3U4RGV5Y3pSK1JXMm1WMDk0NkpGcDhEWEdhUUdZWjlYV09nMDl0Q3NHaVdoUzN2b1B2VmFRalY5UWpLSHFCaDVVNlhhUTlEVitOT3RwYlFjZk53MlRzVGVxWDFKZHp1S3RPSlVpUVMyRk9BOUxhVC9kT1NyUnczMVZBbDVFNjZYMS9Fd2ltNExOMHVKanJTRDZXcENKbTJlMEtWenF1TnRtQ3R0bHRqRHUrbGI2RXlXWlYyS3dkSkp4QTRuWkpYcWdBcFdtamNTNHhSbDR5bWFwZnh3RUV2QTRaQ0o1Q0lKNVNQMk03UWFCZUsyMVN6dG5DLzA3THZHYXFYL1J2M2EybU9YWmZxYnFRT2ZyaityVGVtVllHVmRpbDFWOGdBSjFrTXptdE9zaG5Fbi9RWkVnSXNndDNYbGk1MG8rc3ppSEFPNVhURitPSVhFS3dZRnpxZFZTcHc1L1BaK2xzQk1YeW1uNEhGVjI3aEErUmNyOG9Jakl0UmJlSUtNWkRpdzFLaHc0QUJraGU5MVhNVTNKRGluUy9WNUFtNWRXdkpqbU5QNnhyTFlmK1dNRUt5TzJ1SStoejJEVlNqdTFsR1hnNkZ1QzdZaTB3OGlwSkY3N09IWXJBQ2MyL200NlgwZ1JGTkFZZUtuNHNjNVdxcFpDWUlMbFJubWQiLCJtYWMiOiIzYjQ5ZWRmY2YyOTRiZDVmMzY3MDhlZTdmYTQ5M2JlNDk2YTRmODJmMDlkZjkzYzhmMmEwYzg4YWI3YTkwNDQ4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:56:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Iis4cGs4R1dhNEhVZGhYQ0R1b2N0U3c9PSIsInZhbHVlIjoiVEgrL2pwWVN3YU52ZzJJQXk2Um02TElhY3Z2UTA5ZW5oUURYendGQWR2WW1sOFI4YS9adUllM29PZnU3VnNhU1RNRGVxTitTTElaNzRLd1loRE9oR0tsUjcyMUEwWGFKM1JnUCt5THJhT0V5QlNXVU10Z0JJVTZSQ0JyTmpFUkUxU1NLRG1TU1N1dDd0ZmdUTkZtQjN6VDBlOUVuS3JNT0RuamdEZmtYWVNmY3hWZ2NIUXU3Vm9HWEpSQmdyVyszbUtoNElqRGc5cE5TN3VWVU1NTDNadnU0M005VEQ1TG56aWVmUWFSV2hVUVUxKzFwTS83eGFOOHpNSDQxeE9DOWV4ODJqd0l4dElmUmpkd1hmZE5uTlh2dmk0Y2I1YTNQZzk4L2ZQWHY0TWNGNG1Oc25zNG5JK0JHOUdPeHdxM2VEVGNCZzhKRVhXbGFESjU3WWZZSXhCUVdtcTIzU3hYeWcyNkRBRTVGSmtVZ0F5OGxabVBxNXZvK2RjWm5qZ2RHQmVKbFBCODJkenFlVEFtZ1JDZ1RQSGJndFBQcjMvWm5QbC9yUFR5OVZPNERGVlpneGFSdXRzWnZrNmEvaUxtTFprWUsyL0c5KzArYllPUmVJUmR6SVcyc1d1TDlvY29GajZJSk5Bcjl2d2FXTnFuU1lzb3cwM2M4dzBORyszeWMiLCJtYWMiOiJiYzgzYmZkYWZmYjY2N2Q3NzFiZTI2MThiYjlkZDBhZWRmMmZjMWE5MmY0MTY4ZGQ2MTYzMmFlMzhmZjM0NjEzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:56:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-600348362\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2080758682 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2080758682\", {\"maxDepth\":0})</script>\n"}}