{"__meta": {"id": "X89a74a2ff245446566283658fa290f6e", "datetime": "2025-06-08 00:10:11", "utime": **********.882401, "method": "GET", "uri": "/pos/5/thermal/print", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 77, "messages": [{"message": "[00:10:11] LOG.warning: Implicit conversion from float 160.79999999999998 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 186", "message_html": null, "is_string": false, "label": "warning", "time": **********.836051, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 1.4 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.836715, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 3.5999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.836942, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 3.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.837099, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 7.199999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.837271, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 7.399999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.837422, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 13.2 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.837575, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 13.399999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.837803, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 16.799999999999997 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.837979, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 16.999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.838131, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 20.399999999999995 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.83829, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 21.799999999999994 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.838449, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 26.399999999999995 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.838604, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 28.999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.838754, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 31.199999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.838911, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 33.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.839069, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 37.4 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.839228, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 39.6 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.839382, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 39.800000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.839538, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 44.400000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.839736, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 47.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.839886, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 49.20000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.840039, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 50.60000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.840203, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 52.80000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.840362, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 54.20000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.840518, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 56.40000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.840674, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 59.000000000000014 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.840833, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 61.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.840995, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 61.40000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.841146, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 66.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.841302, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 66.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.841463, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 68.40000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.841618, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 71.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.841842, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 73.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.842006, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 77.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.842172, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 79.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.842327, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 80.60000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.842474, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 82.80000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.842632, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 84.20000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.842815, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 87.60000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.842981, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 89.00000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.843141, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 92.40000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.843331, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 93.80000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.843514, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 96.00000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.843747, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 97.40000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.843933, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 100.80000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.844125, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 102.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.844327, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 105.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.844541, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 105.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.844732, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 108.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.84494, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 111.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.84512, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 114.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.845318, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 116.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.845488, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 118.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.845671, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 120.20000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.845931, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 122.40000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.846164, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 125.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.846369, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 128.4000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.846541, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 128.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.846705, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 132.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.846876, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 133.4000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.847033, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 139.2000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.847189, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 139.4000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.847366, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 142.8000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.847522, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 143.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.847675, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 145.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.84786, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 146.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.848025, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 151.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.848186, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 153.80000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.848341, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 156.00000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.848497, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 156.20000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.848658, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 158.40000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.848815, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 159.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.848972, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 160.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.849127, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 159.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.849292, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 160.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.849454, "xdebug_link": null, "collector": "log"}, {"message": "[00:10:11] LOG.warning: Implicit conversion from float 159.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.849606, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.047281, "end": **********.882555, "duration": 0.8352739810943604, "duration_str": "835ms", "measures": [{"label": "Booting", "start": **********.047281, "relative_start": 0, "end": **********.672432, "relative_end": **********.672432, "duration": 0.6251509189605713, "duration_str": "625ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.672443, "relative_start": 0.62516188621521, "end": **********.882559, "relative_end": 4.0531158447265625e-06, "duration": 0.21011614799499512, "duration_str": "210ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52239272, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.thermal_print_clean", "param_count": null, "params": [], "start": **********.821212, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/pos/thermal_print_clean.blade.phppos.thermal_print_clean", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpos%2Fthermal_print_clean.blade.php&line=1", "ajax": false, "filename": "thermal_print_clean.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.thermal_print_clean"}]}, "route": {"uri": "GET pos/{id}/thermal/print", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@thermalPrint", "namespace": null, "prefix": "", "where": [], "as": "pos.thermal.print", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1667\" onclick=\"\">app/Http/Controllers/PosController.php:1667-1726</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.029619999999999997, "accumulated_duration_str": "29.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.723069, "duration": 0.023059999999999997, "duration_str": "23.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 77.853}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.764828, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 77.853, "width_percent": 4.558}, {"sql": "select * from `pos` where `pos`.`id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1669}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.77024, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1669", "source": "app/Http/Controllers/PosController.php:1669", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1669", "ajax": false, "filename": "PosController.php", "line": "1669"}, "connection": "ty", "start_percent": 82.411, "width_percent": 1.823}, {"sql": "select * from `customers` where `customers`.`id` in (6)", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1669}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.778022, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1669", "source": "app/Http/Controllers/PosController.php:1669", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1669", "ajax": false, "filename": "PosController.php", "line": "1669"}, "connection": "ty", "start_percent": 84.234, "width_percent": 4.051}, {"sql": "select * from `warehouses` where `warehouses`.`id` in (8)", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1669}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.782532, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1669", "source": "app/Http/Controllers/PosController.php:1669", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1669", "ajax": false, "filename": "PosController.php", "line": "1669"}, "connection": "ty", "start_percent": 88.285, "width_percent": 1.992}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1669}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.78651, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1669", "source": "app/Http/Controllers/PosController.php:1669", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1669", "ajax": false, "filename": "PosController.php", "line": "1669"}, "connection": "ty", "start_percent": 90.277, "width_percent": 2.633}, {"sql": "select * from `product_services` where `product_services`.`id` in (3, 5)", "type": "query", "params": [], "bindings": ["3", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1669}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7906, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1669", "source": "app/Http/Controllers/PosController.php:1669", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1669", "ajax": false, "filename": "PosController.php", "line": "1669"}, "connection": "ty", "start_percent": 92.91, "width_percent": 2.127}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1695}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.808422, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 95.037, "width_percent": 2.161}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Pos.php", "line": 121}, {"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Pos.php", "line": 144}, {"index": 21, "namespace": "view", "name": "pos.thermal_print_clean", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/pos/thermal_print_clean.blade.php", "line": 269}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.850886, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "ty", "start_percent": 97.198, "width_percent": 2.802}]}, "models": {"data": {"App\\Models\\PosProduct": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPosProduct.php&line=1", "ajax": false, "filename": "PosProduct.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/5/thermal/print\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos/5/thermal/print", "status_code": "<pre class=sf-dump id=sf-dump-1742197957 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1742197957\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-564478197 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-564478197\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1287338106 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1287338106\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkU4QmZDM2hzcTE5Tm91bFJRN2xCS1E9PSIsInZhbHVlIjoiOVhJZGlwR0F0VXZYaUFRbGJYMVBKdG5FSTlQZ2wxSE5rSXQ4dEFMeXVTamQ5azBiSFVOK2k0RTJ6NFpNSWlYREdhWW9CL1YxSmRZOWs1YVp3UWhibTZJNjdveERGZlV5bmZGS0VoaEkvV2gzZ1I3VUpESzhBY0xXMWl4U09leE9ZQ2xQbUFDYlFWL0FNeUNOcCs1NSs0ckRCaUY1d1hrb01sMlB2MkIxL25GQzNFRlh1eWxWWG5KY3RTTXVyMXg4VGg5cVNCN1NEcHdYYzR3Wm1BZUQxWXZWV1pSV25wdkRBVWJleWxnZnR0V3lla0UxSU9NWGhKb1A2VFlUMjNUdTlkMUNBVzRGdlUyK3drT2l3d0doY1dFN0tVOFFSakg5aUhEMm1KcXYycnF4RTJEV1hRZEZzTlhTbGkwUVkrNFJDTmJxa296TFpMTDRZdmNpSG5PQ3pqTjZWeGhzYXFHR0EvN2ozbVVlRzN1ZEg3dzFCR0ZaYnV6NHhCTWNiaDBLNjZUSytJNnp5UkowQTVVM2h4cmFEZngrR1pyNE5jUUoySjVYSlppc2MrbHdxT0R5bU44N2pGL2ZvTE1xWDVxdjV4UlYwN05kZUlJRU4rWDNuUTFycE5UQi9GbmVDUWZiWXJjVG1GdmNDZW85cFJRcWY0eExUTTN4NWJEWFViYTkiLCJtYWMiOiJjODIxMzcyMTY2NzYxYWYwZjdkM2NjZjQ2MjJiZmY5MDI2YjFlYzMyNTdjY2M4MzA3NWNlMDEyM2M4ZDQxMGE2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlRmMGdYN3VhWXFFMU9oRWJCektrT0E9PSIsInZhbHVlIjoiMzVNMkJib2R5VERpR2d0UHAwajRCUCt2cEd3UldwVnJQcmtudFB6aU1nNGVEdGcyMFo0R3NLYWh2bkwwNWE0VTNBZ2dIZHR5dGpQOWNOSU1sc05vckxLb3F3VThrT0xEQVNUcERYTEhOZ2E5WXhWZXlERWpkTkYrekZqZU1YRHB4aU0wbkNLajJEaW14UERIdllqOEc2RkphdG53QnFjSFZNWTBtbVFESCtoaGFYOWwxVmU3UWlBR3RYQlNVcWY5RlVoRjZaVTI0TFRGUUFNTW5EaERiRmRxY3BtN1pVV0tmckhMSkpJbExveWZJUm8wbHNqMVhWZU84REQva3ZhTllHcjlyQTNvbzBHWGxVbG9RM3pjMTFMZHI2ZmJXdHJvTG1iNnBVVG01OFN4YTZaSis2WjlJK1lzWXQ3RUFINXJSdXhXZURJOVpVUWk1dEsxYktSajJQK3BZWEZ5TnhSR1RuTmNoM2pDUGxQeElNRXpsamE0ZUg0Y2tKTU41SEpVWXhSV1NoUEhxT0piNnhzSWN5SDc0bE85Y2JVQ1lBVzlJRXRaY3ZXV21SS2d2L09keWdITzNVTGFrUEF2N2dTWXVGN2pCSUQ1SWZPTXoxZDMyeUUxL05DcCtvNjBaRFdnUU11UXVkODhnNXZZSnprZEdNTlAzTmFWS0xBdnRzbU8iLCJtYWMiOiI4NDQ3YjczMjRlMmMwY2UxOWQ5OTZiMDliODk5ZmNkMDI3N2E3ODkwNzUwODkwMmVmMzRiMTdmNzNmMWU3YmQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-896062527 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:10:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZjanBiQ29yWkhLemxqdVZCdmNYYmc9PSIsInZhbHVlIjoiKzNPcmNtd1Z2SVdKNVI3SHdBSFlFUTNneWdhU1U2ZUZ6MHQzQ1VhY01pWGdOdWY4R3ZVL2UyRVhLZzFLaFRFSnVwMmFDb0JsRjE2aDNBWjRqL054Z2lycUlyWVVnOWhhRzZwaXp3eDBRZms2VEQ3UnFEK2licUo2RkxYdXhWSGQ1b0lKR1k5akNLdGpTaXhCeGk2alFQcWNsRDZmck5aanZUMVJGUngvT0prV0lLOGF4V3NBQmMyNTVDdFI1T3h5TVRVL2VGT1UrQm5lT2RJZHBSVFhGVXRYUkFjTHZwTklKSmg0SWZ3bWxaUFRRMTNXRWRVRk8zNWMrS2tmUENsTHpFcy9zdkVFQmZ2ekp0eklQazF5Zm9HcHIxU0srVERrUzNNamR5b2NFbG1SU01UM2VkVjYwbjAzamgxY1M1Q2c4bjBhWGZDRk82SDhRN0o2Z2R1d2d3bXBxK3VIV3k2K0JXc1M0YUxTc3QwNmszMDQxWFpjVmVqYVhnOUU4NUhCdVkwbGlZSm9SWk1VM01NRUJGSzQwdGd2ZDZRNXRnUzB1NFhZMFpzUlRLRjFYTXN0NWtLaStZSUZ1NFNjN1NoMnlLQjU1dnVNdk1xeFNZMlhGSmVPN0NoMWNpdzczNnpDYkEwNmFRYWM4NTdTRm15OC9rNU4ycldsNXRQNDdpbEgiLCJtYWMiOiJmNWQzYTU1ZDYxNDBlODA5NGUyOWEwOTlkNTIwZDY1NTMzZWE3YjE1MTY1MjM5Y2ZjYTI4MGQ1NWRkOWFhNTc1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:10:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjRZMFNRbDlmMy85L3hPUmdOVjd2U1E9PSIsInZhbHVlIjoiSGhBSkxkdkZjNU1UV1J2Mi9nYkZobTlyVHFUL2NIbkczMHEvZk5pci9YRlA2M0gycGFOWFF6Vk1oc29lRjFtZnlRTkJldTZiOVdzMy9Zck9XQTVoUVhPVmc4WE1ZSnZaRzkyV0pHZUxkWlpPZHpqL1NpN2h0VEhWSzBDS29iOTZzeFZnQ2hoU2lnTDV2QnRVN3dIS0hqbDNTM2hkOUxlTm9RTkRhSkZXQUxOQ1VHRU5UekVBczVIR0YzYzRZM2ZjOGVhVUJ2dHVkVTMyMFJndy8xU1VPN0FlWjZJL0dNZ0VlczdwK3ROOHcram44UGdOUWZ0b2hoQzg5N0p5dm15RXVDTmc2QkJIam9xK3dxVlF3dTdQYllWL1FDWkFpK3dQdXJDWHhnYTUzZWFyc3JIMU4wWVNzL0l5b3U0TFg5bDhZajJGQ2w3VVlmallqQjFIY3NoY3BCVmNQdTZ1U3lCak11SHJFNnFQU3JlTkNVYnllbWN6clRxREdsS1UwdzdGZm9CekRzK1U1MnQyQkFhNStWZjFZR2dKUThicE92UHpzSHhDcDBrclY2cm1RVGdmV1hVclpoMG1ab1hsYlM3cHVCSkg4dnArcUxCVGI4TnprMy92a09MeEwxQjUwUUFhZTF6SktqVUdFNzhtaVcrd0RKNkwzU1UxWWNOdjFzV1EiLCJtYWMiOiI3Y2RiZDBhZWRmYTdhZDY3MzQyYjU1ZjViMDM2NzMwNTdlZDkzODFjNDQwMDZmNjhkZWRlNDE5YTNiNmY3YzFkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:10:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZjanBiQ29yWkhLemxqdVZCdmNYYmc9PSIsInZhbHVlIjoiKzNPcmNtd1Z2SVdKNVI3SHdBSFlFUTNneWdhU1U2ZUZ6MHQzQ1VhY01pWGdOdWY4R3ZVL2UyRVhLZzFLaFRFSnVwMmFDb0JsRjE2aDNBWjRqL054Z2lycUlyWVVnOWhhRzZwaXp3eDBRZms2VEQ3UnFEK2licUo2RkxYdXhWSGQ1b0lKR1k5akNLdGpTaXhCeGk2alFQcWNsRDZmck5aanZUMVJGUngvT0prV0lLOGF4V3NBQmMyNTVDdFI1T3h5TVRVL2VGT1UrQm5lT2RJZHBSVFhGVXRYUkFjTHZwTklKSmg0SWZ3bWxaUFRRMTNXRWRVRk8zNWMrS2tmUENsTHpFcy9zdkVFQmZ2ekp0eklQazF5Zm9HcHIxU0srVERrUzNNamR5b2NFbG1SU01UM2VkVjYwbjAzamgxY1M1Q2c4bjBhWGZDRk82SDhRN0o2Z2R1d2d3bXBxK3VIV3k2K0JXc1M0YUxTc3QwNmszMDQxWFpjVmVqYVhnOUU4NUhCdVkwbGlZSm9SWk1VM01NRUJGSzQwdGd2ZDZRNXRnUzB1NFhZMFpzUlRLRjFYTXN0NWtLaStZSUZ1NFNjN1NoMnlLQjU1dnVNdk1xeFNZMlhGSmVPN0NoMWNpdzczNnpDYkEwNmFRYWM4NTdTRm15OC9rNU4ycldsNXRQNDdpbEgiLCJtYWMiOiJmNWQzYTU1ZDYxNDBlODA5NGUyOWEwOTlkNTIwZDY1NTMzZWE3YjE1MTY1MjM5Y2ZjYTI4MGQ1NWRkOWFhNTc1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:10:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjRZMFNRbDlmMy85L3hPUmdOVjd2U1E9PSIsInZhbHVlIjoiSGhBSkxkdkZjNU1UV1J2Mi9nYkZobTlyVHFUL2NIbkczMHEvZk5pci9YRlA2M0gycGFOWFF6Vk1oc29lRjFtZnlRTkJldTZiOVdzMy9Zck9XQTVoUVhPVmc4WE1ZSnZaRzkyV0pHZUxkWlpPZHpqL1NpN2h0VEhWSzBDS29iOTZzeFZnQ2hoU2lnTDV2QnRVN3dIS0hqbDNTM2hkOUxlTm9RTkRhSkZXQUxOQ1VHRU5UekVBczVIR0YzYzRZM2ZjOGVhVUJ2dHVkVTMyMFJndy8xU1VPN0FlWjZJL0dNZ0VlczdwK3ROOHcram44UGdOUWZ0b2hoQzg5N0p5dm15RXVDTmc2QkJIam9xK3dxVlF3dTdQYllWL1FDWkFpK3dQdXJDWHhnYTUzZWFyc3JIMU4wWVNzL0l5b3U0TFg5bDhZajJGQ2w3VVlmallqQjFIY3NoY3BCVmNQdTZ1U3lCak11SHJFNnFQU3JlTkNVYnllbWN6clRxREdsS1UwdzdGZm9CekRzK1U1MnQyQkFhNStWZjFZR2dKUThicE92UHpzSHhDcDBrclY2cm1RVGdmV1hVclpoMG1ab1hsYlM3cHVCSkg4dnArcUxCVGI4TnprMy92a09MeEwxQjUwUUFhZTF6SktqVUdFNzhtaVcrd0RKNkwzU1UxWWNOdjFzV1EiLCJtYWMiOiI3Y2RiZDBhZWRmYTdhZDY3MzQyYjU1ZjViMDM2NzMwNTdlZDkzODFjNDQwMDZmNjhkZWRlNDE5YTNiNmY3YzFkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:10:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-896062527\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1190720552 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://localhost/pos/5/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1190720552\", {\"maxDepth\":0})</script>\n"}}