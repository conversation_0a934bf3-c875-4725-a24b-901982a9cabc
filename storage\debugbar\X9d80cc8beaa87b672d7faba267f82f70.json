{"__meta": {"id": "X9d80cc8beaa87b672d7faba267f82f70", "datetime": "2025-06-07 22:39:15", "utime": **********.699859, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749335954.904436, "end": **********.699884, "duration": 0.795447826385498, "duration_str": "795ms", "measures": [{"label": "Booting", "start": 1749335954.904436, "relative_start": 0, "end": **********.566397, "relative_end": **********.566397, "duration": 0.6619608402252197, "duration_str": "662ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.566418, "relative_start": 0.6619818210601807, "end": **********.699887, "relative_end": 3.0994415283203125e-06, "duration": 0.1334691047668457, "duration_str": "133ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48114888, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00721, "accumulated_duration_str": "7.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.625457, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 40.638}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.642126, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 40.638, "width_percent": 9.293}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.668384, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 49.931, "width_percent": 9.986}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.672167, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 59.917, "width_percent": 11.789}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.681163, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 71.706, "width_percent": 16.782}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.687015, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 88.488, "width_percent": 11.512}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1800643037 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1800643037\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.67928, "xdebug_link": null}]}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1015728234 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1015728234\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1081555509 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1081555509\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1026361144 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1026361144\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1652645779 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749335947329%7C7%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpMQWZVNUlFWjVUaHpmbDVFUnBuemc9PSIsInZhbHVlIjoiYXY0YmtGdFZMVVYycDNvQ09rZlQvTnFUQ1lHMGRTNFNxTVdZWnZtUDREem9IaWxZbGVlMGFRbDdUNFYvQ1UxYnpZTjlOcXcxS0k3YktUYUtnbmNWRGpzaFBFY2Z6OENESWtVK1hyMnJzaGVJbTZlZzhhRXV2elV4WDJuUEZzQjJwcm05Uzhua1JvQzhxYjNNTjhUb2xET21rMjh3dVJvdnpOK3dFL0hsSStwYmZDWlM2ZnA4WFg2dHlJVkhQWU1JRmRWTm01L1pzSURkUUZaQXNXMGZubW5HNFcvOUhRYjFOUVNyNGNGWnJ3d3hSUkVoK0FQTFZIaVlxUjlLZHd3cmt6MXNLMzZUZzkxcFpWWUd0QzVNOVl0b3huOTEwTWR2SDNEYWhhZEdXZHM3bU5zL3JqajZQLzJDT3dQUzY1OWFkeXJXcjVreWt0WkdHcHBVdTRiNjBEazJWdGgydzMwRU1UR3lHS1VxUFNnMit0cHBQZVRBWTVJMTdBQTJrSFhpQ2wranVqbWpTZVRNM2h0VjZUTlpKQlcrMVNlcDMrYXZBZnpiaHNVeVBxMzZ4ODRPNjZFK3dmZk5LRHBORERrRVBTMUd0Q1d3VHRvUkNZVHlTbExXMFJpWVJZNG9tYmd2ckJzZE5Td1h6Y3dlMENDMnRVaWhoNzg0SWdFOTFDZUoiLCJtYWMiOiIwN2RlNWNlZWUxN2RmZWZhNjhmOWRhNzM3MTZiNDcyNTkzYjMwMTNmYTc4NGE0YjY1OGU3NGJjNzJlNzlkOTU4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdYU2plUW96TEE2Zk5qcjFSL1dyRUE9PSIsInZhbHVlIjoiMktJS3hlQ2VNSExPZkI3SnZ0U3FULzhuckMxd016Y1FsNW5Yc0dKYjJFT3htSmhNU0Q2VU9nNjB0V0x0dkw3dDJ3K1FxcHFTMXRGV21jNUJJVEVNMUJwUSsxWFNHTE9Ybzd2dlM3ekgvczNLM29Pd29JN2hvYURXbUloN0NEQ0J6cENQcitwOTd4V0V5aTNORUJUaE84anFNUG5Ea0hpNlpkUDl4YlIyYUVuUGtCTUFVYzVpNWZ3MkhNZzhscVlSanNMQjNxaHVDZ2k2b1dDY1FpaGxwT0VJY0pLWndaRFAvL2dSektqR09ncWRlcHBWVVhEbmFCUjNKbnZmQjhMakNPSFVRYmU4UmVYYXRBRGMxSjlPdHV0TzFxd0tiUXk4V0JmZS9BVGpiVHhtcXJKY0ZrM2lxYmwwRS8ya05uVjlCdmpvNnFWRzV6VTBaeWthVzAyVGNLdE13YWxmOGl3dGRxUHlXUktUQ0REMnplODM4akZzRHZsMVhrOTM0K1FmeXN1cURoRmV0RTJ1ekk3ZzJHZ2FWM3h5aVJsWVk3OHVORmNNRWNCek1vWlZZcVFvRExITkVmQm1YYldIbGFLMklkeGNwTHZsMnJIVWM3YzVGeEZRTEdoRjJCMU1ySUorZ2tlMHV1bkdVRms0UnNQRlkzZlRKbjlxYWV0VXMrZmgiLCJtYWMiOiI3YTFiNGRhNGRkMjcyMTYzYmQ5YjU0NjkxNzcyNWM0MmFiMWJkNGEyOGU1ZGRjMzEzMzI2MjVlZGQwOGIzNWRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1652645779\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-312345830 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-312345830\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1458190894 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:39:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjMwdjZiU3FKcURhb0JORXBJQzVDWVE9PSIsInZhbHVlIjoibEJOaEtFMjRaSDdER1kxeitkUzVSdmdaZW9tWHVib0JQQ1dJWHIyQStmTUk5Rnl6ZmpsR1MvYk5jUnhWVVM5NGxUUFdEaGdyNEI3ZGlFKzdIUUd0RXRQQ09Od3V5cTMrZEdENnlaYTdDYUE4aXFxR3ZPWWNOYytSMVZ2SEt6SVczdk1CVGE4N25OTGVUQ0pxOW5YZlJGMWZFblBxVk5DVUZkNUJ3Ukx0NXNLNm5PenY0VXdYUm12cXc2dzVSOGd6b1B4dW83dEJMc3l0Y0xjQk9yNjNOWGY3NUZmcVlGVm53M3AwUzd3bTcwamtVRTlkcHpTWTRkMVdSSWtDNnpXbkMxNzUzR0laOEQ2UGVwbHU3VFVpNlg0L0xPS3VSRmNEQkpQZHphMlBDamc1c3VIZlZ3OTRlWXRKU1BRZEt0cWNubHhmWnpuMm5ZUlJWUGdPN3dBbFlQWmN2eGd2MWpNcVNZVGhnMHZPWlloYmRObmZrUkVPUzBBQmVDZWRBQU5WQWJwUDh5cGhOYW5IRWpRbzlTMXBMb210UnFxcGMvSG1wZjB5T1IwK2dEN3I0NFhXNjlsYzlsOHg5QWRnK2tZdy9EUHFVYVNCVWxZMmM5QjBRNE1OS083Rk84R3hjV01UMFJCRGdDOUN6OU5XekRZZktpeHNzSFphMnJsbVkyNW0iLCJtYWMiOiJkN2M4Mjc3Y2E3NzE3YzcwNWJlZTY5NzY0ZjFhMjA3MTllMDYzZmJhMjE1N2QyMjQ4Yjc3M2UxY2FjNTBiNTI0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:39:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im9GTm9rWVEwSFdaenhkS0pPbEdEZ2c9PSIsInZhbHVlIjoiaGp2TW9mNHErWVFzMlpoQjQraURTelJQcHB0dHdCN1ZVbkhKOFRiSjhtN1ZHMDZLazNtamFxRzZtM0FaZkI0NFlSSVIwZ1Z3TnQwenY3NnlXL0RPRXlQQVNqODZoZTlwWmZOb0g3dm1wREg5bjMrT0Z3N3lzTVJqUjg0Q3phazlLZTJkdzU4elE5YkxwY1RKWWkyZ0pIajdZT0loazQxZk1WcTgvcFZiRE9LWWJhcVZYR2JLay9rbDVpKzJ6bEJ5a0M2Q0NFSE1oMGg4cmVHckRmVWkrS0NZcWpTNTNCd2pFemZwSks1OEdndFUzM1lDTTVzKzBaM2JRdTgwQXhmcGNBYVdIVUp1VEw5c1NPUEhoSmduSlFkdzcrdWlieHl1dUo1NHcxUjJ6bDhuWEJwdGwrYk9EOEt1U3NtMzl0R2tkT1RpM3A2eEZTK3BNUWFIYW56MVlhZmpTbTB1QnE2NTM1MG9rTVRSRmlzU29DQ09rRHc0dFZTUzdYUmFucTRWdjZqRGFRNXV6Vlpmdm5UcTFvWjJ5b09GNWVOZ2JPUnlTUFVIeVhURXVWR1RGYnVkdjN2VUx3ODZzWEo1ZGgxdlpiQXlwM0NXY1ZGOHByOW9mS2NXcXprMlkyc0pJa3NaU1FYWk40OHFXc3NQNG1SQWo2THp3TGl4VGVRU05aRk0iLCJtYWMiOiJkYzJiZWNkYzcxYzllYWI3NjI1NmZlZWNjMzM0OGU5NjJlYWQxZjNiYTZmODJjMDA3ZmE2Mzg4NzgzOTVmMzQ5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:39:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjMwdjZiU3FKcURhb0JORXBJQzVDWVE9PSIsInZhbHVlIjoibEJOaEtFMjRaSDdER1kxeitkUzVSdmdaZW9tWHVib0JQQ1dJWHIyQStmTUk5Rnl6ZmpsR1MvYk5jUnhWVVM5NGxUUFdEaGdyNEI3ZGlFKzdIUUd0RXRQQ09Od3V5cTMrZEdENnlaYTdDYUE4aXFxR3ZPWWNOYytSMVZ2SEt6SVczdk1CVGE4N25OTGVUQ0pxOW5YZlJGMWZFblBxVk5DVUZkNUJ3Ukx0NXNLNm5PenY0VXdYUm12cXc2dzVSOGd6b1B4dW83dEJMc3l0Y0xjQk9yNjNOWGY3NUZmcVlGVm53M3AwUzd3bTcwamtVRTlkcHpTWTRkMVdSSWtDNnpXbkMxNzUzR0laOEQ2UGVwbHU3VFVpNlg0L0xPS3VSRmNEQkpQZHphMlBDamc1c3VIZlZ3OTRlWXRKU1BRZEt0cWNubHhmWnpuMm5ZUlJWUGdPN3dBbFlQWmN2eGd2MWpNcVNZVGhnMHZPWlloYmRObmZrUkVPUzBBQmVDZWRBQU5WQWJwUDh5cGhOYW5IRWpRbzlTMXBMb210UnFxcGMvSG1wZjB5T1IwK2dEN3I0NFhXNjlsYzlsOHg5QWRnK2tZdy9EUHFVYVNCVWxZMmM5QjBRNE1OS083Rk84R3hjV01UMFJCRGdDOUN6OU5XekRZZktpeHNzSFphMnJsbVkyNW0iLCJtYWMiOiJkN2M4Mjc3Y2E3NzE3YzcwNWJlZTY5NzY0ZjFhMjA3MTllMDYzZmJhMjE1N2QyMjQ4Yjc3M2UxY2FjNTBiNTI0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:39:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im9GTm9rWVEwSFdaenhkS0pPbEdEZ2c9PSIsInZhbHVlIjoiaGp2TW9mNHErWVFzMlpoQjQraURTelJQcHB0dHdCN1ZVbkhKOFRiSjhtN1ZHMDZLazNtamFxRzZtM0FaZkI0NFlSSVIwZ1Z3TnQwenY3NnlXL0RPRXlQQVNqODZoZTlwWmZOb0g3dm1wREg5bjMrT0Z3N3lzTVJqUjg0Q3phazlLZTJkdzU4elE5YkxwY1RKWWkyZ0pIajdZT0loazQxZk1WcTgvcFZiRE9LWWJhcVZYR2JLay9rbDVpKzJ6bEJ5a0M2Q0NFSE1oMGg4cmVHckRmVWkrS0NZcWpTNTNCd2pFemZwSks1OEdndFUzM1lDTTVzKzBaM2JRdTgwQXhmcGNBYVdIVUp1VEw5c1NPUEhoSmduSlFkdzcrdWlieHl1dUo1NHcxUjJ6bDhuWEJwdGwrYk9EOEt1U3NtMzl0R2tkT1RpM3A2eEZTK3BNUWFIYW56MVlhZmpTbTB1QnE2NTM1MG9rTVRSRmlzU29DQ09rRHc0dFZTUzdYUmFucTRWdjZqRGFRNXV6Vlpmdm5UcTFvWjJ5b09GNWVOZ2JPUnlTUFVIeVhURXVWR1RGYnVkdjN2VUx3ODZzWEo1ZGgxdlpiQXlwM0NXY1ZGOHByOW9mS2NXcXprMlkyc0pJa3NaU1FYWk40OHFXc3NQNG1SQWo2THp3TGl4VGVRU05aRk0iLCJtYWMiOiJkYzJiZWNkYzcxYzllYWI3NjI1NmZlZWNjMzM0OGU5NjJlYWQxZjNiYTZmODJjMDA3ZmE2Mzg4NzgzOTVmMzQ5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:39:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1458190894\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1250883654 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1250883654\", {\"maxDepth\":0})</script>\n"}}