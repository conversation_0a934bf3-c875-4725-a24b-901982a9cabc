{"__meta": {"id": "Xcc524b511d7ff311466d9fa316b248c4", "datetime": "2025-06-30 15:34:44", "utime": **********.531842, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.112921, "end": **********.531856, "duration": 0.41893506050109863, "duration_str": "419ms", "measures": [{"label": "Booting", "start": **********.112921, "relative_start": 0, "end": **********.47572, "relative_end": **********.47572, "duration": 0.36279892921447754, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.475731, "relative_start": 0.3628098964691162, "end": **********.531858, "relative_end": 1.9073486328125e-06, "duration": 0.056127071380615234, "duration_str": "56.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45569040, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027, "accumulated_duration_str": "2.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.506957, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.63}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.51722, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.63, "width_percent": 20.741}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.522785, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.37, "width_percent": 19.63}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-102997859 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-102997859\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1907532103 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1907532103\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1524238269 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1524238269\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-39118349 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297680604%7C5%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlnVE1YWGY2OXZoTWFOL2YyUGhXL3c9PSIsInZhbHVlIjoibjNNaTI0OGIveVNDWmU0eG9kdUxoWEMyMWladHpyS1YxeTZEeTZRZithVnJZOXJsWXhmWENLT3k0azAzK0cwSlk1TVNYeWdqVFV3ajNaeFR1cDhpRU1va2VSUitDN0tWcitRQ1ZnUFNBU2ZtYm9Jc2VleWx2ampZWTZqV1huWnNHLy9mWmJHMDNLdHB2Y0hnNkR2K3lYUW1FaWtjdFJPVGNmbmNsMjdBUkdUQWZQN09wa2FWYzUybk8xakRTTEpwQUFORXZIVWxLdXQ2UjNkdXVJa0M4U1R0TGx3RGUyRjcvbXlINEF0Z3FIb0N1THQ0MnIzUnpHVFBTTzQ1aEtSeG1KUEk0NWtQN010Q3BsUThiNXJEN1FMTmtNMFc4dDJNaU05VnJDUHRrbzRzVm9lYURnWjdFSlAvbzB6UVAwNFhvOXp2SDdoUkZuNnhxUDBYY2VBTVFYQW5NWUFCRS84Slc0b3d2b3I5NFB5eFE3YXVYbEV4MUpoZ0pOcGE5cE56SVNzdTRWb3UycDEzQ2I5enlNWi9adkN4TWgyUG83eHUwSlpaME1GaE5LWm5xNFUwZk9EMGkyMUJDTmpIcTF3L1RZWExrMlBEYktCNzF4K1AxS05QVFo3aThxTHdZNTlQc2EvU3RSWnFOM2lNbUNReUFQbTVoY3ZoTXplcFFTenkiLCJtYWMiOiJjMWQ3ZTY2MjVjMWE5MjU0M2NhYTY4ODFkMGQxYzYwMDU5OTVlZmM2Nzg0ODk1NWUxNjUyN2YyMjUyMmE4ZWYwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVZSzIxQlR1Szg2bUg0TlNOQ04rUXc9PSIsInZhbHVlIjoidVlLNXBBL3NtMEpKR204SWJUc3hwQktsTDd5d3N2R2R0YTVuMmhtNkR4dlIzYzhqRGVhMktVUHF5UVQ4NmpnQUVyLy9sdDJLWVdoTlV3eXZ6QVQvdnNTR3dEKzFMbjVmamhBRlFWaWlKU3Mvb0pnWkhOckE5RHNPVWwrQy8rRzFhN2NMWGlmaEFMendiRmZoUzFhNGNkbzdpNGl4QloyVlZoU1BwYlVsdHpkSnF4c3hQNVpNcDFvazhFcm80RFhmWmFZSUJNKzdHbXgwd2lwTk8raVJxcC9XWXNscHR3YUd4eTY2ZWNrWEpNZm1vdkJkZ1ZPUEpHbkJuNmo1UTR3YnhQcWVvRzRrY2xDSmdEQXhmN1RQV1NraVZucjRlNFBRQ3A4dVhyRUZNUngycktnb3plQVBNVEFIaTNOaUk0bUcwb0hJRVBZS2dWTEZNTXBDdEx2RXYrMGtnTVIvT3hFeUNaSFhnUThEUEdvdHN6VEp2ekFVSk9aNUNkMDhzcDVaNVdGVUtqbmh2eFc2Y2s4NzFaM2lkTHQ2T0RHa1d1SGtUK1dIMEpzMzUvVW9Yb1JtZjYzVnVDQ1Vqc2lNOStBdFR1TnpLWFlQYjJKZS96L3B3eW1PdXlENWlpTDNBYnU5SW9aaHZPSE9ZblV5WS9NSm9ESmxhRHZBVFh4UENmK24iLCJtYWMiOiJiY2UzM2M2NTRkYjczNjYxN2Y4ZDgwNWRhNTE0YzY3NTYxNDc2ZjRhMTI3ODJkNTc1ZjM5NzE4NTdmMzM2NGIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-39118349\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1969476244 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1969476244\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2082833633 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InMxM0l3VElSU3BVVXJjMzFWZHVaYXc9PSIsInZhbHVlIjoiZWxvN0tWdG4vV1dnakYrV3J2YnE5Tk5QMVlZM2wwQVJwV3dqcnZuYnQycTBCcGxjOTZiQkpJZ1RBZVUxRmIxdDJKUzJTZEdycExvN3hEZXFDV0l5QnkyTHl2RU5KTzlpNHVBM2VuYVhnQk1maU10K2xvMFdwQ1R6MkFJN1djbm5MaVRwTEYxTTJsVnlhVU15SXlvWW9JczlqcDBQWkRlR3ZXTkhxTVdtQUIreHpoOEVuakkwZDlaZHJwTFdnOGltSjlmZGpPSzYwTzNUSHRabFR4aE0rOUN1djdxUzFMbndhdzJzbUthWlB2Q3p1MDdJVVloK0dpbzltWGNXUjlVenZ2M3RhZVpBdlNhUkpyR2QwMUh6R0M1NEZiczJXS1hyWFZGRFk1RWFHS3FiWFQ2QmxDYW0vT0dvSm5ZMy9maTVoM2w4cDhHVThuTlJhYm9pMnQ1U0FhTys3a1hzVGJhT0xpdWhSSGdkTVFCZnBTeXd0dGx1QkxWa2FZS2dhOGtYWGY2cjMrQU41NTBieDRwNi9CeGZZazZ0K0h4QnE4NkozTzdVTjQ0blUzRno0anNHZXdyYTRjcmRpTVZyU3gvb2NMNUoyc2VFcm54WEFCbVNseUN2dEwrS0JEQzd5eEJSQW82WnN2cEw4UTFTSFhUZ3V1NmxPRTA4c0FDWXZlL1MiLCJtYWMiOiIxNzZiODNhOWY5ZWRhZDIwZTBmOTI5NDBiNWM1NDViMzg4ZDhjMjdiNmU0M2MyZDg3NWMyNTA0MzBlY2RjZmUzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkhHNjQyQWozZVpRanJDQTE1VnoyZlE9PSIsInZhbHVlIjoiZGVuVHBCL01HdUNvalFuRjBLWkhNMUllNU10dE1IOFdyaWEwRFByMWRubko4d1BoOEc2OE83NjZSRno2WHdSc3IwWUgvcjY0U1AyOHp1QzUwc3hlYllUZExQU01PTTlZVkQzVVNMUS82TWFENkdUL2pKM2U4ZlhLbWt6SVIwOGk2eG1DcFNPV3p5Z1J6LzFkU3dUaTlCa1k4TDFFM1FvZGwrV2c3VU9sMzlDaXlrdWZQSzduV0JITkZWMWZzVTllSmIrdk0vQXNZWUF3aXNnTFdUdDJrYjEreittK1BYaFBxWTczV1oybThRb3Y3UHRHdmN0Vk5RVWVyVUNKYUxrRUlPOUNCVlYyak52YzBZelM3WmV3ek5BMDJrUkdFdGthZklRQXl5d3U0cFdtUFMxQW81aHlmdVp5c21ITjFkU0ZCbWtYK0VYSHFjaHB6V0R0RVBwNFR3OGdnb1VGMzR5ZGJIR0Jnb1pkWkpwOUpud2N1Z004dnZmeFl5SWRuRWtTT1NTSmk3NGVBc0FCR3FoemNyZUdZZGV1NmhwY3MzRDlPOTd3S0o4eE0wSXdjcTV3bHhXMHUrMG92MmhlY29ta1VoNlJNaUM0Y3pwWHRTSkF0bUd5Zzk4d1F2Zm9TOWk1TW1zdFY3cU1ENzRndDJPY1M2UkFSNmdwTTBBdDgxWXciLCJtYWMiOiJlMjU3NmNkMzYzYjEwZGI5ZjY3YmJmYjBiZDI0NGY2M2MzYzBlOTVlNzVmYThjNmRmYTg3OGJlNWVjMmI0OGI1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InMxM0l3VElSU3BVVXJjMzFWZHVaYXc9PSIsInZhbHVlIjoiZWxvN0tWdG4vV1dnakYrV3J2YnE5Tk5QMVlZM2wwQVJwV3dqcnZuYnQycTBCcGxjOTZiQkpJZ1RBZVUxRmIxdDJKUzJTZEdycExvN3hEZXFDV0l5QnkyTHl2RU5KTzlpNHVBM2VuYVhnQk1maU10K2xvMFdwQ1R6MkFJN1djbm5MaVRwTEYxTTJsVnlhVU15SXlvWW9JczlqcDBQWkRlR3ZXTkhxTVdtQUIreHpoOEVuakkwZDlaZHJwTFdnOGltSjlmZGpPSzYwTzNUSHRabFR4aE0rOUN1djdxUzFMbndhdzJzbUthWlB2Q3p1MDdJVVloK0dpbzltWGNXUjlVenZ2M3RhZVpBdlNhUkpyR2QwMUh6R0M1NEZiczJXS1hyWFZGRFk1RWFHS3FiWFQ2QmxDYW0vT0dvSm5ZMy9maTVoM2w4cDhHVThuTlJhYm9pMnQ1U0FhTys3a1hzVGJhT0xpdWhSSGdkTVFCZnBTeXd0dGx1QkxWa2FZS2dhOGtYWGY2cjMrQU41NTBieDRwNi9CeGZZazZ0K0h4QnE4NkozTzdVTjQ0blUzRno0anNHZXdyYTRjcmRpTVZyU3gvb2NMNUoyc2VFcm54WEFCbVNseUN2dEwrS0JEQzd5eEJSQW82WnN2cEw4UTFTSFhUZ3V1NmxPRTA4c0FDWXZlL1MiLCJtYWMiOiIxNzZiODNhOWY5ZWRhZDIwZTBmOTI5NDBiNWM1NDViMzg4ZDhjMjdiNmU0M2MyZDg3NWMyNTA0MzBlY2RjZmUzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkhHNjQyQWozZVpRanJDQTE1VnoyZlE9PSIsInZhbHVlIjoiZGVuVHBCL01HdUNvalFuRjBLWkhNMUllNU10dE1IOFdyaWEwRFByMWRubko4d1BoOEc2OE83NjZSRno2WHdSc3IwWUgvcjY0U1AyOHp1QzUwc3hlYllUZExQU01PTTlZVkQzVVNMUS82TWFENkdUL2pKM2U4ZlhLbWt6SVIwOGk2eG1DcFNPV3p5Z1J6LzFkU3dUaTlCa1k4TDFFM1FvZGwrV2c3VU9sMzlDaXlrdWZQSzduV0JITkZWMWZzVTllSmIrdk0vQXNZWUF3aXNnTFdUdDJrYjEreittK1BYaFBxWTczV1oybThRb3Y3UHRHdmN0Vk5RVWVyVUNKYUxrRUlPOUNCVlYyak52YzBZelM3WmV3ek5BMDJrUkdFdGthZklRQXl5d3U0cFdtUFMxQW81aHlmdVp5c21ITjFkU0ZCbWtYK0VYSHFjaHB6V0R0RVBwNFR3OGdnb1VGMzR5ZGJIR0Jnb1pkWkpwOUpud2N1Z004dnZmeFl5SWRuRWtTT1NTSmk3NGVBc0FCR3FoemNyZUdZZGV1NmhwY3MzRDlPOTd3S0o4eE0wSXdjcTV3bHhXMHUrMG92MmhlY29ta1VoNlJNaUM0Y3pwWHRTSkF0bUd5Zzk4d1F2Zm9TOWk1TW1zdFY3cU1ENzRndDJPY1M2UkFSNmdwTTBBdDgxWXciLCJtYWMiOiJlMjU3NmNkMzYzYjEwZGI5ZjY3YmJmYjBiZDI0NGY2M2MzYzBlOTVlNzVmYThjNmRmYTg3OGJlNWVjMmI0OGI1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2082833633\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-529001629 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-529001629\", {\"maxDepth\":0})</script>\n"}}