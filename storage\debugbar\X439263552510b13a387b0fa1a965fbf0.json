{"__meta": {"id": "X439263552510b13a387b0fa1a965fbf0", "datetime": "2025-06-30 15:21:24", "utime": **********.621421, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.135407, "end": **********.621437, "duration": 0.48603010177612305, "duration_str": "486ms", "measures": [{"label": "Booting", "start": **********.135407, "relative_start": 0, "end": **********.541885, "relative_end": **********.541885, "duration": 0.4064779281616211, "duration_str": "406ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.541894, "relative_start": 0.40648698806762695, "end": **********.621439, "relative_end": 1.9073486328125e-06, "duration": 0.0795450210571289, "duration_str": "79.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028384, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02446, "accumulated_duration_str": "24.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.573539, "duration": 0.0235, "duration_str": "23.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.075}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.606132, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.075, "width_percent": 1.472}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.612243, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.547, "width_percent": 2.453}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2094294375 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2094294375\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1184298805 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1184298805\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2121871989 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121871989\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-923191792 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=2uy8by%7C1751296699440%7C5%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkUrN2xGcGlBU2czdGloZjlNYkNzbkE9PSIsInZhbHVlIjoiakxrS1IvRWdYNURnZnJEM05xNXRhT2Mwb0lJNzgvNU5TbjBCeFFyVDV1S2wvMlFId0gvdkNQK1ZhYzNFdjFCbUZuMlIxR1lpOW91eHRKeGduZjVkTTFWL0VYYWt3aFpMTUlnaFErQ1pocEFLWFN4K25LNDRVWFFwaXpaNDQrY3JiSGRNRDkvcWlOVUdiM3hvY0JBSFB0SmhURXdKZExIZHNtQjZSaXNLQTJickRiVU8xRlc2Vk5xL3dGRDJnYlVCL1JPZnZUaVR5VFVEUm5wRWhRTXk5aHVTRGVzVXc1eFdncFZVdUt5RmNnQVljZEdjMjAvZEhuTTVEaFNSNmhGRFhSUXJFeE44K1I0ZDJQTjFmOVNDb0lxeE1raWl6SHh6dFZIcDQ4Z1UzcHZpbFNZTHF5bEo4L3kxWGE0aTV5NUdmNHZJK0JrM2E0MnBnaENjS0s4R0FDcXZRYnBadHVjQkpZRTlNZTlha2VEYlBPaTRGdkw5WGJBaURxVlQ3MDIwTWZJb2ZIZHlvcDVvSDFxRjdGNVNJaExhYXRUT2w4eUp6VzlLcmpSaXIxVDA3WFlFQnhsbDNlaytRWU4wMG1QelAxa3lVUGJwTjU2NXY1SU9naktwMnVhSjBGR3FwSkdMMnFwUDlYaEJBMjFYVDJ6aU5HbDJMY3AxNXJkQWUxbCsiLCJtYWMiOiIxMGE4Njc2Y2RmZTM5NmI4MGNiNjE1YzUyMGRiOGRlZmQyNGY1MjVlMzk1NDdlYzlkZWFiMzE4YzAzZTBjMGI4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik82cVcyZStCeWt4dEttUW5qanZNQ3c9PSIsInZhbHVlIjoiYW9YN3Y5Q3NBNFVZRHJ5aHRMbzRSQmRtSW1qZWJ3Vld3cG0xMmpOTUtFUldnT1JZRXBjVnp3Q28zdElJMVVvWWVrMEh4YVlvNFo3b05jMDVXeFAyS2pRZ0I2NnJQbzlKcDlQMXpUUEIvQkloVWJtc3BHbmIrSmtJaXg0djZpUDJGR2lTeEl5b1lrR1BRbEoraDVGNnIxM3VCcDlWZ1ZGNWxPVTJLZzNqeHNFYXlIeWR3emZtbFczbXl5UGdTYllkUDlLWmNrMTE5RzhKdXJBY1lFTm9iYjhXWlVHQXo5QWZJSGRvcFhxYlNNMzBvdytGVnNCWVhIdGZkcEdPUksxaUM3elBMMHcrQWk3RUFuTUtxTFlEcDNTQnFyN1p0ZXFLcUNZUmhvaDA5S0R0bDI1ZngrZGRkU21WS1ZvZ1ZkRXc4VHBIazdoZXI4aEZsNm9vOTdrcWxFSHF5Yml5VkQ4NEw2M3gwbmk1ZWNCSmJlOUIydHpLUEd2U2c3blh2bmNYMmxoOUh2dXJuS1VSc2Y2MGJZR2ovZEs4amtNZTVpU0VKOWZHbzZ6UG1xQk1FTEJ0dlZVck0wRUc1WnFrZ3pXWURhWVg1dThSdEswR2dlRHhCWjRnbnF2d1R2QXdYM2VOSDNqMWJ0ZTBNN1MydGNBTlRQTnp2RUVwQWxpTE5EZDYiLCJtYWMiOiJhMjA2YWRmM2FjMjA1M2U4N2QxYjRhMmJmNDlhNGYzNTRjZGUyNmQyYzE1MDgzYTVkOTVkNzg1ZmM0ZTgyYzM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-923191792\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-821655679 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h9WVO03qniSiJmg5fuu4tum1lr0j0c1k18JkoyAo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-821655679\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-715490747 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:21:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlF6NTc4eERTQkdLc2xlMTRXc3dqeVE9PSIsInZhbHVlIjoiZjlzNFpmUDBOYjg1M1dvc29VZkNCT1NldGNRRVFvSm9DU01wajUzQkZFWWp1VTZHb3JGTDZTblovUnVvTjFlKzhTQXZMRjFkLzJzZ3hPOGZ2cFRXY2hRN09USXpOdHU5VXA4NWNmK3lEbHZOUEh5OWh3ZVVkY084ZnpSM0JYSmNBK3RmQ0xGT29jV2RpSjZtcFF3Njl6bTNkMnRqVDFNaUt4YW03Z2taU0VXdzdXZEtVS2RTOENpcmJxZDdvK0lEbjQ3bXZNZGVCeDJJTHVNb25KRlM4SXcxYkVNYTZLMEtJZWxmZ2FyN05tRVdCc3gxaE1XMHc5b0ZzUk13UG5oSmV2WTBFQjFoT29XaXpPK2ZoblllazFFc0tvdC9JRE8vTGVINEJ0Q0EzUWgxeFh2L1RFYjRkV1o0T0w4dU0wSzYxTE8vclNCYWlxNmVhTHdSUlZSTU81RStKQnR3cGQ4a0ZkOS9lMjByVmtjOHcvVEpPUnZQMUxrOWhiQXRmZXVIU0MyZTFmc3o0WGc1TUJ3Q1BwVUJld2l0T0ZMb2MxUDQ3KzloWkpIUWdFTGFkNUM5ZFRpMmVKWWhObVZWbmliQWlaY213WFBUVkNiWURiYWNJMDdvU1IyNTBUWWI1TnVNRmJhTG9SRHozTzMxaVRoNjE0MlV3RzFERzBWd0ZLU2ciLCJtYWMiOiIyYTBlODRhOGY5ZjIzMjZhM2VlYjBiYWVmODcxZmQyZGFjODVmMzU4YmEwMjczMjUwMzQ4MTE0NjRkMmQ3ZjVlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:21:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IklnT1ovZGxlTFRzL01rbkJPbCtJaGc9PSIsInZhbHVlIjoiTUZqV2llVkNPWDUvdU9qV0ZMTGpBYmVmeFhMcUhKd3JOaHJTUVU0ZXZvTGZRRjFUQzBaSk5JVnR6amtpMXo2Y002Sm1iTFJueVF1OGlXUTZUQ3ZMTHgzS3ZNS3JhYzZaQjlHNkFyYVh5TXh2dlVINDRyOVZIUlQ3YmN6K3FicDJkWGdQZmFmWFNvSFM5aS9oMER4d3ZiQWE5MXVNendvZ2lPQldDL3c2QkN0TWZPVHRPWjErS2JOelYwdWVWb3h3d2FUSmFFWWZqWFJoQ3gvazc5eERBaC9JQlBYbEpMWXBnRVJwR3dLd1JaUkxpOGU5aExvV005L1U5KzA3R2syKzJuS0k5Qk9aL3pPcHptWmFSVUdVZGVlN0pnb2xmU0MwSHJPK01NeUlteDJXQzhqUUVicXBsRElPVjdMdFA3QTZlZXhITmlPRHFmZHJBZjkxQnRXVjJzeEtFRVdLWnh4V281Rm5DNUJsZVdaanNRUHhoNEZhUWxGVVMybFgwT3Y2dmJ1L3JtRmFjYjM4REZ0ZERoVC9FQ2N2RUZJeDVveExkNENiWXpFcTZlTFdXcElDcXRkRmhqVy9TWHlMOEdWMHlCbTlyeHJGVmFyM1MxQ0hYbHNRQ1lBeFpDQmxvSFc3K0VMbnBBMUt3V0xLUk5uU25GeHBmVXlDbFJtUElyWW0iLCJtYWMiOiIwMmY5OWUyNjVlNjE3MjExYzk0YmYxZTZiOGQ5ZTdjOGJkYTA0NDQ0OGRlMmVkZjY4YTZkNGE0Y2E0YjA2OGNkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:21:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlF6NTc4eERTQkdLc2xlMTRXc3dqeVE9PSIsInZhbHVlIjoiZjlzNFpmUDBOYjg1M1dvc29VZkNCT1NldGNRRVFvSm9DU01wajUzQkZFWWp1VTZHb3JGTDZTblovUnVvTjFlKzhTQXZMRjFkLzJzZ3hPOGZ2cFRXY2hRN09USXpOdHU5VXA4NWNmK3lEbHZOUEh5OWh3ZVVkY084ZnpSM0JYSmNBK3RmQ0xGT29jV2RpSjZtcFF3Njl6bTNkMnRqVDFNaUt4YW03Z2taU0VXdzdXZEtVS2RTOENpcmJxZDdvK0lEbjQ3bXZNZGVCeDJJTHVNb25KRlM4SXcxYkVNYTZLMEtJZWxmZ2FyN05tRVdCc3gxaE1XMHc5b0ZzUk13UG5oSmV2WTBFQjFoT29XaXpPK2ZoblllazFFc0tvdC9JRE8vTGVINEJ0Q0EzUWgxeFh2L1RFYjRkV1o0T0w4dU0wSzYxTE8vclNCYWlxNmVhTHdSUlZSTU81RStKQnR3cGQ4a0ZkOS9lMjByVmtjOHcvVEpPUnZQMUxrOWhiQXRmZXVIU0MyZTFmc3o0WGc1TUJ3Q1BwVUJld2l0T0ZMb2MxUDQ3KzloWkpIUWdFTGFkNUM5ZFRpMmVKWWhObVZWbmliQWlaY213WFBUVkNiWURiYWNJMDdvU1IyNTBUWWI1TnVNRmJhTG9SRHozTzMxaVRoNjE0MlV3RzFERzBWd0ZLU2ciLCJtYWMiOiIyYTBlODRhOGY5ZjIzMjZhM2VlYjBiYWVmODcxZmQyZGFjODVmMzU4YmEwMjczMjUwMzQ4MTE0NjRkMmQ3ZjVlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:21:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IklnT1ovZGxlTFRzL01rbkJPbCtJaGc9PSIsInZhbHVlIjoiTUZqV2llVkNPWDUvdU9qV0ZMTGpBYmVmeFhMcUhKd3JOaHJTUVU0ZXZvTGZRRjFUQzBaSk5JVnR6amtpMXo2Y002Sm1iTFJueVF1OGlXUTZUQ3ZMTHgzS3ZNS3JhYzZaQjlHNkFyYVh5TXh2dlVINDRyOVZIUlQ3YmN6K3FicDJkWGdQZmFmWFNvSFM5aS9oMER4d3ZiQWE5MXVNendvZ2lPQldDL3c2QkN0TWZPVHRPWjErS2JOelYwdWVWb3h3d2FUSmFFWWZqWFJoQ3gvazc5eERBaC9JQlBYbEpMWXBnRVJwR3dLd1JaUkxpOGU5aExvV005L1U5KzA3R2syKzJuS0k5Qk9aL3pPcHptWmFSVUdVZGVlN0pnb2xmU0MwSHJPK01NeUlteDJXQzhqUUVicXBsRElPVjdMdFA3QTZlZXhITmlPRHFmZHJBZjkxQnRXVjJzeEtFRVdLWnh4V281Rm5DNUJsZVdaanNRUHhoNEZhUWxGVVMybFgwT3Y2dmJ1L3JtRmFjYjM4REZ0ZERoVC9FQ2N2RUZJeDVveExkNENiWXpFcTZlTFdXcElDcXRkRmhqVy9TWHlMOEdWMHlCbTlyeHJGVmFyM1MxQ0hYbHNRQ1lBeFpDQmxvSFc3K0VMbnBBMUt3V0xLUk5uU25GeHBmVXlDbFJtUElyWW0iLCJtYWMiOiIwMmY5OWUyNjVlNjE3MjExYzk0YmYxZTZiOGQ5ZTdjOGJkYTA0NDQ0OGRlMmVkZjY4YTZkNGE0Y2E0YjA2OGNkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:21:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-715490747\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1196762331 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196762331\", {\"maxDepth\":0})</script>\n"}}