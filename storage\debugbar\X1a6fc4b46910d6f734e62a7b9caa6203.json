{"__meta": {"id": "X1a6fc4b46910d6f734e62a7b9caa6203", "datetime": "2025-06-30 15:44:07", "utime": **********.416038, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751298246.929004, "end": **********.416056, "duration": 0.48705196380615234, "duration_str": "487ms", "measures": [{"label": "Booting", "start": 1751298246.929004, "relative_start": 0, "end": **********.353033, "relative_end": **********.353033, "duration": 0.4240291118621826, "duration_str": "424ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.353043, "relative_start": 0.4240391254425049, "end": **********.416058, "relative_end": 2.1457672119140625e-06, "duration": 0.06301498413085938, "duration_str": "63.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45556512, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029499999999999995, "accumulated_duration_str": "2.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.385417, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 56.949}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.398051, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 56.949, "width_percent": 16.271}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.404945, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 73.22, "width_percent": 26.78}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-7446033 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-7446033\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-969472786 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297684545%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjIxeHhUd1FGL3ROelJjWDBYcTFNNnc9PSIsInZhbHVlIjoiRGJodGJ0cHNiMEpGQXZ0UlQyeEVtZ1FZeFZPVHh6SUFUVS9rcGVXcmZpdDZWLzB5eHVmM25qckt3aGRIM3d1RlpHWVhSNmtDVjBybEs0MTNMbjdWNCsya2tBVWZ5VWw0U1hBa0FqRGlkWmRWOFVPTXQ0ejB3Z20yS3dieXoxT056QnZROU5YeFA2REZDb1VkWWoyWVBwY1FXd0hhSkU2ZGJZNUliTVU5QjRIUmxjVXhncjRDWmdDT1lWZGVXNVkyemhhRmdmUnN4MzgrSkZKU2RiRm40NDZ2Y3RvY2FmV1orWjk3OFEvQjg1NTRSanBNSDF6ZDMzVVJ3MDFHZk1sR05lcWUzbElJeThxenR6U1Z5ZWltMFhzNFFuWXU0blNVaVpoSEdCUmY3UWZNVlJoYmM5MUJ6RzJXZ3BYMmhJVGJRSHRYTndVR3dkV0dMaU85MVZ1MkJqYmM2Y0pMTGlYUTZwRndRUnRJNk4rRVYzMmZSSWk0UUJGcEZBUU85ekNVZ3B5VS93eFVOZWIxQTVOaXpFVlE5SEQ4dlZWNWFaMHJIZXR2OGd4ZDdoUGs2YTQvTGIxQnhNMWVMV2Y2K1F5eFRSVjdJcmdpU2tVM2lleFRmQkZsbmdiRnhqOEt4SDB2S2MrUE5PQVZMZjJaQlFCMC91ZXJvTURkNDg4bTBhUUgiLCJtYWMiOiI2OTQzNmVmZjg4Mzc3ZjQ1OTlhOGY0OGIzMjA3MzAxZmIwYjM3NTE4NTdhYTIwZjJmZmViYmY0ZTdhODYwOTI1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlY1T1A4KzVva3NNSDJqWDEzbDVFOFE9PSIsInZhbHVlIjoiUmtHWExoWDBkc2NvUTVqUFoyUHdiekhHOGkwS2s1dng1UGxBYXFZM3lEbWp1cVBrZlR4Y1Y3U0lnYWxDYVE1UmxUWmRIVU8rcTFwZGhkRHIyWmdTdnlZZ3NsMUl3VjhHZW9FTlNkN3p1ZmthclVHaVk1ZTJmczA1Rm93WnV6ZWF2MDlwU1pMZHE2N0pJYStDcGhmUHdTVU45dnd6RjJLWW5KSlZveU5nbkxKSGM0QzBjSWx0ZXFZVGhQMGdIMGY1ZUtoaG84djVnYjhiQmVMcWw2NjhoNy9nTVVZVXF6bURKZUhmQ00zUE5DMklGVERiVi9hY2NpR2dnMDlsYjFGWWJEVFpHSUNoMXVlVzJuL2pDQzNWKzBPeVY2UHBPYStnVFBTcVhOWmkwd1dkVjBrOGtXajdjY1Yrd3J1VXVDdzBod3lTNEFnTUVlRHdmUDdMRWtVSTJ4MzQzZDJSUmU2enY5L05Ta05ScElHNnIvT0VOWFhDdXFFKzRrTzJiOEVmdStTS3pxUVJiUFpab1hvd1ZaVTlJZmpaZTNteENvdjJ2WFM0RjhxdTFucmszdno5c1RTSWdZUUZmRmxqbzEydmd6RVBqTUxOMnlVbWk3ck4yMGJzbmtCOHN2aitUbG56T1M0eUdWMHVhejZ2bm8xbVhqYlVGZm5oUkFOZjNobmwiLCJtYWMiOiI5ZDBkMTQ5YjAyNWVkYjQwMmRlYTcxYTgyNzQzYWYxZjRhOWQ3ZmVmMTZhYmUwMTA4NTMxM2YyNGU3YTMwZTM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-969472786\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-864551145 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-864551145\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:44:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNrTUtFclpjeXo2dm5vWXNLbDBNb0E9PSIsInZhbHVlIjoiKzhwSHU4Wm5FR3J3Ull3SkJzbEpqVzdZYS9ET0dCUkNYRkpia0tLV2VhQlF2RExGYm5ieDB3aUxFbng3cnVTZUNDQ1lGVHFuZEwybHdLK2prRmZEOE1LK3lBTFFxTmhJMyt3RE14RmFyOXNoMUJwL1hRMEVNOGIyMDA1QXhDSkRoYXQ3cGkxOEtRSlZtRDU4WFMrL2E2cVl5Sk5OMzFyVW1aMnRrcStKU2tsYXlkMFNUazU3WTNrVE1BdjAwM3poVy9qR3FSM1pMUXk5NkdZR3Fhdkh5VmlVMzQ5b3QwWis4bW1zdzNXRFg4QmNhUlhoaFZiUk40MjdlV1FVMS9mcTlpYzIvOStYb2tUQ3YxY2RlTWVET3Uwc21iay9QcWJwYXBrZCttczFmMWJvWmJPZlMyL3J0dXQ4MFBaZ2VNdlRIeFVRZnZzWTczc1VieUVyQThOVDZ0NzhFcmxIVmlKZ1BKYU9OODNDbGFZc2xJcnBlWUhyUmovWTdaVitneC95b0hjQWpFU3I0STZHbnFxRTA2bkQveUNmUkNGUXJBQ1hjL0ZEdFdkN3JQN3BUdWFxWFlSbDQ3emJrbzlPZkRwd0NFRE1haHlBOGRiL3huYTZjT2VLUkVaQlh2WFdqUDB5WkptMGZMMUxWMFA3OWVoSGo0aFF3N0EwSGtINUk2VFUiLCJtYWMiOiIzOWRhMTc4MmM1NDM3NjZlNDJmZmE4N2ZhOGYyNjg0NWEwZjAzYzQzMDBjYWUxNTQyZGRmNTFhNGY0ZmYxYTMxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkZnUzI5U1RRaWxKVmNvUnpiNGhEQWc9PSIsInZhbHVlIjoiV25kTnhyUThva1U0NGxLdmo5Z1VBdlFpdmRCOGdLblB0MGxvVkRKb1A3T0xaUUpXNG4wTzdTM1M1SEdGMnNOMjBHdkxkaEVaNWRYTTZBOUpQb2Z3d1AwZW9QVkx3Ky8rM1Bab3lQL1diTy9oRURvd1lEejVJaWtESDh6UTdwdE1oTUI3cGFDMDlnRXpBcGtvVytwZVRsb3VHMVBwb2hOamMyV3lDTVgzK0VlZ0JVTFk2RUM4NjRod2haRXdzZlRFeG5yVkFodVNLckxVK0l0RFlrNlk0SSs1cTYrVDVXbVJha2pEOFlwcnJIaGJ4SkZiWE44VFRxQTY1WERrZGM4Umord0NNU2p2dWFVd1BNVnlaR3NNQUMzWUw0ellLWDRaSThQZG1FZ2t5UzA3ZFdHVmlleG1qaEljWGlIR2FhdkRxWENWb3NCeUs2MllvdCtVSC9memRkQlNPcTVwQXBRdHd0Q2VTclVad2xNb05xa0JqY0g1YVR4MTVHZzkxVXYwZzNheXo5VHRyMmdIZDU5dURHa0l1UHhuNmN0bUI5cGRnOVg1UW8ySjBSdXArMEZxY3E0VlNWR0NUMnFTSm94citjS21tMVhTc2R0cXIyTDc0YkY4bG13cG1YTzVDUHZpMmRXTnIzUkFxVlVqc1BEaGRtRHhYbnJ1aWJ3V2lLMGUiLCJtYWMiOiI5MDM1OTY2M2EyYWZlOTFhZDI4ZWFiNmI4ZGI3NjM4YzMwMzAxZDVhMjlmYzFlODlhOTI3ZDg2ZjYxODk2MjU4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNrTUtFclpjeXo2dm5vWXNLbDBNb0E9PSIsInZhbHVlIjoiKzhwSHU4Wm5FR3J3Ull3SkJzbEpqVzdZYS9ET0dCUkNYRkpia0tLV2VhQlF2RExGYm5ieDB3aUxFbng3cnVTZUNDQ1lGVHFuZEwybHdLK2prRmZEOE1LK3lBTFFxTmhJMyt3RE14RmFyOXNoMUJwL1hRMEVNOGIyMDA1QXhDSkRoYXQ3cGkxOEtRSlZtRDU4WFMrL2E2cVl5Sk5OMzFyVW1aMnRrcStKU2tsYXlkMFNUazU3WTNrVE1BdjAwM3poVy9qR3FSM1pMUXk5NkdZR3Fhdkh5VmlVMzQ5b3QwWis4bW1zdzNXRFg4QmNhUlhoaFZiUk40MjdlV1FVMS9mcTlpYzIvOStYb2tUQ3YxY2RlTWVET3Uwc21iay9QcWJwYXBrZCttczFmMWJvWmJPZlMyL3J0dXQ4MFBaZ2VNdlRIeFVRZnZzWTczc1VieUVyQThOVDZ0NzhFcmxIVmlKZ1BKYU9OODNDbGFZc2xJcnBlWUhyUmovWTdaVitneC95b0hjQWpFU3I0STZHbnFxRTA2bkQveUNmUkNGUXJBQ1hjL0ZEdFdkN3JQN3BUdWFxWFlSbDQ3emJrbzlPZkRwd0NFRE1haHlBOGRiL3huYTZjT2VLUkVaQlh2WFdqUDB5WkptMGZMMUxWMFA3OWVoSGo0aFF3N0EwSGtINUk2VFUiLCJtYWMiOiIzOWRhMTc4MmM1NDM3NjZlNDJmZmE4N2ZhOGYyNjg0NWEwZjAzYzQzMDBjYWUxNTQyZGRmNTFhNGY0ZmYxYTMxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkZnUzI5U1RRaWxKVmNvUnpiNGhEQWc9PSIsInZhbHVlIjoiV25kTnhyUThva1U0NGxLdmo5Z1VBdlFpdmRCOGdLblB0MGxvVkRKb1A3T0xaUUpXNG4wTzdTM1M1SEdGMnNOMjBHdkxkaEVaNWRYTTZBOUpQb2Z3d1AwZW9QVkx3Ky8rM1Bab3lQL1diTy9oRURvd1lEejVJaWtESDh6UTdwdE1oTUI3cGFDMDlnRXpBcGtvVytwZVRsb3VHMVBwb2hOamMyV3lDTVgzK0VlZ0JVTFk2RUM4NjRod2haRXdzZlRFeG5yVkFodVNLckxVK0l0RFlrNlk0SSs1cTYrVDVXbVJha2pEOFlwcnJIaGJ4SkZiWE44VFRxQTY1WERrZGM4Umord0NNU2p2dWFVd1BNVnlaR3NNQUMzWUw0ellLWDRaSThQZG1FZ2t5UzA3ZFdHVmlleG1qaEljWGlIR2FhdkRxWENWb3NCeUs2MllvdCtVSC9memRkQlNPcTVwQXBRdHd0Q2VTclVad2xNb05xa0JqY0g1YVR4MTVHZzkxVXYwZzNheXo5VHRyMmdIZDU5dURHa0l1UHhuNmN0bUI5cGRnOVg1UW8ySjBSdXArMEZxY3E0VlNWR0NUMnFTSm94citjS21tMVhTc2R0cXIyTDc0YkY4bG13cG1YTzVDUHZpMmRXTnIzUkFxVlVqc1BEaGRtRHhYbnJ1aWJ3V2lLMGUiLCJtYWMiOiI5MDM1OTY2M2EyYWZlOTFhZDI4ZWFiNmI4ZGI3NjM4YzMwMzAxZDVhMjlmYzFlODlhOTI3ZDg2ZjYxODk2MjU4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-9******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9********\", {\"maxDepth\":0})</script>\n"}}