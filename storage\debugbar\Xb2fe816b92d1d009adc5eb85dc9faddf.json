{"__meta": {"id": "Xb2fe816b92d1d009adc5eb85dc9faddf", "datetime": "2025-06-08 16:17:55", "utime": **********.681545, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.073799, "end": **********.681563, "duration": 0.6077640056610107, "duration_str": "608ms", "measures": [{"label": "Booting", "start": **********.073799, "relative_start": 0, "end": **********.606348, "relative_end": **********.606348, "duration": 0.5325491428375244, "duration_str": "533ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.606361, "relative_start": 0.5325620174407959, "end": **********.681565, "relative_end": 2.1457672119140625e-06, "duration": 0.07520413398742676, "duration_str": "75.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152752, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0077, "accumulated_duration_str": "7.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.643474, "duration": 0.00646, "duration_str": "6.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.896}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.662663, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.896, "width_percent": 9.351}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.670811, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.247, "width_percent": 6.753}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-630841804 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-630841804\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-783038616 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-783038616\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1646026035 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1646026035\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-613581676 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=1y4k55%7C1749399469579%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjEzU0VSZVZ2Z0JTT1hjL3R3L1Y0QXc9PSIsInZhbHVlIjoiSUtMSkdSWnVURmMyejVwRlJGTTc2cENMU3N4aWhHQnk4bUN4bSsxNHhOY3BQZGluMUxnc29Ud1RXSzNNZWdvNDM5VFFqeDJ4ZmJ5RnVSTm82RzBFb1BZaitzQWJ3Uyt3cjFqNldOcWRqSTJ0UXkySzhHWnVSa3RLK25YQjZtVlhOUnBpMlhqZHpyQklKaHN1QVFHeVgrZFkwRnpuMVlPd2hTdEVSRUxYcHRBa3BsVDJIeW9xaUVsUEdmeU14NnkzYU96YnNrRkZEd2lIYndkNFFBbW9veTBFRk04aklsVTc4YVo4eHJKR25XU09hYVVXc2s5L01tbmExK0l5c3ExZFRxUVFhanBmemJldzJsY0ppMERpSThRcEFHcXlMcU5FaTg0czg0bkFzeVhSRDJtR0ZSSHVIcG11L2x3TVBvL3pEUGhDMnVCOThtSjF0VDE3cGUzSVBhTmhwaEN4ZndjdERhejFWb1JNSGlvYWpTd05wZkQwams3VTRsaEE3S3FicWVxdE02WU52QldQSEtHbFhadFo2OU9MVWhlTzBTaE42YzFYbHBhK3dqNmhrbWg3VEVQbmhJNVEreGMwV21sZEdKaDRML2VYV2VjUzZvRE5Sa1pKMFFLcm9EYmZRNm9QcENXVFN3NUlTRE5rczQ5YWRxTmM1SjNYZ1hQTTNlQjEiLCJtYWMiOiI2ZTk2YmVlMjJjNDRlZjczNjk1ZmIyYzc2YTg2OTJkYjNhNWY1MWUxMzFlOTllYTAzOGU4MjFhMTZhNzljN2NmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndOU1VLZitLL2pqN0RKWGN4M0E3ZkE9PSIsInZhbHVlIjoiTVJWYkFQREE0MmxUUGxaS205eVdkZjlSMGo0MHJpVWV1Y0dYRTkxd1NYZGlOVXNoajBMMGJZS2FydUlJYnpwT1MrQVN0KytoVlRrN3VRSnJRY0h0MFhxYTBuMjl0dDBvcUZway9NUXVnMDZnNTVaZVBIRkpDZHBDYk9hV2tucDBNZzNTdWk5cnJPU3JVMlI5bFRZM0dESGxkVVQ1aDAvOGZjTk5CMUVNTyt6OFowRkhlNk9ocFVrd1RXcDVCVDUvUEFKclVwVExYYmVONldocGxLZ3VOUUUrNC9iOXJrMUFzZHhWUGJBM1dLdUh2NlVOV3RzQ2w2ZjltOVdiRDZRQm1qcy9zczk5RmtSWEQySWd4ellrYzczeXBpQVRlWXZRK01USmlnczdJTUgwUW9aNW9WNndpMm95UVh1KzZsRVBDOTQxUVo4bCtGazhZbVE5OXdYaXoxK2ZERFZpYklmU29NcnM1L3pIR0xnaUxkR0Y0MDN5aHVUbVBnVlVueEZSVWdOdWdlUW50L2pjd2hPbVgyblJPVzBOZy9UVjhTZW1zV1BCdmdsNUp3YVpOZUEwV3JYNVJOa0dLYlJ6VVRKQW4rMnd5endheGlhZkhzUi9pa1hwOGMxa2VSbUY2cVgzNHE1bFlmSUpvaVNYblQyM25qZGNDKytmL2hWT3pRaEEiLCJtYWMiOiI5ZWNlNzZjMmYzMzY3MDJjYWY3MTQ2ZmFkNDdkZDE0NzQ1ZWM4ZTE0ZjcyYmY5OTg5NDI0NDhmZTM4MzBlMmViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613581676\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1684299073 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1684299073\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-506988678 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:17:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFmOGVGaElIMUU3R3RHQk1UaFNyaUE9PSIsInZhbHVlIjoiVU5nTnZ0ekdHYkxPQ0ptRGwvcDFPU3FGODJZOFBEd3JJd0FOSjUxWTNONmM0TndLY3VMdkI2YjdrQVdveGIwdFF2UmcvOHVRMktsOExaRlBTQ2lVNWNuY2QwbG9icnV2anVBZFc5MlBiQkxQZEczN2E3d0ZOSGFoZ1MweHVBVFlCR3BLOHBKZG9VMk9ZR0x0aTJFS0tNcGU2Wi92NXYyM1pjY1BOSWtxZi83VS9QbUhpMUp1WkJSd1dTQXlGTTU3N3lTUmhvclpvdmQ2RHVCbTQrSWVBdHErNHBHdGJ6U3JXSGVsSzE5eDl3S2dLamdYVzhFM0tFVnpRNmMxSnZHbUViOU51MVh6eUVXM05qdjBXRlZtWnBPcWlWTUhEQkdsWHZnTDZpakxyY2pxQmZLOCtvUUtoL0N5bTh0SlFyN0poNi85eXJaNHBoaEgvMGNaYlV3M3RzZlVrbC9WMTZ2SzJjS3BsS3UxeWNnZ2VXNTNOYkc2QXJJQUp0MEpjOXdmblZuVGtsM1lZdUxpUVRvTGV5cXFtNzNLdU42UXo2UWJrWWRWQUNYRVFwTURRNkZ3KzdsWWErVmZJdUVqMTlOd0RCcXJYYUN3L0UvREV3b2VpWUlEMnBuNzdkYUdTNXJpNkxtU1IwUjZHWjRWV0JPQ1gxRi9pY3ZNM0V3TXhtLzciLCJtYWMiOiI0YjQwMDFkOWQ2NWJkZjY3NjRmODZlY2IxMTE3Nzk5MTdhYzk3NDQ1YjljYjViMDc3NTI1YjhjMzI0ZTk2ZTNiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:17:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik41OHRIZ3doQk5kZHhFd3BmRXJLR2c9PSIsInZhbHVlIjoiS3RSSVRZaEZ6ZldWWHVQaGZDUTFITStzb2V6ODdoQlpMNUZLckhTVkxsRzBnczZEcEZkU2FpTGlmTVYrMmViOE5INlJrMlZyLzh4Uk5sRFVnRHBKWGR3cW9FYS9UWm04cXB0VjFJcnBGWkx6NWdiWkh0bmU0WWlDSlRpbElVQlB0V3J5eEdVVU43TWxGNHRteGNOS09jRm5ybWcxVDRqZjFvSThTQk1aZk1UOGVMbmtTajhYbzFDcXdJNUpIV2pkbWlkQnQ2UGpsbkFNa2R4OVFPNHVTWjY0aTZPZUxjcElaZW90WitmcnZwQTg3T1JXaGxGZWZ5SW1lSkhwSUk0TWJxVkdQQllYSzE2SmpUTXY1WktPTFJkZ0J0dXNDNUUwbmtkVFBob3NYVUNrbS9nS0R2YXF0MmM5UTVaZzlnRGxVaG5QWmZTUGNOb04vRlhvSGF5a1dSenRaTSs4REtBQ2VzSFd6QWduc3pqYVRJSWI2UkdZM2llY2JvSFlPREZtTzRTUmlIWlNQZGdTUllRV2ZTcVhkTXdoTWR2RCtnR0JuM2tmb1NJK3p1N3BISTFzWGJ6emRFcXdIMVJWYTdJd2RVNXpZcm9mWE1DY2hYbjBLSnljQ1JGa2UyOUVzS3I1QzJ5MTVSNFlMZVJiRmo1RGhTY1gzYlBjdXQ4SjlwVGsiLCJtYWMiOiIxODkwZTQxMzk2MGZhZjZhYTFjNDQ4NjAxZTVlMTNlMWM3M2E1Y2U4OTYyZGE1ODZmYzAyMTQxZjkwOWFhM2Y3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:17:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFmOGVGaElIMUU3R3RHQk1UaFNyaUE9PSIsInZhbHVlIjoiVU5nTnZ0ekdHYkxPQ0ptRGwvcDFPU3FGODJZOFBEd3JJd0FOSjUxWTNONmM0TndLY3VMdkI2YjdrQVdveGIwdFF2UmcvOHVRMktsOExaRlBTQ2lVNWNuY2QwbG9icnV2anVBZFc5MlBiQkxQZEczN2E3d0ZOSGFoZ1MweHVBVFlCR3BLOHBKZG9VMk9ZR0x0aTJFS0tNcGU2Wi92NXYyM1pjY1BOSWtxZi83VS9QbUhpMUp1WkJSd1dTQXlGTTU3N3lTUmhvclpvdmQ2RHVCbTQrSWVBdHErNHBHdGJ6U3JXSGVsSzE5eDl3S2dLamdYVzhFM0tFVnpRNmMxSnZHbUViOU51MVh6eUVXM05qdjBXRlZtWnBPcWlWTUhEQkdsWHZnTDZpakxyY2pxQmZLOCtvUUtoL0N5bTh0SlFyN0poNi85eXJaNHBoaEgvMGNaYlV3M3RzZlVrbC9WMTZ2SzJjS3BsS3UxeWNnZ2VXNTNOYkc2QXJJQUp0MEpjOXdmblZuVGtsM1lZdUxpUVRvTGV5cXFtNzNLdU42UXo2UWJrWWRWQUNYRVFwTURRNkZ3KzdsWWErVmZJdUVqMTlOd0RCcXJYYUN3L0UvREV3b2VpWUlEMnBuNzdkYUdTNXJpNkxtU1IwUjZHWjRWV0JPQ1gxRi9pY3ZNM0V3TXhtLzciLCJtYWMiOiI0YjQwMDFkOWQ2NWJkZjY3NjRmODZlY2IxMTE3Nzk5MTdhYzk3NDQ1YjljYjViMDc3NTI1YjhjMzI0ZTk2ZTNiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:17:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik41OHRIZ3doQk5kZHhFd3BmRXJLR2c9PSIsInZhbHVlIjoiS3RSSVRZaEZ6ZldWWHVQaGZDUTFITStzb2V6ODdoQlpMNUZLckhTVkxsRzBnczZEcEZkU2FpTGlmTVYrMmViOE5INlJrMlZyLzh4Uk5sRFVnRHBKWGR3cW9FYS9UWm04cXB0VjFJcnBGWkx6NWdiWkh0bmU0WWlDSlRpbElVQlB0V3J5eEdVVU43TWxGNHRteGNOS09jRm5ybWcxVDRqZjFvSThTQk1aZk1UOGVMbmtTajhYbzFDcXdJNUpIV2pkbWlkQnQ2UGpsbkFNa2R4OVFPNHVTWjY0aTZPZUxjcElaZW90WitmcnZwQTg3T1JXaGxGZWZ5SW1lSkhwSUk0TWJxVkdQQllYSzE2SmpUTXY1WktPTFJkZ0J0dXNDNUUwbmtkVFBob3NYVUNrbS9nS0R2YXF0MmM5UTVaZzlnRGxVaG5QWmZTUGNOb04vRlhvSGF5a1dSenRaTSs4REtBQ2VzSFd6QWduc3pqYVRJSWI2UkdZM2llY2JvSFlPREZtTzRTUmlIWlNQZGdTUllRV2ZTcVhkTXdoTWR2RCtnR0JuM2tmb1NJK3p1N3BISTFzWGJ6emRFcXdIMVJWYTdJd2RVNXpZcm9mWE1DY2hYbjBLSnljQ1JGa2UyOUVzS3I1QzJ5MTVSNFlMZVJiRmo1RGhTY1gzYlBjdXQ4SjlwVGsiLCJtYWMiOiIxODkwZTQxMzk2MGZhZjZhYTFjNDQ4NjAxZTVlMTNlMWM3M2E1Y2U4OTYyZGE1ODZmYzAyMTQxZjkwOWFhM2Y3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:17:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-506988678\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2094989580 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2094989580\", {\"maxDepth\":0})</script>\n"}}