{"__meta": {"id": "X91ed911d89e04d64d512d96452666b84", "datetime": "2025-06-08 15:42:51", "utime": **********.807242, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397370.972035, "end": **********.807274, "duration": 0.8352391719818115, "duration_str": "835ms", "measures": [{"label": "Booting", "start": 1749397370.972035, "relative_start": 0, "end": **********.690613, "relative_end": **********.690613, "duration": 0.7185781002044678, "duration_str": "719ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.690632, "relative_start": 0.7185971736907959, "end": **********.807277, "relative_end": 2.86102294921875e-06, "duration": 0.11664485931396484, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45154336, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01693, "accumulated_duration_str": "16.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.745321, "duration": 0.01453, "duration_str": "14.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.824}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7773712, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.824, "width_percent": 4.962}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.791343, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 90.786, "width_percent": 9.214}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1017881362 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1017881362\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1519633841 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1519633841\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-496525987 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-496525987\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1057409750 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397363959%7C13%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJkeFAyQkxNeDY5R1VpK1libE5YT0E9PSIsInZhbHVlIjoibS9zZ1kza3pXVU1wSWQ0aHYvcE1LS0M3aVN4L1Rqd05la3RFUzhtcFE0N0xzVUY1YXBVSWpDdWdnZkdXOThBVDM2NjJsdTlZazFOcSt3TFFrcUo3WEUxRmFIUmYrTEs0U1pIMFVUeUM4ZllMRmhGanBuRjltak5iYnFtUDR5L0ljK2U4dEdXSjJIbnhxalhuK3hjR3dKaFZ0T1VVMlVQK1QzWFpRSUlKQkJaZElTdG5KRWkrRUtFbnFTOEtvQTNhVmJNeXo5U1NraEJPVDRpeHdPamI1T0dhNkhINDVrNG94MjlwOVAvRWw5c3hlTm1CdjdCeFFKN2hDMHB3cEdBeFZiRTVlWFpYa29LV2gyMnhaZVFOTjFjZXlETlIwTk8vOWFVaUdaNkhqaU1QbWlKMjhyaW1QK3pRRktLNjd4ZzdURUtmVUV2a3lINll0WjRwSVIrSkRlY2c0QkdENXFydERscWY1SE9ZbzdUODljWmFEV2JvbTdIWkcwVFpSdGxCRWc1dlpzZWt0YmQxeFRCZkJhT3pVaFdBMVF5WFpyby91Z3dPeUZQMmFseEE0MS84cUhpNW9DU0hiM3RwTmV0VzQrdlN4OFNOUDZTT2tsSGVXR3c3VW1ac3NoTWpPRlZENFIyeXdGNGh1UVpSVEdORDFhd3M3M2tlQktNaG9YbG0iLCJtYWMiOiI0OTdkM2ZjNWFiMmI4MmY4YTg5YzY1ZmJiMjQwZDhmYTFkNGM0MjMwZDJmOTliY2ExMWUwNGQwNzIwZGU1MmNlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InhxS05ZT1lhc0dWVWl1YWFsTnRvbmc9PSIsInZhbHVlIjoiMTZ4d1crYXZmSzgraTZxSmFwc2RQMUdweUFJSUhFb3VTVnRJa2Fjd3JocFNBTHA0c0JEWEZLSjMrMmF3WTR0dG5ralQ5bDhGeTg1emJuY0FjYWZRZHJpdElFaUtCS0N3VnpaRnVDOHlDT256MVp2RXpVRjJGenRyVm5kM1piWjdDU2tHekI5YmRPNzNyWjRVYnBpSDA0UndSRmxYWmd0bDdYSVBOZ1pFT0trYnNpaXg3M2h2Q3g1TFBwVWxZRnNud1RlNVV2NVMzeUVrMUx6V2JiWmU3OEhYSEdwRXBjdWk1alhCaS84UXpLQ1BpTFNHOUpNckFkcTdQVnlNakszdVd1MHNTakdOZmI0d1AxQ05odmQ5RXAyL0xtNkduNkJISlY5TUg0cjZzd3oxRmV5eWZjWmtHWEx2V0N4WldKZTRGWE03b056aG1oN2RicXVCcFJOVkRHemx4MHNsWU9EZlgyWU10K2tyMXlkOW9BNk4rcVJNNE41cWxYOXh4ekdDTFRIbVJUMmF5U01nRUZWZHZnM0RwVXN3RUFGaVlhTzVmcE1veEhnVCs0V3BkSzFFK1YydWxKVXZwWElYcWNBdVg4Q0wwSHhObmxVV1NLVFhiazZ5T3k0QmY2V0JKRVcrZXNUdTBDZDdTMjlpMjB5eGlSbEowS3A3Q1BQUHR4OUwiLCJtYWMiOiI5ODUzMTk5NzlkNGM1ZWVmN2QxNmQ1NTAzZGRmNmJhZmZlZWVlZDE2NDgxMzIxYWRhMTFhMjJjYjhmMDNiNjQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1057409750\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1595833068 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1595833068\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1981463680 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:42:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNiREh2ME1uTndvRnlzalpkdlFHQUE9PSIsInZhbHVlIjoiT25zY2ZncmFwekRkZDMyTXpJWXVFN0xVVkEzSkRZem8rcTY5RENrQjlwcU5FcC94Y1VLeTJBMmZQd1lxRDFhMW0xU3NwbWR6N1VQRG5NTnR6ZXl2NmNyRC9hM2xJMHJENnlhbHU4bjYwMGdQcTJiNjMwN08xZzc1YTlOM2h3UEdaeFFzd3JxWHVRV2doN2JvWEJQeTJRNExrM0liOGlrbXFhaUdvak1GdHVPZEFoazNNTnFtQm1VVDNiZWQ4a2NBdU00WGtTZ1gvTE5MQ1UvVHZIU3RvY2dQOFhHdHh0Ny9nUlFpQnZTdXNGUDd5a05mbjNxcGNPVUdYdEt1TmZhVGkzNjZqcXFwMkVXazc0UmRtRU5DWFVZSlNDb1hTWnUvaTM3dEd2dXVhbmV0SEpKY00xSkVFcXViU0Zhd0Y2d0tzQk9sbG1kN3FzOWljK1pGeDZqYXBrQUdIMTNxdTJtYnZDbmVKN3BUTmdDZG12YUsxL3JaMUxPWDA3MWd6S2pEMzFPaWVPM04ybDRWUVc2MldXOWhhM1daanRkWmpobFIvZFlLbkViOVJPSHY3VDcxL2lYeVlVUVNSZzlsTnl2dkE4NHRRdGZVSXVxdkNOZmRBVHp4N1RTUFBId0VyUFVJeXUydVROR0FSQmVQVXprdGljbGIxSkU3Y2FnVThleTkiLCJtYWMiOiJiYjIzYjA0OTJiYzUzZTliZTdjMzJkMzk2YzlkYjEzM2M1ZmQ0MTIyZDdiZGNiOGYzZWEyYmYwMDI2OTZiZjgzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImJBMk11K3JHYmpwUWh3ZlYzQXR2Z1E9PSIsInZhbHVlIjoiRWZHQlRoazl5Y1BiZFhTYzRoYzVqaHdNWXloQVFGN3FWZXBEZDZzSXBNOW9UY1BZSlRhaW8yNGtIaWZKdUZzK1VBZlJUelByczVCUlpIMU5ueU9KbHUrT1cyMjdPMGxPYzVwcnRZcWtmNnpHSFN6QTk1MFAwYnQyYVFjdG1GM1BlTk9XaDVVb3JtTzBKVm5ieHpFeStwTnMyR05mQ2FXR3dHSm9HTXlGc0dVcVQrbWV4Ym54Z0gzNDNFTlZXWUgwd3FaQWlHZHZURkVRS2JXb1cvN1VENGtSamxXOS9TdGJTOFVxNjAzTGFGM3c2blNublpYY1R4S25NYUZScDA0WkpJUkV2Ung0N0pKaEozeXZ2OXMvSHpPNjNaTlRYbUJWcUpyTlJkT1hFd09ENUlKS0VXUzZpVzVFSkNHb293NG1URVBnTnZVM2E4bHA3TzRIcWFPcW50bVVKSnF5V0swNUlqQk5naHJxVk5SL3l3WXRvYnJ4UlZjSUppazBpbHRra2twaDMrQTY1ZDBUampGYThPVnZxSzVlUVRMazZCRzhybk9MVit0Uy9OTnVySE03RE0vQTFqa0dwbjROb0svR2Q5V0h2MGNCMFhpeTI0bFFVRytGMjlRM052OHlLQ2pvZzVrREsvVW9ZK3dXQk5EZDFlK25VZXV5RS90N1pvQUoiLCJtYWMiOiI1YzliNDMwOGU5Y2FmMThkYTk1OTMyYzQ3NjNmMjYwMWRkZDE2YTFlY2NjMmFjZWUyMjhlZTg4MjA0NDNkNzNkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNiREh2ME1uTndvRnlzalpkdlFHQUE9PSIsInZhbHVlIjoiT25zY2ZncmFwekRkZDMyTXpJWXVFN0xVVkEzSkRZem8rcTY5RENrQjlwcU5FcC94Y1VLeTJBMmZQd1lxRDFhMW0xU3NwbWR6N1VQRG5NTnR6ZXl2NmNyRC9hM2xJMHJENnlhbHU4bjYwMGdQcTJiNjMwN08xZzc1YTlOM2h3UEdaeFFzd3JxWHVRV2doN2JvWEJQeTJRNExrM0liOGlrbXFhaUdvak1GdHVPZEFoazNNTnFtQm1VVDNiZWQ4a2NBdU00WGtTZ1gvTE5MQ1UvVHZIU3RvY2dQOFhHdHh0Ny9nUlFpQnZTdXNGUDd5a05mbjNxcGNPVUdYdEt1TmZhVGkzNjZqcXFwMkVXazc0UmRtRU5DWFVZSlNDb1hTWnUvaTM3dEd2dXVhbmV0SEpKY00xSkVFcXViU0Zhd0Y2d0tzQk9sbG1kN3FzOWljK1pGeDZqYXBrQUdIMTNxdTJtYnZDbmVKN3BUTmdDZG12YUsxL3JaMUxPWDA3MWd6S2pEMzFPaWVPM04ybDRWUVc2MldXOWhhM1daanRkWmpobFIvZFlLbkViOVJPSHY3VDcxL2lYeVlVUVNSZzlsTnl2dkE4NHRRdGZVSXVxdkNOZmRBVHp4N1RTUFBId0VyUFVJeXUydVROR0FSQmVQVXprdGljbGIxSkU3Y2FnVThleTkiLCJtYWMiOiJiYjIzYjA0OTJiYzUzZTliZTdjMzJkMzk2YzlkYjEzM2M1ZmQ0MTIyZDdiZGNiOGYzZWEyYmYwMDI2OTZiZjgzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImJBMk11K3JHYmpwUWh3ZlYzQXR2Z1E9PSIsInZhbHVlIjoiRWZHQlRoazl5Y1BiZFhTYzRoYzVqaHdNWXloQVFGN3FWZXBEZDZzSXBNOW9UY1BZSlRhaW8yNGtIaWZKdUZzK1VBZlJUelByczVCUlpIMU5ueU9KbHUrT1cyMjdPMGxPYzVwcnRZcWtmNnpHSFN6QTk1MFAwYnQyYVFjdG1GM1BlTk9XaDVVb3JtTzBKVm5ieHpFeStwTnMyR05mQ2FXR3dHSm9HTXlGc0dVcVQrbWV4Ym54Z0gzNDNFTlZXWUgwd3FaQWlHZHZURkVRS2JXb1cvN1VENGtSamxXOS9TdGJTOFVxNjAzTGFGM3c2blNublpYY1R4S25NYUZScDA0WkpJUkV2Ung0N0pKaEozeXZ2OXMvSHpPNjNaTlRYbUJWcUpyTlJkT1hFd09ENUlKS0VXUzZpVzVFSkNHb293NG1URVBnTnZVM2E4bHA3TzRIcWFPcW50bVVKSnF5V0swNUlqQk5naHJxVk5SL3l3WXRvYnJ4UlZjSUppazBpbHRra2twaDMrQTY1ZDBUampGYThPVnZxSzVlUVRMazZCRzhybk9MVit0Uy9OTnVySE03RE0vQTFqa0dwbjROb0svR2Q5V0h2MGNCMFhpeTI0bFFVRytGMjlRM052OHlLQ2pvZzVrREsvVW9ZK3dXQk5EZDFlK25VZXV5RS90N1pvQUoiLCJtYWMiOiI1YzliNDMwOGU5Y2FmMThkYTk1OTMyYzQ3NjNmMjYwMWRkZDE2YTFlY2NjMmFjZWUyMjhlZTg4MjA0NDNkNzNkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1981463680\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-593339370 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-593339370\", {\"maxDepth\":0})</script>\n"}}