{"__meta": {"id": "X7aab927e498ff1c000796a862cfe8330", "datetime": "2025-06-07 23:26:44", "utime": **********.661627, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749338803.717557, "end": **********.661657, "duration": 0.9441001415252686, "duration_str": "944ms", "measures": [{"label": "Booting", "start": 1749338803.717557, "relative_start": 0, "end": **********.549221, "relative_end": **********.549221, "duration": 0.8316640853881836, "duration_str": "832ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.549248, "relative_start": 0.8316910266876221, "end": **********.661661, "relative_end": 3.814697265625e-06, "duration": 0.11241292953491211, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45056344, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00568, "accumulated_duration_str": "5.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.609412, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 62.852}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.630423, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 62.852, "width_percent": 18.486}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.644472, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 81.338, "width_percent": 18.662}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-145968497 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-145968497\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1985133698 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1985133698\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-666266532 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-666266532\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1334009446 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749338598681%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikp2WXAxcVZUU0pkUlFOL20wcjltcXc9PSIsInZhbHVlIjoiVDB1cVQ1MnhuVW9kUjBlOTVsWFBSeVNmUGxQbndPK09ndDZKSHhFOEgrb0pJbFlNNXdLUjlYVHFPcTFwclFYa2NpNG5vWmt0c2tJaVlzUTA5b1dMaE5mbEY5cDUzcmtJajJmanVTbUg1NWRrUWdJSjRrWmRsNFFJdzNyY3dES2gxSk54V2xJY0c2YmFXcEk5a0dtRWNQMlNaM2NMUDNiS3FKSUd1UXJxc0JTKzV2U0VNaSsrM0lhd2Q2VGVnekZIaVVNNGpuLzlRZ1FDbm9OK3dPYTcyOEZWS25ocXBkaWpSdmd6UERoWmpCRmZSM0wza1pkaUJ6MXhwbXdoSlZZNnhtZDZvNFRuVmxlREhEZzAxdTFpRE9GQm1KMERua2VQWU1tWE05UmlzNXQyUE9yQzkvUWVhTm4ydHZXVGMzOTZ5d2VBaUd5M2RhWFlTQWFicDREamZZVVdkZEZjem8zWFFmcFVOaFF3NzhCYmdxbVJKdnJGVzJidXQ0aEh5TGF1M3J1aU43OU9ObjZnd0xpL0g5T3paQlVPaDI1c1hZY3BtRlc5OUQxVFJCdXZQdFVLNlZQUFpPa05ESkhwckRDS3VGMFBHWit4UDRicU9rTC9EZExpVms5SzliN2lCRm9RdVgwaHBtTXJtbE1FMDlJeTBjSjhScGFNcUhqVjNKSUoiLCJtYWMiOiIyMDA4MmU0YjE0YjI1MjZhYzkxYTM3Yjk3OWRmMGNjNzgzYmI3MWRmZjQyOGNmOTNiZjU5YzFkZDdiNTBkNjQxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjMySVEyam9FQ041L2JQZWI3NUU4eFE9PSIsInZhbHVlIjoialorRXU2MTczZVBGbE1GQ1ZHWkdNaGlZK29vbGx5a3R2UnpFOU0vaHJvdkY0ZS9KQnAvdWpHUUlLRGczVFZpRit2UjBmK1hzeU85SzNZWVViNkp0MDFTVnlCMUErWk9obERxU0tmejI2cU5UQkpvdlpFY1JFWVdWV2Q5VWZMYU44d3lqWmVUcXh2emZIOUNLMEdpYmFieVEwbWo3UjBmNmloUmpLTENvU0VtZ2NpZE9EL0Z5NlViVjlLd3YyZ3JibE5HUFN3eUZEdW8reTdSSDJEcS94T2RUZU9OeEJORFRwUEpUMzBNN2NyRW9rMUdWam9pZWdyZkZvQUJIelI1SFdQeWx3eXBZQk5Yc3hUbTBmUWRjLyt4SnliME9vVDlrT3Q4SkNyVUY2VUFYNks0eE5pRjg2bktDczM4amRKU2o0Q2plVk5jRWliazhsamVMQzhBbmFZWUNoNlN2L2NCaTJtdEpyc2YxclNueVExaE5OaVh1U3JjdVNUN1Z5QUZnY012VnU1RTBXRGtBeVhZbHNZOWt2ekYzMnNBODVxR2dNT3RjaTNXbnVEOWZTclhiWEVRWDh3RG9iQy9OTzB6N2ZsUktRTDZpakFsMUtId1B3RDVuUjZwQ3RyZDlERmFQTHlZNE9VTmlxQVJ5bldpeFhRSk5Cb2VWWHRZc3VGV00iLCJtYWMiOiI4MjY1NDk0MzViZjE0OGMxNTA5MTc0MTE5MDE3ZjdiZWI4YmRhYTYxNzUwN2M4ODhlNzgyYmFlOWFiYmQ2M2I2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1334009446\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1817456480 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1817456480\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1703458480 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:26:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFld1B1S1pXODNqSk13TkpnNk9PVHc9PSIsInZhbHVlIjoibE5JMUp2Z0U1RkZrVjZzaFBzRFNDWC82QW9qcXEwc2JRUEhncnRqSFFpUHEybVJIY2RkdUhFMzZ1azlwZzA1YTdJWi8yenNkK21HOVQ5M2haLzdPNU02Qy9tT0tyQUVvWTlENHRXb1dlOUI1V1dXcVBEZWNoTFVERzB6TVFVaFpNQmhMREhTY0NTeDRqUGpEcEZPS2l6bzZ5VE1IYzQ1WGZncGhEN2NFakVreTROUUxlZG1UclgvOVl3Vi8ramlwS2d0cnVXTEUydHUxWlNnaS9WUnp4TndDbVRCWTlhTWNxdXZ2YUo5Zm5Bd0RWdEZBV0h1L1A2dlZ3TE5pVkdYczI4TnZ6cDNPSTU4cVFheE1RMWJuaGZOZ3E0b2t2em1MUDNBc25KWjJmeDI2dDRKSlJwYzdiWXpiRjNhZm9nY2swdG4rejNISjFyUm1lTWJVOGdWVXNvSjlLMDVjeVhaZ0lYUWtTTTVGR2hvNkY1cVNDMmxHSU1vUTBoVjA4QTM5eE1RQlNWU05MQnRGaldIenlwajZtanJFaFlLVk9aSGdQS2RYekIyVkZKdmQxTWpJUmJ6eUo2R2hXSUZJUnBXVXA2UUZzSW5Ba2g2TFA3NXdhTjFqbS9VVnhoZlk3em5LSGpsTHRSeG9QY0NiNE91Rm8zaXZnRzBtNm1hZEwzaXgiLCJtYWMiOiJjYzkwMzZlNGZmOTkxZTZmMzMyNmY4MmEyYjJmMDI5YmY1ZjQ5NjdmYTIwNTAxMGQwNjRmY2IxY2U2MDgxZWNmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:26:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii9EdHNGVmJCT2JKQXI0b05xMHpKZFE9PSIsInZhbHVlIjoiY0h3RjZTekM2TzZaNVIzWmpzSHNMOFo4ZGg1eDZZdS8zdDFQRGJuYzBLN3dJVnhndzNUZ0JSY0Jac3AyRFhvUjRjdzMyd2FLOUt6TVhkL3loUUppUTdGZWJMT041L2N6Wis4WWdnWVoyaFVvOXBCcHBsK211V0pmalQ4ZzAzeVBWcGxYcUw2ZklZK0N0ME13TzB5N0NMeUpCUEcyaHJvUWVRdnBlZ0ZBaDNXY0NscktXeFBoVys2bmJ3aHNzYzRQY1JjRVpjK0g4a1RIT3lWMVFJb2tkSHU1QnM4UVg1TFc0QWNmd2JqS0ZISDlSVEdJd1ZzNk1pSXZGYUtFK21ERGp6OGZJdFM5MnpGSzVwSmNIVW9CRnNWeVQzd21aMjIyK3l2NGRvbVlNVjZLQUt4WHZmMWJIQ0YzR2Z6R1RVQ0dEaURMVVhhK3E0TW5kV0FQenZGUnhGdUFBQTRuanhyd2lMeG44L2FqQVdKWDN5Qk1KWkswMHpvMm1aZS9FQXhzd3pYTGw0enJtc1NtdldMVTI5WGhSZ1JrSGt2T29nU3l6TlFmR2hXN3RMOHZlVmNGVUFsc1VuQVRwTW83MGVjT2dDcmRldm0vMDRLSzdUZC9oM0J1MVRmRHlZdE1abndKZ1BBWnI1M2crYjFDV3NtR3BjWlNpUUxNbStCY2pCRC8iLCJtYWMiOiI1NzQwZTYzYTg0NmQyNGYyZDg1Mzc5N2YyNDFlMTUyMTlmZDY4ZTI1MzYwYWI4MDA3ODdkYTdhZmFlZDk3MDMyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:26:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFld1B1S1pXODNqSk13TkpnNk9PVHc9PSIsInZhbHVlIjoibE5JMUp2Z0U1RkZrVjZzaFBzRFNDWC82QW9qcXEwc2JRUEhncnRqSFFpUHEybVJIY2RkdUhFMzZ1azlwZzA1YTdJWi8yenNkK21HOVQ5M2haLzdPNU02Qy9tT0tyQUVvWTlENHRXb1dlOUI1V1dXcVBEZWNoTFVERzB6TVFVaFpNQmhMREhTY0NTeDRqUGpEcEZPS2l6bzZ5VE1IYzQ1WGZncGhEN2NFakVreTROUUxlZG1UclgvOVl3Vi8ramlwS2d0cnVXTEUydHUxWlNnaS9WUnp4TndDbVRCWTlhTWNxdXZ2YUo5Zm5Bd0RWdEZBV0h1L1A2dlZ3TE5pVkdYczI4TnZ6cDNPSTU4cVFheE1RMWJuaGZOZ3E0b2t2em1MUDNBc25KWjJmeDI2dDRKSlJwYzdiWXpiRjNhZm9nY2swdG4rejNISjFyUm1lTWJVOGdWVXNvSjlLMDVjeVhaZ0lYUWtTTTVGR2hvNkY1cVNDMmxHSU1vUTBoVjA4QTM5eE1RQlNWU05MQnRGaldIenlwajZtanJFaFlLVk9aSGdQS2RYekIyVkZKdmQxTWpJUmJ6eUo2R2hXSUZJUnBXVXA2UUZzSW5Ba2g2TFA3NXdhTjFqbS9VVnhoZlk3em5LSGpsTHRSeG9QY0NiNE91Rm8zaXZnRzBtNm1hZEwzaXgiLCJtYWMiOiJjYzkwMzZlNGZmOTkxZTZmMzMyNmY4MmEyYjJmMDI5YmY1ZjQ5NjdmYTIwNTAxMGQwNjRmY2IxY2U2MDgxZWNmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:26:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii9EdHNGVmJCT2JKQXI0b05xMHpKZFE9PSIsInZhbHVlIjoiY0h3RjZTekM2TzZaNVIzWmpzSHNMOFo4ZGg1eDZZdS8zdDFQRGJuYzBLN3dJVnhndzNUZ0JSY0Jac3AyRFhvUjRjdzMyd2FLOUt6TVhkL3loUUppUTdGZWJMT041L2N6Wis4WWdnWVoyaFVvOXBCcHBsK211V0pmalQ4ZzAzeVBWcGxYcUw2ZklZK0N0ME13TzB5N0NMeUpCUEcyaHJvUWVRdnBlZ0ZBaDNXY0NscktXeFBoVys2bmJ3aHNzYzRQY1JjRVpjK0g4a1RIT3lWMVFJb2tkSHU1QnM4UVg1TFc0QWNmd2JqS0ZISDlSVEdJd1ZzNk1pSXZGYUtFK21ERGp6OGZJdFM5MnpGSzVwSmNIVW9CRnNWeVQzd21aMjIyK3l2NGRvbVlNVjZLQUt4WHZmMWJIQ0YzR2Z6R1RVQ0dEaURMVVhhK3E0TW5kV0FQenZGUnhGdUFBQTRuanhyd2lMeG44L2FqQVdKWDN5Qk1KWkswMHpvMm1aZS9FQXhzd3pYTGw0enJtc1NtdldMVTI5WGhSZ1JrSGt2T29nU3l6TlFmR2hXN3RMOHZlVmNGVUFsc1VuQVRwTW83MGVjT2dDcmRldm0vMDRLSzdUZC9oM0J1MVRmRHlZdE1abndKZ1BBWnI1M2crYjFDV3NtR3BjWlNpUUxNbStCY2pCRC8iLCJtYWMiOiI1NzQwZTYzYTg0NmQyNGYyZDg1Mzc5N2YyNDFlMTUyMTlmZDY4ZTI1MzYwYWI4MDA3ODdkYTdhZmFlZDk3MDMyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:26:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703458480\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1038828924 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1038828924\", {\"maxDepth\":0})</script>\n"}}