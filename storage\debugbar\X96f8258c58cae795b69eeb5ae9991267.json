{"__meta": {"id": "X96f8258c58cae795b69eeb5ae9991267", "datetime": "2025-06-30 15:32:36", "utime": **********.226776, "method": "GET", "uri": "/add-to-cart/331/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751297555.691626, "end": **********.22679, "duration": 0.5351638793945312, "duration_str": "535ms", "measures": [{"label": "Booting", "start": 1751297555.691626, "relative_start": 0, "end": **********.10979, "relative_end": **********.10979, "duration": 0.4181640148162842, "duration_str": "418ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.109809, "relative_start": 0.4181828498840332, "end": **********.226791, "relative_end": 9.5367431640625e-07, "duration": 0.11698198318481445, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48567720, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.02924, "accumulated_duration_str": "29.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.155385, "duration": 0.02467, "duration_str": "24.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 84.371}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.189507, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 84.371, "width_percent": 1.471}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.2033749, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 85.841, "width_percent": 1.881}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.205363, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.722, "width_percent": 1.402}, {"sql": "select * from `product_services` where `product_services`.`id` = '331' limit 1", "type": "query", "params": [], "bindings": ["331"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.210233, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 89.124, "width_percent": 1.573}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 331 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["331", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.214284, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 90.698, "width_percent": 8.379}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2180462, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 99.077, "width_percent": 0.923}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-402693880 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-402693880\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.209319, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  331 => array:9 [\n    \"name\" => \"طقم عدة\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"331\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 9\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/331/pos", "status_code": "<pre class=sf-dump id=sf-dump-1227651680 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1227651680\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1056899701 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1056899701\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZBbzFTSEthYnptRXlGeTV1eFRUbGc9PSIsInZhbHVlIjoiajhjUlVPdjdFM2J5SUE4bXh1UFF4eGgzRGR3ZUUvOWp4TThCV2dYbHFRWFBad2llRXRBVWJmVUtrSXBDZWdxWnNmVmoxa1h5MitNSXJIaTErUE9MUjE4NHUyb0daOE54aGxCU0d3eVdHZmQzdzBpcnJIc1Y4dGViTUt0dHE2akY1czRqV2cyOW4rOGFSRHM1WlJFdDFLZFN2dENCeFd3Uy81eXoxTnVnUGpjL05kcXV1MHZ0d05pSGRwcjRZd1VTdHJUM3Y0UGpwb0UrK2FaU1lyK1YycUlhL0tyTWZrUExOL3MxVVQ0TW5XWjBNN1A3S0llcURzSi9wZXFWZmgrVFAwNTBYYUwrWnoyZzdGYlhPVk9Ja0phMjJtdW5aOUgwVjB3ZFNKZHdSV2pYdzF1cG9MVFNvM0JVdWQyblJJNjNYbVBZUHNlTG1aYjMxNnpTNC82OVN0c0VvK05BUk1ndWduZUpzWmVYMzloN3NSS1YveGpLL2JDTVlMSC9yZDZQK0ZwM0FUeWVwUXJ4VXdUTG0rSS9PZVJZa2NLYzhzaFZIdzE5dThETllldSs3b3M2NFhyb3lad3NlbG5XRFdZT3FKU0Q1alZOL3JFa2FvbURSS3BBaFRYQXNhYU1zRU8wK3g5V1dJbFVYTVRFMlV0L3c0aDBmaUptWk0rQU01anMiLCJtYWMiOiI1ODM5NGZhZDZiMDE4MmMzOWFjN2ZlYTYyNDMyMjY0ZTcwZjMxYjk2MDRjOWQ0ZDRmNGU0NmIxZGE2M2MxMzZjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik43Q1ozcDBJU0dpbCtnWXR6VXZnQUE9PSIsInZhbHVlIjoiMUFyeFcrNDVHM216Nm9KVjhtOTc5ckF4eVJKdVJHS1IvdE1ZWHhmNmtZdThWVFNDNVBDUUZmRFRSbHNRM0hNdmdObGNsSVE5emVBWjNNMFNhR1dzRmJTbTArNG15ekRFay9rZjBsRm1WOGZGWEVpS3dJQzRLaVQyRUczdTZDNlhGMm1ld1E1djVpUUJXSDc4b0p0NllUVDd6RmhNWjZtcEdsQUhwekMxb1JJUitJOEE5Mm9icTF2WjdkT2VXWFhJRitkZVlFQjZkdllaNjRxT1Vsb05iaG9YQUphZ0F0cENmL0JOTE9XQ3IwRzdwVStLaEl4blZFVGJ4MVpmM0hkREg1c0hvV1pDRWxtdkFna3FjclFScC9jU2dVNUxHNUtlM1lrdGNyZkk5dG1yRXd6WTcrc1VRcmlnb1RFdDRZVE9WT0k0YTZUZUUwYmVlTDJPK1lYL216UUc0VVNGN0ZPS0FURVE4WmZrTkx6SHh5ci9VZWhaYmZSMTBMS29tYnlSSlIraGdxNnpFOGZjWlZ4RFIyOTJPaHVMQ3h4NUQ5N2pVSGpTQUFGS04yUW14Vm9BQmhoWGN4ZGY4TXd6eHM2Z0JuMlRweXczWW04aytrbmx6dFVOa1pYYTBBcUtjc2FKdXZBelpDQW1qeFpEZ29YQTQzOEw3TmZxOEFrNDFnYU8iLCJtYWMiOiI3NmVjMzMwNzVkMGZiNzM4NGUwYjQzNTE2NjRkNDUwNTJiNGM2MmFiZGYzYjg2MGY3ZmYxNjdjNjc0NTE4Y2VjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1094681476 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094681476\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:32:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdoR2ZmRTF5MjZNemtMOGRvS2JFUUE9PSIsInZhbHVlIjoiRnN6cE1pc1Jab2ZyWkVtWndDekYvVkFRNWhzZnBpYUw1bVpkRVViYmlhMlE5eFVTalZjUjZ1WGE0OGp4SHZOWkdoUTVIRDlKV1JzUzVyYnVMdlZGUlduVzA2SktYNnp1Qis4NXdJd1JKMlFxanMvWkhIM25IVVBsR2doTENVR0RQOUhRY3RqTC9td1liRmF0Z0puWC9zU1ZteUZBbWRXaGY1WWRWWjRVTHV3ZkNKc2VFa1I0cDg1ZHk3UitSbFM5THp6MEIyWXNFVHMrV0RCb1dnUnJ2b0YwdjRkazlDUmVrWFJKZVNaRzlEazdTN1ZOTExBQVhObnNaMDJsTFZ6Vi8yYUpvVW9MUk5XREpnNEIrTkVaYzdBSDM0ZFNPazc3cklmL1pPYVhtRkI1VnJWQWxlVk5EUk1nNnRSZExUdUF2ejlXM0owb21acHFPeDlLRTVlcHBpRUw0VkZqZXVZTVhUU25IdjljWlRsMTd0SG5ualBZOENvc1VLQjZiUmZwU1hVSmRaZ2dsMllKTnE4SkRMT0FqQXp0V3cwL2xXZGhwQU4zS1FGUUNzVWliUVhTY3Q1UmNta2FSclJMRlMwb21DK1JUeFluRTBWOFpWOFd2b2pLVHNPdkJ5SEZkSWZZUEFGckpHNjViZzRFNGMwRisvc0lqN2R4ellzRXoxR2QiLCJtYWMiOiJmZDVlZTNjMGZhNzZhODhhZDAzOTgzZWIwZGRlNmFiODViYTVlNDlhNWJmMTViYjY0NTNmMGE5NmE4MDBlZjZlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:32:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkVCNXg5bmQ2c040NThtbW01OXREdHc9PSIsInZhbHVlIjoiU0s3QXB2ejR1SWtoL0VacnBGKzRMcWJiR3d3UVNBVXVZSUlEdkhiL2pUbjNRM1dUNktub2FONnpXTTMzdWpRNmlVeGVzYlRRMi82OUpZTW5vaXdXVzh0ZTcxdVhubnQ1UERrUS94R2dKVEJDbmJpWEJqZHpZSDFLa0N3UzBTQVp3Qit0UXdSSzZLTmdqdE9tcmpnZ1NGUUR1dVpYbzZuS3VtTEtpeWFMYUg1Z0JRUU5sbzdidnRTTy9hQkRYM3FlV3o5VUdhamd6SUJkMDhIbmZ6NjEwQ3ZSWUVoQk5qNlIzNjFFeFBDRWRCeTRwUjVzM2hFTS9hMVltS0V5YzdhNzdDaURUckk2V1ZVU2FidGU2Sjk2SmM2TjdFRGhCVWFBMUt2eHVGZXJZRTdiN0ZVSE9uZnRXajRGTUlrSkdjZ0lqcTNZVlpzR08zSXFXME03ckNzNStUV2xSRFhFbUErT1RWdGZPcFlsQzJETHFsOW5TREtNaSsyZ2I3MGtIMWNZaVJSVzdGd0JxM2Zqcy8wSkZCZG8rVTNFaEcwd3htbVRIOGxKQUowQnFkWWFObWkzdFpoa2xpbjlVeHo2cS9xMlFWV3MwbUNqeVE3ZmxxUjgzbk9SUVB1cERoT1ExdmdiUjkxY3NvM2krWW9qMWRTZ0ZLbjE2OVFSd1FMazZRVm0iLCJtYWMiOiJiMzZiZTQxNzYzM2MxZWIxMTA0MmE5YzQ5Y2YyOWI1MGUzZGUzNmU0Y2Y4NTM4ZDYzZDRjODdmM2NlNTQ4OTVkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:32:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdoR2ZmRTF5MjZNemtMOGRvS2JFUUE9PSIsInZhbHVlIjoiRnN6cE1pc1Jab2ZyWkVtWndDekYvVkFRNWhzZnBpYUw1bVpkRVViYmlhMlE5eFVTalZjUjZ1WGE0OGp4SHZOWkdoUTVIRDlKV1JzUzVyYnVMdlZGUlduVzA2SktYNnp1Qis4NXdJd1JKMlFxanMvWkhIM25IVVBsR2doTENVR0RQOUhRY3RqTC9td1liRmF0Z0puWC9zU1ZteUZBbWRXaGY1WWRWWjRVTHV3ZkNKc2VFa1I0cDg1ZHk3UitSbFM5THp6MEIyWXNFVHMrV0RCb1dnUnJ2b0YwdjRkazlDUmVrWFJKZVNaRzlEazdTN1ZOTExBQVhObnNaMDJsTFZ6Vi8yYUpvVW9MUk5XREpnNEIrTkVaYzdBSDM0ZFNPazc3cklmL1pPYVhtRkI1VnJWQWxlVk5EUk1nNnRSZExUdUF2ejlXM0owb21acHFPeDlLRTVlcHBpRUw0VkZqZXVZTVhUU25IdjljWlRsMTd0SG5ualBZOENvc1VLQjZiUmZwU1hVSmRaZ2dsMllKTnE4SkRMT0FqQXp0V3cwL2xXZGhwQU4zS1FGUUNzVWliUVhTY3Q1UmNta2FSclJMRlMwb21DK1JUeFluRTBWOFpWOFd2b2pLVHNPdkJ5SEZkSWZZUEFGckpHNjViZzRFNGMwRisvc0lqN2R4ellzRXoxR2QiLCJtYWMiOiJmZDVlZTNjMGZhNzZhODhhZDAzOTgzZWIwZGRlNmFiODViYTVlNDlhNWJmMTViYjY0NTNmMGE5NmE4MDBlZjZlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:32:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkVCNXg5bmQ2c040NThtbW01OXREdHc9PSIsInZhbHVlIjoiU0s3QXB2ejR1SWtoL0VacnBGKzRMcWJiR3d3UVNBVXVZSUlEdkhiL2pUbjNRM1dUNktub2FONnpXTTMzdWpRNmlVeGVzYlRRMi82OUpZTW5vaXdXVzh0ZTcxdVhubnQ1UERrUS94R2dKVEJDbmJpWEJqZHpZSDFLa0N3UzBTQVp3Qit0UXdSSzZLTmdqdE9tcmpnZ1NGUUR1dVpYbzZuS3VtTEtpeWFMYUg1Z0JRUU5sbzdidnRTTy9hQkRYM3FlV3o5VUdhamd6SUJkMDhIbmZ6NjEwQ3ZSWUVoQk5qNlIzNjFFeFBDRWRCeTRwUjVzM2hFTS9hMVltS0V5YzdhNzdDaURUckk2V1ZVU2FidGU2Sjk2SmM2TjdFRGhCVWFBMUt2eHVGZXJZRTdiN0ZVSE9uZnRXajRGTUlrSkdjZ0lqcTNZVlpzR08zSXFXME03ckNzNStUV2xSRFhFbUErT1RWdGZPcFlsQzJETHFsOW5TREtNaSsyZ2I3MGtIMWNZaVJSVzdGd0JxM2Zqcy8wSkZCZG8rVTNFaEcwd3htbVRIOGxKQUowQnFkWWFObWkzdFpoa2xpbjlVeHo2cS9xMlFWV3MwbUNqeVE3ZmxxUjgzbk9SUVB1cERoT1ExdmdiUjkxY3NvM2krWW9qMWRTZ0ZLbjE2OVFSd1FMazZRVm0iLCJtYWMiOiJiMzZiZTQxNzYzM2MxZWIxMTA0MmE5YzQ5Y2YyOWI1MGUzZGUzNmU0Y2Y4NTM4ZDYzZDRjODdmM2NlNTQ4OTVkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:32:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1446163714 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>331</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1591;&#1602;&#1605; &#1593;&#1583;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">331</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1446163714\", {\"maxDepth\":0})</script>\n"}}