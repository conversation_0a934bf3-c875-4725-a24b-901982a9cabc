{"__meta": {"id": "X68b37146349bb410125483f14c8b1cbc", "datetime": "2025-06-30 15:32:35", "utime": **********.684569, "method": "GET", "uri": "/search-products?search=1323&cat_id=0&war_id=9&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.177099, "end": **********.684587, "duration": 0.5074880123138428, "duration_str": "507ms", "measures": [{"label": "Booting", "start": **********.177099, "relative_start": 0, "end": **********.533709, "relative_end": **********.533709, "duration": 0.3566100597381592, "duration_str": "357ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.533718, "relative_start": 0.35661911964416504, "end": **********.684589, "relative_end": 1.9073486328125e-06, "duration": 0.15087080001831055, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50055064, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1318</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.04147, "accumulated_duration_str": "41.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.569252, "duration": 0.020300000000000002, "duration_str": "20.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 48.951}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.59908, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 48.951, "width_percent": 1.23}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.613605, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 50.181, "width_percent": 1.061}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.615688, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 51.242, "width_percent": 1.061}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '9'", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1236}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.620157, "duration": 0.0047599999999999995, "duration_str": "4.76ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1236", "source": "app/Http/Controllers/ProductServiceController.php:1236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1236", "ajax": false, "filename": "ProductServiceController.php", "line": "1236"}, "connection": "kdmkjkqknb", "start_percent": 52.303, "width_percent": 11.478}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname` from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `product_services`.`id` in (165, 450, 451, 452, 454, 455, 1388, 678, 691, 1634, 242, 262, 244, 263, 239, 238, 251, 243, 255, 254, 253, 257, 453, 430, 411, 448, 445, 968, 439, 427, 406, 442, 440, 444, 200, 385, 433, 441, 423, 386, 387, 384, 1199, 429, 443, 403, 1343, 1705, 1694, 1696, 120, 812, 813, 1054, 1325, 1707, 1702, 307, 1704, 1700, 1701, 1698, 1699, 1709, 1697, 1692, 1693, 1703, 1653, 479, 1285, 1282, 1550, 1280, 476, 1654, 1652, 1650, 477, 1363, 1286, 1283, 426, 904, 1255, 1038, 1256, 1027, 1040, 1449, 1023, 538, 1049, 541, 542, 506, 1156, 513, 512, 1513, 1512, 345, 324, 351, 518, 905, 1511, 515, 1160, 507, 347, 337, 1176, 339, 516, 1118, 1113, 1114, 1297, 1302, 1517, 693, 431, 1628, 954, 1444, 977, 972, 981, 495, 976, 1034, 1415, 760, 499, 498, 1405, 1407, 1408, 548, 491, 7, 502, 482, 500, 496, 485, 484, 497, 1181, 1436, 1712, 510, 330, 346, 505, 348, 1157, 1441, 349, 341, 1265, 326, 350, 328, 361, 362, 359, 357, 358, 353, 356, 360, 352, 1042, 1039, 614, 1717, 1716, 1715, 1718, 1719, 1720, 1721, 1722, 1723, 1724, 1725, 1726, 1727, 1728, 1730, 1731, 1732, 536, 228, 1733, 1734, 747, 1809, 227, 236, 1780, 842, 1782, 1779, 1788, 221, 1812, 1813, 1814, 1815, 1816, 1817, 1818, 1819, 1820, 1821, 852, 1822, 1823, 836, 643, 1824, 866, 1825, 1826, 1827, 1828, 1829, 1830, 1831, 662, 663, 1798, 1832, 633, 628, 225, 631, 632, 634, 630, 1833, 1775, 325, 699, 1834, 1835, 1836, 1838, 408, 1839, 1840, 1068, 952, 985, 982, 983, 1358, 1359, 959, 1191, 1671, 1348, 753, 751, 754, 757, 1810, 761, 758, 597, 1892, 1893, 1894, 1899, 1898, 1897, 1896, 1895, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 60, 62, 63, 64, 65, 66, 67, 68, 1983, 1115, 1984, 1111, 1985, 1986, 1116, 1663, 1110, 1112, 1987, 1312, 1323, 1988, 1515, 1247, 1147, 1989, 1376, 1990, 1991, 1533, 1335, 1992, 1293, 1338, 1252, 1993, 1495, 1455, 1655, 478, 668, 1467, 1543, 674, 1981, 530, 529, 528, 527, 526, 531, 532, 533, 534, 535, 669, 667, 679, 664, 1097, 1096, 1095, 1094, 1098, 1102, 1100, 1108, 1107, 1104, 1101, 1354, 1994, 1995, 1099, 2, 3, 46, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 69, 70, 71, 72, 73, 74, 75, 77, 76, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 222, 223, 224, 226, 229, 230, 231, 232, 233, 234, 235, 237, 240, 241, 245, 246, 247, 248, 249, 250, 252, 256, 258, 259, 260, 261, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 327, 329, 331, 332, 333, 334, 335, 336, 338, 340, 342, 343, 344, 354, 355, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 404, 405, 407, 409, 410, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 424, 425, 428, 432, 434, 435, 436, 437, 438, 446, 447, 449, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 480, 481, 483, 486, 487, 488, 489, 490, 492, 493, 494, 501, 503, 504, 508, 509, 511, 514, 517, 519, 520, 521, 522, 523, 524, 525, 537, 539, 540, 543, 544, 545, 546, 547, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 629, 635, 636, 637, 638, 639, 640, 641, 642, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 665, 666, 670, 671, 672, 673, 675, 676, 677, 680, 681, 682, 683, 684, 685, 686, 687, 688, 689, 690, 692, 694, 695, 696, 697, 698, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 733, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 744, 745, 746, 748, 749, 750, 752, 755, 756, 759, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 776, 777, 778, 779, 780, 781, 782, 783, 784, 785, 786, 787, 788, 789, 790, 791, 792, 793, 794, 795, 796, 797, 798, 799, 800, 801, 802, 803, 804, 805, 806, 807, 808, 809, 810, 811, 814, 815, 816, 817, 818, 819, 820, 821, 822, 823, 824, 825, 826, 827, 828, 829, 830, 831, 832, 833, 834, 835, 837, 838, 839, 840, 841, 843, 844, 845, 846, 847, 848, 849, 850, 851, 853, 854, 855, 856, 857, 858, 859, 860, 861, 862, 863, 864, 865, 867, 868, 869, 870, 871, 872, 873, 874, 875, 876, 877, 878, 879, 880, 881, 882, 883, 884, 885, 886, 887, 888, 889, 890, 891, 892, 893, 894, 895, 896, 897, 898, 899, 900, 901, 902, 903, 906, 907, 908, 909, 910, 911, 912, 913, 914, 915, 916, 917, 918, 919, 920, 921, 922, 923, 924, 925, 926, 927, 928, 929, 930, 931, 932, 933, 934, 935, 936, 937, 938, 939, 940, 941, 942, 943, 944, 945, 946, 947, 948, 949, 950, 951, 953, 955, 956, 957, 958, 960, 961, 962, 963, 964, 965, 966, 967, 969, 970, 971, 973, 974, 975, 978, 979, 980, 984, 986, 987, 988, 989, 990, 991, 992, 993, 994, 995, 996, 997, 998, 999, 1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009, 1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1018, 1019, 1020, 1021, 1022, 1024, 1025, 1026, 1028, 1029, 1030, 1031, 1032, 1033, 1035, 1036, 1037, 1041, 1043, 1044, 1045, 1046, 1047, 1048, 1050, 1051, 1052, 1053, 1055, 1056, 1057, 1058, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 1066, 1067, 1069, 1070, 1071, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1080, 1081, 1082, 1083, 1084, 1085, 1086, 1087, 1088, 1089, 1090, 1091, 1092, 1093, 1103, 1105, 1106, 1109, 1117, 1119, 1120, 1121, 1122, 1123, 1124, 1125, 1126, 1127, 1128, 1129, 1130, 1131, 1132, 1133, 1134, 1135, 1136, 1137, 1138, 1139, 1140, 1141, 1142, 1143, 1144, 1145, 1146, 1148, 1149, 1150, 1151, 1152, 1153, 1154, 1155, 1158, 1159, 1161, 1162, 1163, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1172, 1173, 1174, 1175, 1177, 1178, 1179, 1180, 1182, 1183, 1184, 1185, 1186, 1187, 1188, 1189, 1190, 1192, 1193, 1194, 1195, 1196, 1197, 1198, 1200, 1201, 1202, 1203, 1204, 1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227, 1228, 1229, 1230, 1231, 1232, 1233, 1234, 1235, 1236, 1237, 1238, 1239, 1240, 1241, 1242, 1243, 1244, 1245, 1246, 1248, 1249, 1250, 1251, 1253, 1254, 1257, 1258, 1259, 1260, 1261, 1262, 1263, 1264, 1266, 1267, 1268, 1269, 1270, 1271, 1272, 1273, 1274, 1275, 1276, 1277, 1278, 1279, 1281, 1284, 1287, 1288, 1289, 1290, 1291, 1292, 1294, 1295, 1296, 1298, 1299, 1300, 1301, 1303, 1304, 1305, 1306, 1307, 1308, 1309, 1310, 1311, 1313, 1314, 1315, 1316, 1317, 1318, 1319, 1320, 1321, 1322, 1324, 1326, 1327, 1328, 1329, 1330, 1331, 1332, 1333, 1334, 1336, 1337, 1339, 1340, 1341, 1342, 1344, 1345, 1346, 1347, 1349, 1350, 1351, 1352, 1353, 1355, 1356, 1357, 1360, 1361, 1362, 1364, 1365, 1366, 1367, 1368, 1369, 1370, 1371, 1372, 1373, 1374, 1375, 1377, 1378, 1379, 1380, 1381, 1382, 1383, 1384, 1385, 1386, 1387, 1389, 1390, 1391, 1392, 1393, 1394, 1395, 1396, 1397, 1398, 1399, 1400, 1401, 1402, 1403, 1404, 1406, 1409, 1410, 1411, 1412, 1413, 1414, 1416, 1417, 1418, 1419, 1420, 1421, 1422, 1423, 1424, 1425, 1426, 1427, 1428, 1429, 1430, 1431, 1432, 1433, 1434, 1435, 1437, 1438, 1439, 1440, 1442, 1443, 1445, 1446, 1447, 1448, 1450, 1451, 1452, 1453, 1454, 1456, 1457, 1458, 1459, 1460, 1461, 1462, 1463, 1464, 1465, 1466, 1468, 1469, 1470, 1471, 1472, 1473, 1474, 1475, 1476, 1477, 1478, 1479, 1480, 1481, 1482, 1483, 1484, 1485, 1486, 1487, 1488, 1489, 1490, 1491, 1492, 1493, 1494, 1496, 1497, 1498, 1499, 1500, 1501, 1502, 1503, 1504, 1505, 1506, 1507, 1508, 1509, 1510, 1514, 1516, 1518, 1519, 1520, 1521, 1522, 1523, 1524, 1525, 1526, 1527, 1528, 1529, 1530, 1531, 1532, 1534, 1535, 1536, 1537, 1538, 1539, 1540, 1541, 1542, 1544, 1545, 1546, 1547, 1548, 1549, 1551, 1552, 1553, 1554, 1555, 1556, 1557, 1558, 1559, 1560, 1561, 1562, 1563, 1564, 1565, 1566, 1567, 1568, 1569, 1570, 1571, 1572, 1573, 1574, 1575, 1576, 1577, 1578, 1579, 1580, 1581, 1582, 1583, 1584, 1585, 1586, 1587, 1588, 1589, 1590, 1591, 1592, 1593, 1594, 1595, 1596, 1597, 1598, 1599, 1600, 1601, 1602, 1603, 1604, 1605, 1606, 1607, 1608, 1609, 1610, 1611, 1612, 1613, 1614, 1615, 1616, 1617, 1618, 1619, 1620, 1621, 1622, 1623, 1624, 1625, 1626, 1627, 1629, 1630, 1631, 1632, 1633, 1635, 1636, 1637, 1638, 1639, 1640, 1641, 1642, 1643, 1644, 1645, 1646, 1647, 1648, 1649, 1651, 1656, 1657, 1658, 1659, 1660, 1661, 1662, 1664, 1665, 1666, 1667, 1668, 1669, 1670, 1672, 1673, 1674, 1675, 1676, 1677, 1678, 1679, 1680, 1681, 1682, 1683, 1684, 1685, 1686, 1687, 1688, 1689, 1690, 1691, 1695, 1706, 1708, 1710, 1711, 1713, 1714, 1729, 1735, 1736, 1737, 1738, 1739, 1740, 1741, 1742, 1743, 1744, 1745, 1746, 1747, 1748, 1749, 1750, 1751, 1752, 1753, 1754, 1755, 1756, 1757, 1758, 1759, 1760, 1761, 1762, 1763, 1764, 1765, 1766, 1767, 1768, 1769, 1770, 1771, 1772, 1773, 1774, 1776, 1777, 1778, 1781, 1783, 1784, 1785, 1786, 1787, 1789, 1790, 1791, 1792, 1793, 1794, 1795, 1796, 1797, 1799, 1800, 1801, 1802, 1803, 1804, 1805, 1806, 1807, 1808, 1811, 1837, 1841, 1842, 1843, 1844, 1845, 1846, 1847, 1848, 1849, 1850, 1851, 1852, 1853, 1854, 1855, 1856, 1857, 1858, 1859, 1860, 1861, 1862, 1863, 1864, 1865, 1866, 1867, 1868, 1869, 1870, 1871, 1872, 1873, 1874, 1875, 1876, 1877, 1878, 1879, 1880, 1881, 1891, 1900, 1901, 1902, 1903, 1904, 1905, 1906, 1907, 1908, 1909, 1910, 1911, 1912, 1913, 1914, 1915, 1916, 1917, 1918, 1919, 1920, 1921, 1922, 1923, 1924, 1925, 1926, 1927, 1928, 1929, 1930, 1931, 1932, 1933, 1934, 1935, 1936, 1937, 1938, 1939, 1940, 1941, 1942, 1943, 1944, 1945, 1946, 1947, 1948, 1949, 1950, 1951, 1952, 1953, 1954, 1955, 1956, 1957, 1958, 1959, 1960, 1961, 1962, 1963, 1964, 1965, 1966, 1967, 1968, 1969, 1970, 1971, 1972, 1973, 1974, 1975, 1976, 1977, 1978, 1979, 1980, 1982, 1996, 1997, 1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2082, 2086, 2083, 2084, 2085, 2087, 2088, 2081, 2090, 2092, 2093, 2094, 2095, 2096, 2098, 2099, 2100, 2101, 2103, 2104, 2105, 2106, 2107, 2108, 2109, 2110, 2111, 2112, 2113, 2114, 2115, 2116, 2117, 2118, 2119, 2120, 2121, 2122, 2124, 2125, 2126, 2127, 2128, 2129, 2130, 2131, 2132, 2133, 2134, 2135, 2136, 2138, 2139, 2140, 2141, 2142, 2143, 2144, 2145, 2146, 2147, 2148, 2149, 2150, 2151, 2152, 2153, 2154, 2155, 2156, 2157, 2158, 2159, 2160, 2161, 2162, 2163, 2164, 2165, 2166, 2167, 2168, 2169, 2170, 2171, 2172, 2173, 2174, 2175, 2176, 2177, 2178, 2179, 2180, 2181, 2182, 2183, 2184, 2185, 2186, 2187, 2188, 2189, 2190, 2191, 2192, 2193, 2194, 2195, 2196, 2197, 2198, 2199, 2200, 2201, 2202, 2203, 2204, 2223, 2208, 2213, 2226, 2206, 2227, 2230, 2229, 2228, 2231, 2240, 2239, 2241, 2234, 2232, 2233, 2236, 2237, 2242, 2218, 2220, 2217, 2211, 2249, 2225, 2250, 2214, 2224, 2251, 2252, 2253, 2260, 2259, 2256, 2261, 2262, 2267, 2266, 2255, 2268, 2270, 2269, 2274, 2286, 2292, 2293, 2294, 2295, 2296, 2297, 2301, 2254, 2307, 2308) and `product_services`.`sku` LIKE '%1323%' order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "165", "450", "451", "452", "454", "455", "1388", "678", "691", "1634", "242", "262", "244", "263", "239", "238", "251", "243", "255", "254", "253", "257", "453", "430", "411", "448", "445", "968", "439", "427", "406", "442", "440", "444", "200", "385", "433", "441", "423", "386", "387", "384", "1199", "429", "443", "403", "1343", "1705", "1694", "1696", "120", "812", "813", "1054", "1325", "1707", "1702", "307", "1704", "1700", "1701", "1698", "1699", "1709", "1697", "1692", "1693", "1703", "1653", "479", "1285", "1282", "1550", "1280", "476", "1654", "1652", "1650", "477", "1363", "1286", "1283", "426", "904", "1255", "1038", "1256", "1027", "1040", "1449", "1023", "538", "1049", "541", "542", "506", "1156", "513", "512", "1513", "1512", "345", "324", "351", "518", "905", "1511", "515", "1160", "507", "347", "337", "1176", "339", "516", "1118", "1113", "1114", "1297", "1302", "1517", "693", "431", "1628", "954", "1444", "977", "972", "981", "495", "976", "1034", "1415", "760", "499", "498", "1405", "1407", "1408", "548", "491", "7", "502", "482", "500", "496", "485", "484", "497", "1181", "1436", "1712", "510", "330", "346", "505", "348", "1157", "1441", "349", "341", "1265", "326", "350", "328", "361", "362", "359", "357", "358", "353", "356", "360", "352", "1042", "1039", "614", "1717", "1716", "1715", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727", "1728", "1730", "1731", "1732", "536", "228", "1733", "1734", "747", "1809", "227", "236", "1780", "842", "1782", "1779", "1788", "221", "1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819", "1820", "1821", "852", "1822", "1823", "836", "643", "1824", "866", "1825", "1826", "1827", "1828", "1829", "1830", "1831", "662", "663", "1798", "1832", "633", "628", "225", "631", "632", "634", "630", "1833", "1775", "325", "699", "1834", "1835", "1836", "1838", "408", "1839", "1840", "1068", "952", "985", "982", "983", "1358", "1359", "959", "1191", "1671", "1348", "753", "751", "754", "757", "1810", "761", "758", "597", "1892", "1893", "1894", "1899", "1898", "1897", "1896", "1895", "6", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "47", "60", "62", "63", "64", "65", "66", "67", "68", "1983", "1115", "1984", "1111", "1985", "1986", "1116", "1663", "1110", "1112", "1987", "1312", "1323", "1988", "1515", "1247", "1147", "1989", "1376", "1990", "1991", "1533", "1335", "1992", "1293", "1338", "1252", "1993", "1495", "1455", "1655", "478", "668", "1467", "1543", "674", "1981", "530", "529", "528", "527", "526", "531", "532", "533", "534", "535", "669", "667", "679", "664", "1097", "1096", "1095", "1094", "1098", "1102", "1100", "1108", "1107", "1104", "1101", "1354", "1994", "1995", "1099", "2", "3", "46", "48", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "61", "69", "70", "71", "72", "73", "74", "75", "77", "76", "78", "79", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "110", "111", "112", "113", "114", "115", "116", "117", "118", "119", "121", "122", "123", "124", "125", "126", "127", "128", "129", "130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164", "166", "167", "168", "169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "190", "191", "192", "193", "194", "195", "196", "197", "198", "199", "201", "202", "203", "204", "205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219", "220", "222", "223", "224", "226", "229", "230", "231", "232", "233", "234", "235", "237", "240", "241", "245", "246", "247", "248", "249", "250", "252", "256", "258", "259", "260", "261", "264", "265", "266", "267", "268", "269", "270", "271", "272", "273", "274", "275", "276", "277", "278", "279", "280", "281", "282", "283", "284", "285", "286", "287", "288", "289", "290", "291", "292", "293", "294", "295", "296", "297", "298", "299", "300", "301", "302", "303", "304", "305", "306", "308", "309", "310", "311", "312", "313", "314", "315", "316", "317", "318", "319", "320", "321", "322", "323", "327", "329", "331", "332", "333", "334", "335", "336", "338", "340", "342", "343", "344", "354", "355", "363", "364", "365", "366", "367", "368", "369", "370", "371", "372", "373", "374", "375", "376", "377", "378", "379", "380", "381", "382", "383", "388", "389", "390", "391", "392", "393", "394", "395", "396", "397", "398", "399", "400", "401", "402", "404", "405", "407", "409", "410", "412", "413", "414", "415", "416", "417", "418", "419", "420", "421", "422", "424", "425", "428", "432", "434", "435", "436", "437", "438", "446", "447", "449", "456", "457", "458", "459", "460", "461", "462", "463", "464", "465", "466", "467", "468", "469", "470", "471", "472", "473", "474", "475", "480", "481", "483", "486", "487", "488", "489", "490", "492", "493", "494", "501", "503", "504", "508", "509", "511", "514", "517", "519", "520", "521", "522", "523", "524", "525", "537", "539", "540", "543", "544", "545", "546", "547", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "629", "635", "636", "637", "638", "639", "640", "641", "642", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "665", "666", "670", "671", "672", "673", "675", "676", "677", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "692", "694", "695", "696", "697", "698", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "748", "749", "750", "752", "755", "756", "759", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "837", "838", "839", "840", "841", "843", "844", "845", "846", "847", "848", "849", "850", "851", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862", "863", "864", "865", "867", "868", "869", "870", "871", "872", "873", "874", "875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "953", "955", "956", "957", "958", "960", "961", "962", "963", "964", "965", "966", "967", "969", "970", "971", "973", "974", "975", "978", "979", "980", "984", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1024", "1025", "1026", "1028", "1029", "1030", "1031", "1032", "1033", "1035", "1036", "1037", "1041", "1043", "1044", "1045", "1046", "1047", "1048", "1050", "1051", "1052", "1053", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1103", "1105", "1106", "1109", "1117", "1119", "1120", "1121", "1122", "1123", "1124", "1125", "1126", "1127", "1128", "1129", "1130", "1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140", "1141", "1142", "1143", "1144", "1145", "1146", "1148", "1149", "1150", "1151", "1152", "1153", "1154", "1155", "1158", "1159", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1177", "1178", "1179", "1180", "1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1192", "1193", "1194", "1195", "1196", "1197", "1198", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246", "1248", "1249", "1250", "1251", "1253", "1254", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1266", "1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1281", "1284", "1287", "1288", "1289", "1290", "1291", "1292", "1294", "1295", "1296", "1298", "1299", "1300", "1301", "1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1324", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333", "1334", "1336", "1337", "1339", "1340", "1341", "1342", "1344", "1345", "1346", "1347", "1349", "1350", "1351", "1352", "1353", "1355", "1356", "1357", "1360", "1361", "1362", "1364", "1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374", "1375", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387", "1389", "1390", "1391", "1392", "1393", "1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1406", "1409", "1410", "1411", "1412", "1413", "1414", "1416", "1417", "1418", "1419", "1420", "1421", "1422", "1423", "1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435", "1437", "1438", "1439", "1440", "1442", "1443", "1445", "1446", "1447", "1448", "1450", "1451", "1452", "1453", "1454", "1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476", "1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1514", "1516", "1518", "1519", "1520", "1521", "1522", "1523", "1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542", "1544", "1545", "1546", "1547", "1548", "1549", "1551", "1552", "1553", "1554", "1555", "1556", "1557", "1558", "1559", "1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602", "1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613", "1614", "1615", "1616", "1617", "1618", "1619", "1620", "1621", "1622", "1623", "1624", "1625", "1626", "1627", "1629", "1630", "1631", "1632", "1633", "1635", "1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643", "1644", "1645", "1646", "1647", "1648", "1649", "1651", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1672", "1673", "1674", "1675", "1676", "1677", "1678", "1679", "1680", "1681", "1682", "1683", "1684", "1685", "1686", "1687", "1688", "1689", "1690", "1691", "1695", "1706", "1708", "1710", "1711", "1713", "1714", "1729", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761", "1762", "1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771", "1772", "1773", "1774", "1776", "1777", "1778", "1781", "1783", "1784", "1785", "1786", "1787", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807", "1808", "1811", "1837", "1841", "1842", "1843", "1844", "1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855", "1856", "1857", "1858", "1859", "1860", "1861", "1862", "1863", "1864", "1865", "1866", "1867", "1868", "1869", "1870", "1871", "1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1891", "1900", "1901", "1902", "1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911", "1912", "1913", "1914", "1915", "1916", "1917", "1918", "1919", "1920", "1921", "1922", "1923", "1924", "1925", "1926", "1927", "1928", "1929", "1930", "1931", "1932", "1933", "1934", "1935", "1936", "1937", "1938", "1939", "1940", "1941", "1942", "1943", "1944", "1945", "1946", "1947", "1948", "1949", "1950", "1951", "1952", "1953", "1954", "1955", "1956", "1957", "1958", "1959", "1960", "1961", "1962", "1963", "1964", "1965", "1966", "1967", "1968", "1969", "1970", "1971", "1972", "1973", "1974", "1975", "1976", "1977", "1978", "1979", "1980", "1982", "1996", "1997", "1998", "1999", "2000", "2001", "2002", "2003", "2004", "2005", "2006", "2007", "2008", "2009", "2010", "2011", "2012", "2013", "2014", "2015", "2016", "2017", "2018", "2019", "2020", "2021", "2022", "2023", "2024", "2025", "2026", "2027", "2028", "2029", "2030", "2031", "2032", "2033", "2034", "2035", "2036", "2037", "2038", "2039", "2040", "2041", "2042", "2043", "2044", "2045", "2046", "2047", "2048", "2049", "2050", "2051", "2052", "2053", "2054", "2055", "2056", "2057", "2058", "2059", "2060", "2061", "2062", "2063", "2064", "2065", "2066", "2067", "2068", "2069", "2070", "2071", "2072", "2073", "2074", "2075", "2076", "2077", "2078", "2079", "2080", "2082", "2086", "2083", "2084", "2085", "2087", "2088", "2081", "2090", "2092", "2093", "2094", "2095", "2096", "2098", "2099", "2100", "2101", "2103", "2104", "2105", "2106", "2107", "2108", "2109", "2110", "2111", "2112", "2113", "2114", "2115", "2116", "2117", "2118", "2119", "2120", "2121", "2122", "2124", "2125", "2126", "2127", "2128", "2129", "2130", "2131", "2132", "2133", "2134", "2135", "2136", "2138", "2139", "2140", "2141", "2142", "2143", "2144", "2145", "2146", "2147", "2148", "2149", "2150", "2151", "2152", "2153", "2154", "2155", "2156", "2157", "2158", "2159", "2160", "2161", "2162", "2163", "2164", "2165", "2166", "2167", "2168", "2169", "2170", "2171", "2172", "2173", "2174", "2175", "2176", "2177", "2178", "2179", "2180", "2181", "2182", "2183", "2184", "2185", "2186", "2187", "2188", "2189", "2190", "2191", "2192", "2193", "2194", "2195", "2196", "2197", "2198", "2199", "2200", "2201", "2202", "2203", "2204", "2223", "2208", "2213", "2226", "2206", "2227", "2230", "2229", "2228", "2231", "2240", "2239", "2241", "2234", "2232", "2233", "2236", "2237", "2242", "2218", "2220", "2217", "2211", "2249", "2225", "2250", "2214", "2224", "2251", "2252", "2253", "2260", "2259", "2256", "2261", "2262", "2267", "2266", "2255", "2268", "2270", "2269", "2274", "2286", "2292", "2293", "2294", "2295", "2296", "2297", "2301", "2254", "2307", "2308", "%1323%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1252}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.643007, "duration": 0.01294, "duration_str": "12.94ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1252", "source": "app/Http/Controllers/ProductServiceController.php:1252", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1252", "ajax": false, "filename": "ProductServiceController.php", "line": "1252"}, "connection": "kdmkjkqknb", "start_percent": 63.781, "width_percent": 31.203}, {"sql": "select * from `product_service_units` where `product_service_units`.`id` in (6)", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1252}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.673196, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1252", "source": "app/Http/Controllers/ProductServiceController.php:1252", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1252", "ajax": false, "filename": "ProductServiceController.php", "line": "1252"}, "connection": "kdmkjkqknb", "start_percent": 94.984, "width_percent": 1.085}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '9' and `product_id` = 331 limit 1", "type": "query", "params": [], "bindings": ["9", "331"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 267}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.674849, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:267", "source": "app/Models/ProductService.php:267", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=267", "ajax": false, "filename": "ProductService.php", "line": "267"}, "connection": "kdmkjkqknb", "start_percent": 96.069, "width_percent": 3.279}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1284}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.677499, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 99.349, "width_percent": 0.651}]}, "models": {"data": {"App\\Models\\WarehouseProduct": {"value": 2241, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}}, "count": 2245, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1303765096 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1303765096\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.61949, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-156200228 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-156200228\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-912659903 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1323</span>\"\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-912659903\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-852947639 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-852947639\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1442983698 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9wcmRRMitmc0NGekd0SGtTaG9SUEE9PSIsInZhbHVlIjoiOE82by9PYlF4eXhvMFdsUEptZ3dKdEx4RzZlNWYycFo3cERISmhLZkFsQ1BxdmVNNGZDQWo0OGdYakF5akZYVjZqbGNJenl0eVI5UE1lc1hnNEVVZkVpQ0pWTjUxWWNGTWVER3JNZFc4bkVzRzE2UFdhNEo2QytkYlptdE5SLyt5TGtUTkttTXJPRjNpSEVUbEdPaDBnclB2b0tnTHo2d2VPYkRjcjF4YzNUSnJSM0QvVjlXb2t2RTl0MUZlb3RlWTJiNFdOMDRnaTFwQzZqS1h5MjBYL0d3RGdaSHNkUmVBVmRpcVRzWWNyMXFBb2hQQkZZRGdSZDhId0tHbFczYlBEb1NWWXV3NHNvRGxDcXB5NENST2Q0SGRkK2lJVno3bGE2WTNuODlzNDNnWFc0bEROcWFNWHk5Ujd4MXZ4KzViZUtJNDdJNzJib3U0OGFubUtleWhnQlAzT1VjTEt3MHVSc1JkTU56SEYrQi9Ra2Y4ZEp2WnJDY2lldmRibVVqNWRYNW9wUU9wQUtmRjFQRHB2UC94aWZNWE1jMlo0YXZIQUlxbmdhNjYzbnlRSWlkTkwvanpWZXRDVFJYNEpCbjJCYUNKb0R4Q0lGVUtYUXJWWWZTYWJWYkZhcG5uZEcranZTVlQ4cE5EbUZiRHpiaHF0UnM5YnBMd1JFNStoUUciLCJtYWMiOiJkZWNlZTExMTM0NjJmYzNiY2Y5MmIxYjBiOWIzNDg5MzA2OGNmMWQ1ZTVmMGM3NzYyMmVmN2RhMDE4ZTI2YzkyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlFrRDh5bVRrWCsrWHpvMG5BK1F0ZHc9PSIsInZhbHVlIjoiVGRCc0JtK2c2YlF0SGdXZWtaanQvRm5HdWFwWTUxdjl4eFYrRGQ0bCttTHQ3ZjJ4czd4NjB6amw3ekFrOGQ1YjlGclljaGRYbUVkL0FhUGNFVkxvbElmSEhDbk1KV0RPV0RaYThIUlMvTC9BdXFPTlBORmRrbDZ4WjdhekJibzVVQloyZGFvLzVWMno5VWVSTFZRcWdiY2pGS3MxSVNtdGlHV1BmeFlSOUVUZlJ0dGsvakNxZm1pMjI5QjZxRTNYbUdmUFY1SjU1UGM5VDNXbWlFckhGb2VRaWhmNU1EL1Excjd5eUNZdHBYd1FuWFg0dldEUi9lcEJtcStRdWdZSHVSYnlQT0l4OWorbkw2cW9zeGtjeUdNK2kxdlRtZ1lJY2kvYTB5NkpPaCtaczIvVHZsMXpkQm9CdzhBWDVubWRtV1VPcWZFNFZqblBaNVFIdUw5T2V6VFdyNm82UVFVbnhBM0IvaWM4MkYzdUxtcXl2cWplZjhkWVJlZW12UzlZSFlFQk9lVU1VUjJLb3J6TUtlTDN2NVNEbWhqYytHdWpBRFcrL2RzUzEzN3FjYWowVkNka1VrWHYrR1Q4dnFvRmlkV2tkZEYyRnNIRExJcDBoblJGSStlbjhOVWdXSlNLYzhab3M0S2FxRUg2TndJS3BLdi80Y0FTL0dSckVPc0ciLCJtYWMiOiI2MTBiZmIzYjM1YjljMDc0ZjdmMDg3ODY5OWMwM2ZiN2VkZjRiMDE4NWRhMmM0NzRhZGUxMmJjNjQ3ZDljOGI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1442983698\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1743788052 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1743788052\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-904884784 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:32:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZBbzFTSEthYnptRXlGeTV1eFRUbGc9PSIsInZhbHVlIjoiajhjUlVPdjdFM2J5SUE4bXh1UFF4eGgzRGR3ZUUvOWp4TThCV2dYbHFRWFBad2llRXRBVWJmVUtrSXBDZWdxWnNmVmoxa1h5MitNSXJIaTErUE9MUjE4NHUyb0daOE54aGxCU0d3eVdHZmQzdzBpcnJIc1Y4dGViTUt0dHE2akY1czRqV2cyOW4rOGFSRHM1WlJFdDFLZFN2dENCeFd3Uy81eXoxTnVnUGpjL05kcXV1MHZ0d05pSGRwcjRZd1VTdHJUM3Y0UGpwb0UrK2FaU1lyK1YycUlhL0tyTWZrUExOL3MxVVQ0TW5XWjBNN1A3S0llcURzSi9wZXFWZmgrVFAwNTBYYUwrWnoyZzdGYlhPVk9Ja0phMjJtdW5aOUgwVjB3ZFNKZHdSV2pYdzF1cG9MVFNvM0JVdWQyblJJNjNYbVBZUHNlTG1aYjMxNnpTNC82OVN0c0VvK05BUk1ndWduZUpzWmVYMzloN3NSS1YveGpLL2JDTVlMSC9yZDZQK0ZwM0FUeWVwUXJ4VXdUTG0rSS9PZVJZa2NLYzhzaFZIdzE5dThETllldSs3b3M2NFhyb3lad3NlbG5XRFdZT3FKU0Q1alZOL3JFa2FvbURSS3BBaFRYQXNhYU1zRU8wK3g5V1dJbFVYTVRFMlV0L3c0aDBmaUptWk0rQU01anMiLCJtYWMiOiI1ODM5NGZhZDZiMDE4MmMzOWFjN2ZlYTYyNDMyMjY0ZTcwZjMxYjk2MDRjOWQ0ZDRmNGU0NmIxZGE2M2MxMzZjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:32:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik43Q1ozcDBJU0dpbCtnWXR6VXZnQUE9PSIsInZhbHVlIjoiMUFyeFcrNDVHM216Nm9KVjhtOTc5ckF4eVJKdVJHS1IvdE1ZWHhmNmtZdThWVFNDNVBDUUZmRFRSbHNRM0hNdmdObGNsSVE5emVBWjNNMFNhR1dzRmJTbTArNG15ekRFay9rZjBsRm1WOGZGWEVpS3dJQzRLaVQyRUczdTZDNlhGMm1ld1E1djVpUUJXSDc4b0p0NllUVDd6RmhNWjZtcEdsQUhwekMxb1JJUitJOEE5Mm9icTF2WjdkT2VXWFhJRitkZVlFQjZkdllaNjRxT1Vsb05iaG9YQUphZ0F0cENmL0JOTE9XQ3IwRzdwVStLaEl4blZFVGJ4MVpmM0hkREg1c0hvV1pDRWxtdkFna3FjclFScC9jU2dVNUxHNUtlM1lrdGNyZkk5dG1yRXd6WTcrc1VRcmlnb1RFdDRZVE9WT0k0YTZUZUUwYmVlTDJPK1lYL216UUc0VVNGN0ZPS0FURVE4WmZrTkx6SHh5ci9VZWhaYmZSMTBMS29tYnlSSlIraGdxNnpFOGZjWlZ4RFIyOTJPaHVMQ3h4NUQ5N2pVSGpTQUFGS04yUW14Vm9BQmhoWGN4ZGY4TXd6eHM2Z0JuMlRweXczWW04aytrbmx6dFVOa1pYYTBBcUtjc2FKdXZBelpDQW1qeFpEZ29YQTQzOEw3TmZxOEFrNDFnYU8iLCJtYWMiOiI3NmVjMzMwNzVkMGZiNzM4NGUwYjQzNTE2NjRkNDUwNTJiNGM2MmFiZGYzYjg2MGY3ZmYxNjdjNjc0NTE4Y2VjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:32:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZBbzFTSEthYnptRXlGeTV1eFRUbGc9PSIsInZhbHVlIjoiajhjUlVPdjdFM2J5SUE4bXh1UFF4eGgzRGR3ZUUvOWp4TThCV2dYbHFRWFBad2llRXRBVWJmVUtrSXBDZWdxWnNmVmoxa1h5MitNSXJIaTErUE9MUjE4NHUyb0daOE54aGxCU0d3eVdHZmQzdzBpcnJIc1Y4dGViTUt0dHE2akY1czRqV2cyOW4rOGFSRHM1WlJFdDFLZFN2dENCeFd3Uy81eXoxTnVnUGpjL05kcXV1MHZ0d05pSGRwcjRZd1VTdHJUM3Y0UGpwb0UrK2FaU1lyK1YycUlhL0tyTWZrUExOL3MxVVQ0TW5XWjBNN1A3S0llcURzSi9wZXFWZmgrVFAwNTBYYUwrWnoyZzdGYlhPVk9Ja0phMjJtdW5aOUgwVjB3ZFNKZHdSV2pYdzF1cG9MVFNvM0JVdWQyblJJNjNYbVBZUHNlTG1aYjMxNnpTNC82OVN0c0VvK05BUk1ndWduZUpzWmVYMzloN3NSS1YveGpLL2JDTVlMSC9yZDZQK0ZwM0FUeWVwUXJ4VXdUTG0rSS9PZVJZa2NLYzhzaFZIdzE5dThETllldSs3b3M2NFhyb3lad3NlbG5XRFdZT3FKU0Q1alZOL3JFa2FvbURSS3BBaFRYQXNhYU1zRU8wK3g5V1dJbFVYTVRFMlV0L3c0aDBmaUptWk0rQU01anMiLCJtYWMiOiI1ODM5NGZhZDZiMDE4MmMzOWFjN2ZlYTYyNDMyMjY0ZTcwZjMxYjk2MDRjOWQ0ZDRmNGU0NmIxZGE2M2MxMzZjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:32:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik43Q1ozcDBJU0dpbCtnWXR6VXZnQUE9PSIsInZhbHVlIjoiMUFyeFcrNDVHM216Nm9KVjhtOTc5ckF4eVJKdVJHS1IvdE1ZWHhmNmtZdThWVFNDNVBDUUZmRFRSbHNRM0hNdmdObGNsSVE5emVBWjNNMFNhR1dzRmJTbTArNG15ekRFay9rZjBsRm1WOGZGWEVpS3dJQzRLaVQyRUczdTZDNlhGMm1ld1E1djVpUUJXSDc4b0p0NllUVDd6RmhNWjZtcEdsQUhwekMxb1JJUitJOEE5Mm9icTF2WjdkT2VXWFhJRitkZVlFQjZkdllaNjRxT1Vsb05iaG9YQUphZ0F0cENmL0JOTE9XQ3IwRzdwVStLaEl4blZFVGJ4MVpmM0hkREg1c0hvV1pDRWxtdkFna3FjclFScC9jU2dVNUxHNUtlM1lrdGNyZkk5dG1yRXd6WTcrc1VRcmlnb1RFdDRZVE9WT0k0YTZUZUUwYmVlTDJPK1lYL216UUc0VVNGN0ZPS0FURVE4WmZrTkx6SHh5ci9VZWhaYmZSMTBMS29tYnlSSlIraGdxNnpFOGZjWlZ4RFIyOTJPaHVMQ3h4NUQ5N2pVSGpTQUFGS04yUW14Vm9BQmhoWGN4ZGY4TXd6eHM2Z0JuMlRweXczWW04aytrbmx6dFVOa1pYYTBBcUtjc2FKdXZBelpDQW1qeFpEZ29YQTQzOEw3TmZxOEFrNDFnYU8iLCJtYWMiOiI3NmVjMzMwNzVkMGZiNzM4NGUwYjQzNTE2NjRkNDUwNTJiNGM2MmFiZGYzYjg2MGY3ZmYxNjdjNjc0NTE4Y2VjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:32:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-904884784\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1258161036 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1258161036\", {\"maxDepth\":0})</script>\n"}}