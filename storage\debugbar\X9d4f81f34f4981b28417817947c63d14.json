{"__meta": {"id": "X9d4f81f34f4981b28417817947c63d14", "datetime": "2025-06-08 00:04:39", "utime": **********.14016, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749341078.439782, "end": **********.140195, "duration": 0.7004129886627197, "duration_str": "700ms", "measures": [{"label": "Booting", "start": 1749341078.439782, "relative_start": 0, "end": **********.038109, "relative_end": **********.038109, "duration": 0.5983271598815918, "duration_str": "598ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.038124, "relative_start": 0.5983421802520752, "end": **********.140199, "relative_end": 4.0531158447265625e-06, "duration": 0.10207486152648926, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45039936, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02537, "accumulated_duration_str": "25.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.075967, "duration": 0.023289999999999998, "duration_str": "23.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.801}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.113482, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.801, "width_percent": 3.626}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.125745, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.428, "width_percent": 4.572}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-461145270 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-461145270\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1797140103 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1797140103\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-295897680 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-295897680\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-297419452 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749341040746%7C26%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilk5enNReHhOM2kyd0ZOUzZ0YVlwZ1E9PSIsInZhbHVlIjoiUEFLY01vaUorTzBEOXM2N2xqcHVJSHZ5bzhMeHVtUzZ2OG9xZHRzREt3UnBJWFo4S1BoTFNaZE16OXNVeWdJb0xNSXFlRjVUYjRyLzdRbHFHTGF6MHdmcjBla3haTHJEVDVUSC8zOFc3M0JiM0k0ZWtVTDI0K25DRmpzdThXV280cDlPZ21PT0c2b1BvNHBXUTVFSXBNUTR4MHd3ZEo2eTJyenJyem5lRVdPaHU1RWgxbU5aQWt3YkFWNythVlBnNXhCbmU5RnYzd2RXcExlckcwalhBVm00SXpKVCtsNFcrQnc1MmY2TzgxVlBWY1NuNzNBbURXazlPK24zZDlGMVJjMEF2aVdNa2F0ZUxiNmhCcVBGdVgyRkZXSGE2V3RuZmpqbHFNb2s5VUx3ZHltdFptTzdhTG1nT2xYTld4cjdEYWtNOGlVb0R1a1JMYS81bVduckw2cjZSZDVwSUYvYlUreGRrbG5xa0lmUjhCa3VFVUxYbThPZGxiK2hQQzlmYkhPd3cvd3VoWVFUY21hNlZLUUxkM0J3ZDgrZ2lBNWpYT0RPWHVjdnVoOFpwZFdxandzSFE0MzVwNEE5NkVZdXE5NDE1UG1MK3lHSkNWclJveXA3Sm52Y2prTUx3ZFJwSUpmQVAzdGYzYTdEVCsvZkR4alp4dU40cnVwTVI2RFciLCJtYWMiOiI1MzI3ZjRjODlmZjRkMjA4NGQ4MjdlMDFiNDY3OGY1NjYyYTA1NThmYTE3MzFjNWFkNGY4MDRhYzM1NmJhYTRiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InhMbE1KRk5YVSs5N2J0MHYxSWF0YUE9PSIsInZhbHVlIjoiZW51NnV1T0wvSW1Zam1mSXhjRHE2VG9iZ04rL0dCWkdsZ0NQVnRSNSt1N0V3Y2lyQ1pqRHhTVU5Xend2SnhyQlUxbmxwMloyclhOaGpnYUErVkJ2eWtuQ1Zqd0JWTStOVEpVVkNDeElHTVlDTHpITUtoQTh4Z2ttQkpneUdjM0tvYkxiL2VUdzFZOGJHQkI0aGpFVGdpajlDOW41Y2NxdnF5RytrOGV1U2RwVDQ3cVlLM1Zic29hN1NkcFNRWGxmRWtDdmloVEthbmxlSXorSVFTN1h1NmhIZk9mOHZPdmhiT2hzUUFqQVJqZzJGZ25vRmFZVjhMTmg4OG1XNDJsYzk5OGtnQ2pzb0VPUlhKNE1VNWFOeldtVlJEdWx5Z3ZGQVVDUnZxcGFKVkVKeFNOQjFkNERYcVRYWCtTOEFqM0kvbUVMSi9waFFOUEhoT0dXd3BTdEwrVzgwMkJ3akVZbXF5eXR3QS9pZm4xenlBa1d2WWxmOGlNSkZyZ3JEVW5vVUJCWWRCMG1RTkhGTU85UzNsbFdieTVtenZEOW1oaW9xdVZscUg1UUs5cTBNNEM1VFpNYklsa2VHZUxYZTRQRTdYWWdyREhobXpMVVo2VEJHa0lOc3hwTlF2ZENUeXk5dkFrWE4wMUtseVQxRHFia3Vvc093RmVhbExod1VTVGgiLCJtYWMiOiIzNmViNDJmNWQ0NTJlZmYzMzNjN2IwZjUzNTgzYjdlNWY2MjM4N2YyZTVjNDk5NGU3NmRhN2Q0Mjc0NGUzOWM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297419452\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1627166772 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627166772\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:04:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVpU2pYY1l1dDR1MERNUGJPTzFFV3c9PSIsInZhbHVlIjoiMHc2aDhWMUtMQTFpUm43eU9iM2xCbnltVUtkWExVeVVOend1ZGZpRk9WaU9TN0dyUmxvSlFjVlRmWXM2UUVacU5ac3FxRjNqOUlsUzlyUTJQUzlGK0ZyWVhydDViSTdvSGZsdEhZek81Z1FzMExicXc5VTNENWI3bStyNWtBQUIzTzY1Qno3NnlHajQ4aHUwODFxWGtEUzZ3QnNhaXhaOVoxTCtoL0tlOC9xSnJOcU9Tb2dKNTg4M2hzZ2xVK2RQYU4zTEVieVZBVGsrWFVKQWkySTBiZnFFRllkZXVjdi9yZ1FaQmZZMjlnWFAwa0JVbWZrSnpsK3JjdGxDaW5uSFBMcGVJQjNMRElKd1RjTnErZUpuZXlUUHYxalJVaktLbDFUczhyQ0Y1czdBVW42UDFieXU4dmhWcmN2S0c2ZDFkRUJQbWN2VHlDV1FGODd5V1MyWXAxTEtGeXJURWE5blhWSTgvVEUvbWdObUcvMjhTNDZYNnM3UkpaVHZrdVcxb0Z5aVFjeVJObUxvNG5mOUl4OCszUWdWcHZFbnVwWDZCZVNURjZxRno0REN3b1V1clZreWY3QitVSFh3MGQvRFA2WmU0a2NmaERYeHI4VXhZencrT2NQR0N1WDg2MW9RMmUrd0trY1JRVXNPOE1RSDFKYXhnV2lhYThiZlZmWXMiLCJtYWMiOiI1Njg5MmE4YTk5M2VmYmNiY2QzOTI3ZGY1NTc1ZTA1N2Y5ODYxYTJmOWYzMDkxNDE1NDViNDNhOGIyYzBjMDU0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:04:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IldVMzFlZlhiM1VQT0Z5Rk94Q3p4OUE9PSIsInZhbHVlIjoiNDJTZXNSR2tTUXdORFhxZGk5SGxwWDdtREVTaU0vYU9tVkl2K1J5VFRObC9kWGxNTjJVZHJqbERTeE03OHR4ejBXREwzbWk0NVltS2Y3ai9NSUtlUHNidnNkdXVnSE5pK1FJdnBFZVNISXhDQm1MLzRmUHBHbXprL3lYNlFIeEFQTmN2aTlablVJQ25KZlc2dGtQME5iUmZnT0FOK2RDWGF6QU9BRXNxK3dNZXhuR1lwRlAzclVRcnpNcjZZMDZreWlFdDhkNytUSW9jY0FVSW04anRTeFd4My9VZUtnVW9MSXFva0dWRjI5OXl6WUFDaWdxc1l3N3V6cEExdE10Mmt5bU1BSXhwS2xuS001NmpieE9nMUNyTk5CUzRHNHNOZ0xBSndkTHA4R2RJUFBXRm8wa3g0NVZ2MnpQUXkxdVRZdW5zZG91MUE2VXNIRDNOVnZBSzRRaGJiMHRQcjZrWEhDM3ROQ3ZIRUpYbis1RCtoNlgwcFphMDJNZjIzMVpHV2tQbWNCMlpqdmhKblFlSHFPUmJwV2VPREZDWVlFWUFhL0hFVUV3M3JUL0tYSFM3U0pUL043L0lZdzJJQjBlUFNMQTh3V3ZvZVlCOHBHeXVaSlIrb1Y1ZVVKYWFEVGhDdHp4OHZCKzM2UHJGZC9tRFNEUjluck9ldGpQaHF3SVciLCJtYWMiOiIwYjNjMWRiNTAxODdmNjIyZWE5YjA2ZjhmOGIwMDg4YmNkYzMyODFjZWRiYWFhNDUyNzQ0ZDJlMjNhZWQ4ZDcwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:04:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVpU2pYY1l1dDR1MERNUGJPTzFFV3c9PSIsInZhbHVlIjoiMHc2aDhWMUtMQTFpUm43eU9iM2xCbnltVUtkWExVeVVOend1ZGZpRk9WaU9TN0dyUmxvSlFjVlRmWXM2UUVacU5ac3FxRjNqOUlsUzlyUTJQUzlGK0ZyWVhydDViSTdvSGZsdEhZek81Z1FzMExicXc5VTNENWI3bStyNWtBQUIzTzY1Qno3NnlHajQ4aHUwODFxWGtEUzZ3QnNhaXhaOVoxTCtoL0tlOC9xSnJOcU9Tb2dKNTg4M2hzZ2xVK2RQYU4zTEVieVZBVGsrWFVKQWkySTBiZnFFRllkZXVjdi9yZ1FaQmZZMjlnWFAwa0JVbWZrSnpsK3JjdGxDaW5uSFBMcGVJQjNMRElKd1RjTnErZUpuZXlUUHYxalJVaktLbDFUczhyQ0Y1czdBVW42UDFieXU4dmhWcmN2S0c2ZDFkRUJQbWN2VHlDV1FGODd5V1MyWXAxTEtGeXJURWE5blhWSTgvVEUvbWdObUcvMjhTNDZYNnM3UkpaVHZrdVcxb0Z5aVFjeVJObUxvNG5mOUl4OCszUWdWcHZFbnVwWDZCZVNURjZxRno0REN3b1V1clZreWY3QitVSFh3MGQvRFA2WmU0a2NmaERYeHI4VXhZencrT2NQR0N1WDg2MW9RMmUrd0trY1JRVXNPOE1RSDFKYXhnV2lhYThiZlZmWXMiLCJtYWMiOiI1Njg5MmE4YTk5M2VmYmNiY2QzOTI3ZGY1NTc1ZTA1N2Y5ODYxYTJmOWYzMDkxNDE1NDViNDNhOGIyYzBjMDU0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:04:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IldVMzFlZlhiM1VQT0Z5Rk94Q3p4OUE9PSIsInZhbHVlIjoiNDJTZXNSR2tTUXdORFhxZGk5SGxwWDdtREVTaU0vYU9tVkl2K1J5VFRObC9kWGxNTjJVZHJqbERTeE03OHR4ejBXREwzbWk0NVltS2Y3ai9NSUtlUHNidnNkdXVnSE5pK1FJdnBFZVNISXhDQm1MLzRmUHBHbXprL3lYNlFIeEFQTmN2aTlablVJQ25KZlc2dGtQME5iUmZnT0FOK2RDWGF6QU9BRXNxK3dNZXhuR1lwRlAzclVRcnpNcjZZMDZreWlFdDhkNytUSW9jY0FVSW04anRTeFd4My9VZUtnVW9MSXFva0dWRjI5OXl6WUFDaWdxc1l3N3V6cEExdE10Mmt5bU1BSXhwS2xuS001NmpieE9nMUNyTk5CUzRHNHNOZ0xBSndkTHA4R2RJUFBXRm8wa3g0NVZ2MnpQUXkxdVRZdW5zZG91MUE2VXNIRDNOVnZBSzRRaGJiMHRQcjZrWEhDM3ROQ3ZIRUpYbis1RCtoNlgwcFphMDJNZjIzMVpHV2tQbWNCMlpqdmhKblFlSHFPUmJwV2VPREZDWVlFWUFhL0hFVUV3M3JUL0tYSFM3U0pUL043L0lZdzJJQjBlUFNMQTh3V3ZvZVlCOHBHeXVaSlIrb1Y1ZVVKYWFEVGhDdHp4OHZCKzM2UHJGZC9tRFNEUjluck9ldGpQaHF3SVciLCJtYWMiOiIwYjNjMWRiNTAxODdmNjIyZWE5YjA2ZjhmOGIwMDg4YmNkYzMyODFjZWRiYWFhNDUyNzQ0ZDJlMjNhZWQ4ZDcwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:04:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}