{"__meta": {"id": "X6ffa3aceccbc433f7d0fb428125126e9", "datetime": "2025-06-08 15:32:11", "utime": **********.637809, "method": "GET", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.024858, "end": **********.63783, "duration": 0.6129720211029053, "duration_str": "613ms", "measures": [{"label": "Booting", "start": **********.024858, "relative_start": 0, "end": **********.498754, "relative_end": **********.498754, "duration": 0.4738960266113281, "duration_str": "474ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.498767, "relative_start": 0.4739089012145996, "end": **********.637832, "relative_end": 1.9073486328125e-06, "duration": 0.13906502723693848, "duration_str": "139ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47708864, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@index", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=33\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:33-84</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.02837, "accumulated_duration_str": "28.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5470438, "duration": 0.02447, "duration_str": "24.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.253}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5828831, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.253, "width_percent": 3.701}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.589168, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:37", "source": "app/Http/Controllers/FinancialRecordController.php:37", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=37", "ajax": false, "filename": "FinancialRecordController.php", "line": "37"}, "connection": "ty", "start_percent": 89.954, "width_percent": 4.406}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 17 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.612585, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 94.36, "width_percent": 3.067}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.616172, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 97.427, "width_percent": 2.573}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 5, "messages": [{"message": "[ability => manage pos, result => null, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-826439493 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-826439493\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.623264, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1613196077 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1613196077\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.625161, "xdebug_link": null}, {"message": "[ability => manage pos, result => null, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2136901857 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2136901857\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.626356, "xdebug_link": null}, {"message": "[ability => manage pos, result => null, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-438281465 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-438281465\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.627565, "xdebug_link": null}, {"message": "[ability => manage delevery, result => null, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-115670730 data-indent-pad=\"  \"><span class=sf-dump-note>manage delevery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage delevery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-115670730\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.629086, "xdebug_link": null}]}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-347211621 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-347211621\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1996878242 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1996878242\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-120649796 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-120649796\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1015588741 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImNiYzkwakxlbFl5M0hmWXd6cnhmZWc9PSIsInZhbHVlIjoiaWxjU3hBK1J4cjlkSXBwVGZ2eXNWZz09IiwibWFjIjoiYmJkM2U2NzNiY2EzMmQyY2VlY2YyNjdhNmQxNjY4ZGM2NTgxOWJlMmVmMWJjZWUzNDYzYzA3MTViZjAzYzRhNCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749396672276%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxqQXZZc2JDaCtXSUkrS2FUQzVBOXc9PSIsInZhbHVlIjoiWENadWdHVTVFU3RsdGg4TVRkQ0xwOXdraFpiTlI4MUFjTUlodjNJM25UVXhzY01yajlUclJ0WVMxZGVKdTQyRnBuZkF1TTREbVVqVnZhRnJqd1RCRko5V213aStBM2c1UlVHNnhKSWpNVkx0QUZ6cTEybTExZW05VmE3Q09tbTlPRXptUlNySXpYWkFCMmc2d2NpWGY1ODl2QTVwRWU4R2F5TVYyTHBCN1VMbDR3eFR5Nm8yUC9aU0haNmx1MmZrNkJJVXM3MHN6SkdRUm5wTG5XdzhNS0NhYkxRVjd3OCtQbEh2Szl1WWFRMjdFdVh3aUF4a0NBNHRHaHFQKzAvMzg1TzRpaFU2RlUwTFF0cXZ1RU93MzREd243RkhHYXNtbDFYai9kdHk3VEc3S1JBd1p5NXBWcE54ZDNiRld6cE1ackx4VjJneEk4bkZiVk56VHBkVFNVcEVuR2lkcjQ3dmJMYlkyWU9uNVh4bTRZR3FUcUVhbnVxcTd3NlVXVkgyRW1ETW9BNVdqSUZhSDNGV24reVlBSXFVS1hDQTBQU0JTc1pDeXB6bWlnNTZlQS95Q2ZQZ2RjclFiTXhaUmFkbnkyUERGd2Q3V2dIWU1rRVl4S2RYTzc3SkF6WXZSSFZNak5TeG12T1RMVi9PVFdobnRyWXFrOFMybHNjOWtmWUkiLCJtYWMiOiJjYTk3MjM4Y2E3ZmRlMTQyZTUzOTEzMjQ4YjQyODQ2NGZiMzRmNDNhNjk3ZjI4MDI5NWJhMzNlMmQ5OGVhYjdhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikx1STdQTzFRc1JkV216VGZ6alBJWUE9PSIsInZhbHVlIjoiNm5yRXlIRjB5M2tuUnJQS1V4dy9iSnkvU1VDelQ1VGFFeWVsODdPM0V2dCs4UTR6MWVlVVpySGpNTW9QZzUrSmNzSzFtdVd4TTZLZFBtSGlIMVJITjd4eTZPWHIwcDBzeGNRb1ZjbWdRdzlnbXo2bXIxd2ZiZjRRd0c3cFJhM2QvdVlTc2tqbk8zaUV0Z1Fua01aSWdPZGZobWRwSzAxMXlnSlB4Y05LY3JXK29Cd3JydjQ3SkFMb2c1ak5UWC9kaFVLcVJzdXpuVHYxd2hmWm9DUWxSSU1RclJTMWpSck84dGIxOTRSNkk0b0xiSHNmK0taK25mZlBRL3YzUzN4ZFo2Z3NGelVvUWZJTDRzbnZDWWVncGErRUJNTnBtQ2M2d2k1dTJkZm9jTURIQWdlYzd6VnlHaWl6NlVPRzNlNDY2cVA5L1hvS1BreVJDRUJrWkFMRkxGUk1oVERBeTU0TEhDNmt4ZDFZTXhPNHJEVzBpa0lkaGdULzBCSzl6WXBwK3lIM2ViQ3lMTExQcUFRUENVNEN6dlMxR0VvbUM5UytEczRSWWFlNVZNS1BOVGkyYTJZT242OGJiREwzY1cxakhXZ3BBdnY1TmY0NlQxSkdMQ05WakhZTXpDS2JnZzl3MzlHalZZeit6VmV5N2U2Z2ZKOGFSNlZ6elR6TWJZbE8iLCJtYWMiOiIzY2EzNGE0OGFiZDM4YzE2MzMyOWRhNThhNjU2YmM3YTA1ZGRiZDZmNWQ4ODEyYjFmZjU1NzM5YTM3MDkxYTc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1015588741\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-345141436 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-345141436\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2097540898 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:32:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9ESzlPL3B3MGFIanJESTh5cHk3amc9PSIsInZhbHVlIjoiZnhJQXdDLzRCTDFDa0JCSitpQjU1ZVJhMEhBMytGL3d0bjZkd3FSZm1abW9GZUF6a0FVLzJpdkRNakhhVUxjTklWT3ZSck9XTm1KbVVNK3pvbkJ2YXh0REIvNzRHdlM3ay92NVhLRlJMaEFkM1VCUjFJRk1nZXZlT1A5bmRtS2srdFZORndDbTZ6T1RXc1ZMc2t3aHdHZTdsYnJUcFFsZG5CU1BqcnBXZ1dNakVhM2krblJUQ0xtb1lJc3RUQWQzVHRIeVJQdE5tekVlN2lEQmkyNDVUWEpqa3dqanNLUGFudW5oSitycmhYbVRRUmVKYzBudzE4OER5ZTVwb2w0dTBHZmFNM2ExcVY3K2xKQ3Y0WWxkS3lGQTN3ODZpNTdEeUFqZnVRN3hqbDR4alFORXdxZ3FsQ0pTMTE3NjFqVFNkS25FTS8xMDZ3a0ZSSWNnYjlURDZ1ZC9DVFNCSGZ3NEN2cm5iMWdXNXMrVTFHbFd5cVRhcUlIZkZ3WWc2ei9nVlNkVGU4K3Ewcm1rUUZlQVlEckFkbmJTYXVIM0hzREtnRmxJN3B0WVd1QmZvSlNhOVhnbDRlNEdBb3ZxVG40dHRhMk9EVVlzeDdzNDRDMVMyTDJxSzU5d3Y2aGVtYkxkM09nUFBaRERKR2NOMHNIMDBtcEs5WEtITk5Za3FQaWYiLCJtYWMiOiJiZDM0OTVkZDgwMzc4YjcwMTlmNTIyNDQ1MTNiOTVlYWRmYjEzZDc2MWRhMjA3NjhlNzkzYjQwZmUzZTMyZDJjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:32:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImI3eU5CWDZaNnl2V0JyNWJtaDUvL1E9PSIsInZhbHVlIjoiQ1g2aEJZK3dGd1UrMTM3TkozNHdXSzFpOU0xZ3RhdUFUYkkzZ2E2S0JpNkorNUpnU0tkTHA3aFhrZm5VMVgraUEwd08xK3h1WG1OS3ZUdTVhZDkrZi9RaFU0dHRqQjdGclVHc0pyejFkWXVvYnpac3pGbVNSQXZONFN5eWV5ek4yUmQ2R1dYdHFnWGlUZ3JEN3Jzb0ZDRlNSS25yTFNLNk9vdjlqczFIN2Z3SmRFcFV5UE5hZklpNFFzb01OcWhnVTJIaGJXYWJnOHpSWFNOTjlXZDdqWWQ1QndnZGJWRG53YXdQcG8rNTJmcVlzanhvam5QOVpHK0JUanB6cG8yZjE1UTcrMlVIYTF4aWlzYjJYZzhhZWZNQWI1WE5XOE9VTFpLY1NvNUl2d1pwOThMZENJTHRJcFlMTHdZZXRuWWpmNEZXMjZSY3JBRG53aEpMalV6djNGalJ4eHBKQUJ1TURSUjVYZU9IYVJ0SnlncFB3Tno3cHRvSXpveHlBc0VOS1FqRnhtSTNLSTFDY1hqR1JQUmVwNllQWG9SYm5FaExUZjB3Ynk3Ty84SloySjFuMGdTL0JOUnNHWEdLRkR3RmtvelZvYkpjbWxKejM1cktWbG5GS0c3aWFhNzVVTXZ5U3lVcWt1bVBNaFZhczBGVGlDeDZ5ODZTQlJ1UjN5NEMiLCJtYWMiOiI3OGUyNDEzZmQ3NDgyOTUxM2Y4ZDNkZDRjOGMzYmMzZTIxYTRjMGMwMTI4NGRmNzE0NmJlOGQxMWMyMjc4YmU3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:32:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9ESzlPL3B3MGFIanJESTh5cHk3amc9PSIsInZhbHVlIjoiZnhJQXdDLzRCTDFDa0JCSitpQjU1ZVJhMEhBMytGL3d0bjZkd3FSZm1abW9GZUF6a0FVLzJpdkRNakhhVUxjTklWT3ZSck9XTm1KbVVNK3pvbkJ2YXh0REIvNzRHdlM3ay92NVhLRlJMaEFkM1VCUjFJRk1nZXZlT1A5bmRtS2srdFZORndDbTZ6T1RXc1ZMc2t3aHdHZTdsYnJUcFFsZG5CU1BqcnBXZ1dNakVhM2krblJUQ0xtb1lJc3RUQWQzVHRIeVJQdE5tekVlN2lEQmkyNDVUWEpqa3dqanNLUGFudW5oSitycmhYbVRRUmVKYzBudzE4OER5ZTVwb2w0dTBHZmFNM2ExcVY3K2xKQ3Y0WWxkS3lGQTN3ODZpNTdEeUFqZnVRN3hqbDR4alFORXdxZ3FsQ0pTMTE3NjFqVFNkS25FTS8xMDZ3a0ZSSWNnYjlURDZ1ZC9DVFNCSGZ3NEN2cm5iMWdXNXMrVTFHbFd5cVRhcUlIZkZ3WWc2ei9nVlNkVGU4K3Ewcm1rUUZlQVlEckFkbmJTYXVIM0hzREtnRmxJN3B0WVd1QmZvSlNhOVhnbDRlNEdBb3ZxVG40dHRhMk9EVVlzeDdzNDRDMVMyTDJxSzU5d3Y2aGVtYkxkM09nUFBaRERKR2NOMHNIMDBtcEs5WEtITk5Za3FQaWYiLCJtYWMiOiJiZDM0OTVkZDgwMzc4YjcwMTlmNTIyNDQ1MTNiOTVlYWRmYjEzZDc2MWRhMjA3NjhlNzkzYjQwZmUzZTMyZDJjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:32:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImI3eU5CWDZaNnl2V0JyNWJtaDUvL1E9PSIsInZhbHVlIjoiQ1g2aEJZK3dGd1UrMTM3TkozNHdXSzFpOU0xZ3RhdUFUYkkzZ2E2S0JpNkorNUpnU0tkTHA3aFhrZm5VMVgraUEwd08xK3h1WG1OS3ZUdTVhZDkrZi9RaFU0dHRqQjdGclVHc0pyejFkWXVvYnpac3pGbVNSQXZONFN5eWV5ek4yUmQ2R1dYdHFnWGlUZ3JEN3Jzb0ZDRlNSS25yTFNLNk9vdjlqczFIN2Z3SmRFcFV5UE5hZklpNFFzb01OcWhnVTJIaGJXYWJnOHpSWFNOTjlXZDdqWWQ1QndnZGJWRG53YXdQcG8rNTJmcVlzanhvam5QOVpHK0JUanB6cG8yZjE1UTcrMlVIYTF4aWlzYjJYZzhhZWZNQWI1WE5XOE9VTFpLY1NvNUl2d1pwOThMZENJTHRJcFlMTHdZZXRuWWpmNEZXMjZSY3JBRG53aEpMalV6djNGalJ4eHBKQUJ1TURSUjVYZU9IYVJ0SnlncFB3Tno3cHRvSXpveHlBc0VOS1FqRnhtSTNLSTFDY1hqR1JQUmVwNllQWG9SYm5FaExUZjB3Ynk3Ty84SloySjFuMGdTL0JOUnNHWEdLRkR3RmtvelZvYkpjbWxKejM1cktWbG5GS0c3aWFhNzVVTXZ5U3lVcWt1bVBNaFZhczBGVGlDeDZ5ODZTQlJ1UjN5NEMiLCJtYWMiOiI3OGUyNDEzZmQ3NDgyOTUxM2Y4ZDNkZDRjOGMzYmMzZTIxYTRjMGMwMTI4NGRmNzE0NmJlOGQxMWMyMjc4YmU3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:32:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2097540898\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-67143592 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-67143592\", {\"maxDepth\":0})</script>\n"}}