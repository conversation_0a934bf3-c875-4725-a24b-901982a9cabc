{"__meta": {"id": "Xaecca8c78a1750046a8cd8f2ed570f63", "datetime": "2025-06-08 00:12:01", "utime": 1749341521.010726, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749341519.915105, "end": 1749341521.010761, "duration": 1.095655918121338, "duration_str": "1.1s", "measures": [{"label": "Booting", "start": 1749341519.915105, "relative_start": 0, "end": **********.879176, "relative_end": **********.879176, "duration": 0.9640707969665527, "duration_str": "964ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.879204, "relative_start": 0.9640989303588867, "end": 1749341521.010765, "relative_end": 4.0531158447265625e-06, "duration": 0.1315610408782959, "duration_str": "132ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45039840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.007189999999999999, "accumulated_duration_str": "7.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.951844, "duration": 0.00471, "duration_str": "4.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.508}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.975957, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 65.508, "width_percent": 16.968}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9911919, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.476, "width_percent": 17.524}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1873757032 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1873757032\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-569444713 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-569444713\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-554091887 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554091887\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-34978969 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749341500137%7C30%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZkdXFCWlZDOUNyNGRjSjZ0Qnk5Unc9PSIsInZhbHVlIjoiaGJaTjNkZWUrUWhFSzZWcW9QQUcwN3NxZmF2RzcvNjFmSDY2V3RNcTcxNTZ4V3IrcmI4M29GL2xlT0dWazIydm82bUpMMUVmSUlSN3FGbXZJZmtTenlUMzIvRWl6dzA0eklGd0ZtTjBtR2ZXcEZ1V3JWaGlUMXZ5bDljbzlUNGU5Z2p4dEpGZ3pWeEZrQ2lwY1ZMakFHcFBNeUpIbWR5b0ZTL3JMR3FnWmdCM3Z6ZmhVcU1WMXdEcVJOVHNrRVgzWDRQS2pETDlESWFTbE9zamhDTUJQa0dYYkI1NUpuU1cwbGZxam9VRjJFTE02Ly9QRFA2dkhRTGZqTWFMSjdZbndsRlptY2RZSHIwY3FlcGNjY2tHbmZKUDQ0cmJYVWtZaGp1VFUyV3JRa3hxMXFOYnNSbElGTk9oU2NMcmk5VVBjSzlsV1JDZHdDMWgxYVhVaEZsRXVQSVJnblpoRkFIM3JxcWU5VldIVCtJbEhEYmVvczdYTnR4WFdlRUN2UGM5bUpydUJTQVkzZTFUSTRtRDd5L2VScVRmeVdIRDFRRFFkdHRRSTIxaEZ4czYra2JSS244Szl3aE9ONVJnREhJd2UvRUZkSE5tZXJaTmlTNitWVStqS2lrQVhqZ3pseE9RZCtKS2gzblc3ZmFEcG11cnFGdHZQMmdPU0oxelZkRDEiLCJtYWMiOiJhM2VmMDQ5MGEzMGM4Nzc0YmU2MzAyMGRlNmQ0Y2NjOTg0MmExZGU1NTM3NGY2MzA4ZTAxYjZjNWIzZDRhYWRkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InFUemVLc3ZScTZsd3Y0WDNZOEV5N3c9PSIsInZhbHVlIjoiQ1JoOUZhaFVHUG1KTlZ2YS9qbW1HejZxYmk3S3g2TGhUZ2ZFZjVIejROV1lELzBFUG5QK3ZxdGRwZDF1NW5Lb0RkWHlLUWFaeFdnZ2xpYkVmZVFtc0pwTVV3S0NsSURQMTJ1blA1U1ZjVStwMFBINXBqd0ZmbXhZbVRNUWxTMXBqOFkxeXd4NXdEb0ZrR3llQ2VoYm5DUG5PK1pxWTNxaVU5VWNrZjcrQXFOUW9aUkhrTGc4K0tYMk42dUFzcXYyOE9yZFB2ZVZxMURkdTQ2UXhFS0ZCNlN4eDRLUkZSalUzaWFRZC9CQVpvOGpRcVZkTU5sK1lJaGtraVI0WVVSLzdkQ21lL0FTYWZxM2N6ODk3TlFSUHZGQ1VPZ2tTVVBGSDUrOXFYTXVvc3R0K0VsS3UySzhRK3JHeVBkaTVsZldKdXBYQ1NGK2htSTZLY01JMHRFbnFMOVdRRjlBVWJUcmFaMVpPRENnRkg5dHBHNTBzQkIvZVJMYWU5c3VRR2NicUNFN09STTQxVitELzBvZkFZdjBOL3E5VitZR1lBWS9pZzVyOG1wWkRMMW9MUDhHSEx4eVNCUFRpSzJFZjB5KzhlTTR2ei93ZmMyQ1FTR2ZsbVI0YUhOcE1FUGVmTGw5NGFIT3BJTVhUVk9GU1FxVzk4bVZJRCtSa1M1aHAvOVIiLCJtYWMiOiI1N2IzNTAzZTE3YzE5YmM5ZjZmMDhiOWFlYjViNjYxY2IzZDM1ZjIyMzYxNzRmMzk5MzliOTA2MzJiZjYwZTA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34978969\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-436029684 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436029684\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:12:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitldmZhUUo4dXQ3cHk5YlQySEFtemc9PSIsInZhbHVlIjoiSFg1YkdFaVNuQUxMRC95bk1jSzNNUWJodmtSOGxJdUdwaVFvQ1N0bkg2NDV0TVA3dTluaVpscS9MdTdST3l2RXVrOUNlM1hZdU5rdE5uZkFXZW5Ha1lSMVpmZ1QzOXFiZlZKdXhxWGpRalUwYWVObVpYN09hZjlPQWhLUGI1bUs4QW5lYlJKekNjTkN5UjBhQit1dVZPc2VIRm1TRzVIOFRCL2lVT0h3SWk3L1ZUbUFxRGJsdTJOZ0llOWFtQXdXcXJFWmwyenVTdUNNRnhLV3dVTk1LZmtPNTYrMEJ1a3hZbkt1bXNUZTRWVFhGSGRIK3VrMktRYlJVamkvbnVLb3BoeEVuMlRSbHdydXV1dTJsbkwxMUo1UHBYMWhJTXhwRnV3T0ZYc0NmRWszWFZLUDk0TFRpT3VQblkrVDZaZ3pLVExjSTdrSzFqZmM0eVQ0ajh6M2Nmd1VpTzhNbE9BTlkyWFNYOFVBRGFGQVhFWU1lZDhrUVVnSEFnK3pyRVk0QXFqQ2sxN0tGdzlMbFAxbkFFOXd3RGFlL3VRTWZrN2ErZ1djakw4V1BiTmR6VW52Mk1tb1Z2cXJ2R1NCZnJ4L1FpSjRiZTdlZmd0TmpMNHAwbHFicUJON1BKMm1oZUYyVkpUWlpENk83czQxd2pVZlpQY0NiZjlaNEtPWnpRYVciLCJtYWMiOiIyNzc4NTcxZDU3YmFhMTM4ZjMzN2ZhZjExNzFiMWRmMDQ2Y2E0YjNmNzhmODM0MmQyMGZmMWRlOTkzMjgxZTM1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:12:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InIxdTB0RGFTaHVaTnpRa1Y1aXhUSlE9PSIsInZhbHVlIjoiMVZnWVBWekZoRnN4VTFNZHV5OHdja3ByRkVqcUlZOTUvbEhOYllHbVZCelcvLytiRlBFY253cUU2UE8rN2QxNmNDdHg1dGQzZzIvNENxMXhhd1U2TStneFp0SjdvMGtZM2tWRTcrNFhuMkpreVR5UFdEZGluRTRxQ3pqRk1STDNqWkxvand5NEs5d2JQMGJvY2tGZ3Y1SUt3OWpsVEFIbmhWUVc0LzV3VXZzMjVEa1hYSFgwak1VbVp1bWRDbDJXbk0yclJHQzNCZHU0OEdlOEhEMzhxNldlRW1acEZHOWNPeXFnOEtraXdFWkQ5aVVZcm1iWjg4blJSaTNQZVR1QlFkWWFTMGtLZTJPUkVpU0lXdXBvbUk4TkJhb0RJQXQ3ZTUranh1S01saDFHM280MUkyTURzQTk0YUtZb1lwN3A3bnhwN3NYYjRTc0pHSlV5L2ZTOVF6OTErWVlNU0xYaG95dnFzU05xNzBRSmdwWE1LcHFRak9pZFhybCtsV3NmOHhSb255S00wZnZxSk12WC9USVRnZWhrRzZBSlNmbjd2aFJHZjdIaFo0ajZtbjVZbmNsV0Q3a3Bhd2RyUnR1WVFZQUtsV2hWRVpMSGxxN2lwbmtqUWtqMVp1WStRWjlDVGVpSDBueHlhVldUVXpGUXRDckVuTjdrUldYNUhPbjkiLCJtYWMiOiI4M2Y2NzcxMTE4NTkwNzlkNWNkNGNiMjU0ZTE3NzIzZjFlYWViYmM2NzU3ZTYyZDk0MzkzNmQ5ZTIyNjVjMDgyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:12:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitldmZhUUo4dXQ3cHk5YlQySEFtemc9PSIsInZhbHVlIjoiSFg1YkdFaVNuQUxMRC95bk1jSzNNUWJodmtSOGxJdUdwaVFvQ1N0bkg2NDV0TVA3dTluaVpscS9MdTdST3l2RXVrOUNlM1hZdU5rdE5uZkFXZW5Ha1lSMVpmZ1QzOXFiZlZKdXhxWGpRalUwYWVObVpYN09hZjlPQWhLUGI1bUs4QW5lYlJKekNjTkN5UjBhQit1dVZPc2VIRm1TRzVIOFRCL2lVT0h3SWk3L1ZUbUFxRGJsdTJOZ0llOWFtQXdXcXJFWmwyenVTdUNNRnhLV3dVTk1LZmtPNTYrMEJ1a3hZbkt1bXNUZTRWVFhGSGRIK3VrMktRYlJVamkvbnVLb3BoeEVuMlRSbHdydXV1dTJsbkwxMUo1UHBYMWhJTXhwRnV3T0ZYc0NmRWszWFZLUDk0TFRpT3VQblkrVDZaZ3pLVExjSTdrSzFqZmM0eVQ0ajh6M2Nmd1VpTzhNbE9BTlkyWFNYOFVBRGFGQVhFWU1lZDhrUVVnSEFnK3pyRVk0QXFqQ2sxN0tGdzlMbFAxbkFFOXd3RGFlL3VRTWZrN2ErZ1djakw4V1BiTmR6VW52Mk1tb1Z2cXJ2R1NCZnJ4L1FpSjRiZTdlZmd0TmpMNHAwbHFicUJON1BKMm1oZUYyVkpUWlpENk83czQxd2pVZlpQY0NiZjlaNEtPWnpRYVciLCJtYWMiOiIyNzc4NTcxZDU3YmFhMTM4ZjMzN2ZhZjExNzFiMWRmMDQ2Y2E0YjNmNzhmODM0MmQyMGZmMWRlOTkzMjgxZTM1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:12:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InIxdTB0RGFTaHVaTnpRa1Y1aXhUSlE9PSIsInZhbHVlIjoiMVZnWVBWekZoRnN4VTFNZHV5OHdja3ByRkVqcUlZOTUvbEhOYllHbVZCelcvLytiRlBFY253cUU2UE8rN2QxNmNDdHg1dGQzZzIvNENxMXhhd1U2TStneFp0SjdvMGtZM2tWRTcrNFhuMkpreVR5UFdEZGluRTRxQ3pqRk1STDNqWkxvand5NEs5d2JQMGJvY2tGZ3Y1SUt3OWpsVEFIbmhWUVc0LzV3VXZzMjVEa1hYSFgwak1VbVp1bWRDbDJXbk0yclJHQzNCZHU0OEdlOEhEMzhxNldlRW1acEZHOWNPeXFnOEtraXdFWkQ5aVVZcm1iWjg4blJSaTNQZVR1QlFkWWFTMGtLZTJPUkVpU0lXdXBvbUk4TkJhb0RJQXQ3ZTUranh1S01saDFHM280MUkyTURzQTk0YUtZb1lwN3A3bnhwN3NYYjRTc0pHSlV5L2ZTOVF6OTErWVlNU0xYaG95dnFzU05xNzBRSmdwWE1LcHFRak9pZFhybCtsV3NmOHhSb255S00wZnZxSk12WC9USVRnZWhrRzZBSlNmbjd2aFJHZjdIaFo0ajZtbjVZbmNsV0Q3a3Bhd2RyUnR1WVFZQUtsV2hWRVpMSGxxN2lwbmtqUWtqMVp1WStRWjlDVGVpSDBueHlhVldUVXpGUXRDckVuTjdrUldYNUhPbjkiLCJtYWMiOiI4M2Y2NzcxMTE4NTkwNzlkNWNkNGNiMjU0ZTE3NzIzZjFlYWViYmM2NzU3ZTYyZDk0MzkzNmQ5ZTIyNjVjMDgyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:12:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-379131637 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-379131637\", {\"maxDepth\":0})</script>\n"}}