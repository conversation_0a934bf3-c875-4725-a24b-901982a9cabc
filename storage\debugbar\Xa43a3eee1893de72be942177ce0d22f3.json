{"__meta": {"id": "Xa43a3eee1893de72be942177ce0d22f3", "datetime": "2025-06-30 15:30:26", "utime": **********.27905, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751297425.826681, "end": **********.279062, "duration": 0.4523811340332031, "duration_str": "452ms", "measures": [{"label": "Booting", "start": 1751297425.826681, "relative_start": 0, "end": **********.216769, "relative_end": **********.216769, "duration": 0.3900880813598633, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.216779, "relative_start": 0.39009809494018555, "end": **********.279065, "relative_end": 2.86102294921875e-06, "duration": 0.0622859001159668, "duration_str": "62.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43871256, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01978, "accumulated_duration_str": "19.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.248959, "duration": 0.019469999999999998, "duration_str": "19.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.433}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.272124, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 98.433, "width_percent": 1.567}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-627579408 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-627579408\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1668196405 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1668196405\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-204068650 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-204068650\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2132708924 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijl0NGZGZ0psc0FZOWxYYUhqb0NZL3c9PSIsInZhbHVlIjoiUWQ3QjZLYWptS3ZxdGIrZVM2d1JOazRCL2RheFQ2S0U1TSt5R1N3cks4MWJycEJDdG5uUHdjdmlBY2NqWFI2L3F1dlczdkJEQ0pVUU5nMDJWUTY2QW5OSDRzNUo0aWpXelVrbXlwc3ovMFB1OVp3dFlyWUpnVXRNdERQVUUyZTJPUU5vV0RXYWdRZnFwcEM4YTBCRHhsSjRaOXR4dHNsbDZiQ05KUURLMnAzdUhGeVBTcldyVVNDd2N6ajkwa1A3Z29lS0Yvbko3aUJmOGxsOGtwd25kRGRPcmltcDgyRnJJalhYYXM3b3NZVENmWm83WEtzM2pGOGZKY2lKZUZKU0JDd3lPR1YwWlVVVHRKaVhwYVN6UHpxV25jcUZtL3Fzc2pSUGdDZC95UlNmNnlrL3MzWW9ybmo2QlQ5MVZ0N2ZRdkZqRml5Q2FZcW5pdElQTWc3M2QvUFVPUm1ZTEJTL0xZd04vbjBmN3JRYUtRMERjNC9tL3U2b1J2WDhTMERUdmtVeGFEZEdQTkNGSTJqQVZMd1FYcmcyUFN5azYvSlI0YmFkSjJqMDZZY1h2SDUyZ3ZQQnQ4UmQydko3MytpWHlBU3c3Tnp6Z3RNWmVpWXJlcFJoVXdQM1lQZTV2VXZOWU54RXdHbFF3VUtvMnVyaUdyRnRHQ0lSMkZpK0tJTmciLCJtYWMiOiIwM2VjNWMyMjMwZWUxYWNiMmYwNjU4ODM1NWJmYzcyODZiNWRiMDQ5MTU5M2VhM2YzZDJiY2NiNmIyNWM2MGMxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InUyMWIxWVZMalpvdmthUjR4dUtEWHc9PSIsInZhbHVlIjoiOFFRV21FMkV3c3pPcmZqcXJSWVp2anE0aXZJZTAvYUdHUElWcjRyajJZK2FHckR4Rlc0U0VzbSt2bmphSEY3S2MyYWdSbkRHekREVTJjRTBZalprekJnMkdSbXA2cEpnVHk3RS8vbnoxT1kxQWRHL09maGxyQTdESDZ1WlAvYkpmOEF2OUVGZFAzQmdYTDM1cGlldjBXZWRQZzlzOTRKY1BVVVFoZHZRVHNqRWk5dXJsOUwxcjlyZFJxSzcxcllvYms2SWRtNVBCSGhabTdheDI1UVcrdEJvVjFSK05uZ3NXRUhSbmUycXY1Wlh0QTZUck1KTWF6WisxRGJFNkxlUnVCVW5aYXQ3Sm50UVdtL0NKb1NML2s3YjJIU2tXdVphQ1B5dzhWZWEzK3MwQk1lWXhrNGNXdjVMOXVBRXRuTHV2dlZtbU9oVGFwd3N5L0wraEhkRE5oVDFPZzUvSDJqK2RmRmlSRzZFNG5MRmlYK25QendZbGJ4a1FTT2pFYWFUZEIvQlRqRG9lTDdMQTV0aE9iWXRuTWJ6UDFtc0x1OGZUTjhIbEozQ1E5M1o1MVpyT2NqbURXZnJDanRFMDQveVhqUU52TkdwOCtuREZUb0tpQ3lCNzRoZG41Zm5xeWhPVm8zZHhMdEFIMkNwTGIwcUtaKzFrRGRGR1pvY0oydWwiLCJtYWMiOiJlOWQ4ZjVlOTM3NGNlM2NmNTc5NjJlNDI2NjExNjFiNzJiZTc0YWFkNjY4ZDJkNWIyMDY4ZTQwMDRhMWI1ODU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132708924\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1014649741 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014649741\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-113646430 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:30:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik13QlBML1ovRkFKVmhyenRvZ3RlekE9PSIsInZhbHVlIjoiM0dudDhtRGlZOVlWSnQ0cUQ5ZW1SS29tdFVWNHVWd00wMlV5YWpaZGQ2b2Jwb3A2dUVJNmhoVVBCU0NmM3FVZTdmb2U5Y2lleTlmVmpMNUJOV0xVSU1WS1FmdGpRYXBuRHVJanRVb3V3a1dzbm53NlgvUDREbEhwdXRSRWU5QXoyU3cwWlNuZEpuZjNDM2traEpPQ09RTWtUdlpTbWxMYVNaUE1kcEk0d1NjZHQvZVNOS2o4S1FLcUZIOGdmRVhXUHlHeTN5clZCUjNUSE5UdWVYeVRLZkJCbVUwNDRFcW1hNWRoWlNpU1JzM1lQWGQ3TGdCMFpVK04vZVk3Wnh0bEJiK3dJOXJRcEZRRzlON3hudGFxeUdXR1R2NnduaDQxVDR5RXdxT1k2TzgzUXhPVGJEbTlIei9wZERtaGxGY0czT2k3cjhLaEM3M0lFSVpZRmtSRVpWUTdFck13cGpTa2xRRlFlTnNIMHNkL2xHSnJSNUp0a0ttN3JtcmZsRXhoeUpVZW1PKzVoNGsrS0pVUmdydzRMZ29CZUtmVmR0NkNLajhEb1hQRFlxdDJnaE9IWlV1NURaVnpMaDRIK056eEhPL2pnQWlucE5UWGVSQW44aHNXQU12eUxtellWMUljdEJYMVp1bjVMVkJGZTZhejhzTVMxWXAwZXhGSXBxL1kiLCJtYWMiOiI3YzM1YzZhMGY4NjMzYzFhZTgxNjUzZWYzMzdhYTAzNGQ3ODZlZDZhNDhkZWQ3YTJmMTVmM2M1MjY5YjgxMzMzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:30:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InhPOTF2ZkIvcTk0Und0dnJiMkhnekE9PSIsInZhbHVlIjoiTGZkSWUrVGEySnU2NWdQTHpnNUdNZ3ltWklMYmlMWXcwMGFxU1h3bTl2OXpHZFVPUE9xMjJzd0dkUzdoRHYrV1NydzBxM05jS2hickpEQXFjSjQ3blcwNXRiTzlqNS80M0ZGY1Z3eUg4QmRGK1dOOWRkUzZWckVkYTd3YlI2RVIyVUR2R09aR3h5eHJ0VElTNWRNK2ppWXJ6L1ArWTc0TWVGS0U1U0g2V1JpTmhNNUFuMUVwTGdtcTkvWStRUjZORkNydFJVNnFDM2VkYkJaYXg3YnZQdXRRRW5VNWFRZWp0OVJCczEwVndZZ28rQk9sOU8vU0ZjOFR4dFl3VU9NeklLc1ZBbWFiUFRJek1US3UrbzBtMHplaDB1T2tWdjBkaUlkSFRNMWlTRlY5Z2U4dmdMaUg4bkFzcEdnRE1UMGZwakEvUVByVWJxbUJ5TkYxQTJzY1VRZ0tvNjJtcE1aeW9PVkxwd3lHSEcvdzA4S2phc0VLbXNQQ1REKzU0V2I0Sm5keXg2TWt5Ykh0dlJrVng4VXdFd3drUzNidDZpTHJlSkl6WXpJNGFyS1lTcWtSNXZ0SmdTWVBLRkVaaFZUQllYN1FGL2lWVnVjUnBGRHZKZVNkSzFrOEZZMm5GeFJMSjNOeWhURlBobGMyYzJOdENBdDRJZyt1WVB3YlpqUDQiLCJtYWMiOiJkODEwMGY0MzA0ZDJjMTY0OGI5MWZhOTQwMjM3ZDNiZWQ5ZGU1MWM1ZmE5ZTY1MWI2YmVkNDJhNjc3MjdhYWMwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:30:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik13QlBML1ovRkFKVmhyenRvZ3RlekE9PSIsInZhbHVlIjoiM0dudDhtRGlZOVlWSnQ0cUQ5ZW1SS29tdFVWNHVWd00wMlV5YWpaZGQ2b2Jwb3A2dUVJNmhoVVBCU0NmM3FVZTdmb2U5Y2lleTlmVmpMNUJOV0xVSU1WS1FmdGpRYXBuRHVJanRVb3V3a1dzbm53NlgvUDREbEhwdXRSRWU5QXoyU3cwWlNuZEpuZjNDM2traEpPQ09RTWtUdlpTbWxMYVNaUE1kcEk0d1NjZHQvZVNOS2o4S1FLcUZIOGdmRVhXUHlHeTN5clZCUjNUSE5UdWVYeVRLZkJCbVUwNDRFcW1hNWRoWlNpU1JzM1lQWGQ3TGdCMFpVK04vZVk3Wnh0bEJiK3dJOXJRcEZRRzlON3hudGFxeUdXR1R2NnduaDQxVDR5RXdxT1k2TzgzUXhPVGJEbTlIei9wZERtaGxGY0czT2k3cjhLaEM3M0lFSVpZRmtSRVpWUTdFck13cGpTa2xRRlFlTnNIMHNkL2xHSnJSNUp0a0ttN3JtcmZsRXhoeUpVZW1PKzVoNGsrS0pVUmdydzRMZ29CZUtmVmR0NkNLajhEb1hQRFlxdDJnaE9IWlV1NURaVnpMaDRIK056eEhPL2pnQWlucE5UWGVSQW44aHNXQU12eUxtellWMUljdEJYMVp1bjVMVkJGZTZhejhzTVMxWXAwZXhGSXBxL1kiLCJtYWMiOiI3YzM1YzZhMGY4NjMzYzFhZTgxNjUzZWYzMzdhYTAzNGQ3ODZlZDZhNDhkZWQ3YTJmMTVmM2M1MjY5YjgxMzMzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:30:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InhPOTF2ZkIvcTk0Und0dnJiMkhnekE9PSIsInZhbHVlIjoiTGZkSWUrVGEySnU2NWdQTHpnNUdNZ3ltWklMYmlMWXcwMGFxU1h3bTl2OXpHZFVPUE9xMjJzd0dkUzdoRHYrV1NydzBxM05jS2hickpEQXFjSjQ3blcwNXRiTzlqNS80M0ZGY1Z3eUg4QmRGK1dOOWRkUzZWckVkYTd3YlI2RVIyVUR2R09aR3h5eHJ0VElTNWRNK2ppWXJ6L1ArWTc0TWVGS0U1U0g2V1JpTmhNNUFuMUVwTGdtcTkvWStRUjZORkNydFJVNnFDM2VkYkJaYXg3YnZQdXRRRW5VNWFRZWp0OVJCczEwVndZZ28rQk9sOU8vU0ZjOFR4dFl3VU9NeklLc1ZBbWFiUFRJek1US3UrbzBtMHplaDB1T2tWdjBkaUlkSFRNMWlTRlY5Z2U4dmdMaUg4bkFzcEdnRE1UMGZwakEvUVByVWJxbUJ5TkYxQTJzY1VRZ0tvNjJtcE1aeW9PVkxwd3lHSEcvdzA4S2phc0VLbXNQQ1REKzU0V2I0Sm5keXg2TWt5Ykh0dlJrVng4VXdFd3drUzNidDZpTHJlSkl6WXpJNGFyS1lTcWtSNXZ0SmdTWVBLRkVaaFZUQllYN1FGL2lWVnVjUnBGRHZKZVNkSzFrOEZZMm5GeFJMSjNOeWhURlBobGMyYzJOdENBdDRJZyt1WVB3YlpqUDQiLCJtYWMiOiJkODEwMGY0MzA0ZDJjMTY0OGI5MWZhOTQwMjM3ZDNiZWQ5ZGU1MWM1ZmE5ZTY1MWI2YmVkNDJhNjc3MjdhYWMwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:30:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113646430\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-487352044 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-487352044\", {\"maxDepth\":0})</script>\n"}}