{"__meta": {"id": "Xf67ebd9a8aedbc98b582fa925994d170", "datetime": "2025-06-08 00:11:46", "utime": **********.12739, "method": "GET", "uri": "/users/17/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749341504.890405, "end": **********.127441, "duration": 1.2370359897613525, "duration_str": "1.24s", "measures": [{"label": "Booting", "start": 1749341504.890405, "relative_start": 0, "end": **********.859697, "relative_end": **********.859697, "duration": 0.969292163848877, "duration_str": "969ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.859725, "relative_start": 0.9693200588226318, "end": **********.127447, "relative_end": 5.9604644775390625e-06, "duration": 0.26772189140319824, "duration_str": "268ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51443800, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x user.edit", "param_count": null, "params": [], "start": **********.08944, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/user/edit.blade.phpuser.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fuser%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "user.edit"}, {"name": "3x components.required", "param_count": null, "params": [], "start": **********.112275, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 3, "name_original": "components.required"}]}, "route": {"uri": "GET users/{user}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "users.edit", "controller": "App\\Http\\Controllers\\UserController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=235\" onclick=\"\">app/Http/Controllers/UserController.php:235-250</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.02681, "accumulated_duration_str": "26.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9367251, "duration": 0.00507, "duration_str": "5.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 18.911}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.960069, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 18.911, "width_percent": 4.551}, {"sql": "select * from `roles` where `created_by` = 15 and `name` != 'client'", "type": "query", "params": [], "bindings": ["15", "client"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 238}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.970095, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "UserController.php:238", "source": "app/Http/Controllers/UserController.php:238", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=238", "ajax": false, "filename": "UserController.php", "line": "238"}, "connection": "ty", "start_percent": 23.461, "width_percent": 3.357}, {"sql": "select * from `warehouses` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 239}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9784641, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "UserController.php:239", "source": "app/Http/Controllers/UserController.php:239", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=239", "ajax": false, "filename": "UserController.php", "line": "239"}, "connection": "ty", "start_percent": 26.818, "width_percent": 3.879}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.023113, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 30.698, "width_percent": 4.812}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.030066, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 35.509, "width_percent": 4.14}, {"sql": "select * from `users` where `users`.`id` = '17' limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 241}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.043147, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "UserController.php:241", "source": "app/Http/Controllers/UserController.php:241", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=241", "ajax": false, "filename": "UserController.php", "line": "241"}, "connection": "ty", "start_percent": 39.649, "width_percent": 3.879}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'user' and `record_id` = 17", "type": "query", "params": [], "bindings": ["user", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 242}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.048762, "duration": 0.01295, "duration_str": "12.95ms", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "ty", "start_percent": 43.529, "width_percent": 48.303}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'user'", "type": "query", "params": [], "bindings": ["15", "user"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 243}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0669792, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "UserController.php:243", "source": "app/Http/Controllers/UserController.php:243", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=243", "ajax": false, "filename": "UserController.php", "line": "243"}, "connection": "ty", "start_percent": 91.831, "width_percent": 3.021}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 17 and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "user.edit", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/user/edit.blade.php", "line": 100}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.117042, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "user.edit:100", "source": "view::user.edit:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fuser%2Fedit.blade.php&line=100", "ajax": false, "filename": "edit.blade.php", "line": "100"}, "connection": "ty", "start_percent": 94.853, "width_percent": 5.147}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 13, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2002746786 data-indent-pad=\"  \"><span class=sf-dump-note>edit user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2002746786\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.041989, "xdebug_link": null}]}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/users/17/edit", "status_code": "<pre class=sf-dump id=sf-dump-1022956685 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1022956685\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-528368134 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-528368134\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1363615768 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1363615768\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749341500137%7C30%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikc2MUlXNGFjUGZlWWRCaGNSdjFSaFE9PSIsInZhbHVlIjoiZWlyRGtPQ2xkMHBkYW90WmN6L01wUGNDQTFXRVFnaE9BNzhQWkNmM2tsc0hDQ2RJeFNBOFV5OHc2RGlNUUZBQm95T3dvRng5RXNRSjRLeHZ0cnpEM1ZwVnc2QVpsdElpbzBtbDRMVUp2amFjL3FGVExhRVBvRzR2aGtJYU9ZdDJxdXdoV043NmlDV1JPcDF6MU9WNTUwZzcwbEIrQ05iTXNyMG9wSnl1czViUEw2cjhMZUVyYzVXTWZjOVhQcXp0aEQyaGsyU0FEeHBXQ1k1KzUwYzdjK2loeU9aMVBkblhnc1dYZkc2dktCLzVOeUl4TkY3MC80Y2hyVmtyb295aC9MQVRtSmpseU1pYzMxWDc4VCtZZUZpcURQNm5oMU5pSS8rWGlZR21COFk1WmtNWGd2RHlYUkQrUjBBWVNjUkRtUzJoK1JTbUJib1NXTmV5NWNMQXFmTEtiMTBUOTF4bVE2ODhmS0IzNWprRDE0WVZGUE1sNUhHQnZ3YmExVkhDajAxWG5aQnRwZzNidXBJLzBrbHcyK3ZhWUREbDZ6T0JBOVNxc0ZvZ2ZHTHhxK2FJVjFtZy9JWXJ3RmxzdWNoWThWMHpuYWhSK29ZWGtqVlVOVHY2cXpYZEhOMEZFNUlWWVBYUWtqNHhGaFB4K01ROUVaMGlkR0xPUUJLTzlEdjEiLCJtYWMiOiI2MjY4NjM0NWFiN2M5MGM0ZjdlZDNhYmFjYjdiZGI5NTBhZGFhNGRkNDA5NDVhOTAyYWM1ZTU0NmI1OGY5ODRiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imh0bnVlUThWQmJPdlJKZXE2M2F5Z0E9PSIsInZhbHVlIjoiTEM4eG90bmhwYk1XQXozTmVPd3ovM0dVQWJHT1FmMG8xUVRheEhuQmxNZzBZUlYvdTFFVWdXRHNUa25wNXNla2hIVGh4MkZRZ2J5ek5YbkNGRmxGYWtWTmQxMndkVjA3Y1I5b3EyTE52QkMyWVZpN1dMOStJZ0pvY3dnc2dmVEVQRkwvOGFTUlYxcFp3NWs3V2Z6TFZOME9nQ2hCdGlzQlZnR2hGWThZRGlab2pPQzJiRDFlR0NnbjgzaTJOU3Q0M0R1MGNmN1RuTkFUaFRtZHZhR1BNbFFiMjFOSDlraU1hL00ycXpITXlPUXh3UExuQndwbXpYQmJXbE56Q3NLVGR6S3Z1WnladVZlaDYyc2tTVzNXbjhMOG5Ia0tDY3loQllnMUE0MVNYKzh4YzJjMmJrZ21MZEx0ZWJlaDhSV0ZaWjlLTmQyZXV6OWdUcE1STWNKRFpvclk0NDVDL25ReklzdHZ5WHUrbzVWVTZ1OTA4RUNZdnRkNkpKNmpZRWJacVliMEpaZXRUMlQ5TzAzNmFvMGE0SGhTODBTeFJlRjU0d3krZnY4TXdXbVA3OWlKZml3cEVpQnpIOVY0bnlWRTZUQXFIUnF2MDllTEZOMExPWlF4VmwvMWdlODNsNzJKOHoxU3ZQSWdNZmtRcERaV3RXYnBjQ1FsS2VEUXA1Qk4iLCJtYWMiOiJjZmY1YTFkNDEzNTJkYmM0MDA2ODgzM2M4OThiYjBlNzhkNTcyYTFlOTVmZmFhYzJjOWRjMzU1OTBkMzZiYzZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-214523057 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:11:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImppRnczcmM5bHp0azNYaWdPTzluS1E9PSIsInZhbHVlIjoiZ3NYaU5aQVFNbnVPZXdpRE5CYkNtVmdpV0R4MnFWNWVNOGlyeWhwUHFidUtLWWZmOFZUYmRjdXNFczh0RVpuL1hpWGNlNGtaWVVQK3YvcjRKVmIwNmowdFZSbEFJTlVSQ3JwRnozYVNObkU4dmRxYVdWKzJZdTdDaTgvdG81WG1oeHYxUlM5a25TcnlkUWRpMU5XUDNuWEhWVG5VeFVUbWVaSnJVSmR5OFFVckU0SlUrTW43MEI0NkF0RUxXUDl1MHpwUTlBUTVLMmhicG9qb0pYOFpBMTFXUlVVMVpIcWJIYmNmcEJkellEQXA0Q1lOR2dueDduL1ZHUUNGK0lXSkt4Y0lmT2psQncxWEo3STJEOFBoekJQVjI4SjhSMkl4bWhrQUpmaWhtTnUvQUlwZUhNL01QY3VNdXQ2SU9rMEQ2OFRrT1BJalBLWVN3VzBKYUFWOGlFcWR3bExrdkI3clA0OThVUDdoSVNBU2NxbFR5bGN5Y0NYQVNRaS9MWUJqYVNtWllML3ZmeWVPNzNMS2d3U3IwVkdRSk85dTNnaE16dVRDOEdMeDVnWTRlSjNkS2IyMUNsTHp3TWJrcE55SUd4QUF3TjhxODZNdDVVb2ZlZlVxenZxZmdKQWlYa0NTck03ZUgzMmRReDN1ZXlxRTV3VldETTlxM3QyVGpKRkIiLCJtYWMiOiI2ZDcwYjkwMjNjYzA3ODg5YmQ3ODEyYjM5N2QxOWRkOTYwNGNlZDU2ODRjZmNmNThmODdiNDZhOTYyOTU4ZmRjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:11:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InJiaTdCSlVBK0ZlNmY1RmllVkpXSkE9PSIsInZhbHVlIjoiK2hESlhlMnRnZnFuV0prUmlUYm1EZzg5ZzFwSGVGVkl0RGpJa3FkMjdFMDF0RzluVDljOGJzWmM1TEttMjVXcTdXcXhyMnc5QTYyd2NrQ1FiZkszRVJXdDIzbjExZnoyTU1rWVdneHhlZXBZWXRUcUk2cU9kbjd0ODdKV2dwbmJsZU9KZFFkeE5DQzhmTmN5SlRweUtsK2pSRDNQbkp1Vlg1bGhDTjBRb3dVejQ4Z0xKbTRQUjNRYTZPN01CU2RlbjUyNlAxV2xqelRnaHhuYnNBV1NRZUFRTkYrTlJjaGJ6QVpqL1psdmh2cGlIWWZYcFZKMUpwcVBmWFZudnVyeWtOQXRSYkROT0hhM3pIYit1ejlyQk0yb2I1b1JHaHNLa3RyaDhxZXc5S0s0S3RjOTJ5OGlIVFlCaWthMk9qUkRreUpMSWhSc2Z6Ulh6MHZLTW14Vlg0amUzdkdyZXFrNVc2NUVNZDJ3c25aanAzV3QrbHVNSjJmVnJOU0ZsaVdmN3JuRlErWktuTG1Ea21oQWg2V1BiNVhHSnlCcnRzaXBEVkRMK2M0UlBTRE94RndkODhQR1JPY01Kbmt0T0VlYjZpbnlya2dFaWdXWGx1UmtIOGxGbUdTWUtKTE1Bak5hanBtWU1ZeUdGU2hwNG9BOE9ER1lJZjdLYXplSmdpRVMiLCJtYWMiOiJmNjEyM2U5MzMwMTVkNjNlODU4NjE2MTYwZDg1Y2E4MDQ2OWE0NTY2YWYyZDIwZjY2ZjliMTJhNjNkZWU5MmI1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:11:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImppRnczcmM5bHp0azNYaWdPTzluS1E9PSIsInZhbHVlIjoiZ3NYaU5aQVFNbnVPZXdpRE5CYkNtVmdpV0R4MnFWNWVNOGlyeWhwUHFidUtLWWZmOFZUYmRjdXNFczh0RVpuL1hpWGNlNGtaWVVQK3YvcjRKVmIwNmowdFZSbEFJTlVSQ3JwRnozYVNObkU4dmRxYVdWKzJZdTdDaTgvdG81WG1oeHYxUlM5a25TcnlkUWRpMU5XUDNuWEhWVG5VeFVUbWVaSnJVSmR5OFFVckU0SlUrTW43MEI0NkF0RUxXUDl1MHpwUTlBUTVLMmhicG9qb0pYOFpBMTFXUlVVMVpIcWJIYmNmcEJkellEQXA0Q1lOR2dueDduL1ZHUUNGK0lXSkt4Y0lmT2psQncxWEo3STJEOFBoekJQVjI4SjhSMkl4bWhrQUpmaWhtTnUvQUlwZUhNL01QY3VNdXQ2SU9rMEQ2OFRrT1BJalBLWVN3VzBKYUFWOGlFcWR3bExrdkI3clA0OThVUDdoSVNBU2NxbFR5bGN5Y0NYQVNRaS9MWUJqYVNtWllML3ZmeWVPNzNMS2d3U3IwVkdRSk85dTNnaE16dVRDOEdMeDVnWTRlSjNkS2IyMUNsTHp3TWJrcE55SUd4QUF3TjhxODZNdDVVb2ZlZlVxenZxZmdKQWlYa0NTck03ZUgzMmRReDN1ZXlxRTV3VldETTlxM3QyVGpKRkIiLCJtYWMiOiI2ZDcwYjkwMjNjYzA3ODg5YmQ3ODEyYjM5N2QxOWRkOTYwNGNlZDU2ODRjZmNmNThmODdiNDZhOTYyOTU4ZmRjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:11:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InJiaTdCSlVBK0ZlNmY1RmllVkpXSkE9PSIsInZhbHVlIjoiK2hESlhlMnRnZnFuV0prUmlUYm1EZzg5ZzFwSGVGVkl0RGpJa3FkMjdFMDF0RzluVDljOGJzWmM1TEttMjVXcTdXcXhyMnc5QTYyd2NrQ1FiZkszRVJXdDIzbjExZnoyTU1rWVdneHhlZXBZWXRUcUk2cU9kbjd0ODdKV2dwbmJsZU9KZFFkeE5DQzhmTmN5SlRweUtsK2pSRDNQbkp1Vlg1bGhDTjBRb3dVejQ4Z0xKbTRQUjNRYTZPN01CU2RlbjUyNlAxV2xqelRnaHhuYnNBV1NRZUFRTkYrTlJjaGJ6QVpqL1psdmh2cGlIWWZYcFZKMUpwcVBmWFZudnVyeWtOQXRSYkROT0hhM3pIYit1ejlyQk0yb2I1b1JHaHNLa3RyaDhxZXc5S0s0S3RjOTJ5OGlIVFlCaWthMk9qUkRreUpMSWhSc2Z6Ulh6MHZLTW14Vlg0amUzdkdyZXFrNVc2NUVNZDJ3c25aanAzV3QrbHVNSjJmVnJOU0ZsaVdmN3JuRlErWktuTG1Ea21oQWg2V1BiNVhHSnlCcnRzaXBEVkRMK2M0UlBTRE94RndkODhQR1JPY01Kbmt0T0VlYjZpbnlya2dFaWdXWGx1UmtIOGxGbUdTWUtKTE1Bak5hanBtWU1ZeUdGU2hwNG9BOE9ER1lJZjdLYXplSmdpRVMiLCJtYWMiOiJmNjEyM2U5MzMwMTVkNjNlODU4NjE2MTYwZDg1Y2E4MDQ2OWE0NTY2YWYyZDIwZjY2ZjliMTJhNjNkZWU5MmI1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:11:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-214523057\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1501773351 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1501773351\", {\"maxDepth\":0})</script>\n"}}