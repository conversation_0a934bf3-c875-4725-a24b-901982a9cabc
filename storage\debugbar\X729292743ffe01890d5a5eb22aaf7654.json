{"__meta": {"id": "X729292743ffe01890d5a5eb22aaf7654", "datetime": "2025-06-07 22:50:08", "utime": 1749336608.009945, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.150744, "end": 1749336608.009971, "duration": 0.8592269420623779, "duration_str": "859ms", "measures": [{"label": "Booting", "start": **********.150744, "relative_start": 0, "end": **********.908694, "relative_end": **********.908694, "duration": 0.7579500675201416, "duration_str": "758ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.908711, "relative_start": 0.7579669952392578, "end": 1749336608.009975, "relative_end": 4.0531158447265625e-06, "duration": 0.10126399993896484, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45399200, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00527, "accumulated_duration_str": "5.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.969825, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.137}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.989894, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.137, "width_percent": 17.457}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.995712, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 81.594, "width_percent": 18.406}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-685605600 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-685605600\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-703932388 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-703932388\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-212298408 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-212298408\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749335947329%7C7%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNkRVVkWFhPdGRUY0hKMk96OVhDV1E9PSIsInZhbHVlIjoiaXljS1Z1NmxZdFJyaWlaZ25PaGRxWExUWDFGL1J0M3lNMlB1RTQxQ25LN0VGc1VCU1ova0xPRHU3ODlnVjQ3WUtUVE9VUS9KdTY3d1ltMkpTd0NSTGxpcGR0TmM0RE4vWHRwck9zY3F4T0dYWktaaHNjd3NYK1hWN2pJWC9ZdEYzUFVabTBvUnZPZDhhdnpMR1ROWGJiVmc3Z3dkL01YbmNMcXA0ajRReXhzUXVBS1pLdDBaYUphMGdraFBFNERXYmR4NjdqbGt3ZTB1R01hdmx0UUpla1VYVUhEaE55dXU4TU5MRFo0VlQvbVE5UVBKR0R2Z3lEaHBxOWtWbk5sclV4VVFlVEZaQWhZd2dpUW9nMlIzclc4bEpuSFZHaFU1MHhnM3F5cDJXb0hNaGdzRi9lY3J1S3BoOWhvWlJFb0U2Y25UKzNaTGxKZlBCbngrdzZEc2hzRWpzWjFreUJmN09GZ2h1MTNwUTRrSTBqVW5uWnJtVnBvYXNKNWlKUGxOaWtsVDlPdDV2c2c5WDVCaVBOYm9WbGtsWlUrQTJzck5nLzdoN3JPZmdrUlRUYThiaThiS1FjRGg2Z05sVGUzYmYrYnZXN2FPbGlSWjJwZkJZdWVqTVpvRnc4bjgwamxGZ1gwQWlwNnZKVi9FU1JtY2lnZU5ZQ3RIYVN4MWEzMFQiLCJtYWMiOiIwYmRkZjEwMTdjNDE3NDI0ZmNlZjk1MTgxZWZiYTFmNTM4NjE2ODQxMzNhMTVlMGIyZGVmYjdjM2VkNGFlZDQ4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImViN0syRXRzcjNkK21HaWdVeXFKWkE9PSIsInZhbHVlIjoiaWJmKzVHOVNMN1NKQmpmajQyclQ3OUxJZ2FGbmtFWDUxZllkMXgyYm9MYVBYa0Y5clpBU1pQaXRQSEVSQmtDSnZlSXpUU1dTWHUvL3hTcjBQVkJnR1kxWkFCS0dtc1JpTkFCcWpvOUJ2b0Qxcm5wWnI3eVp2blZ1bVQvZmYzSjdYOGFoancxOHNHYXdPcTlOYXN1NHJ2a01NajYxQ2pwTDNaU2NRSHZJQ0NpY3ZXRVk2UDMwNDliZlRqMlUwTkdiZklQbFN5SHNtV3RKaGViTTNsb0lYemdySVNuUnY0M2d1K3ZKbXFrRlZwKy9aMVhYbjl6di8zSnROSU9USXN2Q29Wd0NaeS9NQmJRenN3YmE2VVV2cHRkWHE1UFZiS0c4dDZLdy9XLzRwZGpyY2U2MzNYL3RVL2xacnNJWTFEQjVRY3BkNndEOXVEZlM3ajNybUd0ZDV6UFMrd1lyTTI3RXpoSVVyZW02MjdGVldjSFk3SGpoK3pGNFgwT2wwV054bW9rTTJERnV2ZlpTK0ZMSkQ3QktzdmtySUhLYnNSMlBvVWxuQkI1d01LUE9GelJJWnBVMEJ1MTE2Vll1VkYvMGVkTUFCQktOaG9KUWgvdlFHbDdMby84ZzVUN0tkQW5VUklrL3YrQ0hCSDQyd1drZElQNkRYMmNFeUk2TVpXdk8iLCJtYWMiOiI3OWI3ZGI3MDlmY2M2MjU4YTYzNmUzODM5ZThiMjU5MDY3YTA1NmY2NTRhZDJjZjA5ZDQ5MmJlZTViZDQ5NzlkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1488802766 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1488802766\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-645445709 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:50:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZhM0MvVHpvY2o4dGQzNmdEVmJPY0E9PSIsInZhbHVlIjoiU0d1d3hNVFhmb3p5dm1BdThXVHdsdmxDM05xaHl0SmRZaTJ2QU4wTXRRYlB1djNscTRIUE9HTDNiNHdrYXNZR084a2wwOTJDTEM1TWh6ekhsZFVZam9FRWFxS2V6ZlozTlhpY3Z4bTNlbklCUWZXSkJEa0cwZ2kyNDFWakNHdm5PNVRqc1BETVA3TGRuSFVlNUpwdUcrc2VUVlE0OERkeE9YTlYxZ0llL2QxcUw5ZnZWczR0cmYvSU5HbEFjTTMxNTE0Sm1hdXAxdHB4SGlEYktMREF1ZXg4SXhiR1V1RDlYZUZST2x1ZXpPeCs5ZFkvTHdQTnBYRkxWQXFEZTNOWlJJNWJqYmRVNE40NzJHZjFjbjU5TFRSZE9ZdGRZNWJtT0QwU0x3WVNDQmNkeENHMUpLY0ZJRmJRV3lxanV6akpEYjJYL09HMy9Ybk5HNjBBOGlSaE5DTENWc1d2eHJibHIvaUplYWwrTDd1RFJnR2VOUjRiWUFHU3MyRHJ3ejZFR0JmZzMvdDJqTFdWQlc0S0dJY280TmxKcHJTQlY3Y2FHRmtMVHpFTjZmSkF1OG1CblZPekZ1ZTJFbVRSdmxtNmZybmkrb1N0TkpqaHJ1cDlOa0FZN3JyRkVPbDNTbU9SSDg2b1dKaExXNi9QUVNOa0IvQWhDb3QzQkZxK0RnS0YiLCJtYWMiOiIwYjNlM2JkNzI2NjUxYmE4MmU2NGE3NWQzZDllZjQyMWRkMTlmNzRiNmQ4NGNjMTc1M2U1ZjRkYjkwZTZhOTcwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:50:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlFRNTBrZ2hMSXVsZkJtK3d0NDV6UGc9PSIsInZhbHVlIjoicVRuTDZGKytZbzV5Z1E4WmZXcUl4OEc1em1VTWM3YmdEWS9zaURhRnFsc3h4STRmc0tiZXhSbndQaGdYSUZVQjJiak9pNjFxZE1iYlZEOURGM0x3TVppRzViaHlXL0E5bGRNWDhQeVFXc0tZWStvZFFPYW1RNUc1WU1kMTJVSFBPRHZ3dWovSGdLaVB4MVFuSTAra0NablRZZnN2TUhlZ3FiM1hHMFZmWFNBcW9aZzRGMXNSVXJaS2x5czVObUVkUXZIY3JOU1Q0S0g0UVNvSjB0MlBjeG85ZVVTNlJRSk5tRUlOalh6VXBvS1FEM2JyK3MwZmI0M0k5eWl4RlFEZVdFbTBla2xOSGE1QmtzSXZvUlY5Mm95Nnd4QmlVTmZ0bkdoeHpCVFcwTVBWa2ZpSXBRMWF1V1hwMllGbndXZzVXSnVXUUpnUFFiSmY1Unl5Z011N3MvNXBlRlpXNVVHalpVVlBKSnQ1ZGZjbVErb2FybjgzSXl0dUlyaFhMd3VlZVhBdXBGVk9PSDB3ckNsSW44blcyUnlWRVp2eCtkU05ENjhFcXYyd1hqUU9JSEFOOTZPZFhxWS9uVHhwU1h2Ukg1N1RYTWRvQ0NkeG9CVGFoUGNydjFDYThMUExRZ2hBczRtb2V1WkxZQmVpcHM2WkljUzNoNGlXa3IxYWxSK2QiLCJtYWMiOiJiYTc4NmNkZDhmMWUwZGIyMjU5Zjc0YWI1OWNhZWY0YjljODRjMTI3ZjliODgwZWM1ZTBiZDk5YzQyYjcwMzQwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:50:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZhM0MvVHpvY2o4dGQzNmdEVmJPY0E9PSIsInZhbHVlIjoiU0d1d3hNVFhmb3p5dm1BdThXVHdsdmxDM05xaHl0SmRZaTJ2QU4wTXRRYlB1djNscTRIUE9HTDNiNHdrYXNZR084a2wwOTJDTEM1TWh6ekhsZFVZam9FRWFxS2V6ZlozTlhpY3Z4bTNlbklCUWZXSkJEa0cwZ2kyNDFWakNHdm5PNVRqc1BETVA3TGRuSFVlNUpwdUcrc2VUVlE0OERkeE9YTlYxZ0llL2QxcUw5ZnZWczR0cmYvSU5HbEFjTTMxNTE0Sm1hdXAxdHB4SGlEYktMREF1ZXg4SXhiR1V1RDlYZUZST2x1ZXpPeCs5ZFkvTHdQTnBYRkxWQXFEZTNOWlJJNWJqYmRVNE40NzJHZjFjbjU5TFRSZE9ZdGRZNWJtT0QwU0x3WVNDQmNkeENHMUpLY0ZJRmJRV3lxanV6akpEYjJYL09HMy9Ybk5HNjBBOGlSaE5DTENWc1d2eHJibHIvaUplYWwrTDd1RFJnR2VOUjRiWUFHU3MyRHJ3ejZFR0JmZzMvdDJqTFdWQlc0S0dJY280TmxKcHJTQlY3Y2FHRmtMVHpFTjZmSkF1OG1CblZPekZ1ZTJFbVRSdmxtNmZybmkrb1N0TkpqaHJ1cDlOa0FZN3JyRkVPbDNTbU9SSDg2b1dKaExXNi9QUVNOa0IvQWhDb3QzQkZxK0RnS0YiLCJtYWMiOiIwYjNlM2JkNzI2NjUxYmE4MmU2NGE3NWQzZDllZjQyMWRkMTlmNzRiNmQ4NGNjMTc1M2U1ZjRkYjkwZTZhOTcwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:50:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlFRNTBrZ2hMSXVsZkJtK3d0NDV6UGc9PSIsInZhbHVlIjoicVRuTDZGKytZbzV5Z1E4WmZXcUl4OEc1em1VTWM3YmdEWS9zaURhRnFsc3h4STRmc0tiZXhSbndQaGdYSUZVQjJiak9pNjFxZE1iYlZEOURGM0x3TVppRzViaHlXL0E5bGRNWDhQeVFXc0tZWStvZFFPYW1RNUc1WU1kMTJVSFBPRHZ3dWovSGdLaVB4MVFuSTAra0NablRZZnN2TUhlZ3FiM1hHMFZmWFNBcW9aZzRGMXNSVXJaS2x5czVObUVkUXZIY3JOU1Q0S0g0UVNvSjB0MlBjeG85ZVVTNlJRSk5tRUlOalh6VXBvS1FEM2JyK3MwZmI0M0k5eWl4RlFEZVdFbTBla2xOSGE1QmtzSXZvUlY5Mm95Nnd4QmlVTmZ0bkdoeHpCVFcwTVBWa2ZpSXBRMWF1V1hwMllGbndXZzVXSnVXUUpnUFFiSmY1Unl5Z011N3MvNXBlRlpXNVVHalpVVlBKSnQ1ZGZjbVErb2FybjgzSXl0dUlyaFhMd3VlZVhBdXBGVk9PSDB3ckNsSW44blcyUnlWRVp2eCtkU05ENjhFcXYyd1hqUU9JSEFOOTZPZFhxWS9uVHhwU1h2Ukg1N1RYTWRvQ0NkeG9CVGFoUGNydjFDYThMUExRZ2hBczRtb2V1WkxZQmVpcHM2WkljUzNoNGlXa3IxYWxSK2QiLCJtYWMiOiJiYTc4NmNkZDhmMWUwZGIyMjU5Zjc0YWI1OWNhZWY0YjljODRjMTI3ZjliODgwZWM1ZTBiZDk5YzQyYjcwMzQwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:50:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-645445709\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1454469495 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1454469495\", {\"maxDepth\":0})</script>\n"}}