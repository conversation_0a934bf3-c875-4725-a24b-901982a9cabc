{"__meta": {"id": "X8263085a8f350f06c477a5d248e9f570", "datetime": "2025-06-07 22:57:16", "utime": **********.801925, "method": "DELETE", "uri": "/remove-from-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 3, "messages": [{"message": "[22:57:16] LOG.info: removeFromCart called {\n    \"id\": \"3\",\n    \"session_key\": \"pos\",\n    \"request_data\": {\n        \"id\": \"3\",\n        \"session_key\": \"pos\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.757003, "xdebug_link": null, "collector": "log"}, {"message": "[22:57:16] LOG.info: Cart before removal {\n    \"cart\": {\n        \"3\": {\n            \"name\": \"\\u062d\\u0645\\u062f\",\n            \"quantity\": 1,\n            \"price\": \"10.00\",\n            \"id\": \"3\",\n            \"tax\": 0,\n            \"subtotal\": 10,\n            \"originalquantity\": 17,\n            \"product_tax\": \"-\",\n            \"product_tax_id\": 0\n        }\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.791409, "xdebug_link": null, "collector": "log"}, {"message": "[22:57:16] LOG.info: Product removed successfully {\n    \"removed_id\": \"3\",\n    \"cart_after\": []\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.79168, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.100681, "end": **********.801957, "duration": 0.7012758255004883, "duration_str": "701ms", "measures": [{"label": "Booting", "start": **********.100681, "relative_start": 0, "end": **********.686669, "relative_end": **********.686669, "duration": 0.5859880447387695, "duration_str": "586ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.686684, "relative_start": 0.5860028266906738, "end": **********.80196, "relative_end": 3.0994415283203125e-06, "duration": 0.11527609825134277, "duration_str": "115ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47538488, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "DELETE remove-from-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@removeFromCart", "namespace": null, "prefix": "", "where": [], "as": "remove-from-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1597\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1597-1670</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00631, "accumulated_duration_str": "6.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7349029, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 53.249}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.751894, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 53.249, "width_percent": 10.935}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.778687, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 64.184, "width_percent": 19.334}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.783252, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.518, "width_percent": 16.482}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1972388466 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1972388466\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.790754, "xdebug_link": null}]}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "[]"}, "request": {"path_info": "/remove-from-cart", "status_code": "<pre class=sf-dump id=sf-dump-2049602445 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2049602445\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1758483032 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1758483032\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1545856965 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1545856965\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749337003065%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNjK2RsSkloQTFLUWJ1MGZmdDBQZEE9PSIsInZhbHVlIjoiQTBVbUtJNDhEU0xmbVJmWWNjOGwwRktyUzcwZGpIN21aSjNxS2pLWG9Oc0RWOFRhaG9YQ0dmTUVhMEtyN3dpS09wUTM2aDZsekIraVlPSWdUc25wQXR0T1QzWGJVdTI5d0l5S1ZjWnN4aXczQjVockxWSkt3S3lNMGNSTGlPWW5sb29tQ0wyNlhCczFQT0RkWk5xV2ZUUnNsam9FeXU4QklHbFNLR2VkU0NnTEJkbEZoTmI3eEltQnAwZHQ1V0lGUXhuYlBvaHoxcGpzNktIRVA4NXBPRWtLYnZ2REIyVmJ0Um4rb1V0eXVFNmora0lBWWUyTkQ5NCtjNER4SENKaVlHVG9MdU5FNHMzYVJVZ095ckptUUZvcDBKQkFBUzVXZ21lQy9NTlhhSUZmVEExcXlicVVEUE5rMWgvWjZOV3BWY2dIcTI1OE1TRDFzeFp1WE1QRDR2bE1wL1YrK01DSTIrOXlNZW41VUlpZFk0UzJwZEpONHE1Y1FVclQ2L0hkbGVDNHc0b29kbStSTjlRalU1YXVxQWk2dlJkc1BlREVjZ0kwMzhWdmFGazBLSzY3blFpeFR2am02c1FwdjdhMDJEU0tMYXNvTlNpY3lqOUQwK2ZNdTd0RVI0QitEbElybkx6cFpRMG16cmdNQUk1YzYra2tFVlkxWXNxa001VUEiLCJtYWMiOiJkOTU5NjZjNTE4ZjAwZmI1MWQ0NDI1YjQ1YTYxYzMwMmQwMjc3M2ExNjQ2YmU4NGJhNTFiYTAyNWJkZTVlYTdjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjQ1bEZ3MnFSU2dCbDZnOHV1eWhmeGc9PSIsInZhbHVlIjoiWmI5Y3FsMm5UMFBhRWRkQk5NSkVDL3hzcUJMRjVGeFdHQ0REdTVnZDAzcnBDeE11L0FIY2lsZkdQbTZTOFhMRGZFMTFFcnFKKzN5MHk2M1dTVk8rQVRCSzFXUFRTOExnQWZ5SGd6MWtjaWZWQkZ1K2NMSzkvMU9Ldmk2L0phVEgzeEIxT2pVVis0QkQ3M0FkczJuVVlFR1NRalN2Y0ZDZzlSb2FEeThpR3N6SE84dk9YeUxtSlg0ajh4cUdNL2xUbktyQ1lOQm5CRlpRbnF3U29jeW9YeDhsdnNpWFVidWdhK3ZVczcyT25UdzI5bk5nZ2pTSG0zamdpZ0ZIWTBqVjl2MDl1Qjcva0FRR0R3UnVPZHp6c3I2RjNDcTJzY0tSUnhiRDd3bVVCbmg2V2hBTVhJak5kMklYT3JPYjhSa2NMNUJTY0Y5a2cvMGNYMjRXRENXRXdNR1haSitzSG1nS0ZhOXhrMUVvYTNheFdKa3FMdmJsK2t0SGc0QVE2NEpmMTV3c3ZZd0p4c1BDK05yb29NdTV2WFRkeC9IZk5oU1dkSzlNTy9EeEJkdi82YlZPcmQ0azltSHptaVBJNVJ2aFM3NEgvN29VRjUzMjZFR3lJY09IdkEyVWVQU3VZemtSNm01eSsvWG1qbksweE5WSGx2SUgrRVdkcnBTUTBZQi8iLCJtYWMiOiI0OGViNGEzM2QwZDRmMmExMTgxYmI1ZDc0OWFlMzIzNDFlYmNhMWRlYWEwMjkyOGIyNDk2ODEzNWJiYmE5ZDNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1909835615 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:57:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNCMVA1SE4xd0dGVmNqdGkxZm5ibUE9PSIsInZhbHVlIjoiUjNOUFhoQmx0alZDdFNIa20vVlI0MXJpbTdBR2htdktPRjVLYy9GcXBMaU1lUnl0VXdrN2l2dWNTallIbSsyL2VKSG5vSFJCT0xSK0xEVllrRVVnS0lzdnNoS1EvNEdxVUZ5d29qQjBJYnVMN3ZzTjdHalJ6eVlzcTJNbFFuOUlMbC9ubkNyY1VoQlFBcng1ZllNR2lNNFo5elBxbEtvR0ZIRFFPWnhLM2JpY2dOczdLaUZJMytkYUpHUmppZUVTNjByYng5d3BPVkRUR2tFcUozbDc1aklBemNPZ002bHV4L1VtVHZqcXNsSUVzRExEMkJheVc2Y1FZbnRDRnBVZk00eHVmRlZUSGFSSnFpQVRBZWtiRXIrbFRkcWVrb0NTMmw5TWRweWRIMlp2UEFXaEtDMFVJajB5a0dwNFBRaWkrN2QxM2t0MGdYV0JrbmhQTTA0elNjMmRTYkJOYXM0RkI1Nzk2bHk4VmRNS3YzRnFVcmNrelZxdFpnYWRKS05QTGxMUTFYMFZhTHZ0YW84T3ZTL2dZVS90b3dPd3BoSXRZc0s0cUNWWU9ZVmgyRmYrS1pnU1FOc1VhR3Y2L0E1VitmbVcrTW1vRTdjc0I4dnhBZzkxN0M1cUxDTkQ4R0NpUW5KYWVaREowbGF0SnlXWlBJdVYzWHNqUk1adk1sMUYiLCJtYWMiOiIwOGM0M2YzNmY2ODc3ZDZmMGE5ZWVlOTlkMDk4NTMyNzNlNjNkZWUyMWJkMGE3NzI3ZWRhYzBjOWNjMzYwY2Q0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:57:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNmb0JzZkoza0Z0K2xvYVE2SmZ0TGc9PSIsInZhbHVlIjoiU2MzWVdKSUFhcjIwRDI1S2pla2JRQVlEWHd6eVZ1QitJNDJva2NtZ3F6cUx0SlA4WDRwNHk5QXk2OWNlZFhFdU5td1M2Z3A0MEpjanh5L2FVR2lmaTRiRC8vMk1BUFYrZHBpVFNDT1ZMdkFEVlUxZmM0NmxRVmZFVk9ENmpsb05wOGNGU1RtNUtaRUEwNHhDYjBDd0xua09kMDlsSngrUXEvVm5wOVR1OUU1T2RMMWw3NEdLQTBuM3Q1V0tOVVRjSDRVUDFtN25qMEdWYW0xamJiMlFkbEtpdUpoYUJNREFBdTZycHdsbWhzTThkYjRsU29WRnFTN1JZWmhIeTFsNkV1elhFclFHU3hMa2pvK3Q4QjVYY1h4bC9uV2lha3RuUGt1UHllNHRIdXhvUkROaXhaVDBFdERLaGZKT2dFdGhpSHRHdFA4ZW9LSHlPQm5ER1JIYUg4YWsrZ1VycGRPTFJNQVBlS1hPd0pYaVZxcGF4VXAyTGxwaHBzOTlZTWg0MzZqemlFTlkxUmEyMmFScnFac2l2TlpBR1U4dVhSWjhGbi9vT3hhSHQ2U1JYeC9aR2R3cGJmWlR0WTV3a2NtTWErR1ZZYzlRbUhwUWxEcitSU1BXa3RYK1FVUWd5YnJkcW5sKzZVbitXODZKb0RRV3R0Qk5EcWRHUG4zeEdWemsiLCJtYWMiOiJjYmU5Zjc0MjE0OGRhNmVmMWMzYzZiMWQ3ZGQ4ZGEyMmU2MjdjMGY1Y2Y4NWYxZTQ0N2JhNTRkNzkzMGEwMGM1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:57:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNCMVA1SE4xd0dGVmNqdGkxZm5ibUE9PSIsInZhbHVlIjoiUjNOUFhoQmx0alZDdFNIa20vVlI0MXJpbTdBR2htdktPRjVLYy9GcXBMaU1lUnl0VXdrN2l2dWNTallIbSsyL2VKSG5vSFJCT0xSK0xEVllrRVVnS0lzdnNoS1EvNEdxVUZ5d29qQjBJYnVMN3ZzTjdHalJ6eVlzcTJNbFFuOUlMbC9ubkNyY1VoQlFBcng1ZllNR2lNNFo5elBxbEtvR0ZIRFFPWnhLM2JpY2dOczdLaUZJMytkYUpHUmppZUVTNjByYng5d3BPVkRUR2tFcUozbDc1aklBemNPZ002bHV4L1VtVHZqcXNsSUVzRExEMkJheVc2Y1FZbnRDRnBVZk00eHVmRlZUSGFSSnFpQVRBZWtiRXIrbFRkcWVrb0NTMmw5TWRweWRIMlp2UEFXaEtDMFVJajB5a0dwNFBRaWkrN2QxM2t0MGdYV0JrbmhQTTA0elNjMmRTYkJOYXM0RkI1Nzk2bHk4VmRNS3YzRnFVcmNrelZxdFpnYWRKS05QTGxMUTFYMFZhTHZ0YW84T3ZTL2dZVS90b3dPd3BoSXRZc0s0cUNWWU9ZVmgyRmYrS1pnU1FOc1VhR3Y2L0E1VitmbVcrTW1vRTdjc0I4dnhBZzkxN0M1cUxDTkQ4R0NpUW5KYWVaREowbGF0SnlXWlBJdVYzWHNqUk1adk1sMUYiLCJtYWMiOiIwOGM0M2YzNmY2ODc3ZDZmMGE5ZWVlOTlkMDk4NTMyNzNlNjNkZWUyMWJkMGE3NzI3ZWRhYzBjOWNjMzYwY2Q0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:57:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNmb0JzZkoza0Z0K2xvYVE2SmZ0TGc9PSIsInZhbHVlIjoiU2MzWVdKSUFhcjIwRDI1S2pla2JRQVlEWHd6eVZ1QitJNDJva2NtZ3F6cUx0SlA4WDRwNHk5QXk2OWNlZFhFdU5td1M2Z3A0MEpjanh5L2FVR2lmaTRiRC8vMk1BUFYrZHBpVFNDT1ZMdkFEVlUxZmM0NmxRVmZFVk9ENmpsb05wOGNGU1RtNUtaRUEwNHhDYjBDd0xua09kMDlsSngrUXEvVm5wOVR1OUU1T2RMMWw3NEdLQTBuM3Q1V0tOVVRjSDRVUDFtN25qMEdWYW0xamJiMlFkbEtpdUpoYUJNREFBdTZycHdsbWhzTThkYjRsU29WRnFTN1JZWmhIeTFsNkV1elhFclFHU3hMa2pvK3Q4QjVYY1h4bC9uV2lha3RuUGt1UHllNHRIdXhvUkROaXhaVDBFdERLaGZKT2dFdGhpSHRHdFA4ZW9LSHlPQm5ER1JIYUg4YWsrZ1VycGRPTFJNQVBlS1hPd0pYaVZxcGF4VXAyTGxwaHBzOTlZTWg0MzZqemlFTlkxUmEyMmFScnFac2l2TlpBR1U4dVhSWjhGbi9vT3hhSHQ2U1JYeC9aR2R3cGJmWlR0WTV3a2NtTWErR1ZZYzlRbUhwUWxEcitSU1BXa3RYK1FVUWd5YnJkcW5sKzZVbitXODZKb0RRV3R0Qk5EcWRHUG4zeEdWemsiLCJtYWMiOiJjYmU5Zjc0MjE0OGRhNmVmMWMzYzZiMWQ3ZGQ4ZGEyMmU2MjdjMGY1Y2Y4NWYxZTQ0N2JhNTRkNzkzMGEwMGM1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:57:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1909835615\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-436040425 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436040425\", {\"maxDepth\":0})</script>\n"}}