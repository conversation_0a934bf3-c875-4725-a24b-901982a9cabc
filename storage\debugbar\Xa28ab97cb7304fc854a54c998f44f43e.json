{"__meta": {"id": "Xa28ab97cb7304fc854a54c998f44f43e", "datetime": "2025-06-30 15:30:16", "utime": **********.986999, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.58428, "end": **********.987012, "duration": 0.40273189544677734, "duration_str": "403ms", "measures": [{"label": "Booting", "start": **********.58428, "relative_start": 0, "end": **********.916081, "relative_end": **********.916081, "duration": 0.3318009376525879, "duration_str": "332ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.916089, "relative_start": 0.33180904388427734, "end": **********.987014, "relative_end": 2.1457672119140625e-06, "duration": 0.07092499732971191, "duration_str": "70.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48095536, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0073, "accumulated_duration_str": "7.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.945541, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 26.986}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.955402, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 26.986, "width_percent": 4.932}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.967723, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 31.918, "width_percent": 7.397}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.969445, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 39.315, "width_percent": 5.205}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.973672, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 44.521, "width_percent": 34.247}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.978405, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 78.767, "width_percent": 21.233}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1212290355 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212290355\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.972723, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1314947765 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1314947765\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1853448823 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1853448823\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-528173897 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-528173897\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFhT1JOeXEyaDlmV2REY3A0OW5DQ1E9PSIsInZhbHVlIjoiVk5PWm5XREdUL3ZyaXd5RUN0MWhNQ3VDQ0RrOEp6WmM0TlZjNHpacE4rcnNOQnZpVHJFam5pMzc3aDFRNHlEWEVzalRFK0NzRGJteTF2N1E2aW5mWDg3UlBuYk9rNzlZdXFIRWFYaHlmbTA4ZzliRkhaaDRvQVpmTGFpSE1FQUdFSFhNRjVKN09SYWZIck5OZnh1ZzBrWUpKT0o5ZXVaeTdLOU9sbGRNRlVBaW1DVm8vTUVmNVJQcmk0ZHNEd2c2RStxSXc3cmFmK2RtSmdkYmhtQWZYbU1MQXV4SCtYc2IwZTEvQVpiQVBOc3dZTlFvcTZWMllrdEVQdk9CNTVCa2NwZGlmbS9EUSt0TVMxdHhGdWxuWG9zQ2MzMWNLRlRROWNPQlpYamJVb2V0MzNiT2NrcThwR3EyRUJqVC9wRmkyZ05XNEdoSjJLdXN5NGcyajdqMDNpRDdmUjZvT2pxK3BhYkdoaVNXUzFNOFpuRnQvblJHbWhSdWdwNVJRL0c5TTB3eWxzNk5jaDRzVDk5ZW0vcmRqbTFZRE93Vk1hcnhSVWN4WVR3azRDQjN1QWtpR3VUQWhLQnR5ekdETzFYZTNXSTBRNERldktmRE9OWDlCd2k0T05OeG1sTTBYOWwzTFhYeDB3ZmFQeTFYVkU3dVRqVHUrMFhWS043UnJCT3IiLCJtYWMiOiJjOTQ0OGEyY2NiMDYyOWMzOTQ3Y2ZjZDkxMTQxMmRmZTQzNmRmMzVhNmY1MjQwMmQxN2ViMTY5NTQ2ODEwMGQ3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImkwYmFvdmpjTkthdG9hRTZQQUNOdmc9PSIsInZhbHVlIjoiZXpvUzZNTFk5QVNmZUYwdVBOUWdVcUJJN2pyNytMdjlxbzdtYTZKY2ZPUDVzY0lja0tqV2NOL2RhYmZZQmJnMUh2cm81Rjd0a0o0UFRRa2JyTVM5RHplODY3OTB6R1MyNmNSNisxS1BNOFFpL0tLSzBIM1d6bDRIWDZiT2hsVnJOQ3F5dHFiNHJEek1kMExXd2E2MHBXRTRYQU9ZcDZid3M0V3llNVdYOWJuQmxuZlN3RkdFSlVmV2c3K09NOVFvVXlOb1BYNkZlbzBDeGo1RWh2SXdyRHc3RTRPeDNxMUliQkJvaXRvb0hKdDcydHk3VllCeTVVeE4zc1JIelR4K3YvQlZtV00zNFpRVlBjNUFJN3laNWFyUTZkZEw0d1MvM3ZrbFM1NGxiSmk0ajFDd2lBS0lxUUJwUFRRRmQ2YisyNW1BMmFwS0pseFpJb0tvMU9RNGU4MEUxN2pSSFFva3A2NE1OaFQxYWpQbFZqalhjTWZMT3lidyt3bTlsMm1laFR4MXYxSzhVWEJxalJhV2hzNkd0cSs2dVFaUkxYWUNaV0x0R2ZKMU0zSzRjT3lRc2VCVVIzaEpObVZwT1NRN1lnTmtkc2dicldVc0pudjlaY254dmgyYTVsSGN3bUpTT0pOVXFSWmM0VG1VVnloeFJVTFhvOThITEhIWDFvMnciLCJtYWMiOiI2YjEyOGMxYjljNzU3NzFmYmQzZjczZWZmZjY3NjA1MWU4ZTQ0ZDAwMzUwZWUzOTRiYzY3MDJhZjc3NmIzZGY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1253491254 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:30:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImQwOFJoT0V5MUZCVFU5WVJ2RE5jc1E9PSIsInZhbHVlIjoiR2tvR1YyazE4MmJiZkFJWDYvdXluczg3N2xpdTN5azBYSjRRNkFSSGFTWkQ2L2sxTmQ4cU1yZU40OG52UDZCcUM2TWlweUVnbG1ZUGdaUXA0bjJVZk1jamVieW1sdWNMWUhmS0xvUUxQY2NsNm8vS1FKWHJzZFdkWlBRSnI5QWJWSGJQWVlRV2Q3bmc2dFNMaHczc0VRdHFCM1RSN3lkWHM4cWw1MmsvUmMwVW5BOG9WUlB0bEdCaDc3eTMxN0pDdmtoTzljVCtEZzF6eENlUlR2c3hjUzFCSFBsVGhibE5RbTRKcVRFWFY5cnMwR1ZnYVhDRkl5Y3lmUFdnYlZsUlNVT3JzakpLZlRMaWhZbFdkdnNvZzV4NnlILzB2TXJiVk9YOE9xZEJmWlBvOHhxS0pvdGNnbEg2TFdndlZ6eGsveDBSL29rdmVrRTQrbGlLV3ZPMGd2L0Q4UFIrSUl1dnUxVitodUhYdTNQbVdXenI3c2JvM29PbXFDWmxGS1V3OHFLdTIxTUVCOS83SlhJRUdHV203NTRxUkk3bWJ1SllvVXh0cmxKdnpCSE94MWpIb0JGd0xTd3FhSXlMTS96NnhIM1FNTXc0ZkhoT3VuTzJkaWZVelBReUY1Q2wvUFRZWHQ0OEF3SFcxallseCtsVHI1aUxGV2d3bUlnVGRodmQiLCJtYWMiOiIyYTg5MGJmNjFjNzgwNzMzZDQ2ZGNmMmRkZWFjZThkNGRiOGZhODVlYmJlNTE4NzVjZWUwNzEyOWQwNTFhYmYxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:30:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImV5d0E4QlRmUzB3SVcyTENoWStsZ3c9PSIsInZhbHVlIjoiQWJNM04yZE9ud200RURuNnAxR3R1NHFyNUJKQWpJdVhpSExJb09rbWpiMmtMTXAxdXZUTHlER3dmdVdlbzMxTVdiTEMrNFRDWEVoWkg5TDZ4ckZnaisvQ1BNdXhtUnprazdPaVcrQU9IdFB3b2hxeDhyazFmSTBzTVFpSmkzMlpnUXlEdi8zTVpoYlB1RVhwSGZpQmxiVlFMYmhKb0FmRHBYalZxeGZ1NWF2c2lXaXoybzRremlLUEVLZm11NSszRHpuMXdZUFltcUdxTDJxZVR5YzVkRElwdC9TSEtBNjc3cEZUbm1YMVhzbS9zdXMzQ2pySjN6cmtzVmsvWWsyN0ZQRk41d1k5TjdLSUpuNzBQVy92TmpWa0gySEJYcUNzUVVjM3ZWK2hROW9lYlh1WkxJQThmWiszK0RjVGJtYzhVVnhUZGpVak82Zk1GSmVGWE9ZT1V4cktnV1BGRXF1dDY4bDQrLy9VMWtYblIzMlZ1NmNiV2k4aGpBRHVHSHMyZjNEc3lWa21FMWcvV0gvQSs1UGZpL3IxL0xqUlQ0UW9PdlBpRFd0WGRUellsVmczTzhqM1N0akdCeFFkeGhOU25oZmNFV2luUUFoeFhNd3FzYmpVUE5ET0QxSUk0WGhMNzJqalJZWHduTXZKQ1hFc0VrVWFpMytGcG5xcWdPWWQiLCJtYWMiOiJmMWRkMDJkYmY0YjAwYjRkOWM0ODY0Y2ZmYTM3YTdlNmYzMzBmNmMxYThkNDBmNDhmZjBmOTZhMDRkNmY3Njc0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:30:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImQwOFJoT0V5MUZCVFU5WVJ2RE5jc1E9PSIsInZhbHVlIjoiR2tvR1YyazE4MmJiZkFJWDYvdXluczg3N2xpdTN5azBYSjRRNkFSSGFTWkQ2L2sxTmQ4cU1yZU40OG52UDZCcUM2TWlweUVnbG1ZUGdaUXA0bjJVZk1jamVieW1sdWNMWUhmS0xvUUxQY2NsNm8vS1FKWHJzZFdkWlBRSnI5QWJWSGJQWVlRV2Q3bmc2dFNMaHczc0VRdHFCM1RSN3lkWHM4cWw1MmsvUmMwVW5BOG9WUlB0bEdCaDc3eTMxN0pDdmtoTzljVCtEZzF6eENlUlR2c3hjUzFCSFBsVGhibE5RbTRKcVRFWFY5cnMwR1ZnYVhDRkl5Y3lmUFdnYlZsUlNVT3JzakpLZlRMaWhZbFdkdnNvZzV4NnlILzB2TXJiVk9YOE9xZEJmWlBvOHhxS0pvdGNnbEg2TFdndlZ6eGsveDBSL29rdmVrRTQrbGlLV3ZPMGd2L0Q4UFIrSUl1dnUxVitodUhYdTNQbVdXenI3c2JvM29PbXFDWmxGS1V3OHFLdTIxTUVCOS83SlhJRUdHV203NTRxUkk3bWJ1SllvVXh0cmxKdnpCSE94MWpIb0JGd0xTd3FhSXlMTS96NnhIM1FNTXc0ZkhoT3VuTzJkaWZVelBReUY1Q2wvUFRZWHQ0OEF3SFcxallseCtsVHI1aUxGV2d3bUlnVGRodmQiLCJtYWMiOiIyYTg5MGJmNjFjNzgwNzMzZDQ2ZGNmMmRkZWFjZThkNGRiOGZhODVlYmJlNTE4NzVjZWUwNzEyOWQwNTFhYmYxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:30:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImV5d0E4QlRmUzB3SVcyTENoWStsZ3c9PSIsInZhbHVlIjoiQWJNM04yZE9ud200RURuNnAxR3R1NHFyNUJKQWpJdVhpSExJb09rbWpiMmtMTXAxdXZUTHlER3dmdVdlbzMxTVdiTEMrNFRDWEVoWkg5TDZ4ckZnaisvQ1BNdXhtUnprazdPaVcrQU9IdFB3b2hxeDhyazFmSTBzTVFpSmkzMlpnUXlEdi8zTVpoYlB1RVhwSGZpQmxiVlFMYmhKb0FmRHBYalZxeGZ1NWF2c2lXaXoybzRremlLUEVLZm11NSszRHpuMXdZUFltcUdxTDJxZVR5YzVkRElwdC9TSEtBNjc3cEZUbm1YMVhzbS9zdXMzQ2pySjN6cmtzVmsvWWsyN0ZQRk41d1k5TjdLSUpuNzBQVy92TmpWa0gySEJYcUNzUVVjM3ZWK2hROW9lYlh1WkxJQThmWiszK0RjVGJtYzhVVnhUZGpVak82Zk1GSmVGWE9ZT1V4cktnV1BGRXF1dDY4bDQrLy9VMWtYblIzMlZ1NmNiV2k4aGpBRHVHSHMyZjNEc3lWa21FMWcvV0gvQSs1UGZpL3IxL0xqUlQ0UW9PdlBpRFd0WGRUellsVmczTzhqM1N0akdCeFFkeGhOU25oZmNFV2luUUFoeFhNd3FzYmpVUE5ET0QxSUk0WGhMNzJqalJZWHduTXZKQ1hFc0VrVWFpMytGcG5xcWdPWWQiLCJtYWMiOiJmMWRkMDJkYmY0YjAwYjRkOWM0ODY0Y2ZmYTM3YTdlNmYzMzBmNmMxYThkNDBmNDhmZjBmOTZhMDRkNmY3Njc0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:30:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1253491254\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1837004499 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1837004499\", {\"maxDepth\":0})</script>\n"}}