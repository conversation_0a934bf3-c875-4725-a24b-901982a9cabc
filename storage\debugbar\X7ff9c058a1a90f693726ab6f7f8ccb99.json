{"__meta": {"id": "X7ff9c058a1a90f693726ab6f7f8ccb99", "datetime": "2025-06-08 15:43:18", "utime": **********.503881, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397397.845371, "end": **********.503901, "duration": 0.6585299968719482, "duration_str": "659ms", "measures": [{"label": "Booting", "start": 1749397397.845371, "relative_start": 0, "end": **********.406915, "relative_end": **********.406915, "duration": 0.5615439414978027, "duration_str": "562ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.406928, "relative_start": 0.5615570545196533, "end": **********.503903, "relative_end": 1.9073486328125e-06, "duration": 0.09697484970092773, "duration_str": "96.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44016400, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.028360000000000003, "accumulated_duration_str": "28.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4565248, "duration": 0.026940000000000002, "duration_str": "26.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.993}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4888859, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.993, "width_percent": 5.007}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 11\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 32\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1499553079 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396510751%7C76%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlROMEJLcFQvL1ZMaFdsQ3dNZzk0bUE9PSIsInZhbHVlIjoiOXNhVmZsK2RMd1BpOHpDYmRoYjhTSVRKOU1vaFZPdnRMS1hYdjBlWEdEQzdvdXNqakxXQ1RmNEgrR1NJbkdJWmY2T2ZvQUhqaUw5NW9xaW5DbmNUOXN3WHM1eXRsZGN2V0tCT2kvQ1RBY0p0SjNUaFl2aGRaNnRCNmFLbjgveTNQaGtUNjNPOEpxOUFlN3RzRVgwMklYN24yaDBUYjRkK3FtKzVQS3FiT0F3V29KdkxpbTdWUXVqdTl5YTNiUndtUjM3UGZ1aE9IT3ZQelZ6d1pLaDNoQnluNXl5d2J2QjJEeklGd1JKK3lJek5ENDZVcE13NU9jZ3g3eGk5KzBpazU2c0ZvcVJmVGMyNmdDZGhxVFJxUkg1eXQ5cEYrSUE3V1llVVoxMlpLV1I3WjM0SzVpR2NrYUFaaGlvMTZWODNlMzI0VVFiMEJHYUVzQ3pjN05kSnlZT2JDbmlQdWZwOFlNQkZFRXRCQ2M5czcySU9jN1pEQWVCS09pTlNLdFhEcXZPSGtPN2J0anBETDhFRUZ2SFI4ZW95WnVGOFFVa3h5b3ZucXQwNVNEMEkrT1V4c2d6dWtQVDlUQVN0aUdVWCtZRTdOZjV1OHpKNENpU0tlS0NMN3RFclNMOFA1UDNIVG9GRmk4akRDSEVNWlRDRkRZaGxIYjVnaWJjNEJRVEQiLCJtYWMiOiJmYjE0MGQ0N2I1NDY1Nzc5MGVlZTgxNTM3MzFhYjU0Mzg2ZTFhODFmZmY3MDUxNzlkYTE5YzIzNmI0NDAwZTE3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImpxeHVVWUJBdTdicmJLbHcxTTRONkE9PSIsInZhbHVlIjoiZ1o0dlVIUDNWOGVnT0pVNHRXNGdiSTliODl2Y2NyQUxHY2R6TUxBb0FKQW5nVm5ibUJqdjdacUQxMFNDWm5mcFkrUndtamN3TVJPMDJsNVJuYVVkTjlzWFQ3RXVMNVZ2RnRXMEJoeHZRS2o3cXY3MXREWVI0THI3Y2p0Rk11OTJZQW9WZTg2ZUluZ2tVK1BxRm54c0YxTVFrRlRLT09JaG1WcXY5T28yUGRNWXNRZm5JOHpyR0VUM2ZCZDFsbEZtRUZSMzJycUVPZklRaENTdVg5cGlaZThJTGZzL0tDeEJWZkp5SGdMMUVZYU9UMzYzSkhqVGJZWEJobnNVZkMwQUxOVXNublZiZVFia0hEWm9LNDhYNWZISjBVQmdwQ280ZTRnU040VzFTT1Q2aEhiTThBTWRYYm1DK1hxMXZGTjVtUlhDOUlNRFR6RVZ0QWs5Q2VseU9yMkJHQ2RuOXJnYTNwYlBneXlNNmhyWG1oWHBRekxjYkxsekphN1VCb1RLRUNrTWxBdkV0bW5xd1hFN0MrMXZxTEtCT3pBN1VIT3M4SlFXTC9JRWdVMnNnWFdwT0piNG0ydzhDZUpYV1UzV1lxeUJ2UVIrTEV3M0I5emZpY0htV0R5UU5KL0pQbktEUHBlOFM3bTZWSVNsdk5od1hqQmYyRmgyZnkwY0xGVlAiLCJtYWMiOiIwMGRiYjBhMzRkNzYwYTVhODk2MTYxZjRhNWM3YjkwZjlhZTZlNTk0ZmM4NjViMjgwMzQ1YTRmZGRjMzYwODhjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1499553079\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-27502221 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-27502221\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-503787027 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:43:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijk3UGtna3hVRU1FLzNGb25GYlZNekE9PSIsInZhbHVlIjoiNVlOeTllUXROUTJTbE5zVEtIQ0IxcEtxUmtiZVhiRmpabUswRUFmREEyZ1FwUmlleDRYQkJMZkVUWVVZWlczTmMxNWFSRnNHYW1jU1ZFMHdGdmdVdytPNTVFV3l2VDVjOU81Sm9nSm0rODk1eC80b0x3aVJjMnZhbkgvT2ZocGZqcDdDbVRhSEgzRCtVL2RCazMyQzIweGVKQVJQb0E3SVl5bUJacUZWamVxQkZxcUEwV0F3cDJncWdXMHVsWDdWTEx3U1lEWmRMOGJYN1VRQmhOOXo4aWpPaDl3d1NMdVBGQnJVT2RWQ3VhNURtdnVicDhRMzM0eHFTSnBJUnJxdGtOTDR4ck5UTTZxcmNvMXVMdlJyTC9xS0N3a0tDQTZzODk5YlJtMUh6QThjVEtjVzluV2VXK0F5ejZORXpPYndXQ1o0R1dVVmNNV0d4RnlKbnkxdVpMQ1MwMm5WekxCeG1qbFg4aFNUbERMOHAwaWNCS3l1aXRhcVU4UXZ6a2pLMERvYkpDa211ajNpWG1hK21rUTJ2K2ZxNjBjQ2dhK2RMUFNRc0NlVE1PUS9jVlY5LzNXeENGc0dUUHQxQkpqcVJsRVYwLzJrNDhia0hTaVorMHRuUDRSUXRNN0lkS0Q2dHVVbFgrRXlGNUtKNDhyaElTeVNuQzVCOUsyTWRGdnYiLCJtYWMiOiI4NDQzYzY0ZjE4ZjdlYzI4Njg3MjMyYTg2NThmNzU0MWRhZTEyODY3YzIwODBjZjBkNDMzNjFlODQwMGU3MjIwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:43:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjErR085QWhyd3MvNmEzYUg5YWNrVHc9PSIsInZhbHVlIjoicXdyV1ppa0RYeXZXakZrYzh4cWN4N2NXWGlOdUhhMWZjNjRkMm9KUjdBOGNiYm1DcUVQL2FYVHBTN1pTaG96SWFzK1Z2aHVhM2ljWVRkZ3grRGRzSFgyU2phRCtieUtxS05LTStMckc1Q2ptMlJ2UjRDMG91SVg3NSt1ZnlqK0FsM0RZdHA5Ui9iS3VYQW1ocjBTYlF6OFdpb2dMc1B5RmN1OXhKMjJpNE1HcFAzaXUyRExTam5GOE1GeWR4TnFKc3lyNXlIaVJYdmFLSzRnUWJQQjZWV3J4YjI1elBKdDJYaThwRGFtL3RpTXozWWVjSUIxeVNtVlI3d0tiN0lDaG40ZGVmQnJiRFNrS1ZCOXlINWpWS1RRR2M2QWg5V1V4NUp1cXV1UUxSRmNmT3MyWEZZc2NjbnArbTkyU2JLRDUvVEJaS2htVzQ3anBPekFkdTdVY3ZGK0pjRi9haGdJWGsrS3d4MjYyYTdEazA5ME13NWlRZDZpVW1jYVBrWEdTZzkva0ozcjZySkxnOE1NMmU2UEFHNUN4VzEzVHhjeXMreUxOSnM3TUVKNjZmRHpubVVRL1B1eWxBbHZpU1E1Q1BjY2tpLzVZekYxM0V6UzlUSnhzdk5NeS9iM0xDLzBWNHJjYTNxNjh1Y3k0dGY5MWMvK2tVTklnUTY4UGptU2IiLCJtYWMiOiI1MDE3NjBkNWJiYTE3NDc1Zjc0MDEzNjgxNmVmZTBmMTczYmNmNDJlMGJiZTZmYzMyYjgyNTk3MWY1MzA5OTEyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:43:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijk3UGtna3hVRU1FLzNGb25GYlZNekE9PSIsInZhbHVlIjoiNVlOeTllUXROUTJTbE5zVEtIQ0IxcEtxUmtiZVhiRmpabUswRUFmREEyZ1FwUmlleDRYQkJMZkVUWVVZWlczTmMxNWFSRnNHYW1jU1ZFMHdGdmdVdytPNTVFV3l2VDVjOU81Sm9nSm0rODk1eC80b0x3aVJjMnZhbkgvT2ZocGZqcDdDbVRhSEgzRCtVL2RCazMyQzIweGVKQVJQb0E3SVl5bUJacUZWamVxQkZxcUEwV0F3cDJncWdXMHVsWDdWTEx3U1lEWmRMOGJYN1VRQmhOOXo4aWpPaDl3d1NMdVBGQnJVT2RWQ3VhNURtdnVicDhRMzM0eHFTSnBJUnJxdGtOTDR4ck5UTTZxcmNvMXVMdlJyTC9xS0N3a0tDQTZzODk5YlJtMUh6QThjVEtjVzluV2VXK0F5ejZORXpPYndXQ1o0R1dVVmNNV0d4RnlKbnkxdVpMQ1MwMm5WekxCeG1qbFg4aFNUbERMOHAwaWNCS3l1aXRhcVU4UXZ6a2pLMERvYkpDa211ajNpWG1hK21rUTJ2K2ZxNjBjQ2dhK2RMUFNRc0NlVE1PUS9jVlY5LzNXeENGc0dUUHQxQkpqcVJsRVYwLzJrNDhia0hTaVorMHRuUDRSUXRNN0lkS0Q2dHVVbFgrRXlGNUtKNDhyaElTeVNuQzVCOUsyTWRGdnYiLCJtYWMiOiI4NDQzYzY0ZjE4ZjdlYzI4Njg3MjMyYTg2NThmNzU0MWRhZTEyODY3YzIwODBjZjBkNDMzNjFlODQwMGU3MjIwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:43:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjErR085QWhyd3MvNmEzYUg5YWNrVHc9PSIsInZhbHVlIjoicXdyV1ppa0RYeXZXakZrYzh4cWN4N2NXWGlOdUhhMWZjNjRkMm9KUjdBOGNiYm1DcUVQL2FYVHBTN1pTaG96SWFzK1Z2aHVhM2ljWVRkZ3grRGRzSFgyU2phRCtieUtxS05LTStMckc1Q2ptMlJ2UjRDMG91SVg3NSt1ZnlqK0FsM0RZdHA5Ui9iS3VYQW1ocjBTYlF6OFdpb2dMc1B5RmN1OXhKMjJpNE1HcFAzaXUyRExTam5GOE1GeWR4TnFKc3lyNXlIaVJYdmFLSzRnUWJQQjZWV3J4YjI1elBKdDJYaThwRGFtL3RpTXozWWVjSUIxeVNtVlI3d0tiN0lDaG40ZGVmQnJiRFNrS1ZCOXlINWpWS1RRR2M2QWg5V1V4NUp1cXV1UUxSRmNmT3MyWEZZc2NjbnArbTkyU2JLRDUvVEJaS2htVzQ3anBPekFkdTdVY3ZGK0pjRi9haGdJWGsrS3d4MjYyYTdEazA5ME13NWlRZDZpVW1jYVBrWEdTZzkva0ozcjZySkxnOE1NMmU2UEFHNUN4VzEzVHhjeXMreUxOSnM3TUVKNjZmRHpubVVRL1B1eWxBbHZpU1E1Q1BjY2tpLzVZekYxM0V6UzlUSnhzdk5NeS9iM0xDLzBWNHJjYTNxNjh1Y3k0dGY5MWMvK2tVTklnUTY4UGptU2IiLCJtYWMiOiI1MDE3NjBkNWJiYTE3NDc1Zjc0MDEzNjgxNmVmZTBmMTczYmNmNDJlMGJiZTZmYzMyYjgyNTk3MWY1MzA5OTEyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:43:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-503787027\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>11</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>32</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}