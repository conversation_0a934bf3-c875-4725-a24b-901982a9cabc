{"__meta": {"id": "Xb9e250c5c2f4363c483128b0f438fb0b", "datetime": "2025-06-30 15:34:44", "utime": **********.549945, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.112921, "end": **********.54996, "duration": 0.4370388984680176, "duration_str": "437ms", "measures": [{"label": "Booting", "start": **********.112921, "relative_start": 0, "end": **********.495528, "relative_end": **********.495528, "duration": 0.38260698318481445, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.495541, "relative_start": 0.38262009620666504, "end": **********.549962, "relative_end": 2.1457672119140625e-06, "duration": 0.05442094802856445, "duration_str": "54.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45348680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00302, "accumulated_duration_str": "3.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.527838, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.192}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5403562, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.192, "width_percent": 15.563}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.542897, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 86.755, "width_percent": 13.245}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1712437495 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1712437495\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-250802477 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-250802477\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-542381315 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-542381315\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-406366332 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297680604%7C5%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlnVE1YWGY2OXZoTWFOL2YyUGhXL3c9PSIsInZhbHVlIjoibjNNaTI0OGIveVNDWmU0eG9kdUxoWEMyMWladHpyS1YxeTZEeTZRZithVnJZOXJsWXhmWENLT3k0azAzK0cwSlk1TVNYeWdqVFV3ajNaeFR1cDhpRU1va2VSUitDN0tWcitRQ1ZnUFNBU2ZtYm9Jc2VleWx2ampZWTZqV1huWnNHLy9mWmJHMDNLdHB2Y0hnNkR2K3lYUW1FaWtjdFJPVGNmbmNsMjdBUkdUQWZQN09wa2FWYzUybk8xakRTTEpwQUFORXZIVWxLdXQ2UjNkdXVJa0M4U1R0TGx3RGUyRjcvbXlINEF0Z3FIb0N1THQ0MnIzUnpHVFBTTzQ1aEtSeG1KUEk0NWtQN010Q3BsUThiNXJEN1FMTmtNMFc4dDJNaU05VnJDUHRrbzRzVm9lYURnWjdFSlAvbzB6UVAwNFhvOXp2SDdoUkZuNnhxUDBYY2VBTVFYQW5NWUFCRS84Slc0b3d2b3I5NFB5eFE3YXVYbEV4MUpoZ0pOcGE5cE56SVNzdTRWb3UycDEzQ2I5enlNWi9adkN4TWgyUG83eHUwSlpaME1GaE5LWm5xNFUwZk9EMGkyMUJDTmpIcTF3L1RZWExrMlBEYktCNzF4K1AxS05QVFo3aThxTHdZNTlQc2EvU3RSWnFOM2lNbUNReUFQbTVoY3ZoTXplcFFTenkiLCJtYWMiOiJjMWQ3ZTY2MjVjMWE5MjU0M2NhYTY4ODFkMGQxYzYwMDU5OTVlZmM2Nzg0ODk1NWUxNjUyN2YyMjUyMmE4ZWYwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVZSzIxQlR1Szg2bUg0TlNOQ04rUXc9PSIsInZhbHVlIjoidVlLNXBBL3NtMEpKR204SWJUc3hwQktsTDd5d3N2R2R0YTVuMmhtNkR4dlIzYzhqRGVhMktVUHF5UVQ4NmpnQUVyLy9sdDJLWVdoTlV3eXZ6QVQvdnNTR3dEKzFMbjVmamhBRlFWaWlKU3Mvb0pnWkhOckE5RHNPVWwrQy8rRzFhN2NMWGlmaEFMendiRmZoUzFhNGNkbzdpNGl4QloyVlZoU1BwYlVsdHpkSnF4c3hQNVpNcDFvazhFcm80RFhmWmFZSUJNKzdHbXgwd2lwTk8raVJxcC9XWXNscHR3YUd4eTY2ZWNrWEpNZm1vdkJkZ1ZPUEpHbkJuNmo1UTR3YnhQcWVvRzRrY2xDSmdEQXhmN1RQV1NraVZucjRlNFBRQ3A4dVhyRUZNUngycktnb3plQVBNVEFIaTNOaUk0bUcwb0hJRVBZS2dWTEZNTXBDdEx2RXYrMGtnTVIvT3hFeUNaSFhnUThEUEdvdHN6VEp2ekFVSk9aNUNkMDhzcDVaNVdGVUtqbmh2eFc2Y2s4NzFaM2lkTHQ2T0RHa1d1SGtUK1dIMEpzMzUvVW9Yb1JtZjYzVnVDQ1Vqc2lNOStBdFR1TnpLWFlQYjJKZS96L3B3eW1PdXlENWlpTDNBYnU5SW9aaHZPSE9ZblV5WS9NSm9ESmxhRHZBVFh4UENmK24iLCJtYWMiOiJiY2UzM2M2NTRkYjczNjYxN2Y4ZDgwNWRhNTE0YzY3NTYxNDc2ZjRhMTI3ODJkNTc1ZjM5NzE4NTdmMzM2NGIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-406366332\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-612486243 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-612486243\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2146127705 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhvSXYyOHlUM2UxUXEzdk80VWNPQkE9PSIsInZhbHVlIjoiWmo3c2w5QXlnYjM5K3NLYmJSNk9MRkhEMjQvSCtaR1Fkb2IxODI3QmlxVm9FVHFGN01uejZUWVJhRDk0bzR0eUtqMXhSQU1lbmU0alI5TEtsMGtWcGRhTTVNWWl6Y3FOV0VxSzMxVmR4V3JjZ1NITkQ1Y0FBVmErWlJMdWdZaGl5alNULzEzdEc0S2R6MExvUFh2YWxnMExsQlZLT2cyTU9WRzVjUEx2dnhPemxDM0JQTys4eU9SRHlOcEhEbTBvdDkrOTN5TEc2RFdnZklDOEZTMk8rSEFjcGtnc0NTV2hBT2UvWC96RWExVzNsM1pKQWdwaFU0WGdla0J6QWp1TkRPQU9md1g0ZUxRLzEwbEo4ZUQ0VnZNVDAzUmJnZU5DVFY1MjVhNVdSSE41MmVwNm42NEFhUlNCbXBCWWRscUJIcXh3WFpSbFppNWxhdTB4OWl4c0xMTk11ay8vNkdVNlNJK1BYbndzVDlrUlZnLzl3TUNhTXo2cThqWFpMQmJjOUF3cWtmYWZPdS9mOGQ4ZU1FRFhMdUVkWS9NaHZoamdxenFmSnV3Y0JGdk14SUYxWnVXUG5lOXU2d1FPVHlXTkFSZWllZzlGbmo1OWMxemFwcTNVa3JhV0oxRHBNSkNIR2lXR1plWWlFMi8yd2FWNkovNmQwMW81ZVZJaWp0dEYiLCJtYWMiOiJlOTdkMjQzMTkxMDkzN2Y1NDkzNjY2NTJjNDI4MTE3ZTk5Y2ZjMWE4ZWRjYjU1ODZjZjQwMDRmYmYxMmFjOTUwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBIaFlveGZyWWxDN3VpOGxBZW5Bc1E9PSIsInZhbHVlIjoibEowVkl2eGV3V1VqVFliRzFORlFhbUR1dEZvTWk3eFNWQXNJYnQ4a2VUcVJyOFhTYWpWQ2l0cnl3OUwrS09HZ2Q5VWNydXowaFZEZFZhRnpSVzdadkpvcENIVk5lK0lhczRjaFFGTlhqYWZrZ1M3cWJsUHhoa1lvSUFxRXNoUlNiQmJRRTROQi9nNTJoVUlVd1EvOHk2VURmTzBrcVJVTmlOY01ZNDFVQjhWRTNtUHRZR3drSUdYYmE4cURWU0lCVkRZM1RZUnlaMUxvcDhSVFdRQlFKNkFQRmtwN2xjYkpSc2UzMFZsTFphQ0FnV1lPTmtPcTJ5TnZlUkN3elJlU3NGRENpVFlzbDVkUVlEVkV3am1aOW9BdExFUXA2dHUrK09xV083VTlSUEZjSllJa1JDZjNDb3BoTFB3S1N0ODM5bks1YTMzQmUwQy8rdU9EanpWR2k3U1FZSG1LV3BodVNxL3k4Q1IxTC9hekp0djFxMXBwV3ZDb01GenhtNDEzU1dCYVBRVGU1UjB2UG9XRG5xVHdpRFNIaWZjVWpiS3VkaEprRitJci8xcmxkZ3QvbklWRW4yVnJrTHl5RW9PejVzaW44SnI0eHBlemZGeU1rbHQ5QWdwZ094MHc4MkY1L0VpYU1uNmxuYnRhK2x6UFBuOFozVmxQKzlMZ2h6MVkiLCJtYWMiOiI1MDYzOTc1NmVmODlhN2ViZjI0YmIyYTc1MmMzMDEwMWM5YmZhYmVhOWY2Njg1OTlhMjE5NTEzMWQxNTAxZjE4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhvSXYyOHlUM2UxUXEzdk80VWNPQkE9PSIsInZhbHVlIjoiWmo3c2w5QXlnYjM5K3NLYmJSNk9MRkhEMjQvSCtaR1Fkb2IxODI3QmlxVm9FVHFGN01uejZUWVJhRDk0bzR0eUtqMXhSQU1lbmU0alI5TEtsMGtWcGRhTTVNWWl6Y3FOV0VxSzMxVmR4V3JjZ1NITkQ1Y0FBVmErWlJMdWdZaGl5alNULzEzdEc0S2R6MExvUFh2YWxnMExsQlZLT2cyTU9WRzVjUEx2dnhPemxDM0JQTys4eU9SRHlOcEhEbTBvdDkrOTN5TEc2RFdnZklDOEZTMk8rSEFjcGtnc0NTV2hBT2UvWC96RWExVzNsM1pKQWdwaFU0WGdla0J6QWp1TkRPQU9md1g0ZUxRLzEwbEo4ZUQ0VnZNVDAzUmJnZU5DVFY1MjVhNVdSSE41MmVwNm42NEFhUlNCbXBCWWRscUJIcXh3WFpSbFppNWxhdTB4OWl4c0xMTk11ay8vNkdVNlNJK1BYbndzVDlrUlZnLzl3TUNhTXo2cThqWFpMQmJjOUF3cWtmYWZPdS9mOGQ4ZU1FRFhMdUVkWS9NaHZoamdxenFmSnV3Y0JGdk14SUYxWnVXUG5lOXU2d1FPVHlXTkFSZWllZzlGbmo1OWMxemFwcTNVa3JhV0oxRHBNSkNIR2lXR1plWWlFMi8yd2FWNkovNmQwMW81ZVZJaWp0dEYiLCJtYWMiOiJlOTdkMjQzMTkxMDkzN2Y1NDkzNjY2NTJjNDI4MTE3ZTk5Y2ZjMWE4ZWRjYjU1ODZjZjQwMDRmYmYxMmFjOTUwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBIaFlveGZyWWxDN3VpOGxBZW5Bc1E9PSIsInZhbHVlIjoibEowVkl2eGV3V1VqVFliRzFORlFhbUR1dEZvTWk3eFNWQXNJYnQ4a2VUcVJyOFhTYWpWQ2l0cnl3OUwrS09HZ2Q5VWNydXowaFZEZFZhRnpSVzdadkpvcENIVk5lK0lhczRjaFFGTlhqYWZrZ1M3cWJsUHhoa1lvSUFxRXNoUlNiQmJRRTROQi9nNTJoVUlVd1EvOHk2VURmTzBrcVJVTmlOY01ZNDFVQjhWRTNtUHRZR3drSUdYYmE4cURWU0lCVkRZM1RZUnlaMUxvcDhSVFdRQlFKNkFQRmtwN2xjYkpSc2UzMFZsTFphQ0FnV1lPTmtPcTJ5TnZlUkN3elJlU3NGRENpVFlzbDVkUVlEVkV3am1aOW9BdExFUXA2dHUrK09xV083VTlSUEZjSllJa1JDZjNDb3BoTFB3S1N0ODM5bks1YTMzQmUwQy8rdU9EanpWR2k3U1FZSG1LV3BodVNxL3k4Q1IxTC9hekp0djFxMXBwV3ZDb01GenhtNDEzU1dCYVBRVGU1UjB2UG9XRG5xVHdpRFNIaWZjVWpiS3VkaEprRitJci8xcmxkZ3QvbklWRW4yVnJrTHl5RW9PejVzaW44SnI0eHBlemZGeU1rbHQ5QWdwZ094MHc4MkY1L0VpYU1uNmxuYnRhK2x6UFBuOFozVmxQKzlMZ2h6MVkiLCJtYWMiOiI1MDYzOTc1NmVmODlhN2ViZjI0YmIyYTc1MmMzMDEwMWM5YmZhYmVhOWY2Njg1OTlhMjE5NTEzMWQxNTAxZjE4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146127705\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}