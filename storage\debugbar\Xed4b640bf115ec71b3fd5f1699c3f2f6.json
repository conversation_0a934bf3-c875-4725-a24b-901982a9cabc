{"__meta": {"id": "Xed4b640bf115ec71b3fd5f1699c3f2f6", "datetime": "2025-06-08 15:34:35", "utime": **********.882381, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.039902, "end": **********.882401, "duration": 0.8424990177154541, "duration_str": "842ms", "measures": [{"label": "Booting", "start": **********.039902, "relative_start": 0, "end": **********.777661, "relative_end": **********.777661, "duration": 0.7377591133117676, "duration_str": "738ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.77768, "relative_start": 0.7377779483795166, "end": **********.882404, "relative_end": 3.0994415283203125e-06, "duration": 0.10472416877746582, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139664, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013629999999999998, "accumulated_duration_str": "13.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.832742, "duration": 0.011699999999999999, "duration_str": "11.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.84}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.859262, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.84, "width_percent": 6.676}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.870491, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.517, "width_percent": 7.483}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-645380652 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-645380652\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-149356099 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-149356099\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-316043841 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-316043841\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396857466%7C13%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNpYXg1c251K2RVbUs2Vk5MZFV1dGc9PSIsInZhbHVlIjoicFBPL2RxL3ZKNVA5WlJqclRGM2lrWFBnNURkL0paeDJtYzFPcTNzdlJKVG5Qb05wT1VUZDE1cVlkb21Nbk9JZGZOeEhPQVM4QitsenBtdkp3d1FMV2k1N1VKdVhhMG01MzRlSjduL0QyVmRZbllYV1dkNHZkZjkrK3RZbnY0bVF5MURNc09hRjRocXZsVjk2ZUVWSkF0Qzc3c3BTR3M1UkZJb2N1Y1JnMm9sei9SZXFLS21UeUFGbnR6WHhLQXViaThHeU95Z3U3NDBlcFJhNHlpUVBNZ1FpWEVRQXNGWDRZSWw4YzBndDIycmMrUG9IdTdtM2FtMUxSVHZPR2Z2QjFrMUIxc29ZRXdHQnNaejFzRkEvcTZMRTBab2hCWFBRV3Bobk1LKy91VXAvMGsvbnBmckdnL2pHRnNIb1BUVWhUaVBjdDk5SERFZS9xcWFKZXFnM2VlUGFQYnlraGRWQTlzS1NkbSswQ2VVdUN2T0U1TitOR3FCRmI4RmU5Yy85YklUa0FTUTJLRXZTNjZSZFBzOXRwQWR1R29HanNUVVB5REwvRFdaUHZka3lhemZwY052bms0ZUw1S3dlNUFMVEx2eGNwQldNNXN3Z0o4OE1PVytQZUlUUlZHc2xyKzJRNEExSmlNR05PSGthNkxIU0xTZ0U1R0dHTC9TTjF0N2ciLCJtYWMiOiIxZGJkNDZmMjVhYWY3NTA5YWYyMTdjNWI1YzBmYmZhYjIxY2RlMWM1NjkxZWY2ODYxMjM0ZGM5YzM2YTJjY2FhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IitKcEhSTGhtaXBWaXNmWWQxeXdQbkE9PSIsInZhbHVlIjoic1Y5WFpBQWRmZjhQRHp3RTRyMmFqakcwMzRBR2dxUGFQYU02dUlmSUZZRU53RHdQelg3V3RweitDS25VTVRldWJyZ1h1blZlUGc5czdlWVRFSjROUjlsR3pjL01kTXFpRm1UUHlDcFdoNlNTVG5vNmd4WklnOElzeU44Z3hPTHA1WmFtTGVycUFZaFE5bk5RckRtbERNQ2NLejVJMXd2Nld6Z3ZsV3hDU0NFNjJ4Y3JLdnhRUHBNSlhodjFhVHZFbG80MVpHL3NBVkZIVlhJVWlIbkc5b3htT3o3QjF2Mnhoc05FMkR6Q0RleTZNdUFERjkwbWhwSWNueXhnZysxbWpxZlA4RitRQjVTbmZiZmhiYXA2YkxIeWFzZks2NjlRSmNGbm1wT3RvYjRtYnJnNi8vQmhiMDIydmd1dFdTZ0JXQjhvN2cydjVQek1YTjdnZTZJVGFhdm1MTCtxR0JpSzRVYW1YWG40S0xpSGZjNCt5UjI4REpmUlZnbmdybUJIM3R5YkNoUUxhNWFXOU9KRFJ3VGQ0TmViRWNxeVZZQzc1STZra2xsVk1Cdk5QMjgzYVQ0eFRwVnM2dkFsMmZkbkN3bHJrbnRkWEgxS1huRzRHNEliUGYyMXR6Z1RvRXVZd0JUSVJKcDgwUWtnOFYrbnJBQlFDTVMyalN1VWxGenkiLCJtYWMiOiJhOTg5OTdiMTdiY2QxYjBjMmRjNGM5MjFhZTEzMzVjYTc4NmJkNTJkNWZmM2RhODcyZWViZDQ4OTJkN2QyNzA2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2013056102 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2013056102\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1692945699 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:34:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNNVGUvbVZ6NnRxNENYQ0xyS08zdFE9PSIsInZhbHVlIjoiTVk5c1JCQVg2TDZ5clBPZUpBdUlTZUJHZmMya2J2S1dzS0pwT3RTdVdOSXRoRXVOMWN3NnFZUktNNGJZRUx3WmNvK2ZhOVVGTGNrNklrN1NsejZzcUkwZ3pHVEcwS0FSK09LV0d0dlZwaWRRUUpRWHVRY3dKc3BoU1VSNTN4MTFWOHN4UmtnU3RMTS9yS3NCR20zSVpwK2pJcXZ3WktLMXZrK1psSE1tTG8xak4rM205UyszaXc0eWJnNXNyeFhEcVJ2MEU5TTBDUG00N1pLWCs4N2phdVF3azdNN0xUV1VpcEJEVUl4K1VIUTdmczBFbGk2R2NTUVNOTzlONElaRGlIQ1VxbkpRdmdIeDdmTzJncXM1RVliUjY5L2tBODMzaHFkcU1MaVJyMUhraDRmVWErZU1PLzk1TlBaaEdxTXZTNzZ1eDYwNU05SVNERFdWK3JGdkd6MEpJM3F3dFMwS1dUZUlJN3dvVXF2YXpNdG01RHNpL240d2l5MTRuQlB5RmtPcWhvZ3RqQzlQSVdJbVpRMXk1aEg5VTlmY2RnZm83VlVBZ2N1V2FRbkplQ09pNVJ0UGVnbjUwZUJqdEc1dlpJOE1BRWdSakhYc3JCRlpjdlk4K2FTUXpnL3BqYUxSQXdvYmRZMTA2a0xiY1NuVjBZUHFvcDcySkJiL1FmNjciLCJtYWMiOiI3ZmU1NmZkNDFlMGRmYTUyNmNmNGVhZTJiMjA2OTdkNjhmMjk1Y2FiMjJlN2MzNzdjYzcxMTEyNDYyNjFjNjJlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:34:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlR3bzd5MTVrVEk2ZW1tUVlDbkxZR1E9PSIsInZhbHVlIjoiU0JDV1J6Y1hzSGUzZXltYzlYK01QZ2djbnJwOWhrVXZaN1hBdUsvU0V0cVNVMkVGcCsxR2tLMWZ1N0syZjhxd0ZCUTdtWlE5d05Vblk1VE1xMHl1SzF5UlhSWkJiMC9nUkVaNUVtL0Vza3g5a092cldnRFJGcGM4dy9xbEQyOW1XZlNDV2E1NzZyT092Zk5FRGtiL050VDh4Y212cFpKZFdxSU54dzRtWVRrOUVQbjg5d1dmVm96L2pMdFJhUktKWCthOXRIM0N4bWJGbmZ6clNFWmJ2STJlMnplNVJIWk5iQ1RRbmhOcXRPTnhPcCthbnp5QzFqWXptWGk4RFdhbVJteUJSdmlDZ1RhUlBJL1lVekFRYTdCZHZhLzBPbjZnT01rYk1SZXdYSjc3cFVtalJBZXU0OEpLWVF3K2t6bVN3MDhSQzBmVzdDaFlCbjBScjZ4RTFERE5zczNvanJiOVRXRWhscHhxQmVmVUxKbW83ZG84RUxBL2lxdDlIbjJzRXoxaTBUUmZ4T2paZEZRSTlWUUM5QmlBUUx5SVJHUXNkYjZuOGZRWldwcXoyQWkybWJWdXBiNTBKbENCZ2w4SnRoU2NxdkE5eFFsWS9hSUZGUDRmbDFGWkJHNk5DQVVvdW45TVBIbFpFL2FNNi9hazlRaHQ1V0RsTEc5L2EvazUiLCJtYWMiOiIwYTQ4MGViMjRjYTAwNmYxNTEyMTZmZWU5YWJjZTlhZjM4MzM3YTliMGMxOTQ4YzBiODYwZDU0NDE1Njk3NDY0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:34:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNNVGUvbVZ6NnRxNENYQ0xyS08zdFE9PSIsInZhbHVlIjoiTVk5c1JCQVg2TDZ5clBPZUpBdUlTZUJHZmMya2J2S1dzS0pwT3RTdVdOSXRoRXVOMWN3NnFZUktNNGJZRUx3WmNvK2ZhOVVGTGNrNklrN1NsejZzcUkwZ3pHVEcwS0FSK09LV0d0dlZwaWRRUUpRWHVRY3dKc3BoU1VSNTN4MTFWOHN4UmtnU3RMTS9yS3NCR20zSVpwK2pJcXZ3WktLMXZrK1psSE1tTG8xak4rM205UyszaXc0eWJnNXNyeFhEcVJ2MEU5TTBDUG00N1pLWCs4N2phdVF3azdNN0xUV1VpcEJEVUl4K1VIUTdmczBFbGk2R2NTUVNOTzlONElaRGlIQ1VxbkpRdmdIeDdmTzJncXM1RVliUjY5L2tBODMzaHFkcU1MaVJyMUhraDRmVWErZU1PLzk1TlBaaEdxTXZTNzZ1eDYwNU05SVNERFdWK3JGdkd6MEpJM3F3dFMwS1dUZUlJN3dvVXF2YXpNdG01RHNpL240d2l5MTRuQlB5RmtPcWhvZ3RqQzlQSVdJbVpRMXk1aEg5VTlmY2RnZm83VlVBZ2N1V2FRbkplQ09pNVJ0UGVnbjUwZUJqdEc1dlpJOE1BRWdSakhYc3JCRlpjdlk4K2FTUXpnL3BqYUxSQXdvYmRZMTA2a0xiY1NuVjBZUHFvcDcySkJiL1FmNjciLCJtYWMiOiI3ZmU1NmZkNDFlMGRmYTUyNmNmNGVhZTJiMjA2OTdkNjhmMjk1Y2FiMjJlN2MzNzdjYzcxMTEyNDYyNjFjNjJlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:34:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlR3bzd5MTVrVEk2ZW1tUVlDbkxZR1E9PSIsInZhbHVlIjoiU0JDV1J6Y1hzSGUzZXltYzlYK01QZ2djbnJwOWhrVXZaN1hBdUsvU0V0cVNVMkVGcCsxR2tLMWZ1N0syZjhxd0ZCUTdtWlE5d05Vblk1VE1xMHl1SzF5UlhSWkJiMC9nUkVaNUVtL0Vza3g5a092cldnRFJGcGM4dy9xbEQyOW1XZlNDV2E1NzZyT092Zk5FRGtiL050VDh4Y212cFpKZFdxSU54dzRtWVRrOUVQbjg5d1dmVm96L2pMdFJhUktKWCthOXRIM0N4bWJGbmZ6clNFWmJ2STJlMnplNVJIWk5iQ1RRbmhOcXRPTnhPcCthbnp5QzFqWXptWGk4RFdhbVJteUJSdmlDZ1RhUlBJL1lVekFRYTdCZHZhLzBPbjZnT01rYk1SZXdYSjc3cFVtalJBZXU0OEpLWVF3K2t6bVN3MDhSQzBmVzdDaFlCbjBScjZ4RTFERE5zczNvanJiOVRXRWhscHhxQmVmVUxKbW83ZG84RUxBL2lxdDlIbjJzRXoxaTBUUmZ4T2paZEZRSTlWUUM5QmlBUUx5SVJHUXNkYjZuOGZRWldwcXoyQWkybWJWdXBiNTBKbENCZ2w4SnRoU2NxdkE5eFFsWS9hSUZGUDRmbDFGWkJHNk5DQVVvdW45TVBIbFpFL2FNNi9hazlRaHQ1V0RsTEc5L2EvazUiLCJtYWMiOiIwYTQ4MGViMjRjYTAwNmYxNTEyMTZmZWU5YWJjZTlhZjM4MzM3YTliMGMxOTQ4YzBiODYwZDU0NDE1Njk3NDY0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:34:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692945699\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-671396380 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-671396380\", {\"maxDepth\":0})</script>\n"}}