{"__meta": {"id": "Xfb3144c33841a1794bbdeae5153a9930", "datetime": "2025-06-07 22:18:18", "utime": **********.788471, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334697.272484, "end": **********.788503, "duration": 1.5160188674926758, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": 1749334697.272484, "relative_start": 0, "end": **********.57474, "relative_end": **********.57474, "duration": 1.3022558689117432, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.574763, "relative_start": 1.302278995513916, "end": **********.788506, "relative_end": 3.0994415283203125e-06, "duration": 0.21374297142028809, "duration_str": "214ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45100464, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01712, "accumulated_duration_str": "17.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.663656, "duration": 0.012150000000000001, "duration_str": "12.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.97}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7117782, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.97, "width_percent": 7.769}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.747923, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 78.738, "width_percent": 11.507}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7667642, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 90.245, "width_percent": 9.755}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1819385578 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1819385578\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1819618920 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1819618920\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1105389394 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1105389394\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1181904815 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334686693%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkwweWhCZW9SeitaQ2N4V1owZGsvbnc9PSIsInZhbHVlIjoiZldpRGFwQ3RDZGtEV2F5ZXozVDRFRGg4aGFRSlI5OFBvam10Qk45bzFPT3hTZTQ5VlN1SjdOdWwvZ1lMSlpzQUg4WU1tZ3dFam91VGpkTENTQmxqUGJlQ0I0emdtaFJpMmFPajBmTG9FQzdkS2dBRTQ5UmNhUWZoT0dmVXlodWN4TGh4UUZaSC9GM00rUmtxc1Y3MndydUpyQnFnd2ttcXlMbElWQWtXNHhlcHVyYlkwVXBEU0kyeXVPRmNvUXdwRkltdTh2anZ3RTk5TUFLYXIxVUpYblFyOU9vRmk1Tm9WdDdUZGMxS2tWUkJodzNwQmJXcDdScG9PYjd5TUJ3WTh5S0FDK3ltUE41aDRFWjBYZDdENUkzTFpYVStPQ1dUOEtTeTNhbngxMEJab0pZQ2JDRjlTd3FCVE5CV0RIa3RMbUJyV0MzaWxmMVllaDNlZWdUTS9IMDAyekx2dFNRUlAvN1BTbXpzU3k5azYvNWYreGhyakZJR0krd3RBTDZ2Y1ZMcW9abjkxbGhRdTljTVVnK0xWR1dYUEhRNnVXSzZnUjN3N1pMYjRwRG4zR2gwMEltRERjaEczSmZNdlZMdVZ6Z1VkWHVicmVzYXc2bHhhM3RMeXNQb3NpNjlTSjJVUzVCNm5UUlpBYXg1Qlg2WEI4WnFvNlpqR3lTVkJmN20iLCJtYWMiOiI2ODY3OGNjZGI2YTUzNzIyMDMzMDlmYjgyZTc4N2FkZGY5NzA5YzVlMmUxZjA5ZGJjOGE5Yjk4ZGIzMmZiYjQ3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlUwU2hYc1VGaVp6eHZRY1pkdE9hL0E9PSIsInZhbHVlIjoiNUViSkNhbUt2YWhXdjJZekxiUUdqV3RLdzBzR05NbnMzdkhpZ2RuTmUrR05LbjUxbENPUHpkZ3h6R1hzM3kxM3FaZXh0dTVpcG1LbVpGT3F5K3N5YkFUdTRiS3c0V21CdkszQ29sMHhIRHhWdVkzMHZ0aDZSeU1IY0duVkJOZm9GWWFVYzE2Q1Y2TUxQQ0RleTBYTEZPaHh5eEpjek9hSm91bGdkblcyNWlOZG9Ub1B3YVcwbE50OEtYMTdQcXFQRVp4eUpWNFlOejY5eDlIajNKMlBMTmxCei9OY0JCVFg5MHVnRW9BWmJiUlpZbVJwMFVraEdWMlgrdFA4Lzd1L202eGMxRUZFQk9hTllkeGtXQ3FxTnhJQjJJc2pmWU9oUlJVZHp0L1A2YmZEeUR6LzRTQ1F1bmZhNGpGUzFxWGtTUkFac1FBVHVLWFhDbVdPZFBKVGNWL3ZsN1ZSaGhRTDRKbWhYOGFob1IzMXBveVh3MVNlL0hyQkJrcnhwT2UzVzVUcUhXV1VWWm9FSW1VUzB2YVNvbTJGekdYQzVKVzk3YnBHYzkvdmltdnoxUHF6cFNHVGJSaHZGc2sweW8yWjF4c0ZCVXdHNHY0RldrUnNzMUF3dnhxZ1FpTHpScTh0eUZMTDcwVjRyNjIvQStXMEpCRVNDNmZlN3J0dnZ4WGQiLCJtYWMiOiI0YWJkNzUzNGIwMjg1NWM0M2UwNjBlNGRkNWYxZTdkOGU5OTcyOTNhOTNmN2QzYWM4OWQ5MDliNDIwNzVjNDkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1181904815\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1263045372 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PmFRgUqysTWH0qB2ROlhITKoLIETZGW2KykVXtOx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263045372\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1831493713 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:18:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdsbENYTWNDeDdhM0pCRzFkdExPbWc9PSIsInZhbHVlIjoiOGlLcDgzWE5yeUo1eEtqRnpSZmFFdnVFV0VIQnV2OVovNjhEd0s1b2lRdVY4Q09Ea2IzV2tZaXh6b24vV1hlQ25ZS1Yxc0pPbWlXQi9mVzR6YUdSSXJyMjZ6UFJvL0pFYTZFSldFNVJERE0zTzhRQW1WSTN0MllSdU40ODVJek80SEZRNHMxcDNSc3QzMmJXMnJML3lkeG91clhIeCt6aThBZE5rUFg1TlpHNzljSmgvMTdvTWhWMEpPd21RNWRUNWFiak5qaWdRSkpzbmFkSHQ4VjZ5SitrcnU1VVZXZUVFUDY5M1ZCTStuWDRBTzVsZ2Q3NFZ3eVdKeVlxNEZMSmgwakxPTzNpS3d3ZWhmZ3VEK2VNbmVvWTgxNFZGaHNDOVo3d0p0c0NzMWhUYXBibWtiWVJsTkhJTzVZYnlDMEFuVlFQejBWQ2JqREdVcWExV3FtMy9vbDdmanR0V1ZUbnQzNDFialB4MFZ5ZnBRSU9PcHVkUHU3VlhCU1B4L25JMkh3djFwU0tIeFg3WmdLMFV1UzhqSXM1S3czVWxaNUNqMStKajJrdUpvZG5uSk5qd1F6SDJDcnhLazhoSCswR0g2RmVDdWZkUEpZL0MxZFloSHhVS29SdTByL0pFQmtUSkF6cWhLMWJDRm5WQ2ljUFRDODV1S2h4K3MzUGI2cmoiLCJtYWMiOiI3MjZhNzY4YTVlMTUyMzM0YjMwYWQ3OTA2N2MyOWQ4OTQ3MjFhMjg4ODFjODNmMTg4ZmY0OWVlMDFjZWJlZDdmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRmWWlCc3VoMVBtZDdSeDlYNE1iUGc9PSIsInZhbHVlIjoibThJczdvdSs5aE9wblZXZ2R5c1Byb1h0aHJLUnZhV3RQd3FaVE1LM0xoYmxSNjZGa2lnWTllUWtSWGZwRXZiWjBtZmZmK1pZVThxY3czTEp1SUhaOGdCbzdIbHEweGh4bmlnS0syTzNuT0lsMHY0bkVxM0FRNmpORU4rUVdSa2h1T1JCcHB5c1JoSDdzd3FFK1Z3eFFXWjVvVlJob0lVamVtYlpjdUZUdTllOUd2UkRwN3pSVUZPMUg5TGZadkZHTU9DSkswU1R1dWdHdEJlbnhaNkZTNHlUajlBaDA5YzVrREdWZ3k0VU95YVJNeWs3Zi9MdENnalFSZ01XZEV3ZjJCUFBHZ2xyZ0JSelVDeE41L1VOeUVCM0NQUHExZXlUK2E2bWtINFJ4WnREU25RVnVjUG1mRnR6eDFvMUlsckNweDNISEF4QUZMUmtuNjVobVFEVmpMWHJBL0R2R3ZHOWhwTkh3ZW93ZlVYcmMveEpUUlZOQkFVNmZ1Ky9yeUdUUEVWUXYwT3AveklvdGZ4WFgrdkthZVhFZFZ2VHdObzBPQXdYYzF5TFRZWEtEakRLcU91WEhheThVRGtoKzYydUN1blQzaFBndEJxZ3JlNUt0UmVNb1hNRUQyTFUxaW9VR3JOUWw3L3VSVURpQWlzbG5vSGhPYm11R2NEZW5yWG0iLCJtYWMiOiJmZGRkNzgyZGJhNTY5NDhlNzI5NTkzOGRlOGQ5YzBhMDZhZWU0NTYwNjRmOTU5YTY0NmVhNWE4ZDc1NzRhMWJlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdsbENYTWNDeDdhM0pCRzFkdExPbWc9PSIsInZhbHVlIjoiOGlLcDgzWE5yeUo1eEtqRnpSZmFFdnVFV0VIQnV2OVovNjhEd0s1b2lRdVY4Q09Ea2IzV2tZaXh6b24vV1hlQ25ZS1Yxc0pPbWlXQi9mVzR6YUdSSXJyMjZ6UFJvL0pFYTZFSldFNVJERE0zTzhRQW1WSTN0MllSdU40ODVJek80SEZRNHMxcDNSc3QzMmJXMnJML3lkeG91clhIeCt6aThBZE5rUFg1TlpHNzljSmgvMTdvTWhWMEpPd21RNWRUNWFiak5qaWdRSkpzbmFkSHQ4VjZ5SitrcnU1VVZXZUVFUDY5M1ZCTStuWDRBTzVsZ2Q3NFZ3eVdKeVlxNEZMSmgwakxPTzNpS3d3ZWhmZ3VEK2VNbmVvWTgxNFZGaHNDOVo3d0p0c0NzMWhUYXBibWtiWVJsTkhJTzVZYnlDMEFuVlFQejBWQ2JqREdVcWExV3FtMy9vbDdmanR0V1ZUbnQzNDFialB4MFZ5ZnBRSU9PcHVkUHU3VlhCU1B4L25JMkh3djFwU0tIeFg3WmdLMFV1UzhqSXM1S3czVWxaNUNqMStKajJrdUpvZG5uSk5qd1F6SDJDcnhLazhoSCswR0g2RmVDdWZkUEpZL0MxZFloSHhVS29SdTByL0pFQmtUSkF6cWhLMWJDRm5WQ2ljUFRDODV1S2h4K3MzUGI2cmoiLCJtYWMiOiI3MjZhNzY4YTVlMTUyMzM0YjMwYWQ3OTA2N2MyOWQ4OTQ3MjFhMjg4ODFjODNmMTg4ZmY0OWVlMDFjZWJlZDdmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRmWWlCc3VoMVBtZDdSeDlYNE1iUGc9PSIsInZhbHVlIjoibThJczdvdSs5aE9wblZXZ2R5c1Byb1h0aHJLUnZhV3RQd3FaVE1LM0xoYmxSNjZGa2lnWTllUWtSWGZwRXZiWjBtZmZmK1pZVThxY3czTEp1SUhaOGdCbzdIbHEweGh4bmlnS0syTzNuT0lsMHY0bkVxM0FRNmpORU4rUVdSa2h1T1JCcHB5c1JoSDdzd3FFK1Z3eFFXWjVvVlJob0lVamVtYlpjdUZUdTllOUd2UkRwN3pSVUZPMUg5TGZadkZHTU9DSkswU1R1dWdHdEJlbnhaNkZTNHlUajlBaDA5YzVrREdWZ3k0VU95YVJNeWs3Zi9MdENnalFSZ01XZEV3ZjJCUFBHZ2xyZ0JSelVDeE41L1VOeUVCM0NQUHExZXlUK2E2bWtINFJ4WnREU25RVnVjUG1mRnR6eDFvMUlsckNweDNISEF4QUZMUmtuNjVobVFEVmpMWHJBL0R2R3ZHOWhwTkh3ZW93ZlVYcmMveEpUUlZOQkFVNmZ1Ky9yeUdUUEVWUXYwT3AveklvdGZ4WFgrdkthZVhFZFZ2VHdObzBPQXdYYzF5TFRZWEtEakRLcU91WEhheThVRGtoKzYydUN1blQzaFBndEJxZ3JlNUt0UmVNb1hNRUQyTFUxaW9VR3JOUWw3L3VSVURpQWlzbG5vSGhPYm11R2NEZW5yWG0iLCJtYWMiOiJmZGRkNzgyZGJhNTY5NDhlNzI5NTkzOGRlOGQ5YzBhMDZhZWU0NTYwNjRmOTU5YTY0NmVhNWE4ZDc1NzRhMWJlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1831493713\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1641702894 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1641702894\", {\"maxDepth\":0})</script>\n"}}