{"__meta": {"id": "Xa9f54e5ce3655ed6c3e5f1d66908bf1b", "datetime": "2025-06-07 22:57:08", "utime": **********.272776, "method": "GET", "uri": "/search-products?search=&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749337027.509619, "end": **********.272799, "duration": 0.7631800174713135, "duration_str": "763ms", "measures": [{"label": "Booting", "start": 1749337027.509619, "relative_start": 0, "end": **********.110602, "relative_end": **********.110602, "duration": 0.6009829044342041, "duration_str": "601ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.110619, "relative_start": 0.6010000705718994, "end": **********.272802, "relative_end": 3.0994415283203125e-06, "duration": 0.16218304634094238, "duration_str": "162ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53117856, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1138\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1138-1234</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.00907, "accumulated_duration_str": "9.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1719139, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 39.03}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1904259, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 39.03, "width_percent": 10.254}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.2195868, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 49.283, "width_percent": 8.049}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.223665, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 57.332, "width_percent": 10.143}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1150}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2324839, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1150", "source": "app/Http/Controllers/ProductServiceController.php:1150", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1150", "ajax": false, "filename": "ProductServiceController.php", "line": "1150"}, "connection": "ty", "start_percent": 67.475, "width_percent": 8.049}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname` from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `product_services`.`id` in (3) order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1166}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.23784, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1166", "source": "app/Http/Controllers/ProductServiceController.php:1166", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1166", "ajax": false, "filename": "ProductServiceController.php", "line": "1166"}, "connection": "ty", "start_percent": 75.524, "width_percent": 10.254}, {"sql": "select * from `product_service_units` where `product_service_units`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1166}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.246226, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1166", "source": "app/Http/Controllers/ProductServiceController.php:1166", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1166", "ajax": false, "filename": "ProductServiceController.php", "line": "1166"}, "connection": "ty", "start_percent": 85.777, "width_percent": 7.828}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 3 limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 267}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1173}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.250002, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:267", "source": "app/Models/ProductService.php:267", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=267", "ajax": false, "filename": "ProductService.php", "line": "267"}, "connection": "ty", "start_percent": 93.605, "width_percent": 6.395}]}, "models": {"data": {"App\\Models\\WarehouseProduct": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}}, "count": 6, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-687434986 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-687434986\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.23119, "xdebug_link": null}]}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-98773168 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-98773168\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1182645214 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1182645214\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-206482511 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-206482511\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1270483716 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749337003065%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik10eFZpakoxQ0NJRTRKVWJvY0NzWWc9PSIsInZhbHVlIjoibWFiM052eklFZExDYjVmQlBhT0p3eUM2K0hYZXNyMTdSOUFmd3Jxb01KcU5GTWpxY0ZUQm1NeE40d3Q1TSt0U1FUeDlmVVUyZUxMMmV2L055RGN1eE9mekI5aGVweXZIVTRtRVBOZWI4SEIxamphZ3NKYy9sR1RuRENMQWFqZVVlaWxxVWpEK0FHL2JJeGxzWXBMMWlEeFVRbW1yS1FtWDVHR1UraDYzUjE2K0hCZ1VDZ1cza2NKNjFCWUpnQmxhOWs0MXgyNnZLNnc3alZQaTBBeEEzQ00yNi80bnI1akFvemthR2tNY3lraHpxSDBFSU9TbDExb1RoWTlvK0dLQm11U0I0bG1XU3dOanMzUXlTU2krMFIzWlNsWExveWtHeThkRS9ySlhWSlkvSEdBQWhYRW0xYnI5ZG5zWkJJVEVMMTFtbko5bHpvWTRQRFdRUFBkS3ZHNmFFN3kvM080Ry9ZOWR6d2UvcmpjVjFJa2VQeHl5OUE0TXVVNFcySU5jQnNaV3Rlc3dQN1F3ZGJmdjJ1cTJoVEI5Q0hGREdJK3NpM20ySmc2NGtpVFNkRWpKcGpJZHZaOVE3ZVhXRWh4UnRjTXZLMmsxT3VVdEhKTEV4ckdZVlBDWEx5MWhBQi9GRU82SndyZ0pCdW8wTEc5QmFHeU56TEY4V3V4aVNtcVUiLCJtYWMiOiI4NjhjYTI1ZWNlMTU4MDRjZGY3NTUwMWIzMjUxMjZjYzI2NGQ3ZjRmNjE4NGU0M2ZmNTkxODJhMzM4MmQ3YzM5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZoa3B1KzBqelpwV1REZUY0MFZhQ1E9PSIsInZhbHVlIjoiRHQwWlBqZ2ZlNy9lRVkzVFJTbHl0VlQ3M3VUVHJ2cTBiekh1YWZvTXdybEpwcFQ1QlVwb1JrMWh4STVIV2ZyMU1MNXlBUmxRaFdpb3pjMGNOQmw4bVBhMUFrYzY0K29pLzErOW9GQW0vNmFxM0JNZEdjMFNDekxJVmY5MWRvYVlPQ1JRUWo3UTB3bC81WFRLZkZHQ0phQ0ZZR1lCenAwa1VvVzFIQnhHVXRUMGdjc09TUnRwYndRbGZSSHMzRE53RVF3T3ZJbVdocFJqaVMxbG9DNWcxN085RmJJL05LWGZUdmZ1aHcyenlQMmgxVDJRSkVtayticHcxNzUwTDZ0ek9HMEFycVFINmNDcGNqcUZ3NTJWelNDNDI3bTg1TG0rVkh1UXhicU9XQWFnVmY0Q0dJOXZHa1VKQUpQK21lNG94NVRGOFQ1RmRSc0txMC9PVDlrMDB2ckUrbEZzM3lUUzRtTnA0QU9obmNsUXFIR1VSYk5vR0JET3JIQWJleEM1MGdPbzBXUWFEellOM2FJUkE1dm4wOWxBdGlBL0lieDMyUVlkODVtOEE1azU4OVpXY3pJU1NwZktQcXZoLzRFL1ZyeWY5Z2thOWZwOGxuY2hzRXdTekhUNWZETHZuQW1LRlBoR2hQNThQWU54aHhBWEMwbmV5MUpnYndNZEtZTzQiLCJtYWMiOiI4ZGY1MTY0MWJjODhiMTAxZjhhOTU5ZmEyMWZiYWY2YjdlYTkyOThjMDA5NmIwOGEwYmYzZGZjMjg4Yzg0YWUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1270483716\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-247909770 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:57:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndFSkQvZ0xTOEc0TWFmOTVDL0tmVFE9PSIsInZhbHVlIjoiL2ZRQ2dVWVhwQWR4ZlFHT3pwd1ErSDVkSXppSnV6SWtUdDZuM1VzYlV3NUpDRHdwK2ZFM1lnQnlZTUZqcjNnbWxQZ2VxNEsxaHFCUjIyd1hOcit3SDRyY1VycFJka1hORDNBRnNCMmRhQkZXQ1hxWHdyeldERnNlUzFoNUdNeFhiOU1mYTBkUnA1d29ZYzcwakFGa2VUZjZuNCsvZXpHekUvNUo1ZHlXSkZjbzdPR3JzeDBWdDNxTmI2OUV2T1FvazB6cUJ0QzBCTGZWNjFTVHVZTHBlaVFVUlZnOE1ROVZnL2VESVBpM0w1VUkrV1dyZFJNSTlTTlBRdSt0VXdKN2FOSFZmNm45VFovK211UEFMd2srbnp5VVMxVkFhMHVpeXVNWVlCNytoSXNvNU5VbGpDRVphL0doaWJhbEVWbkVhTVNUMURVU3M5NGFHYVV3M29aRnBPcXRBMlBQazVJbk5qeGxCSkdTZWJBOVJIRW0yT3duNWVoeDRzb3FrekhjbVZsaGN1K1Q5ZEhqMEhrVzlOaHN1Vy9rRWxjWVJOUDZuTStQSUNpck1HWmZFSmpjOHdKdXBIeldJSjViSzJNMmYwT1JhbHJPRVFQTTlFejVtOUZ4UWZxTUxCeW9EK1JIcEVITEdPZFV2SWdOb0RVY1hTbTdXM0UxNUh6a3lpeWMiLCJtYWMiOiJjODVkNzk3MzJkNjA4NGUzMjk3NGIxZjA4MTRlZmU2YWMzNjFhN2FlYjlhYmQyODhkNTI2YTJmZmJkZjliMjZmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:57:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Iml4WDhJN3A5dUZQSnMrVElCSlFJVGc9PSIsInZhbHVlIjoiVGpZMlhBVVAxbWp5eHI3VE9qa3UvSStPcHpZY1VHbVpIRGc1VCt5VXcxSElNaHBMT2hEbkdjdVJNUFczSmpKTXYrb0YzdGhsMFl3dy9iOVcxa1VkWExYVDNCWC9HUFRuV1FwVmtNalhyM21QSGZZcmlEM0xaK1hnUm5pWVZhdmhTTmQxcVR6THlJRE9CUXdxdTBrTUdVNi9lNVBrSnNaY0VrNnhTbndoU0JUVkxYR2FsaDZXUy9qdE9uWnV5RDFsOWZQV1QreE9lbDZZWEtvdW9peDYvSkpaak1OUStqY2xlRnVhNFVrSjhaNGVVbi9nbm9aTW1JVmxMY1NMOEhxQjh4OFFsWUVBTzYvbm1FQ1ZzWDUybUloaHk4VUJLaGZUaEkyWmVxRmlFVi8wS09jSkdpZDNNYU5mTHRMZ0RVTHMzV05OK3lVR0dFZ0Z1UUtuOFA2TjhWeVBta3ZzYUU0dXBQcGcvM3RLN3JKRXJHWmdOcjlTbTJPRkZXc1hyb2V3VkJ2WloyTHpZWGlzYW1oV0FiMlBiUjZUd1RRWjBxMGt6UG9tUDg4Wlo2dGhqZ2dBdGVzYnF4OWR3d2lxendKNUdxT1g0dnBmRFpKL2IwN1d2NUNMTnNJRm1iRDV2OHo3RUNSRnRwMmJBQ2ErTWR4d3c3MjZITURKbXU2UEZLRHUiLCJtYWMiOiIwN2I1MDU5YzUyMDA4N2U0ZjcxMTliZTI3ODIxOTA0MjJlNGZkYTRiZjRlMTdiMTVkZjY4YjAzNDhmMDEzNWIwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:57:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndFSkQvZ0xTOEc0TWFmOTVDL0tmVFE9PSIsInZhbHVlIjoiL2ZRQ2dVWVhwQWR4ZlFHT3pwd1ErSDVkSXppSnV6SWtUdDZuM1VzYlV3NUpDRHdwK2ZFM1lnQnlZTUZqcjNnbWxQZ2VxNEsxaHFCUjIyd1hOcit3SDRyY1VycFJka1hORDNBRnNCMmRhQkZXQ1hxWHdyeldERnNlUzFoNUdNeFhiOU1mYTBkUnA1d29ZYzcwakFGa2VUZjZuNCsvZXpHekUvNUo1ZHlXSkZjbzdPR3JzeDBWdDNxTmI2OUV2T1FvazB6cUJ0QzBCTGZWNjFTVHVZTHBlaVFVUlZnOE1ROVZnL2VESVBpM0w1VUkrV1dyZFJNSTlTTlBRdSt0VXdKN2FOSFZmNm45VFovK211UEFMd2srbnp5VVMxVkFhMHVpeXVNWVlCNytoSXNvNU5VbGpDRVphL0doaWJhbEVWbkVhTVNUMURVU3M5NGFHYVV3M29aRnBPcXRBMlBQazVJbk5qeGxCSkdTZWJBOVJIRW0yT3duNWVoeDRzb3FrekhjbVZsaGN1K1Q5ZEhqMEhrVzlOaHN1Vy9rRWxjWVJOUDZuTStQSUNpck1HWmZFSmpjOHdKdXBIeldJSjViSzJNMmYwT1JhbHJPRVFQTTlFejVtOUZ4UWZxTUxCeW9EK1JIcEVITEdPZFV2SWdOb0RVY1hTbTdXM0UxNUh6a3lpeWMiLCJtYWMiOiJjODVkNzk3MzJkNjA4NGUzMjk3NGIxZjA4MTRlZmU2YWMzNjFhN2FlYjlhYmQyODhkNTI2YTJmZmJkZjliMjZmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:57:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Iml4WDhJN3A5dUZQSnMrVElCSlFJVGc9PSIsInZhbHVlIjoiVGpZMlhBVVAxbWp5eHI3VE9qa3UvSStPcHpZY1VHbVpIRGc1VCt5VXcxSElNaHBMT2hEbkdjdVJNUFczSmpKTXYrb0YzdGhsMFl3dy9iOVcxa1VkWExYVDNCWC9HUFRuV1FwVmtNalhyM21QSGZZcmlEM0xaK1hnUm5pWVZhdmhTTmQxcVR6THlJRE9CUXdxdTBrTUdVNi9lNVBrSnNaY0VrNnhTbndoU0JUVkxYR2FsaDZXUy9qdE9uWnV5RDFsOWZQV1QreE9lbDZZWEtvdW9peDYvSkpaak1OUStqY2xlRnVhNFVrSjhaNGVVbi9nbm9aTW1JVmxMY1NMOEhxQjh4OFFsWUVBTzYvbm1FQ1ZzWDUybUloaHk4VUJLaGZUaEkyWmVxRmlFVi8wS09jSkdpZDNNYU5mTHRMZ0RVTHMzV05OK3lVR0dFZ0Z1UUtuOFA2TjhWeVBta3ZzYUU0dXBQcGcvM3RLN3JKRXJHWmdOcjlTbTJPRkZXc1hyb2V3VkJ2WloyTHpZWGlzYW1oV0FiMlBiUjZUd1RRWjBxMGt6UG9tUDg4Wlo2dGhqZ2dBdGVzYnF4OWR3d2lxendKNUdxT1g0dnBmRFpKL2IwN1d2NUNMTnNJRm1iRDV2OHo3RUNSRnRwMmJBQ2ErTWR4d3c3MjZITURKbXU2UEZLRHUiLCJtYWMiOiIwN2I1MDU5YzUyMDA4N2U0ZjcxMTliZTI3ODIxOTA0MjJlNGZkYTRiZjRlMTdiMTVkZjY4YjAzNDhmMDEzNWIwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:57:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-247909770\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-21568378 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21568378\", {\"maxDepth\":0})</script>\n"}}