{"__meta": {"id": "Xb3b4f5de7cc74b6b092a935aa57c6adb", "datetime": "2025-06-30 15:44:24", "utime": **********.671491, "method": "GET", "uri": "/pos-payment-type?vc_name=6&user_id=&warehouse_name=9&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.208957, "end": **********.671519, "duration": 0.46256208419799805, "duration_str": "463ms", "measures": [{"label": "Booting", "start": **********.208957, "relative_start": 0, "end": **********.535186, "relative_end": **********.535186, "duration": 0.3262290954589844, "duration_str": "326ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.535195, "relative_start": 0.32623815536499023, "end": **********.67152, "relative_end": 9.5367431640625e-07, "duration": 0.13632488250732422, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53342376, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.bill_type", "param_count": null, "params": [], "start": **********.623325, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/bill_type.blade.phppos.bill_type", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Fbill_type.blade.php&line=1", "ajax": false, "filename": "bill_type.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.bill_type"}]}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1439\" onclick=\"\">app/Http/Controllers/PosController.php:1439-1547</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.0046, "accumulated_duration_str": "4.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5682652, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 33.043}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5778139, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 33.043, "width_percent": 8.696}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.591018, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 41.739, "width_percent": 18.696}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.593348, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 60.435, "width_percent": 10.652}, {"sql": "select * from `customers` where `name` = '6' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["6", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1450}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.598128, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1450", "source": "app/Http/Controllers/PosController.php:1450", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1450", "ajax": false, "filename": "PosController.php", "line": "1450"}, "connection": "kdmkjkqknb", "start_percent": 71.087, "width_percent": 7.609}, {"sql": "select * from `warehouses` where `id` = '9' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["9", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1451}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6000051, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1451", "source": "app/Http/Controllers/PosController.php:1451", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1451", "ajax": false, "filename": "PosController.php", "line": "1451"}, "connection": "kdmkjkqknb", "start_percent": 78.696, "width_percent": 5.87}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 546}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1455}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.602703, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "PosController.php:546", "source": "app/Http/Controllers/PosController.php:546", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=546", "ajax": false, "filename": "PosController.php", "line": "546"}, "connection": "kdmkjkqknb", "start_percent": 84.565, "width_percent": 6.957}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1534}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.616035, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1534", "source": "app/Http/Controllers/PosController.php:1534", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1534", "ajax": false, "filename": "PosController.php", "line": "1534"}, "connection": "kdmkjkqknb", "start_percent": 91.522, "width_percent": 8.478}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1966807350 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1966807350\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.597144, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1913020640 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1913020640\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.602137, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  1621 => array:9 [\n    \"name\" => \"ورق لعب اون\"\n    \"quantity\" => 1\n    \"price\" => \"5.00\"\n    \"id\" => \"1621\"\n    \"tax\" => 0\n    \"subtotal\" => 5.0\n    \"originalquantity\" => 14\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-1979254923 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1979254923\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>9</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1019945618 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1019945618\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-531540283 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751298248439%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InIvNHF1bVBRUXJ6am0wWDB0akw5aHc9PSIsInZhbHVlIjoiRTcrdDMrRERnODZxSGV3cW9iOWN5cVZ3ZE1tN29TYkJUZkR1MWx0bUhkeU5tN2tSZm9vR0xDcSsxbEhrNThxVm1FSUVUQTJTUnNZL1ZCT3JEMzRrK2N5Y01tckc5cXJlaUJrSTFYNEhRNE52R1c4N1NrMXp0WnRIenc1MFQvamxrN1JPMndXTUl2VytjU0NHVDNjWjZJVWxUVEJCWFlWeVlabHp0b0FFZ3VHWVBUZ2kvc3pPaUlaaGgzS0srZ1lQVGhOTWViY1M0WXl4bWNVTzJIVE1IK3VhVFRBSEx1MHJCNHVlTTh0L1JaaGhuZVF6MWE4dVg2VVZQU1lncDhidkl0ODhYbjF1ME5sQnBYcHFPY2NaYzhPWDlBdFpxakJwbmhVZ2UrdmkwZCtjN3EyRmU2RnpZWE1CSzZ2eTJ2SWdqZU9tbWhnOUtjVU8rU3QxUER2VXNUMmJicFZhSGpUdXJUeU9mQUVMRmkvbHBrcXNUT0ZEL1NuOEo3aXNweGZ0TjI3V09vVDY3QzdiZTZRY2JZMGRVY2FnR3RaYXNhdVIwakVWOEZnaTZaUFN5TW5nTGFkVkVnN2hSandWdll2a3VqMlRXS1BOL29QbW5HbG1XVUxpa0NVaXB6cFZEWmxkcmhpZER6KzB0S2JGYU5CdzhxeXhVdGt6bUVxWEUzSS8iLCJtYWMiOiI4MGJiMWVhZTk0YWExZDBlMWQ2YTk5ZjZmZGNkYmM1MmE0NGI4NDk0ZWMxNzdhZWEwZWRmZjQ3Yzc0Yzc0YzBlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjAwaTd2aytHcHVmMlAvWjhtNnNOeGc9PSIsInZhbHVlIjoiVFFFTzNUVUlDT0wvMHdkSWdxRVdFWmJQMHl3UElQZXdObXVZeXdrVmw2VmpUbWZ5Y0FUY1JZMytjOGVPQUNMNHF3U2ltYTFodm1ZOFhhL0FMOE4zOEVYRm1Da0YrSitEV09pM0JxVnRmVEdIcmp0UzRtTU15eVgxelR3Nzdac2ZpZy80UkU2N3cxMlpHTHl0cmc4ZmF5cWw5a2M4Vm9OOElZdXM2aUhTRFcyM2xkdGJURlZDOVkvYnpxOHBRcloyd0wrcmNCT2JjWEZCTmtxWlhEYXdZdzQrYm9CamVXdEU0bCtXelZVK2pSZFNINFU2RWYySXNyTkJFR3dsM2hxU2xZblcwOFlsS292MWxGaE1mTmx1ME4rNmkva2ZJQXhNeVdWNkpCYVdBMlFWM1o2aVliYnlGYmFnRmc1WFRsNkE2ck1BRTlSU256Z1BTSkptaEpkWjArMlNJeWFaT1FzUDdlbTBiL2hvc1Z4RVc0SHNocnhPbEd6THpwQ2FxVThQaWdON1VwMmMvQnI4SExGbk1oOFpkaDBQQUhVTHM1RUp6MmU5dU1iazVWWGh3TlZYT3Zpc3J6c09hZk0zRGYxWXdNNHFqZUhYMnY3VU1tVlRWbURxS3hOYzJHNE9tT1k1UmNQYWIxV1NBb0RHMVg1N2M0WDQyY0dLS2JGaEZMR28iLCJtYWMiOiI3NGVhNzdkYTcwZGIwZDAwMjI2YTMzZGViMGU1Y2QzNWVjOWQ5ZjRlYmUwNjgzNjc3Y2I1MmFjYTVmZWY4NDIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-531540283\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-488288244 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488288244\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-913801210 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:44:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFsdWZCK2pESzV3dWdIOXp3bi9tZ0E9PSIsInZhbHVlIjoiQW1rclpQMDY1ZWVwVmtzaFpiQnlqdkhXcFRXbmdpMGlJZThlcjNkdmdydHM5VDAxUmlSeXJmQnlGa2UzOXBza2lRZkNEZFdTMHdGK2VFQmtXdXJNVnJoNFRDcW1OQnFaN3ZzTnZ4NVlLMVRWQ2NFdytiSVhJOHNvMlNCZVQvUG9GVXg0SmE4azA2S3ZWSWsxeW9hMTg0QUVpM2dCMVlQTlM2cnd0Tm5LZW5LUzNJNFV4Mzl4bGRnbkR0czhKSHR5U2NHdlZPNCtuaVk2UzMxZElzY0JkbTQ0a0ZhbVBnN21PbjZYaGZrSllOdWtRUlNGaW1OazBvVHhEUjZNdXhESTdNbTlYdGJ1ek1pbFJZRHFaMCsxbE02SVNkNTFOVFFDQ1NsL1dRM01nNjVvK2E1b0Q1V0V3Ykg1Z2ZPQWlnTTJTbFNnQTBJaXJqQUx1K3JWWlh1NHpkR04zbXBGZ2ZMVlRSaFY0Zm1rZG8yNWxINnhXYXNjT2t3dkxBZ1hmcDlBUWhBMC83UWpKMjFYSnpmdWwwaDQwdllLTFpwN01OektnTHhiS3FpaThqYXREYmhvNzFsRlhPZFd3Z0RpSHVZMHgyYVFPbXVHV1NNUlBPMHp4N3c0QUt3R0RhZk9YdEZxa0l6emRPVjRZTXhSOW9vc05QdkZFRkNaSEZ5VzNuVWUiLCJtYWMiOiJjYTIzMGY0NjQyMzhiNzA4YWFiYjUyODU0MzhiOTNlYWJjOWQ2MDg1MDY0MjBjZDdjNDA2Mzg5MzA2Mjk1OWYzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBxcWMveXA5MmFlWExQdCtRdEcwVXc9PSIsInZhbHVlIjoiRlhDbTZNWHZHSGxxUTZnQVJvV2Q3bWFCR0c1SjBsK3lRa29meHUxQ0c2cXBKS2Z1Zm5vQ3JncDdxOUFGMndJTWk5Q3M1dm9HVWhUV0t2cmxNczVnK3VUV0FXQlp5MTk4Y2NuMEp6dlhyWVY4aUp4djEyY3VFRGxyZ1Ird3lyZlVPNlRNSGtoRTNqcjJnemdWTml5dWJHU2ZyZ0RNSXRaR2Y1MEJhencwT1pEc29DWkt4SmthbExuVy9mbTBpNCtRKzM2V2dsMXZSczdoeExnbmxDTzNDbVJlMHF5UlRMQ0ovNXg4dk5ZNjRxWjl0VkEzOFF5M3FrY0cxZm9RU3I1MGxJUUN6QnZXYWZiWmR1RkVybi80NlVFWTN6Zjl1SEE4Z3IwQWVVVjNzYXAzck4wOUdVdXBGemFOdllwdzdOU2VOZmYwSkZTY2liVUlTN2tPM05CVHBsbm83amVjeklhRytTc1h3OTRxb1p0c2o0S0IvYlVPY3lTL0h2aFNVQ0h5YUNlUW1lQnhVVk9ZbFhNTlE5WkFlVVMrY0h4REdPdkQyaE1vcFo4R3dZMEI4Mm1hNyttWnJXYUxGSkdWcE02ZDVmdVIxdE5OTERzWVYwK3YyNGs2WWhBSk5HbVFGR3ZsQlczTkZod3hwU2pzWnNRalhOVFdiOXJFWDd2VnFuQmMiLCJtYWMiOiI4M2NjMGI0MTkxYmIyNGFiYTllZWE4YjE0OTg5OGFiNjhiYTI2OGUwYzhkZjY0Y2I3ODI3ZDlkZDViNDM4MTkyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFsdWZCK2pESzV3dWdIOXp3bi9tZ0E9PSIsInZhbHVlIjoiQW1rclpQMDY1ZWVwVmtzaFpiQnlqdkhXcFRXbmdpMGlJZThlcjNkdmdydHM5VDAxUmlSeXJmQnlGa2UzOXBza2lRZkNEZFdTMHdGK2VFQmtXdXJNVnJoNFRDcW1OQnFaN3ZzTnZ4NVlLMVRWQ2NFdytiSVhJOHNvMlNCZVQvUG9GVXg0SmE4azA2S3ZWSWsxeW9hMTg0QUVpM2dCMVlQTlM2cnd0Tm5LZW5LUzNJNFV4Mzl4bGRnbkR0czhKSHR5U2NHdlZPNCtuaVk2UzMxZElzY0JkbTQ0a0ZhbVBnN21PbjZYaGZrSllOdWtRUlNGaW1OazBvVHhEUjZNdXhESTdNbTlYdGJ1ek1pbFJZRHFaMCsxbE02SVNkNTFOVFFDQ1NsL1dRM01nNjVvK2E1b0Q1V0V3Ykg1Z2ZPQWlnTTJTbFNnQTBJaXJqQUx1K3JWWlh1NHpkR04zbXBGZ2ZMVlRSaFY0Zm1rZG8yNWxINnhXYXNjT2t3dkxBZ1hmcDlBUWhBMC83UWpKMjFYSnpmdWwwaDQwdllLTFpwN01OektnTHhiS3FpaThqYXREYmhvNzFsRlhPZFd3Z0RpSHVZMHgyYVFPbXVHV1NNUlBPMHp4N3c0QUt3R0RhZk9YdEZxa0l6emRPVjRZTXhSOW9vc05QdkZFRkNaSEZ5VzNuVWUiLCJtYWMiOiJjYTIzMGY0NjQyMzhiNzA4YWFiYjUyODU0MzhiOTNlYWJjOWQ2MDg1MDY0MjBjZDdjNDA2Mzg5MzA2Mjk1OWYzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBxcWMveXA5MmFlWExQdCtRdEcwVXc9PSIsInZhbHVlIjoiRlhDbTZNWHZHSGxxUTZnQVJvV2Q3bWFCR0c1SjBsK3lRa29meHUxQ0c2cXBKS2Z1Zm5vQ3JncDdxOUFGMndJTWk5Q3M1dm9HVWhUV0t2cmxNczVnK3VUV0FXQlp5MTk4Y2NuMEp6dlhyWVY4aUp4djEyY3VFRGxyZ1Ird3lyZlVPNlRNSGtoRTNqcjJnemdWTml5dWJHU2ZyZ0RNSXRaR2Y1MEJhencwT1pEc29DWkt4SmthbExuVy9mbTBpNCtRKzM2V2dsMXZSczdoeExnbmxDTzNDbVJlMHF5UlRMQ0ovNXg4dk5ZNjRxWjl0VkEzOFF5M3FrY0cxZm9RU3I1MGxJUUN6QnZXYWZiWmR1RkVybi80NlVFWTN6Zjl1SEE4Z3IwQWVVVjNzYXAzck4wOUdVdXBGemFOdllwdzdOU2VOZmYwSkZTY2liVUlTN2tPM05CVHBsbm83amVjeklhRytTc1h3OTRxb1p0c2o0S0IvYlVPY3lTL0h2aFNVQ0h5YUNlUW1lQnhVVk9ZbFhNTlE5WkFlVVMrY0h4REdPdkQyaE1vcFo4R3dZMEI4Mm1hNyttWnJXYUxGSkdWcE02ZDVmdVIxdE5OTERzWVYwK3YyNGs2WWhBSk5HbVFGR3ZsQlczTkZod3hwU2pzWnNRalhOVFdiOXJFWDd2VnFuQmMiLCJtYWMiOiI4M2NjMGI0MTkxYmIyNGFiYTllZWE4YjE0OTg5OGFiNjhiYTI2OGUwYzhkZjY0Y2I3ODI3ZDlkZDViNDM4MTkyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-913801210\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1363556407 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1621</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1608;&#1585;&#1602; &#1604;&#1593;&#1576; &#1575;&#1608;&#1606;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1621</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>14</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363556407\", {\"maxDepth\":0})</script>\n"}}