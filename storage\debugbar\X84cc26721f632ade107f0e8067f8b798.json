{"__meta": {"id": "X84cc26721f632ade107f0e8067f8b798", "datetime": "2025-06-07 22:17:21", "utime": 1749334641.106199, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334631.695704, "end": 1749334641.106254, "duration": 9.410550117492676, "duration_str": "9.41s", "measures": [{"label": "Booting", "start": 1749334631.695704, "relative_start": 0, "end": **********.051208, "relative_end": **********.051208, "duration": 1.355504035949707, "duration_str": "1.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.051257, "relative_start": 1.3555529117584229, "end": 1749334641.106258, "relative_end": 3.814697265625e-06, "duration": 8.055001020431519, "duration_str": "8.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43404088, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 7.***************, "accumulated_duration_str": "7.9s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1662369, "duration": 7.***************, "duration_str": "7.9s", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aeg571Q3ThqeNMfiIAc14ULZrTrG6DjnVuualNlJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1083973264 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1083973264\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2135067753 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2135067753\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1540562962 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IkFOeWY2UkNZTmVJWUV1b0xLcFlFaUE9PSIsInZhbHVlIjoiRG5NM2hOU1dwQmZPUnVvUjNPaXFIdU53Q0pUcHUrdFdXTHlMbmZieFpoazVpbzNtSmcwdC9GaXl5SWdlZ0piNXp6U0NMTVVMUlZqV3ZGN1lEYitPN0VybjBTOTF4R2QwNHBNbURJOCtxRXVzZ0hQMHV0MG8zNG84QTZVbGpFOXBvaVVtSjNtSVZ3NndpM2M0RTRDTkFtcndJRTkyN0NDS1R4MGJaMmNaV0RGUXhSZDdsbjh5ckpwUGU3MGtGY2pjT2IydE83S2VoNmRoTGxDMDRIZjZlTU5ld2Y4c1VheHFIZXQvUkNEUDFQQkJmSGJRdzZYSUh4Zmh1NmlmamN6Zk5rUEZsOTZlMkNWcUZoVmF1TEZab0lMOVVsd1VENlNucDMwUTBobE9sZHNuVVdSVGg5RHcyV3VrVi9zSEt5djB0WGpLNWN3d29BanVHL2VkUFVwYTEyRkdneVQ3ZXNRTmgrbHVGMkJxNWVxQlViQUVnRDdoRExKNkJ1cDlPcTdlZjYxc0ExTTlNUWFDS0FOYUZKUlRPd1lsQ3ZUYVNkTC9NUCtQUFZ4L2NENWVWU210d0E2NHRCdFpIRmh2Z2hHOG81T0JjL1NWQVVDL3loY0xUMzB5N3NleGVnNU5lcnhMUDlGWG1UcFpTM2YzWFZDSFpTSEV1T3VXNnVtVlVJTGkiLCJtYWMiOiJlYmU2ZDQyMDY2NDM1Y2E0ODE4MjBkYTY4MjZiOGQ1MTY5M2FmNjI2MjUwZWJjMTBmZmQ2OTM0ZDNlYzA3YmM0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhxdXVYN3R3dysrelhMbUloTWxJenc9PSIsInZhbHVlIjoiamFRRWVoOStCZzh4ZkF1TjV3aXd1dnhpdkZ0RHlZM1NzYXhxVXEvRXByOUtxU3FIdm1DNlFMY2RlYmxWdmpiMzA1RlhSNHZMMnZieU9YVS9NU1BqdTRxMWxqQnlIT3JiclZnT2NKRWhuNTNCUmlGbmRLdjdQODhjQzAwSW13VklQUzJiV3haMVlsZmg5Qk1RVnJvYW9abGFOUXh2aUwwQ01CQjdRVEFjc0dyRlJvOGJpYm9OTDVWYjdmZmU4eTJKQS9YblhicTlwTXNidWFibytUbnpPVmxSUUdxYnExS0toYWsyVDc3bkM5d3FOOFhLWkw3QTN2d0kyalZ6SjVxZzRGcmdsd0FIaVMvemxPdzRSUS9GQmdlenE1elc3dXFGWTd4Y1VGZnBHcDlXSXJyZXAxV3RaWkhUNjBrMjFBVHFDQXM0UU85ZkxOb2NINnZtcG0zc090enNyVmFUSkN0cit3UC9kdDQyWjY1K2o2NDJwanNzM1JYMkxKcGxybzQ4b3FjN08vUDJkUC90OTFjd1B6N0lERHI4YnJNb3hEUUxvUWw1QkVlUlF2SWZYeVpMbGpnck10SE14ZUFaY1dxdTQrK0pvaVJMbUpnNWl0bmZ4L21scmdGOGpub1N6b3dZaWRDZGIwMktYTG9WWTBheFdlVTlBUnlEeGQ1MDRzbjIiLCJtYWMiOiJiYzgxNDFhZDYzMjRmOGJlOGRiZDQ0MDZiNGFhMDAyNzg4NDE2ZTViMGU4NjhkMDJmY2I3YmE3ZWQwZmUyNTJiIiwidGFnIjoiIn0%3D; _clsk=8djjbr%7C1749332394281%7C1%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1540562962\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1165264662 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">b9LrtSbfqnf8CMsfmucHJzUzrNRU410hMaAWVdtG</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JGbRg3rX9CZC3OYjDIftJuBr6aHnx3C6fHbYY9Rw</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1165264662\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1379374065 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:17:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9rYXY3UEJxSEUveHNWck9LVk5CZmc9PSIsInZhbHVlIjoiZjVsZDQxWDBodHlKTTJPRCtUaEI2VGk5TFRxZ21iR1lWMHI4VlFUblkvMlJ6RWFQUUVRQXZYNCswbkJ2UVV0Q0R2RjNoYWV6eDl6d1RUckQxWlR6U0dIQXpsdzBQRXJ6QjlOVDAzSmEvZGJRKzMvNlUwekVSSHpkTTNReTBlMDkvOGtPdkVOeE9IY0QvbFQ3RzFSOTc0d3BrdDZwTGY2R2hWM1crVTFvc0Z2N01nTFZsOHBwVkw3MmVVZkg5MGk3bEQxOHNlRVV5NkxKSmttZEw1VW91Y2p0aFlwdWVlcnh5MUhkTko1VkhQL2hDRkxwMlRjRnZBSEw1N090UXc2L0VXKzg4L1J6dDBzN01yRm5aV2hXUW1BL2kvZmlwUXpSWmVtYnRHMnRMVDQxOS9McTlsZEt1a0dXS2U5a21QYVRLUU1Wd1RFZXRBV3Z1Wktrc0Q5by9HcFdlR1ArYjk0bmxTZGlXbE5SOURGZnJuamQrTkFNUnZhZ3owMDU5TkdKazF3a1NpbnFXVXNtTkVIT3lUcHYybFJaaGNBR0F3TGpOYjlFUXpUbC9sOURPdzhvb3Vubm5Vd1FlOWJYWExtUHVwZGJYZDJlNjdvWTBvY29TWTZzd2FrS0xYZTlPRmJYMkRWdmpaZUlKcllpOElFNWxZZGVMb3dvOEx6S3d6ZDYiLCJtYWMiOiI0MGJhMWU2OWQ0OWU1ODYwOWUyMGQzOTllNjFmMzdjNTU0YmJmNGI1YjJhMDBlMjk5Y2MzZDhiOTgwNDUzYjFmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:17:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjRwZG1nUi9OT0krM2lqRVZCbVJZbXc9PSIsInZhbHVlIjoickw0V21jcmhIU2RiVDVkcnE1TFE1dVVxRm5DYXA4M2dITnNnSXZlajIrK2psaVFQTWIvQVlGNFpkeXBXcE9teWtDa3BhckVEcTdLZGVwSzN6S3B0emJ1Z0s0YUhLR0g2U2ZtUDNuQkxSMS9VNWtPZ1RqOE1LUEllcVZXajUwSGs1QjFhS080T1h4M2ttNWluenVkaEthVVU2WEZDaGxGSjdxUE5CTnRvQkhDUFpuWE5yWUdHaXhNOEdrTWJvUFlBVE1MQ0VudUYrRldTSXFIejZjLzNwQmRFMHNSUmVYWXVvVWdydnM1d2ZibUcvWVVDSzlBNG5OanVQT2xQV0h4S01FTzFaVnBTK2kvZyt0ZW0zK2pXbVNOSGEwUlJZbVBzWSt5d0JqT3p6UHAxSUJCU0I0S0VZdFdTY0dWemxYQkhvVW5iQWhqakIrL0E4czRJZzlCRmxPemFuNzNHMVVaVzNRZE9nTlRvOFpFRldlOFFiNnhGaE0wcnozREZiTkxtWlc4dlRyY1lyZVNvQWlvaGVyaGJvVktrcVFYT3VkUHBkU1czRUo1Qk9XM1hOaDNWb0Nhbnp1SDg5VEcwMXY1clpEQ2FhQzl2Q0VLdFVvS3NadjUwYnVJM1U0YXViMWNWZE5TZG16SzlhcW0xUFBlZkQ2ZUVYSXFXcFhST2dVbVoiLCJtYWMiOiJiYTYxZGZkZjdlYTFhNzY2ZTZiNmYzMzQ0NGZlZTE0NDY3ODc1YTRlNzM3NjE3ZjA3YWM3NWUyZTg5NjM2MGVmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:17:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9rYXY3UEJxSEUveHNWck9LVk5CZmc9PSIsInZhbHVlIjoiZjVsZDQxWDBodHlKTTJPRCtUaEI2VGk5TFRxZ21iR1lWMHI4VlFUblkvMlJ6RWFQUUVRQXZYNCswbkJ2UVV0Q0R2RjNoYWV6eDl6d1RUckQxWlR6U0dIQXpsdzBQRXJ6QjlOVDAzSmEvZGJRKzMvNlUwekVSSHpkTTNReTBlMDkvOGtPdkVOeE9IY0QvbFQ3RzFSOTc0d3BrdDZwTGY2R2hWM1crVTFvc0Z2N01nTFZsOHBwVkw3MmVVZkg5MGk3bEQxOHNlRVV5NkxKSmttZEw1VW91Y2p0aFlwdWVlcnh5MUhkTko1VkhQL2hDRkxwMlRjRnZBSEw1N090UXc2L0VXKzg4L1J6dDBzN01yRm5aV2hXUW1BL2kvZmlwUXpSWmVtYnRHMnRMVDQxOS9McTlsZEt1a0dXS2U5a21QYVRLUU1Wd1RFZXRBV3Z1Wktrc0Q5by9HcFdlR1ArYjk0bmxTZGlXbE5SOURGZnJuamQrTkFNUnZhZ3owMDU5TkdKazF3a1NpbnFXVXNtTkVIT3lUcHYybFJaaGNBR0F3TGpOYjlFUXpUbC9sOURPdzhvb3Vubm5Vd1FlOWJYWExtUHVwZGJYZDJlNjdvWTBvY29TWTZzd2FrS0xYZTlPRmJYMkRWdmpaZUlKcllpOElFNWxZZGVMb3dvOEx6S3d6ZDYiLCJtYWMiOiI0MGJhMWU2OWQ0OWU1ODYwOWUyMGQzOTllNjFmMzdjNTU0YmJmNGI1YjJhMDBlMjk5Y2MzZDhiOTgwNDUzYjFmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:17:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjRwZG1nUi9OT0krM2lqRVZCbVJZbXc9PSIsInZhbHVlIjoickw0V21jcmhIU2RiVDVkcnE1TFE1dVVxRm5DYXA4M2dITnNnSXZlajIrK2psaVFQTWIvQVlGNFpkeXBXcE9teWtDa3BhckVEcTdLZGVwSzN6S3B0emJ1Z0s0YUhLR0g2U2ZtUDNuQkxSMS9VNWtPZ1RqOE1LUEllcVZXajUwSGs1QjFhS080T1h4M2ttNWluenVkaEthVVU2WEZDaGxGSjdxUE5CTnRvQkhDUFpuWE5yWUdHaXhNOEdrTWJvUFlBVE1MQ0VudUYrRldTSXFIejZjLzNwQmRFMHNSUmVYWXVvVWdydnM1d2ZibUcvWVVDSzlBNG5OanVQT2xQV0h4S01FTzFaVnBTK2kvZyt0ZW0zK2pXbVNOSGEwUlJZbVBzWSt5d0JqT3p6UHAxSUJCU0I0S0VZdFdTY0dWemxYQkhvVW5iQWhqakIrL0E4czRJZzlCRmxPemFuNzNHMVVaVzNRZE9nTlRvOFpFRldlOFFiNnhGaE0wcnozREZiTkxtWlc4dlRyY1lyZVNvQWlvaGVyaGJvVktrcVFYT3VkUHBkU1czRUo1Qk9XM1hOaDNWb0Nhbnp1SDg5VEcwMXY1clpEQ2FhQzl2Q0VLdFVvS3NadjUwYnVJM1U0YXViMWNWZE5TZG16SzlhcW0xUFBlZkQ2ZUVYSXFXcFhST2dVbVoiLCJtYWMiOiJiYTYxZGZkZjdlYTFhNzY2ZTZiNmYzMzQ0NGZlZTE0NDY3ODc1YTRlNzM3NjE3ZjA3YWM3NWUyZTg5NjM2MGVmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:17:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1379374065\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-4543863 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aeg571Q3ThqeNMfiIAc14ULZrTrG6DjnVuualNlJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-4543863\", {\"maxDepth\":0})</script>\n"}}