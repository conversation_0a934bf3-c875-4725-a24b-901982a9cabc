{"__meta": {"id": "X9369fc4625deb50d4a08bbb0859286d0", "datetime": "2025-06-08 16:23:59", "utime": **********.207351, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749399838.657338, "end": **********.207385, "duration": 0.5500471591949463, "duration_str": "550ms", "measures": [{"label": "Booting", "start": 1749399838.657338, "relative_start": 0, "end": **********.131052, "relative_end": **********.131052, "duration": 0.47371411323547363, "duration_str": "474ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.131064, "relative_start": 0.4737260341644287, "end": **********.207389, "relative_end": 4.0531158447265625e-06, "duration": 0.0763251781463623, "duration_str": "76.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44010352, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02236, "accumulated_duration_str": "22.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.17118, "duration": 0.02159, "duration_str": "21.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.556}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.197311, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 96.556, "width_percent": 3.444}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1459388005 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1459388005\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2139225130 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2139225130\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-457441397 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-457441397\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1892277476 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=u1db3a%7C1749399485619%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9TOWFQQUp2NVBwcnZZL3o4UVZEcWc9PSIsInZhbHVlIjoia1lZamFhaTQzMXdxT2x6L3E3ZEpXSVhqWXpoWG16ZVMwckRiVHAzdnZCVlI1N3pDK2xyQWRVWjdncmdxbEZsbGI0VXZmbCtZdllqUkFkM05yYm1xSjlaSjVHbGI0bzBOc0k0TmEzeDYrTlZZT2dxVVAzWEJjRjYwRFdoYnZPTERLQkRGdFhDR0o4dmpDWXdqeFVmNnBxT0RGVkUxbGlVQkFsWU5Mb3ZDbk5pZ3RQUENIOFVNRDVBeFRjckdMMjQyeHNjRUpNaEFUaGF5SHJreFJ5Sy83bldOZHhYUVRmdWdmRG90RXBhV3YvTDVsNUtXeXhSOHhTY0hCSW5NMk5uV3VrWkxlNFpoNUxWd0gyeGZPNkdJek9QOG9GcE5YeWRKRWdHOFZ4QmMyQmpvODJxRmtVL2JqcUsxTlFQRzd6dHNWa2ZDd29meGptK2gwYzdaKzFuelhhNUZaTGgvZmhPbEF3bFlXbG5hT0ZkSVcxdklQOEg1dGgyaXp4T2tCUzNXU3ZaVlU4aGRiZ2hNUzNRdURtcUhiTWxFV2RoZGdrRCtRSU90U05hL2V0ZlFvM2NOclB5L2xEUVQ1dzI1Z1lXempYUTl4b3Z2YVRKaEJ3Mzh6ZlBVVGZNNmhIaUVISER1aUk1Q3BTS1cyTlFTUUo4cm9jbFFxWlJZb2RxZXNDN0oiLCJtYWMiOiI0ZDdjNTE5MjRjZDNjZTgyNzI3ZDEzNmQ5NWY5YTllNWQzODkyNDRmOTJjNmVkNTNhZGVlMWJhNGU5MWUwNDU3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlV2SENWSmduWWNMdm1nN28vTDZ2cmc9PSIsInZhbHVlIjoiQjJoUXRLcU9FWWExZG9uSXV4Q2szamhjMmFBb1RVbGJ5MVFhQjNTUUpEM1M1aXZDaXhneHBjc0VZVEU0WFo1L3RVTVdOd3BxU0Vacm1POVgra1lzdmFyL3VYR0NXRzJXdGZFZ1VMNWpxYzJIUHFwZm1seTF0eTZ0YWZTWDV1VTJraU52ZktzamMzTXljY00wYnh6b24vdTRuVzRZYmR3eXJIaGU5Y3ZRazQ0elJDblhHODdBanJNQXNRd0gxZG9aQW96VFVFWUV1Z0kvOEMxZytLL29Zb3QwcXpSMXFSSDBMMllGV0FaRGtYazJXSGxMQ2RlV29oQzNUU0JFa3YyQit1QTVxU3FRN09Ud2p2ZFVxVHBVa1dzK2N2RHZ3MnpmYWtESUVVY25xOTBha2N2VXdsSEZWSHVtamdhdzFuZmNtTU5pb2dWaU82Q2ZPSlMyb1c3blNrTVR5TTE3bVlWWXBqSDhXSjU3aDJ0Z1l5ZjQzZTNPL28xbmJUY082aUdnTFFRSWNVVlN3T3NVRFRwcHJSaGltUnQ5OUxoaGs3Z2lzYmV0b2lOVUZ5M1pweU9KMjZRbGNzclZwY3pNY3VaakdmWlBUMElncW1tamtDRU9iMFJyaVdlbGFXMmowWll5T1lCaXlYRTlSbjYxZ1VacHNLcE8xdGVZd1QzVGdjZDMiLCJtYWMiOiIzMzY3ODVkZGZhYzY0YzJhZGE1MzFjZTI4OGQxZmUyOTA2NmE1YTEzNjk3ZmYxM2MzMDc5OThmZWJiYzk4MjM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1892277476\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-823477605 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-823477605\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:23:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldLVnNVc1VabU9KcW4zNnl5TDluM0E9PSIsInZhbHVlIjoiVXZwMmdiWEllejhCUFZJZHNmam1EVzdNQ213bzRqNTBnUks0WTBUZ0FDRlhnYzRZYXlQVVFuMmNZZk9kVlZkT2ZiOUJGMlNFeTFRNG5iSHFvcUExZjNGUWNvNW9QRTFibk5pQUVCOXF1L0VUVWNpSGRPdnIrUnZYUENsYzNTSmEvdFcvNWowc3pxcWc0NEJYMXBzaW13dWg1dktKWXpMRE5nODNKZWZpRzEva1kyOVBvSjdtN1U1akdhZGd6MnNDbDR3cHZCMzY1alI3cGFIZXNYR1VDendYamJtTENrNEE1RnpTUU9MY05nSXJScHRHVGRBZWVLWGF5RXRMV0c2bVZ6UFBlbXliMll1cXBMdU1YNVlVR0dhSXdIZmNBTzNoUVlKeEd3akkvbTNaTzRmOXBsbVFXZnFTVGsrOS9FZndBczZyc0VyelFyYSs1N1hleGdldG9RUFRpZ29BQUZBRU44ZnNDSVlkaTNFSGQzR2UvWlBUNW5TK3NJUXdRcy9yaFRIQW5qMjlOelAvOTQ1NnAxZWFaREh0aERnanBUOUFlSmpGYm1NUWRESFdQQ1FVcUZ4RmRsZVlBcFp2eHRwdFRFV25mcFFZWjZveHBrby8rUHNReVFrTVE1cVFhN2lvQzBscU5UeFFsTmlUeUl4Y0k2RlpXOE5QVWJ4VTZqUzciLCJtYWMiOiI1NTg3ZTY1M2FjNmU3MmUyMzkwZjcyOTI3MzI2M2E1MzNiZjQ2YzY1MmJlNzJkYjg4ZWMzMzNkNzI3MjdjMmY5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:23:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InFWbkZ0bzJoSVZiNThBL05rakczMnc9PSIsInZhbHVlIjoiZ053VFlsd0hJeDl5RngrU1l4aTAzaUZpMTUzVVM0WWQxVXNJRjZDcWNhSmIvSjFHelBXMS9lOXVmVm9UMThLYm83UEozYWl3dlpqQ01XUWM1UzZ2RVVXQWUzMFp1azZpN0g3MEVHUXdDek5KOWZDS09iaFpncFZlYVZxeElHc3NjWFI0S1BYbnRHbjJzckcyN09vZnVOMjNSWDl5TEtOQkNnRi9acXg0NFhUK1I1dWhKbm4vVmJCc1VYTEcrWm1ONWJYL0RTaUp4YVNCbXNtM3lvNzdIaG9sWURPaEZBbytZclQ5eXZVMnV5MnZEdkZZVGRIcys4RkhEWHdheGp6OU1aVmQxTGdXdFVBYTMycTNNSFNicktzd2VkTDRlYVcwUzdQaUpoMHdzZEl1KzExRmRVMHR4bC92QmRrZEI0ZW1ST3pPN1c3aFVVbTJLZVZqZFhGRGk5NE9sRVRsUzRaVmFDV2lneVg1djB4UzdvbG44RVova1BVRks2cGtwSVVxQXRCUmsyT0JsZDVBT3M0cXdTUmVQU3MwVGp6bGc0c0VsT3E2Q2hwU0FIb0ZhQnpRNWJEdmZheU5RRVBwVDA4YzdmZWQwcURCK2F0QWY3THhHaDRPbHFFbHlBNXIzN3dnWTNoVGR6U1JOaUtqU2FwTlhMRU92d3RuMzg1ZHBWQTUiLCJtYWMiOiJkMmQwYjAxZDBmZGY4ZGIxYjc5NDdmYTIzMmRhM2EzNTNlMjkyZGQ5ZDcyZTgxYmFkMzU3YjBmYTRkZDg1YzZkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:23:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldLVnNVc1VabU9KcW4zNnl5TDluM0E9PSIsInZhbHVlIjoiVXZwMmdiWEllejhCUFZJZHNmam1EVzdNQ213bzRqNTBnUks0WTBUZ0FDRlhnYzRZYXlQVVFuMmNZZk9kVlZkT2ZiOUJGMlNFeTFRNG5iSHFvcUExZjNGUWNvNW9QRTFibk5pQUVCOXF1L0VUVWNpSGRPdnIrUnZYUENsYzNTSmEvdFcvNWowc3pxcWc0NEJYMXBzaW13dWg1dktKWXpMRE5nODNKZWZpRzEva1kyOVBvSjdtN1U1akdhZGd6MnNDbDR3cHZCMzY1alI3cGFIZXNYR1VDendYamJtTENrNEE1RnpTUU9MY05nSXJScHRHVGRBZWVLWGF5RXRMV0c2bVZ6UFBlbXliMll1cXBMdU1YNVlVR0dhSXdIZmNBTzNoUVlKeEd3akkvbTNaTzRmOXBsbVFXZnFTVGsrOS9FZndBczZyc0VyelFyYSs1N1hleGdldG9RUFRpZ29BQUZBRU44ZnNDSVlkaTNFSGQzR2UvWlBUNW5TK3NJUXdRcy9yaFRIQW5qMjlOelAvOTQ1NnAxZWFaREh0aERnanBUOUFlSmpGYm1NUWRESFdQQ1FVcUZ4RmRsZVlBcFp2eHRwdFRFV25mcFFZWjZveHBrby8rUHNReVFrTVE1cVFhN2lvQzBscU5UeFFsTmlUeUl4Y0k2RlpXOE5QVWJ4VTZqUzciLCJtYWMiOiI1NTg3ZTY1M2FjNmU3MmUyMzkwZjcyOTI3MzI2M2E1MzNiZjQ2YzY1MmJlNzJkYjg4ZWMzMzNkNzI3MjdjMmY5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:23:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InFWbkZ0bzJoSVZiNThBL05rakczMnc9PSIsInZhbHVlIjoiZ053VFlsd0hJeDl5RngrU1l4aTAzaUZpMTUzVVM0WWQxVXNJRjZDcWNhSmIvSjFHelBXMS9lOXVmVm9UMThLYm83UEozYWl3dlpqQ01XUWM1UzZ2RVVXQWUzMFp1azZpN0g3MEVHUXdDek5KOWZDS09iaFpncFZlYVZxeElHc3NjWFI0S1BYbnRHbjJzckcyN09vZnVOMjNSWDl5TEtOQkNnRi9acXg0NFhUK1I1dWhKbm4vVmJCc1VYTEcrWm1ONWJYL0RTaUp4YVNCbXNtM3lvNzdIaG9sWURPaEZBbytZclQ5eXZVMnV5MnZEdkZZVGRIcys4RkhEWHdheGp6OU1aVmQxTGdXdFVBYTMycTNNSFNicktzd2VkTDRlYVcwUzdQaUpoMHdzZEl1KzExRmRVMHR4bC92QmRrZEI0ZW1ST3pPN1c3aFVVbTJLZVZqZFhGRGk5NE9sRVRsUzRaVmFDV2lneVg1djB4UzdvbG44RVova1BVRks2cGtwSVVxQXRCUmsyT0JsZDVBT3M0cXdTUmVQU3MwVGp6bGc0c0VsT3E2Q2hwU0FIb0ZhQnpRNWJEdmZheU5RRVBwVDA4YzdmZWQwcURCK2F0QWY3THhHaDRPbHFFbHlBNXIzN3dnWTNoVGR6U1JOaUtqU2FwTlhMRU92d3RuMzg1ZHBWQTUiLCJtYWMiOiJkMmQwYjAxZDBmZGY4ZGIxYjc5NDdmYTIzMmRhM2EzNTNlMjkyZGQ5ZDcyZTgxYmFkMzU3YjBmYTRkZDg1YzZkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:23:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}