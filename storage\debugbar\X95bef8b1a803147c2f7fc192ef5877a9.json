{"__meta": {"id": "X95bef8b1a803147c2f7fc192ef5877a9", "datetime": "2025-06-08 16:23:58", "utime": **********.649997, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.126027, "end": **********.650015, "duration": 0.5239880084991455, "duration_str": "524ms", "measures": [{"label": "Booting", "start": **********.126027, "relative_start": 0, "end": **********.576622, "relative_end": **********.576622, "duration": 0.4505949020385742, "duration_str": "451ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.576634, "relative_start": 0.4506068229675293, "end": **********.650018, "relative_end": 2.86102294921875e-06, "duration": 0.07338404655456543, "duration_str": "73.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45276552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013389999999999999, "accumulated_duration_str": "13.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.612425, "duration": 0.01081, "duration_str": "10.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.732}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.634662, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.732, "width_percent": 9.559}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.640092, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 90.291, "width_percent": 9.709}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1626715529 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1626715529\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-412687011 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-412687011\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-113894409 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-113894409\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=u1db3a%7C1749399485619%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJicjAveVA2aFRGVjA3M3VWeTJqcFE9PSIsInZhbHVlIjoiU20xc3hVOHAwOXJmR0NjdnRhVXJ3VThrQzliVlJDeXlzVUdHVmQ2UXhic2YrUmpQSXZUZHp1L21DdkdRMmVMN2J6dzQ4Zkh6TlJURGlLejlLWjZ3bkpiNnFoN04zMThQZHhJcndUaTlINTA5V1J2RFVBbDd5SW5mdEY3Zi9LZ3pKNTN3RlRNbFAzWXNJczZ2NU9mQVdHMnpFMHNqUGU4Q0Irejh4Z0ZTUURIZUI0M2tGNHhCb29JTjMxWm1UQmRyNC8vd1VHeVBraFRBUDI4NmxIbXJPRkVwQUlSMjlMRUxRMzJiVWphRCtrdUxZem1zOEVXbm44S2lJbGVFam1iWWl2WVN5N1U4NnI2N1RLQ25ka0hDeS9adTlQeDVQclJjeW45VjZKQk12MUxEbHZOb2YrYWJadERueVpaNXhSOGhpLy94N0hIK3BCN2cwNXdVYW5IU1prVjQxTWMzZHVHMGlnYzdrcW9WcWNoanpvTHBCVkZCeGlLaU9XZWNJWkMzVzFnR3JZbHVFWXI2aHBsYmZUV1IraXFQVCtaNUtTR1BieFA3blhUMUNHVG8xVk1TZ0ZUSlVsYzhhcmplOTNVeXBjdndGdVYzeGhkSFhrZTVLdGhqTm9pcGo4LzZYWDJ0WTVyYldKaXJnc2poWWpGdzZXK0Y5cnI1MWlnTjRVZDkiLCJtYWMiOiI5OGMwOWNhYzhkZjcwZTRmMDIyZjAwZmU4ZWNlNzFlMWYyZGFiZDQwMjQ3N2QxMTA0MTVjMWE5MzRlYTYyOGViIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdCR1RZUW5mS0NsMjZsSDV2ZlVlWnc9PSIsInZhbHVlIjoiOTJENnVXMmZBZy9USUIyNTZYcHcrNEZyQ1ZycDhMZW9ZMXYzS08yY2sxTGtQTEhka0NKRHBFNTM4Uk90dFNSb2FucGpqdU8zcTQ3b0RrUWdtY1FjQkI3VFR6MWJUcC9zckRmYk8yaE9GOE5NYmR1QkZTbkRiSzMxUnkxZmZVeE1jd1cxWVFuaE5uMDBaL3UxZ3M2Q0pkWnBCb0N4T2J5QmFLd0FYYkZrYW1IaWxPYXpGQkdmOFE0Y3Y1L2VHVFB1c2tvcFVPcGQ4ZHhFZXJ4cEwvRmxpTHprZXNEUEJOSWZ2NDVodk9jMnlyNlZGTWZnNXMwUldEa3owYUIyRXV6MFJjUGlsNndjMDBUcHNOSmc2VFVPSzZEdHUzanRjUWRRamptdHdiQXpETktqeG5UVGZBSGVGYW9XNWpVUWRzQmp3NStmOCtUMldMb1BwaEpFMTVVY1NYUkNMbjFrZnVGL0trdmtTMlpUR090enJZUmUrckxiZUJ0aG56ek9FODlvbXh1OE5SRVdmY2pUWmdoZUU3SkRZM2xleGd3b2FOYmdiNjAwanZkUlNHUU5HcEpCS3ZTTStheU1ObXJZb1BCaDVvRXkvdXNGL2lVWmhiUks4U0hUaHkvWUoxcWtWU2FoQ3ZqRzZnTjArN0ljRHhxUkZFako1Q092QVBualp5K04iLCJtYWMiOiI2YWZhOTZmMTAxYTU0NzgzYzcxNzczNmUyYTZlN2VhZWQzMWE0NjAwMTU4Njc1YTk2YWZkNGNiNmZmYjc3ZjY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1092435504 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1092435504\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1303091147 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:23:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9TOWFQQUp2NVBwcnZZL3o4UVZEcWc9PSIsInZhbHVlIjoia1lZamFhaTQzMXdxT2x6L3E3ZEpXSVhqWXpoWG16ZVMwckRiVHAzdnZCVlI1N3pDK2xyQWRVWjdncmdxbEZsbGI0VXZmbCtZdllqUkFkM05yYm1xSjlaSjVHbGI0bzBOc0k0TmEzeDYrTlZZT2dxVVAzWEJjRjYwRFdoYnZPTERLQkRGdFhDR0o4dmpDWXdqeFVmNnBxT0RGVkUxbGlVQkFsWU5Mb3ZDbk5pZ3RQUENIOFVNRDVBeFRjckdMMjQyeHNjRUpNaEFUaGF5SHJreFJ5Sy83bldOZHhYUVRmdWdmRG90RXBhV3YvTDVsNUtXeXhSOHhTY0hCSW5NMk5uV3VrWkxlNFpoNUxWd0gyeGZPNkdJek9QOG9GcE5YeWRKRWdHOFZ4QmMyQmpvODJxRmtVL2JqcUsxTlFQRzd6dHNWa2ZDd29meGptK2gwYzdaKzFuelhhNUZaTGgvZmhPbEF3bFlXbG5hT0ZkSVcxdklQOEg1dGgyaXp4T2tCUzNXU3ZaVlU4aGRiZ2hNUzNRdURtcUhiTWxFV2RoZGdrRCtRSU90U05hL2V0ZlFvM2NOclB5L2xEUVQ1dzI1Z1lXempYUTl4b3Z2YVRKaEJ3Mzh6ZlBVVGZNNmhIaUVISER1aUk1Q3BTS1cyTlFTUUo4cm9jbFFxWlJZb2RxZXNDN0oiLCJtYWMiOiI0ZDdjNTE5MjRjZDNjZTgyNzI3ZDEzNmQ5NWY5YTllNWQzODkyNDRmOTJjNmVkNTNhZGVlMWJhNGU5MWUwNDU3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:23:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlV2SENWSmduWWNMdm1nN28vTDZ2cmc9PSIsInZhbHVlIjoiQjJoUXRLcU9FWWExZG9uSXV4Q2szamhjMmFBb1RVbGJ5MVFhQjNTUUpEM1M1aXZDaXhneHBjc0VZVEU0WFo1L3RVTVdOd3BxU0Vacm1POVgra1lzdmFyL3VYR0NXRzJXdGZFZ1VMNWpxYzJIUHFwZm1seTF0eTZ0YWZTWDV1VTJraU52ZktzamMzTXljY00wYnh6b24vdTRuVzRZYmR3eXJIaGU5Y3ZRazQ0elJDblhHODdBanJNQXNRd0gxZG9aQW96VFVFWUV1Z0kvOEMxZytLL29Zb3QwcXpSMXFSSDBMMllGV0FaRGtYazJXSGxMQ2RlV29oQzNUU0JFa3YyQit1QTVxU3FRN09Ud2p2ZFVxVHBVa1dzK2N2RHZ3MnpmYWtESUVVY25xOTBha2N2VXdsSEZWSHVtamdhdzFuZmNtTU5pb2dWaU82Q2ZPSlMyb1c3blNrTVR5TTE3bVlWWXBqSDhXSjU3aDJ0Z1l5ZjQzZTNPL28xbmJUY082aUdnTFFRSWNVVlN3T3NVRFRwcHJSaGltUnQ5OUxoaGs3Z2lzYmV0b2lOVUZ5M1pweU9KMjZRbGNzclZwY3pNY3VaakdmWlBUMElncW1tamtDRU9iMFJyaVdlbGFXMmowWll5T1lCaXlYRTlSbjYxZ1VacHNLcE8xdGVZd1QzVGdjZDMiLCJtYWMiOiIzMzY3ODVkZGZhYzY0YzJhZGE1MzFjZTI4OGQxZmUyOTA2NmE1YTEzNjk3ZmYxM2MzMDc5OThmZWJiYzk4MjM5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:23:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9TOWFQQUp2NVBwcnZZL3o4UVZEcWc9PSIsInZhbHVlIjoia1lZamFhaTQzMXdxT2x6L3E3ZEpXSVhqWXpoWG16ZVMwckRiVHAzdnZCVlI1N3pDK2xyQWRVWjdncmdxbEZsbGI0VXZmbCtZdllqUkFkM05yYm1xSjlaSjVHbGI0bzBOc0k0TmEzeDYrTlZZT2dxVVAzWEJjRjYwRFdoYnZPTERLQkRGdFhDR0o4dmpDWXdqeFVmNnBxT0RGVkUxbGlVQkFsWU5Mb3ZDbk5pZ3RQUENIOFVNRDVBeFRjckdMMjQyeHNjRUpNaEFUaGF5SHJreFJ5Sy83bldOZHhYUVRmdWdmRG90RXBhV3YvTDVsNUtXeXhSOHhTY0hCSW5NMk5uV3VrWkxlNFpoNUxWd0gyeGZPNkdJek9QOG9GcE5YeWRKRWdHOFZ4QmMyQmpvODJxRmtVL2JqcUsxTlFQRzd6dHNWa2ZDd29meGptK2gwYzdaKzFuelhhNUZaTGgvZmhPbEF3bFlXbG5hT0ZkSVcxdklQOEg1dGgyaXp4T2tCUzNXU3ZaVlU4aGRiZ2hNUzNRdURtcUhiTWxFV2RoZGdrRCtRSU90U05hL2V0ZlFvM2NOclB5L2xEUVQ1dzI1Z1lXempYUTl4b3Z2YVRKaEJ3Mzh6ZlBVVGZNNmhIaUVISER1aUk1Q3BTS1cyTlFTUUo4cm9jbFFxWlJZb2RxZXNDN0oiLCJtYWMiOiI0ZDdjNTE5MjRjZDNjZTgyNzI3ZDEzNmQ5NWY5YTllNWQzODkyNDRmOTJjNmVkNTNhZGVlMWJhNGU5MWUwNDU3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:23:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlV2SENWSmduWWNMdm1nN28vTDZ2cmc9PSIsInZhbHVlIjoiQjJoUXRLcU9FWWExZG9uSXV4Q2szamhjMmFBb1RVbGJ5MVFhQjNTUUpEM1M1aXZDaXhneHBjc0VZVEU0WFo1L3RVTVdOd3BxU0Vacm1POVgra1lzdmFyL3VYR0NXRzJXdGZFZ1VMNWpxYzJIUHFwZm1seTF0eTZ0YWZTWDV1VTJraU52ZktzamMzTXljY00wYnh6b24vdTRuVzRZYmR3eXJIaGU5Y3ZRazQ0elJDblhHODdBanJNQXNRd0gxZG9aQW96VFVFWUV1Z0kvOEMxZytLL29Zb3QwcXpSMXFSSDBMMllGV0FaRGtYazJXSGxMQ2RlV29oQzNUU0JFa3YyQit1QTVxU3FRN09Ud2p2ZFVxVHBVa1dzK2N2RHZ3MnpmYWtESUVVY25xOTBha2N2VXdsSEZWSHVtamdhdzFuZmNtTU5pb2dWaU82Q2ZPSlMyb1c3blNrTVR5TTE3bVlWWXBqSDhXSjU3aDJ0Z1l5ZjQzZTNPL28xbmJUY082aUdnTFFRSWNVVlN3T3NVRFRwcHJSaGltUnQ5OUxoaGs3Z2lzYmV0b2lOVUZ5M1pweU9KMjZRbGNzclZwY3pNY3VaakdmWlBUMElncW1tamtDRU9iMFJyaVdlbGFXMmowWll5T1lCaXlYRTlSbjYxZ1VacHNLcE8xdGVZd1QzVGdjZDMiLCJtYWMiOiIzMzY3ODVkZGZhYzY0YzJhZGE1MzFjZTI4OGQxZmUyOTA2NmE1YTEzNjk3ZmYxM2MzMDc5OThmZWJiYzk4MjM5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:23:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1303091147\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-200810097 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-200810097\", {\"maxDepth\":0})</script>\n"}}