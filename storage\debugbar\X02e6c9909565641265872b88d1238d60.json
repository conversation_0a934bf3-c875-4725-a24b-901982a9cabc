{"__meta": {"id": "X02e6c9909565641265872b88d1238d60", "datetime": "2025-06-30 15:44:21", "utime": **********.056742, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751298260.629376, "end": **********.056756, "duration": 0.4273800849914551, "duration_str": "427ms", "measures": [{"label": "Booting", "start": 1751298260.629376, "relative_start": 0, "end": 1751298260.991438, "relative_end": 1751298260.991438, "duration": 0.3620619773864746, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751298260.991446, "relative_start": 0.36207008361816406, "end": **********.056757, "relative_end": 9.5367431640625e-07, "duration": 0.06531095504760742, "duration_str": "65.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43872936, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02306, "accumulated_duration_str": "23.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.024392, "duration": 0.02271, "duration_str": "22.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.482}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.050206, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 98.482, "width_percent": 1.518}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  1621 => array:9 [\n    \"name\" => \"ورق لعب اون\"\n    \"quantity\" => 1\n    \"price\" => \"5.00\"\n    \"id\" => \"1621\"\n    \"tax\" => 0\n    \"subtotal\" => 5.0\n    \"originalquantity\" => 14\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1152098863 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1152098863\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1481345434 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751298248439%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9KcmRsSncwZTE3dHlFS0VOZjZkb2c9PSIsInZhbHVlIjoiUEZLNmRtR2VrSXJJdnFIUGU1L3MxVjVMK1hOMDdZZmpZbEIzbWkrcXZNUHFCclMyOEJaYVlSZDQ0Ympjenl1ODJFRjViSmU0Q0NJSVdLRFdZT2lXTlUvVFN2cFNJWTFtU3RyUGVZdFJOS3dWZmdGTEJ1TWdmckNVOXlreEVWWTZLeTFaVStOV3lFdGpERk1YZkVUdmVYNkUwMzhrNWIxcWFTdXJoaEt3MXRWN1FZUTVBeHVWVWtaNTlnQTlnbjhsZTMreVVoeTltRG5CcDJZYUZjRHZYbE1PdktXWDkyRTRlcDdrQmY0NWliWmlyajZSVGtFay9ZQng1d29pemFSVjA1LzYvRDlGbWJZM0tGOWJiTkhLYmpoWU9waDBIM3pzQ2F3ZkU2ajJsWGpPd2NBYkI0OXR5QmFrMlFRVld4ZmMxVWc3akNpcFFDZ09iMndJbTdlaFROZ0NGOWNQZzJuR2xJbU5tMTh0NEVpMU5lQ3Y4cnlROG1xdm04RTQ3elp3LzRHckYwYVowSjVNTStCUUFjSUU3Um5pSkRDd0I0akRvV0pKSGZhMmRZNU11R05EazFTRitJNi84RW1HL2p5ZXAvSXR2UkNuOWZDSFdSSDZWRFZ1TForeXdselNybmNiY20rRzRxNDZVWWZLMFMzaUtyeXVBeHR2WXI4Z3pPdDIiLCJtYWMiOiJhYmZlOGQ3Njc4NTI1MDAxZTdhY2RmNTgwZTFjZmY0M2UwMzY0MjcxOTYzNDBkNjFiOGM3YjNkZGMyYjFiMDYzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkwwOFZKNDloVWJDcWxHZUFvNHgwS0E9PSIsInZhbHVlIjoiZzhwbEFTZXlFSjFtZmkreXdRMjJzWURXS2NDU1VJRWw1eGs0ZmRkdWdDR3YyU3hReHphSExFajYzaTY4aHlUTXJ6KzRuS0Y3MWl4S1V4UEJhTGJZbWo2Q1d6ODlxSkFJRGRDQmtlalRsWlhZY0MrUGlUUjhOM0tUZElhTGdFbzZkd1pSQWdZV2l3NExlRVc0ZHk5Vm93ek9yeDRZUmFrN2pkcytPM1Y0enpxd3pWUWFoT0F4c1doSGRPdis4a3FSY1IvdTlvK3JBeUVvVUQwWjcybnYxaGYySjNaOTNzeUpJTkxNSjlJWlpsZ2ptME5xOVV0WHFHMXhPUzJua0Z0QUdhTGFkVGp1Q3gyZTE1MmhNa0IxTi9mbTV3MzF5T2RuT0FRZFU3N0NXZVpONzJ2eDFFU2V0Sk5OUXMwSVZWaEpPNGdvbFlVVjZTMW9oNkJ4SFlWMGsxMGhwTjlXMHJvUGJ0ZGM4MXMvQStYaVRJSUcvSGt5MFl4b3A4eEorbERtNzVGa3F0OHNsR3h0VXNvbGcvMHFNT3lyQm16dzhLMEppVnNrM2FCUmRqSDByUzBjRHpQSmQrRzVYbVlKd1VxQTAvY2xXUlEyajg0VS90QXlWQlpLUkxtNXdBMFowOWxBd0wvVkJ4QmZIL0lSWWh3dnhjNTg0ZDZKRzRhWHVBVmYiLCJtYWMiOiIzMTNhZDg0NWVkOGIxYjViM2IxYmRjOGNhMWEwODdlMTc0Y2VlMjBkOTljYzQ4N2E4NWNkOGRkZDIxODJkMDQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1481345434\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1706455917 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1706455917\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-822376925 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:44:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InIvNHF1bVBRUXJ6am0wWDB0akw5aHc9PSIsInZhbHVlIjoiRTcrdDMrRERnODZxSGV3cW9iOWN5cVZ3ZE1tN29TYkJUZkR1MWx0bUhkeU5tN2tSZm9vR0xDcSsxbEhrNThxVm1FSUVUQTJTUnNZL1ZCT3JEMzRrK2N5Y01tckc5cXJlaUJrSTFYNEhRNE52R1c4N1NrMXp0WnRIenc1MFQvamxrN1JPMndXTUl2VytjU0NHVDNjWjZJVWxUVEJCWFlWeVlabHp0b0FFZ3VHWVBUZ2kvc3pPaUlaaGgzS0srZ1lQVGhOTWViY1M0WXl4bWNVTzJIVE1IK3VhVFRBSEx1MHJCNHVlTTh0L1JaaGhuZVF6MWE4dVg2VVZQU1lncDhidkl0ODhYbjF1ME5sQnBYcHFPY2NaYzhPWDlBdFpxakJwbmhVZ2UrdmkwZCtjN3EyRmU2RnpZWE1CSzZ2eTJ2SWdqZU9tbWhnOUtjVU8rU3QxUER2VXNUMmJicFZhSGpUdXJUeU9mQUVMRmkvbHBrcXNUT0ZEL1NuOEo3aXNweGZ0TjI3V09vVDY3QzdiZTZRY2JZMGRVY2FnR3RaYXNhdVIwakVWOEZnaTZaUFN5TW5nTGFkVkVnN2hSandWdll2a3VqMlRXS1BOL29QbW5HbG1XVUxpa0NVaXB6cFZEWmxkcmhpZER6KzB0S2JGYU5CdzhxeXhVdGt6bUVxWEUzSS8iLCJtYWMiOiI4MGJiMWVhZTk0YWExZDBlMWQ2YTk5ZjZmZGNkYmM1MmE0NGI4NDk0ZWMxNzdhZWEwZWRmZjQ3Yzc0Yzc0YzBlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjAwaTd2aytHcHVmMlAvWjhtNnNOeGc9PSIsInZhbHVlIjoiVFFFTzNUVUlDT0wvMHdkSWdxRVdFWmJQMHl3UElQZXdObXVZeXdrVmw2VmpUbWZ5Y0FUY1JZMytjOGVPQUNMNHF3U2ltYTFodm1ZOFhhL0FMOE4zOEVYRm1Da0YrSitEV09pM0JxVnRmVEdIcmp0UzRtTU15eVgxelR3Nzdac2ZpZy80UkU2N3cxMlpHTHl0cmc4ZmF5cWw5a2M4Vm9OOElZdXM2aUhTRFcyM2xkdGJURlZDOVkvYnpxOHBRcloyd0wrcmNCT2JjWEZCTmtxWlhEYXdZdzQrYm9CamVXdEU0bCtXelZVK2pSZFNINFU2RWYySXNyTkJFR3dsM2hxU2xZblcwOFlsS292MWxGaE1mTmx1ME4rNmkva2ZJQXhNeVdWNkpCYVdBMlFWM1o2aVliYnlGYmFnRmc1WFRsNkE2ck1BRTlSU256Z1BTSkptaEpkWjArMlNJeWFaT1FzUDdlbTBiL2hvc1Z4RVc0SHNocnhPbEd6THpwQ2FxVThQaWdON1VwMmMvQnI4SExGbk1oOFpkaDBQQUhVTHM1RUp6MmU5dU1iazVWWGh3TlZYT3Zpc3J6c09hZk0zRGYxWXdNNHFqZUhYMnY3VU1tVlRWbURxS3hOYzJHNE9tT1k1UmNQYWIxV1NBb0RHMVg1N2M0WDQyY0dLS2JGaEZMR28iLCJtYWMiOiI3NGVhNzdkYTcwZGIwZDAwMjI2YTMzZGViMGU1Y2QzNWVjOWQ5ZjRlYmUwNjgzNjc3Y2I1MmFjYTVmZWY4NDIyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InIvNHF1bVBRUXJ6am0wWDB0akw5aHc9PSIsInZhbHVlIjoiRTcrdDMrRERnODZxSGV3cW9iOWN5cVZ3ZE1tN29TYkJUZkR1MWx0bUhkeU5tN2tSZm9vR0xDcSsxbEhrNThxVm1FSUVUQTJTUnNZL1ZCT3JEMzRrK2N5Y01tckc5cXJlaUJrSTFYNEhRNE52R1c4N1NrMXp0WnRIenc1MFQvamxrN1JPMndXTUl2VytjU0NHVDNjWjZJVWxUVEJCWFlWeVlabHp0b0FFZ3VHWVBUZ2kvc3pPaUlaaGgzS0srZ1lQVGhOTWViY1M0WXl4bWNVTzJIVE1IK3VhVFRBSEx1MHJCNHVlTTh0L1JaaGhuZVF6MWE4dVg2VVZQU1lncDhidkl0ODhYbjF1ME5sQnBYcHFPY2NaYzhPWDlBdFpxakJwbmhVZ2UrdmkwZCtjN3EyRmU2RnpZWE1CSzZ2eTJ2SWdqZU9tbWhnOUtjVU8rU3QxUER2VXNUMmJicFZhSGpUdXJUeU9mQUVMRmkvbHBrcXNUT0ZEL1NuOEo3aXNweGZ0TjI3V09vVDY3QzdiZTZRY2JZMGRVY2FnR3RaYXNhdVIwakVWOEZnaTZaUFN5TW5nTGFkVkVnN2hSandWdll2a3VqMlRXS1BOL29QbW5HbG1XVUxpa0NVaXB6cFZEWmxkcmhpZER6KzB0S2JGYU5CdzhxeXhVdGt6bUVxWEUzSS8iLCJtYWMiOiI4MGJiMWVhZTk0YWExZDBlMWQ2YTk5ZjZmZGNkYmM1MmE0NGI4NDk0ZWMxNzdhZWEwZWRmZjQ3Yzc0Yzc0YzBlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjAwaTd2aytHcHVmMlAvWjhtNnNOeGc9PSIsInZhbHVlIjoiVFFFTzNUVUlDT0wvMHdkSWdxRVdFWmJQMHl3UElQZXdObXVZeXdrVmw2VmpUbWZ5Y0FUY1JZMytjOGVPQUNMNHF3U2ltYTFodm1ZOFhhL0FMOE4zOEVYRm1Da0YrSitEV09pM0JxVnRmVEdIcmp0UzRtTU15eVgxelR3Nzdac2ZpZy80UkU2N3cxMlpHTHl0cmc4ZmF5cWw5a2M4Vm9OOElZdXM2aUhTRFcyM2xkdGJURlZDOVkvYnpxOHBRcloyd0wrcmNCT2JjWEZCTmtxWlhEYXdZdzQrYm9CamVXdEU0bCtXelZVK2pSZFNINFU2RWYySXNyTkJFR3dsM2hxU2xZblcwOFlsS292MWxGaE1mTmx1ME4rNmkva2ZJQXhNeVdWNkpCYVdBMlFWM1o2aVliYnlGYmFnRmc1WFRsNkE2ck1BRTlSU256Z1BTSkptaEpkWjArMlNJeWFaT1FzUDdlbTBiL2hvc1Z4RVc0SHNocnhPbEd6THpwQ2FxVThQaWdON1VwMmMvQnI4SExGbk1oOFpkaDBQQUhVTHM1RUp6MmU5dU1iazVWWGh3TlZYT3Zpc3J6c09hZk0zRGYxWXdNNHFqZUhYMnY3VU1tVlRWbURxS3hOYzJHNE9tT1k1UmNQYWIxV1NBb0RHMVg1N2M0WDQyY0dLS2JGaEZMR28iLCJtYWMiOiI3NGVhNzdkYTcwZGIwZDAwMjI2YTMzZGViMGU1Y2QzNWVjOWQ5ZjRlYmUwNjgzNjc3Y2I1MmFjYTVmZWY4NDIyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-822376925\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1547933185 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1621</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1608;&#1585;&#1602; &#1604;&#1593;&#1576; &#1575;&#1608;&#1606;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1621</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>14</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1547933185\", {\"maxDepth\":0})</script>\n"}}