<?php

namespace App\Http\Controllers;

use App\Models\ProductService;
use App\Models\warehouse;
use App\Models\WarehouseProduct;
use App\Models\WarehouseProductLimit;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Mpdf\Mpdf;

class BranchInventoryManagementController extends Controller
{
    /**
     * عرض صفحة إدارة المخزون للفروع
     */
    public function index(Request $request)
    {
        // التحقق من دور SUPER FIESR BIG - هذه الصفحة متاحة فقط لمستخدمي SUPER FIESR BIG
        if (!Auth::user()->hasRole('SUPER FIESR BIG')) {
            return redirect()->back()->with('error', __('Permission denied. This page is only accessible to SUPER FIESR BIG users.'));
        }

        // جلب المستودعات المتاحة
        $warehouses = warehouse::where('created_by', Auth::user()->creatorId())->get();

        // المستودع المحدد (الافتراضي هو الأول)
        $selectedWarehouse = $request->input('warehouse_id', $warehouses->first()->id ?? null);

        return view('company_operations.branch_inventory_management.index', compact('warehouses', 'selectedWarehouse'));
    }

    /**
     * جلب منتجات مستودع معين
     */
    public function getWarehouseProducts(Request $request, $warehouseId)
    {
        // التحقق من دور SUPER FIESR BIG
        if (!Auth::user()->hasRole('SUPER FIESR BIG')) {
            return response()->json(['error' => 'Permission denied'], 403);
        }

        // التحقق من وجود المستودع
        $warehouse = warehouse::findOrFail($warehouseId);

        // جلب جميع المنتجات مع معلومات المستودع
        $allProducts = ProductService::with(['taxes', 'unit', 'category'])
            ->where('created_by', Auth::user()->creatorId())
            ->where('type', 'product')
            ->get();

        // إنشاء مجموعة من المنتجات مع معلومات المستودع
        $products = collect();

        foreach ($allProducts as $product) {
            // البحث عن المنتج في المستودع
            $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouseId)
                ->where('product_id', $product->id)
                ->first();

            // البحث عن حدود المنتج في المستودع
            $productLimit = WarehouseProductLimit::where('warehouse_id', $warehouseId)
                ->where('product_id', $product->id)
                ->first();

            // إضافة معلومات المستودع للمنتج
            $product->warehouse_quantity = $warehouseProduct ? $warehouseProduct->quantity : 0;
            $product->min_quantity = $productLimit ? $productLimit->min_quantity : 0;
            $product->alert_threshold = $productLimit ? $productLimit->alert_threshold : 0;
            $product->warehouse_id = $warehouseId;

            $products->push($product);
        }

        // البحث إذا كان موجودًا
        $search = $request->input('search');
        if ($search) {
            $products = $products->filter(function($product) use ($search) {
                return stripos($product->name, $search) !== false ||
                       stripos($product->sku, $search) !== false;
            });
        }

        // تصفية حسب حالة المخزون
        $statusFilter = $request->input('status_filter');
        if ($statusFilter) {
            $products = $products->filter(function($product) use ($statusFilter) {
                $isLowStock = isset($product->min_quantity) && $product->warehouse_quantity < $product->min_quantity;

                switch ($statusFilter) {
                    case 'out_of_stock':
                        return $product->warehouse_quantity == 0;
                    case 'low_stock':
                        return $product->warehouse_quantity > 0 && $isLowStock;
                    case 'normal':
                        return $product->warehouse_quantity > 0 && !$isLowStock;
                    default:
                        return true;
                }
            });
        }

        if ($request->ajax()) {
            return view('company_operations.branch_inventory_management.products_table', compact('products', 'warehouse'))->render();
        }

        return view('company_operations.branch_inventory_management.index', compact('products', 'warehouse'));
    }

    /**
     * تحديث كمية منتج في مستودع أو إضافة منتج جديد
     */
    public function updateQuantity(Request $request)
    {
        // التحقق من دور SUPER FIESR BIG
        if (!Auth::user()->hasRole('SUPER FIESR BIG')) {
            return response()->json([
                'success' => false,
                'message' => __('Permission denied.')
            ], 403);
        }

        $request->validate([
            'quantity' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            $warehouseId = $request->input('warehouse_id');
            $productId = $request->input('product_id');
            $quantity = $request->input('quantity');

            // البحث عن المنتج في المستودع أو إنشاؤه
            $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouseId)
                ->where('product_id', $productId)
                ->first();

            if (!$warehouseProduct) {
                $warehouseProduct = new WarehouseProduct();
                $warehouseProduct->warehouse_id = $warehouseId;
                $warehouseProduct->product_id = $productId;
                $warehouseProduct->created_by = Auth::user()->creatorId();
            }

            $warehouseProduct->quantity = $quantity;
            $warehouseProduct->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('تم تحديث الكمية بنجاح')
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => __('حدث خطأ أثناء تحديث الكمية: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تحديث الحد الأدنى للكمية
     */
    public function updateMinQuantity(Request $request)
    {
        // التحقق من دور SUPER FIESR BIG
        if (!Auth::user()->hasRole('SUPER FIESR BIG')) {
            return response()->json([
                'success' => false,
                'message' => __('Permission denied.')
            ], 403);
        }

        $request->validate([
            'min_quantity' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            $warehouseId = $request->input('warehouse_id');
            $productId = $request->input('product_id');
            $minQuantity = $request->input('min_quantity');

            // البحث عن حد المنتج في المستودع أو إنشاؤه
            $productLimit = WarehouseProductLimit::where('warehouse_id', $warehouseId)
                ->where('product_id', $productId)
                ->first();

            if (!$productLimit) {
                $productLimit = new WarehouseProductLimit();
                $productLimit->warehouse_id = $warehouseId;
                $productLimit->product_id = $productId;
                $productLimit->created_by = Auth::user()->creatorId();
            }

            $productLimit->min_quantity = $minQuantity;
            $productLimit->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('تم تحديث الحد الأدنى بنجاح')
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => __('حدث خطأ أثناء تحديث الحد الأدنى: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تحديث بيانات المنتج (السعر، الضريبة، الفئة، الوحدة)
     */
    public function updateProductData(Request $request)
    {
        // التحقق من دور SUPER FIESR BIG
        if (!Auth::user()->hasRole('SUPER FIESR BIG')) {
            return response()->json([
                'success' => false,
                'message' => __('Permission denied.')
            ], 403);
        }

        try {
            DB::beginTransaction();

            $productId = $request->input('product_id');
            $field = $request->input('field');
            $value = $request->input('value');

            // التحقق من صحة الحقل
            $allowedFields = ['sale_price', 'tax_id', 'category_id', 'unit_id', 'name', 'sku'];
            if (!in_array($field, $allowedFields)) {
                return response()->json([
                    'success' => false,
                    'message' => __('حقل غير مسموح')
                ], 400);
            }

            // العثور على المنتج وتحديثه
            $product = ProductService::findOrFail($productId);
            $product->$field = $value;
            $product->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('تم تحديث البيانات بنجاح')
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => __('حدث خطأ أثناء تحديث البيانات: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب الفئات
     */
    public function getCategories()
    {
        $categories = \App\Models\ProductServiceCategory::where('created_by', Auth::user()->creatorId())
            ->where('type', 'product')
            ->get();

        return response()->json($categories);
    }

    /**
     * جلب الوحدات
     */
    public function getUnits()
    {
        $units = \App\Models\ProductServiceUnit::where('created_by', Auth::user()->creatorId())
            ->get();

        return response()->json($units);
    }

    /**
     * جلب الضرائب
     */
    public function getTaxes()
    {
        $taxes = \App\Models\Tax::where('created_by', Auth::user()->creatorId())
            ->get();

        return response()->json($taxes);
    }

    /**
     * طباعة تقرير الجرد
     */
    public function printInventoryReport(Request $request, $warehouseId)
    {
        try {
            // تسجيل محاولة الوصول للتشخيص
            \Log::info('محاولة طباعة تقرير الجرد - الفروع', [
                'warehouse_id' => $warehouseId,
                'user_id' => Auth::id(),
                'search' => $request->input('search'),
                'status_filter' => $request->input('status_filter')
            ]);

            // إرجاع رسالة اختبار أولاً للتأكد من وصول الطلب
            if (request()->get('test')) {
                return response()->json([
                    'success' => true,
                    'message' => 'الطريق يعمل بنجاح - صفحة الفروع',
                    'warehouse_id' => $warehouseId,
                    'user_id' => Auth::id()
                ]);
            }

            // التحقق من دور SUPER FIESR BIG (مؤقتاً نتجاهل للاختبار)
            // if (!Auth::user()->hasRole('SUPER FIESR BIG')) {
            //     return redirect()->back()->with('error', __('Permission denied. This page is only accessible to SUPER FIESR BIG users.'));
            // }

            // التحقق من وجود المستودع
            $warehouse = warehouse::where('id', $warehouseId)
                ->where('created_by', Auth::user()->creatorId())
                ->first();

            if (!$warehouse) {
                \Log::error('مستودع غير موجود - الفروع', ['warehouse_id' => $warehouseId]);
                return response()->json(['error' => 'Warehouse not found'], 404);
            }

            // جلب جميع المنتجات مع معلومات المستودع (نفس منطق العرض)
            $allProducts = ProductService::where('created_by', Auth::user()->creatorId())
                                       ->where('type', 'product')
                                       ->get();

            // إعداد البيانات مع معلومات المستودع
            $products = collect();
            foreach ($allProducts as $product) {
                // جلب بيانات المنتج في المستودع
                $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouseId)
                                                   ->where('product_id', $product->id)
                                                   ->first();

                // إضافة معلومات الكمية
                $product->warehouse_quantity = $warehouseProduct ? $warehouseProduct->quantity : 0;
                $product->current_quantity = $product->warehouse_quantity;

                // جلب الحد الأدنى للكمية
                $limitRecord = WarehouseProductLimit::where('warehouse_id', $warehouseId)
                                                   ->where('product_id', $product->id)
                                                   ->first();
                $product->min_quantity = $limitRecord ? $limitRecord->min_quantity : 0;

                $products->push($product);
            }

            // تطبيق نفس التصفية المستخدمة في العرض
            // البحث إذا كان موجودًا
            $search = $request->input('search');
            if ($search) {
                $products = $products->filter(function($product) use ($search) {
                    return stripos($product->name, $search) !== false ||
                           stripos($product->sku, $search) !== false;
                });
            }

            // تصفية حسب حالة المخزون
            $statusFilter = $request->input('status_filter');
            if ($statusFilter) {
                $products = $products->filter(function($product) use ($statusFilter) {
                    $isLowStock = isset($product->min_quantity) && $product->warehouse_quantity < $product->min_quantity;

                    switch ($statusFilter) {
                        case 'out_of_stock':
                            return $product->warehouse_quantity == 0;
                        case 'low_stock':
                            return $product->warehouse_quantity > 0 && $isLowStock;
                        case 'normal':
                            return $product->warehouse_quantity > 0 && !$isLowStock;
                        default:
                            return true;
                    }
                });
            }

            // جلب معلومات الموظف الحالي
            $currentUser = Auth::user();
            $employee = Employee::where('user_id', $currentUser->id)->first();

            // جلب شعار الشركة
            $companyLogo = $this->getCompanyLogo();

            // إنشاء PDF
            $mpdf = $this->initializePDF();

            try {
                // إعداد البيانات للـ PDF مع التحقق من القيم
                $data = [
                    'warehouse' => $warehouse,
                    'products' => $products,
                    'user' => $currentUser,
                    'employee' => $employee ?: (object)['employee_id' => 'غير محدد'], // قيمة افتراضية
                    'inventory_date' => now()->format('Y-m-d'),
                    'inventory_time' => now()->format('H:i:s'),
                    'companyLogo' => $companyLogo,
                    'search' => $search,
                    'statusFilter' => $statusFilter,
                ];

                \Log::info('تم إعداد البيانات للـ PDF - الفروع', ['data_keys' => array_keys($data)]);

                // إنشاء HTML من ملف Blade
                $html = view('pdf.branch_inventory_report', $data)->render();

                \Log::info('تم إنشاء HTML من ملف Blade - الفروع');

                $mpdf->WriteHTML($html);
            } catch (\Exception $e) {
                \Log::error('خطأ في إنشاء HTML للـ PDF - الفروع', ['error' => $e->getMessage()]);
                return response()->json(['error' => 'خطأ في إنشاء HTML: ' . $e->getMessage()], 500);
            }

        // التحقق من طلب التحميل
        if (request()->get('download')) {
            return response($mpdf->Output('inventory_report_' . $warehouse->name . '_' . date('Y-m-d') . '.pdf', 'D'), 200)
                ->header('Content-Type', 'application/pdf');
        }

            return response($mpdf->Output('inventory_report_' . $warehouse->name . '_' . date('Y-m-d') . '.pdf', 'I'), 200)
                ->header('Content-Type', 'application/pdf');

        } catch (\Exception $e) {
            \Log::error('خطأ في طباعة تقرير الجرد - الفروع', ['error' => $e->getMessage(), 'warehouse_id' => $warehouseId]);
            return response()->json(['error' => 'حدث خطأ أثناء إنشاء التقرير: ' . $e->getMessage()], 500);
        }
    }

    /**
     * تهيئة mPDF
     */
    private function initializePDF()
    {
        $mpdf = new Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'orientation' => 'P',
            'margin_top' => 20,
            'margin_bottom' => 20,
            'margin_left' => 15,
            'margin_right' => 15,
        ]);

        $mpdf->autoScriptToLang = true;
        $mpdf->autoLangToFont = true;

        return $mpdf;
    }

    /**
     * جلب شعار الشركة
     */
    private function getCompanyLogo()
    {
        try {
            // محاولة جلب الشعار من إعدادات الشركة
            $settings = \App\Models\Utility::settings();

            if (isset($settings['company_logo']) && !empty($settings['company_logo'])) {
                $logoPath = storage_path('app/public/' . $settings['company_logo']);

                if (file_exists($logoPath)) {
                    $logoData = base64_encode(file_get_contents($logoPath));
                    $logoMimeType = mime_content_type($logoPath);
                    return '<img src="data:' . $logoMimeType . ';base64,' . $logoData . '" class="logo" alt="شعار الشركة" />';
                }
            }

            // إذا لم يوجد شعار، استخدم شعار افتراضي
            return '<div class="logo" style="background-color: #f0f0f0; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666;">شعار الشركة</div>';

        } catch (\Exception $e) {
            \Log::error('خطأ في جلب شعار الشركة - الفروع', ['error' => $e->getMessage()]);
            return '<div class="logo" style="background-color: #f0f0f0; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666;">شعار الشركة</div>';
        }
    }


}
