<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Pos;
use App\Models\PosPayment;
use App\Models\PosProduct;
use Illuminate\Support\Facades\DB;

class DiagnosePosIssues extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:diagnose {--fix : Fix the issues automatically}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'تشخيص وإصلاح مشاكل نظام POS';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 بدء تشخيص مشاكل نظام POS...');
        $this->newLine();

        // إحصائيات النظام
        $this->displaySystemStatistics();
        
        // تحليل المشاكل
        $issues = $this->analyzeIssues();
        
        if ($this->option('fix')) {
            $this->fixIssues($issues);
        } else {
            $this->displayRecommendations($issues);
        }

        $this->newLine();
        $this->info('✅ انتهى التشخيص.');
    }

    /**
     * عرض إحصائيات النظام
     */
    private function displaySystemStatistics()
    {
        $this->info('📊 إحصائيات النظام:');
        
        $totalInvoices = Pos::count();
        $totalPayments = PosPayment::count();
        $missingPayments = Pos::leftJoin('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                              ->whereNull('pos_payments.id')
                              ->count();
        $invoicesWithoutProducts = Pos::leftJoin('pos_products', 'pos.id', '=', 'pos_products.pos_id')
                                     ->whereNull('pos_products.id')
                                     ->count();
        $paymentsWithoutType = PosPayment::where(function($query) {
            $query->whereNull('payment_type')->orWhere('payment_type', '');
        })->count();

        $this->table(
            ['المؤشر', 'العدد'],
            [
                ['إجمالي الفواتير', $totalInvoices],
                ['إجمالي المدفوعات', $totalPayments],
                ['فواتير بدون مدفوعات', $missingPayments],
                ['فواتير بدون منتجات', $invoicesWithoutProducts],
                ['مدفوعات بدون نوع', $paymentsWithoutType],
            ]
        );
        
        $this->newLine();
    }

    /**
     * تحليل المشاكل
     */
    private function analyzeIssues()
    {
        $issues = [];

        // الفواتير بدون مدفوعات
        $unpaidInvoices = Pos::leftJoin('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                             ->whereNull('pos_payments.id')
                             ->select('pos.*')
                             ->get();

        if ($unpaidInvoices->count() > 0) {
            $issues['unpaid_invoices'] = $unpaidInvoices;
            $this->warn("⚠️  تم العثور على {$unpaidInvoices->count()} فاتورة بدون سجلات دفع");
        }

        // الفواتير بدون منتجات
        $invoicesWithoutProducts = Pos::leftJoin('pos_products', 'pos.id', '=', 'pos_products.pos_id')
                                     ->whereNull('pos_products.id')
                                     ->select('pos.*')
                                     ->get();

        if ($invoicesWithoutProducts->count() > 0) {
            $issues['invoices_without_products'] = $invoicesWithoutProducts;
            $this->warn("⚠️  تم العثور على {$invoicesWithoutProducts->count()} فاتورة بدون منتجات");
        }

        // المدفوعات بدون نوع
        $paymentsWithoutType = PosPayment::where(function($query) {
            $query->whereNull('payment_type')->orWhere('payment_type', '');
        })->get();

        if ($paymentsWithoutType->count() > 0) {
            $issues['payments_without_type'] = $paymentsWithoutType;
            $this->warn("⚠️  تم العثور على {$paymentsWithoutType->count()} مدفوعة بدون نوع دفع");
        }

        // فواتير بمبالغ خاطئة
        $invoicesWithWrongAmounts = $this->findInvoicesWithWrongAmounts();
        if ($invoicesWithWrongAmounts->count() > 0) {
            $issues['wrong_amounts'] = $invoicesWithWrongAmounts;
            $this->warn("⚠️  تم العثور على {$invoicesWithWrongAmounts->count()} فاتورة بمبالغ خاطئة");
        }

        if (empty($issues)) {
            $this->info('✅ لم يتم العثور على أي مشاكل في النظام!');
        }

        return $issues;
    }

    /**
     * البحث عن فواتير بمبالغ خاطئة
     */
    private function findInvoicesWithWrongAmounts()
    {
        return DB::table('pos as p')
            ->leftJoin('pos_products as pp', 'p.id', '=', 'pp.pos_id')
            ->leftJoin('pos_payments as pay', 'p.id', '=', 'pay.pos_id')
            ->select(
                'p.id',
                'p.pos_id',
                DB::raw('SUM(pp.price * pp.quantity) as calculated_total'),
                'pay.amount as payment_amount'
            )
            ->groupBy('p.id', 'p.pos_id', 'pay.amount')
            ->havingRaw('ABS(SUM(pp.price * pp.quantity) - pay.amount) > 0.01')
            ->whereNotNull('pay.amount')
            ->get();
    }

    /**
     * إصلاح المشاكل
     */
    private function fixIssues($issues)
    {
        $this->info('🛠️  بدء إصلاح المشاكل...');
        $this->newLine();

        $fixedCount = 0;

        // إصلاح الفواتير بدون مدفوعات
        if (isset($issues['unpaid_invoices'])) {
            $fixedCount += $this->fixUnpaidInvoices($issues['unpaid_invoices']);
        }

        // إصلاح المدفوعات بدون نوع
        if (isset($issues['payments_without_type'])) {
            $fixedCount += $this->fixPaymentsWithoutType($issues['payments_without_type']);
        }

        // التعامل مع الفواتير بدون منتجات
        if (isset($issues['invoices_without_products'])) {
            $this->handleInvoicesWithoutProducts($issues['invoices_without_products']);
        }

        // إصلاح المبالغ الخاطئة
        if (isset($issues['wrong_amounts'])) {
            $fixedCount += $this->fixWrongAmounts($issues['wrong_amounts']);
        }

        $this->info("✅ تم إصلاح {$fixedCount} مشكلة بنجاح!");
    }

    /**
     * إصلاح الفواتير بدون مدفوعات
     */
    private function fixUnpaidInvoices($invoices)
    {
        $fixedCount = 0;

        foreach ($invoices as $pos) {
            // حساب المبلغ الإجمالي من المنتجات
            $amount = PosProduct::where('pos_id', $pos->id)
                               ->sum(DB::raw('price * quantity'));

            if ($amount > 0) {
                PosPayment::create([
                    'pos_id' => $pos->id,
                    'date' => $pos->pos_date,
                    'amount' => $amount,
                    'discount' => 0,
                    'payment_type' => 'cash',
                    'cash_amount' => $amount,
                    'network_amount' => 0,
                    'created_by' => $pos->created_by,
                ]);

                $fixedCount++;
                $this->info("✅ تم إنشاء سجل دفع للفاتورة رقم: {$pos->pos_id}");
            }
        }

        return $fixedCount;
    }

    /**
     * إصلاح المدفوعات بدون نوع
     */
    private function fixPaymentsWithoutType($payments)
    {
        $fixedCount = 0;

        foreach ($payments as $payment) {
            $payment->update([
                'payment_type' => 'cash',
                'cash_amount' => $payment->amount,
                'network_amount' => 0,
            ]);

            $fixedCount++;
            $this->info("🔧 تم إصلاح نوع الدفع للمدفوعة رقم: {$payment->id}");
        }

        return $fixedCount;
    }

    /**
     * التعامل مع الفواتير بدون منتجات
     */
    private function handleInvoicesWithoutProducts($invoices)
    {
        $this->warn("⚠️  تم العثور على {$invoices->count()} فاتورة بدون منتجات.");
        $this->warn("هذه الفواتير تحتاج إلى مراجعة يدوية أو حذف.");
        
        if ($this->confirm('هل تريد حذف هذه الفواتير؟ (هذا الإجراء لا يمكن التراجع عنه)')) {
            foreach ($invoices as $pos) {
                // حذف المدفوعات أولاً
                PosPayment::where('pos_id', $pos->id)->delete();
                // ثم حذف الفاتورة
                $pos->delete();
                
                $this->info("🗑️  تم حذف الفاتورة رقم: {$pos->pos_id}");
            }
        }
    }

    /**
     * إصلاح المبالغ الخاطئة
     */
    private function fixWrongAmounts($invoices)
    {
        $fixedCount = 0;

        foreach ($invoices as $invoice) {
            $payment = PosPayment::where('pos_id', $invoice->id)->first();
            if ($payment) {
                $payment->update(['amount' => $invoice->calculated_total]);
                $fixedCount++;
                $this->info("💰 تم تصحيح مبلغ الفاتورة رقم: {$invoice->pos_id}");
            }
        }

        return $fixedCount;
    }

    /**
     * عرض التوصيات
     */
    private function displayRecommendations($issues)
    {
        if (!empty($issues)) {
            $this->newLine();
            $this->info('💡 لإصلاح هذه المشاكل تلقائياً، قم بتشغيل:');
            $this->line('php artisan pos:diagnose --fix');
            $this->newLine();
            $this->warn('⚠️  تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل الإصلاح!');
        }
    }
}
