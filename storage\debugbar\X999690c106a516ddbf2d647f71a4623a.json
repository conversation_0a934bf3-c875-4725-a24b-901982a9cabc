{"__meta": {"id": "X999690c106a516ddbf2d647f71a4623a", "datetime": "2025-06-30 15:33:49", "utime": **********.462426, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.047488, "end": **********.46244, "duration": 0.41495203971862793, "duration_str": "415ms", "measures": [{"label": "Booting", "start": **********.047488, "relative_start": 0, "end": **********.41163, "relative_end": **********.41163, "duration": 0.36414194107055664, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.411639, "relative_start": 0.3641510009765625, "end": **********.462442, "relative_end": 1.9073486328125e-06, "duration": 0.05080294609069824, "duration_str": "50.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45140096, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0028399999999999996, "accumulated_duration_str": "2.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.442662, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.915}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.452368, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.915, "width_percent": 17.958}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4552958, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 78.873, "width_percent": 21.127}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:2 [\n  331 => array:9 [\n    \"name\" => \"طقم عدة\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"331\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 9\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  1839 => array:8 [\n    \"name\" => \"ميداليات شكل محتلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"id\" => \"1839\"\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1477138094 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1477138094\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2047246983 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2047246983\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1443009568 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1443009568\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-369530171 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhZUWNuSzBGaTY0Z1UxN1RLWHlvS1E9PSIsInZhbHVlIjoiZUlzNzYyTE5ubzFjaWhFa2JyeFNMZVNnOXdNTzkxbkpPZ0Q4cFhkSzlBejVCNmFUSldadFIxVEIwN1lDc1FXdEp0ODU3Q3J3S3J6V1gvTzlvYlhmaUtKbklhMHBqSTZMbTRndGJqcVJ1WXo1b0ZmVnJWTWdRWGlEOURXcjE2MVgrV0x0dXFzaSs5eTBiKy90T2V5ZFB0MWxFVVg5cjRLbklNbDl0aWhXV2xteVVzbWR6eExPbjNkUGNRbHBtZjRNVklrYWFGcFRrU3VzQW9uRzNGRFNtKzE2bmJTN1B4RDdMNnNrSlNaRVYzWEI2QlBNOFhZcWxDam5lYTZiSE5kMVJCc1ltdTVzbDZIa3o1anU5NWRWei8rRUJndGVnYWprM1hUYjFqQU5rWlFwMmtabEdIbDcxaFFsdzNrVFhxeUI3VlJqb2kzeHJWZ2xSelpiNm4rb3M2VjJ6czlnUnlUTERwRll1L3dETWxJc2Z3NmF3UW90SFk5c25nS1hZeWVmcVMwYnVUcHE3bTc5TVovL2RtdjlHNmpwbkdkbklPYUpUcHh1VlNOSnYwVU1vTXFCRWlSdWsySVhoc3Vrb1hSTk1vbmFUbWFVa2dsSi8zNWhBcEQwYjRMRnVHV0tkUTZZcjBSQk9MeGZiSDZvWG9veG9SRFRXWmdjK1NxVlNwZWoiLCJtYWMiOiJkNGQ3NDcyZmZkYTc5YTFhMDhmNjg1MGQ1YjMwYmViNDIzZjlhMjJkZTMyMGVlZGZkMWE0MDYxMDAwYWM1NjMxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlR1RWM5TFBZTDdEWSttNDlNUyszVFE9PSIsInZhbHVlIjoiSEt4VVBXbW5nQnlZeGdhRTN2d3QwZEw4OERpd3ppRDU0UTFmZ0lLWXdwYnhwQ1RLSzhpYkcwSCsxNHNsQWEvOEVRZzgxeEEwRU5GMHVSNmVsek15YUUvOTRuU1dWK1RLWStHYnY3ekdwZWFKa1NWMWpTay8xYnJ6cmo1T3FmdHc4VmFXUXROK2lKSXRYTXVMN1hBeXJaRUR4MGhLY2hnVkNSSzlNV01RS25VSDVCM0pEbjV5YUxMYWZ5SEpJN1dHWWtvSndEUmIxVkI3VlRobTFXb0FQL3djbG1pWHd1QjBnTWNHVE51ZWxVeUwrQ2t4TUhjK29MeVRab3JGVXpWQytHeU43emxPcGFVVlRvUUpYUXhGUnZRTnNIMjV5K2Fuc3oxd1Z6d0JoY0xJQkl2RmdwcnVhQkdrellnY09zOWxWeVk4WkhJdnZkSU4vN1NEZlhBYnZEMXkzMExodlFUTEQvUnJLb2YzUTRkWmkzUytQV1lxajFjeUtnS3VOdXlHdWZTZi9aNWlDL1ZHNkpIdzI0TmtxWDRtbVFmVk5zeE9RdmpVdHBDaWhvdmhEVlp1cGVWMnZZc3RNLy9ES0hSdWpYQ2JvbCs4eDBPRW00dE0rcEtiV3hvSDk5N2hQUVFnSFNHVk96cFFzNVh4UXB4NjFKUzhlbTlqNUR5QW5ZRVciLCJtYWMiOiI2NjAyOTNkYmVjYzc2MTA4ZGQzNWU0Njc5Y2ZjNDU1ZTA5MTA1OGZjMjkwZTljZmEzZmFmMGQzMjFkMzc3ZmYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-369530171\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1992294598 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992294598\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-442289025 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:33:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iks1TWlnS2dyalN1amVUZCs2SnVjSXc9PSIsInZhbHVlIjoiWnY5R1liWGorQUhWd2s3TWppZlQ5aS9YUjBTSXc1VFhDTW5zdlpNZVpraHdReU4wMk53WEZuOXZDekFBWU5EZ1V1YVNXTzh4VzJpalBRR0JsYlYwMzlpWC91dGp4Sm80eWY0U2U5dlVUZzFKTllyR2FTc1FTUnJDOGxkblNWVmp1RUZ0R0JEUGJGLzV6N0FGZThxMWtPcXdCQUxmcGNWSEZkSlBJY3VkU1RFVlFIS1REYXJDOFQ2MUtqS2ZLQWh4RWRmVGVWeWM3RWdmNEgzMWQvVk1VMzNSRStibnQ1dGR4Mk92bXhObGFqdTVmZXVZWnBqOC9jTEg0R0FyRThQdDR2UVhGQ2lYVGxNM3pkUmU0T3QxcHY1R2x5THRPaHhqK2kzTTNsZGJxb1M5ZkJ4ZDZqMng1WWpjWnJxaWFRdkhXdFprNDFnZGNhZTVPbXRtWk9HZTJHa0N0eU1PSnd6RGlHUDRQWi9HZW9zZW93RzN1cXIyWlhaVldqZGltRjIvSGNPcmxXZXFZK1BvUnJlQXIzc0xuVThCUGZ1enVrV1RKQW9EWmVrK3gyNFBuaGZnN0lXd1dwVmZzN0pFT0UxWGhCZ1NxU2QrWjA1MVJ0MnJLVjE2ZDcyQ3NJa2svellIUmdoQnUzODg3YklBbzk3WGdIYXE5SEdGTzlKdE5iOVMiLCJtYWMiOiI3ZDZiNGJkNTIxYWM1NDYwZWMxNjkxNzAxM2M0ODUzMjkxMDFiMGZkNTFlZmE1MmE1YmI4YTRlNWJiNzI4YWI4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:33:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9maFppT1JRemN0aDRycHdrU3Rvdnc9PSIsInZhbHVlIjoid1NvMFJsSi9mcWNwbHdVTGxLcWo3RlRLZGx4dWNhd1lwK3BJdVA0UlczY3RubS9jUmJrT2JTaTVoZDBIZDN2djJ3QTU4TmJUVFJWb2xkaUhNRFVxbGEvOVJ5RFJIT0hLMGhqTHoyR3VFSVhUWVJQcTdoQnF6dzExSWJvYTZ1K3hWWis0Z0hIb3FuQ3VNUGNiWUVkdXBnaWZqWTdnQzhPSHl1eUZTTmg4RmJBR3J2cXNKWlJ4VnJLTVRmNmFBYVJLblFRZGcxa1JQYlkvSXFick50Y0M3OGowRjlLOERCbWtiaFU1V0ZHU2xYZlF1UEcyN2tIQXVWVTgvUStOeWJGQTBqeTIzdVdpUk55N25zZ0twemxGcEpzaUV4eFJ2K2JwL0hoRmVTam9MZHVTeWw4MHJTNURlUWJvRzVkam9wZFVoTzJETkVFbnNQcHZnYnl5aVEranBHK21GNGkweFl2YU45VzcyVEZrOXJJUEx4ZWlKZ1pLb01FY3hhaWtsNDNUZnZ6UStCaHpvZ2JWT2JYdTBvTHZDQXVyOTI3WVRVVmZaMUI2eG9wQ0VZTzVxVHhkd2l2U1pURG96cENHR3VxcVBOT2wzS2dZZEN0MHJXb1QzbjJBUzdnQTU3ZU9hVDMwcXAxVVFaMmtxbXFnZmNwK21ic3o0eFg3aHJGWmJWZmUiLCJtYWMiOiIzODY5Y2UyZDVhOTY0MzcyZjg0ZTBlNmEwOTUzODFjMDk4ODRkYWVhNWM1NWMwNWE5YjZiZThlOTg1NDVhMmViIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:33:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iks1TWlnS2dyalN1amVUZCs2SnVjSXc9PSIsInZhbHVlIjoiWnY5R1liWGorQUhWd2s3TWppZlQ5aS9YUjBTSXc1VFhDTW5zdlpNZVpraHdReU4wMk53WEZuOXZDekFBWU5EZ1V1YVNXTzh4VzJpalBRR0JsYlYwMzlpWC91dGp4Sm80eWY0U2U5dlVUZzFKTllyR2FTc1FTUnJDOGxkblNWVmp1RUZ0R0JEUGJGLzV6N0FGZThxMWtPcXdCQUxmcGNWSEZkSlBJY3VkU1RFVlFIS1REYXJDOFQ2MUtqS2ZLQWh4RWRmVGVWeWM3RWdmNEgzMWQvVk1VMzNSRStibnQ1dGR4Mk92bXhObGFqdTVmZXVZWnBqOC9jTEg0R0FyRThQdDR2UVhGQ2lYVGxNM3pkUmU0T3QxcHY1R2x5THRPaHhqK2kzTTNsZGJxb1M5ZkJ4ZDZqMng1WWpjWnJxaWFRdkhXdFprNDFnZGNhZTVPbXRtWk9HZTJHa0N0eU1PSnd6RGlHUDRQWi9HZW9zZW93RzN1cXIyWlhaVldqZGltRjIvSGNPcmxXZXFZK1BvUnJlQXIzc0xuVThCUGZ1enVrV1RKQW9EWmVrK3gyNFBuaGZnN0lXd1dwVmZzN0pFT0UxWGhCZ1NxU2QrWjA1MVJ0MnJLVjE2ZDcyQ3NJa2svellIUmdoQnUzODg3YklBbzk3WGdIYXE5SEdGTzlKdE5iOVMiLCJtYWMiOiI3ZDZiNGJkNTIxYWM1NDYwZWMxNjkxNzAxM2M0ODUzMjkxMDFiMGZkNTFlZmE1MmE1YmI4YTRlNWJiNzI4YWI4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:33:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9maFppT1JRemN0aDRycHdrU3Rvdnc9PSIsInZhbHVlIjoid1NvMFJsSi9mcWNwbHdVTGxLcWo3RlRLZGx4dWNhd1lwK3BJdVA0UlczY3RubS9jUmJrT2JTaTVoZDBIZDN2djJ3QTU4TmJUVFJWb2xkaUhNRFVxbGEvOVJ5RFJIT0hLMGhqTHoyR3VFSVhUWVJQcTdoQnF6dzExSWJvYTZ1K3hWWis0Z0hIb3FuQ3VNUGNiWUVkdXBnaWZqWTdnQzhPSHl1eUZTTmg4RmJBR3J2cXNKWlJ4VnJLTVRmNmFBYVJLblFRZGcxa1JQYlkvSXFick50Y0M3OGowRjlLOERCbWtiaFU1V0ZHU2xYZlF1UEcyN2tIQXVWVTgvUStOeWJGQTBqeTIzdVdpUk55N25zZ0twemxGcEpzaUV4eFJ2K2JwL0hoRmVTam9MZHVTeWw4MHJTNURlUWJvRzVkam9wZFVoTzJETkVFbnNQcHZnYnl5aVEranBHK21GNGkweFl2YU45VzcyVEZrOXJJUEx4ZWlKZ1pLb01FY3hhaWtsNDNUZnZ6UStCaHpvZ2JWT2JYdTBvTHZDQXVyOTI3WVRVVmZaMUI2eG9wQ0VZTzVxVHhkd2l2U1pURG96cENHR3VxcVBOT2wzS2dZZEN0MHJXb1QzbjJBUzdnQTU3ZU9hVDMwcXAxVVFaMmtxbXFnZmNwK21ic3o0eFg3aHJGWmJWZmUiLCJtYWMiOiIzODY5Y2UyZDVhOTY0MzcyZjg0ZTBlNmEwOTUzODFjMDk4ODRkYWVhNWM1NWMwNWE5YjZiZThlOTg1NDVhMmViIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:33:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-442289025\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>331</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1591;&#1602;&#1605; &#1593;&#1583;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">331</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>1839</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1605;&#1610;&#1583;&#1575;&#1604;&#1610;&#1575;&#1578; &#1588;&#1603;&#1604; &#1605;&#1581;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1839</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}