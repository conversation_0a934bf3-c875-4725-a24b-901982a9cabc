{"__meta": {"id": "X9ba34fc94e89ddc3058b31b81a9f805f", "datetime": "2025-06-08 00:09:51", "utime": **********.270414, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749341390.609277, "end": **********.27044, "duration": 0.6611630916595459, "duration_str": "661ms", "measures": [{"label": "Booting", "start": 1749341390.609277, "relative_start": 0, "end": **********.187628, "relative_end": **********.187628, "duration": 0.5783510208129883, "duration_str": "578ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.187643, "relative_start": 0.5783660411834717, "end": **********.270443, "relative_end": 2.86102294921875e-06, "duration": 0.08279991149902344, "duration_str": "82.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45176624, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.010709999999999999, "accumulated_duration_str": "10.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.227754, "duration": 0.00835, "duration_str": "8.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 77.965}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2511241, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 77.965, "width_percent": 9.15}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.258263, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 87.115, "width_percent": 12.885}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1758884480 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1758884480\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1489993116 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1489993116\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1293598283 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1293598283\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1869071255 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9BbGtuN1hKZlJZdGRibkk1U1ppbXc9PSIsInZhbHVlIjoiZTdzcGZvL2laS3k5dm9FSnhwenVvWnIyaEVJYXVZN1RZblNsMkJVVExaWVVsZGRSVDQxVm9UVTZzaml6V0VqWWd5L2ozakJjQm5lWmRyVGs3cjJTTk56Q2Y3NkpLZVdrc085ZmNzTlV3ekdIVlRjYjdPZHhkQW0rLzZySnVZUk91R202ZVVITGdUVFZzaUNuNFIvR3VSY1lhS0tsTkVmR2pvRnVPTFowNzRvbTVWZm5GZ3MwOFBYTStxYnFob3VxQnk5R3hHVWFkUGRybnJodzlNM1Rzcld0S3lvelgweElrV09Kd1NTWjRYVTFEUzJRMUF3VGdoRHNGM0daMUl5RmZ4OWRoQ3o4NTNVU045eFlUR3NEcmNqUVBCMkFZeFIvODZuS3ZydmVtZ3NSaGZCOEhRQU9xc2RkRTUwby96QXMwanBsbm12MnhSMnRiRHZKSktGblZZTElFa0F5M0xveUpGMnJFTzFUUlhRaGsxUTA2aXB3dXJBOXlNMk5CVjYxOUJKK0FMa2ZjajE5dHdlWDdQazF0VFFMM3I3YVRURVkzaFBQNmdQQUEwblVoYWVFU084Rm9WQ0o3TUJNVXFwbk5xT3R5SGI2QnFyRE82SWNTRjl1dUwxWHBTbnhBcWlUTSsvWUJMa1R1TzVpZWhNS09zNzAydmpTaURMRkR0OVIiLCJtYWMiOiJmMzZhNWE0ZGQwZjkyOTc4MDE5ODUyMTU5MTA1YjU5ZmNmOTFmYmE3NWVlZTQ3NzJhNjdiYWU3NGIzNmY4MmFkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im01a2FZWlg2bkt0RDM0T2phOVpEN1E9PSIsInZhbHVlIjoiblEzaWNiQVA1NDBUU2NPS2ZqSFlnVExDdDU0WFNmVHd3NUh1WGhvdW8rTkFxbGRtSW0vL3hOTWFjOGlpRHVRUE9HZXYyM1RZeVVkMlhYWk56ZjV5M1Y2UXZGU0U5SXI5VnlqcGp2RFFmUGp0QUh2RkwyOGlRSmFqKzBIWEpNZTV3MjZhN0xjQVhPL2JVekJJRXpFekpsL1BwWThic2U3a0lOVmwzaGR1WjNYRkpPR3VKdU83MWNJcTMxZlJRK3BZWFBoRzAwWG0yUU8zUXpZdm0xUjVDQWRQWjc0anV1VzgvU3c2bXpOaXphVWhaMVFmKzRuNVhlSHAwQm5pcVF6eWRsK09kVWJUMXNXcFlIRkdlaDJDdDUrb0JBR2syV3dQa0NiSStuVzNCdVdrbXBLbnNYdFROVndDL2ZZMVZkaHRuZ3NNcDNUL2RPOVFOR0ppUTQ0RTVnT3JhU1ZXb0p5aHlWaElsVWFNYjRQV2ptMVRRamVhZ3hWYUZkMXdJZnpNY0luMUhEVDc5ZXlEWlhyT1BrcGY0Nks2anVPeWN3aFJIWVFlNTBoOGJDeDgzRWxna1dkRk9LN1JKeGNiT2xqYVNVSjhsY1dzM3UxTFlaa1JhclhSSENza1IweWZybGhZSCs2bWptVmUxV2RxcjlZSkk5L2pBMG12Sy9sWUpvQTIiLCJtYWMiOiI0OGE4N2JhNmRkY2RlMGM2OWVhNGNmMGU3MzRkODMyYmMzYTI5NWU3MDI1NjQyMzZlY2EyYzZmMjAyMjZlYzYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1869071255\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-768383847 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-768383847\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-924799277 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:09:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNWeFBtZktPOFA1T29Pb0xrN1YwSEE9PSIsInZhbHVlIjoiTXF1MGFySnltQmV3V0RaejZOR2R0Zk9OMkU4UkV4Z1VxR3o4OHdQQk5sQmd3eFdQNkFoSWIwSjlSQjREcDduck1mS0NhQ1hSb3QrVkVZZDNHVmhCQ3JhUXJrUG9CZEVvOGJ5MDd2dG5uR0pTaXpuVHRQNTUrSW1oR1EvNkk5TzFZQkl5bDM4Q0tlY24vSGJ3Q3c5bFI3T2dIenFpZVU5cHd3Q2JjbUZJVDUrSy94U2d2MTkzNHB3OGR1RFI1a01qQmtTZEUvUHVYWnFOVXVmNm5sejk3MjFhZjk4clhpTzBBMmZUQkFmaVh6dzRoUjduV0ZjeEdCZjg5b0JhYUxXRldxUHphWG1HVFdSRjF2ZHdGL3RUQ2pNT0lVeExMRjZlNjJMTXRXV05XZkFjRWx6dkJpbHJJQnVackFYN0pWalQvWk9NcFVzZC9XOStSdXNNMmxRTTVFMHVsQ2VuaUFIK2NZcW52eU9HTngrcXZsUUJTZyt0S1ZiVVU5aHRRR2dpZjFBZEVINE90V2p6T2UvckZxQk1vWFgxZkNZOUhlZ1hwNThmRUE3TzJ4d1dIckR3eTZzNmdIb0tzQnkwTVZLaTFnNDZ1aFhaendGVHB0M2ZXT0g1ZWE0TnJSWFArQ0dPVWV3ZmR4dWhtaWV2UURMMjlKV3JYRmNLYndxWU9EY0UiLCJtYWMiOiI1ZWJlM2ViM2RhOTQ5ZTM4NmE5NzE5OGE1YzgzNWNiZWY4YWIzZDk2ZTc1MjU1Y2JjMTIzODNlMDkyNThhMTJiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:09:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjRPakJWVXVqWDBVZ2liamNZUFRwNGc9PSIsInZhbHVlIjoiK2ZnMnd0eWpmQ1hTZ0NhM1NEK05vYXlra2p5NnBDeHRZUktNaVJ6S05uWSs0c0RBcStkOHZsZjBBVU1oOUJNWjQ4aFdkSGVsb3VyZ3VSNTR2MWRTRzB2NURkSk84YUs4Sk4rMjBXelhmdFRNMDRZUDVkRjNDYW1MeFo0K0E5aTVoNnZiQ0lrYmx6TVNMOC9aN0dxSm9EVUZpSzVUeU5OOW81b2lGYklYa0JQL3ZtRTRCY1V4eWM3Z0xnNW1iMTE4ak5zbzQ2NE41RTBpU2ZuQ2M0enZOQWFXQmpHaGdhRTdXeTRLMWtvVzFvWnRGbDd1WkNtTWRKUFUxOVdTTDZ0ODVISTJvRGtwTmRjbUw0OVZIQWhEZjErWjJBcTlENGtKNmd0bkR3dEJqVWY0U0JWVGkwSklMVWYwQmc0ZkxmMllxaVBWc1RMSnN5aU42TnhmNm8yeVVURU9GWG5VampWTmlOSmhrM2R2RXdmQktMc3c5aE5haXVuaENuMll1Zmw2R3BMQnRwck0vOGw5V2lkbnlNZkg0TnFpRzg4OFk2RVpzdFB5SGdVSUp4ZnlaZGJiZHlRRDE2aWpaa3c0NFVJdG9MVGNJalpJUTV4K051UkcxVkFESHpocUI2bWt0UWpZZHZYVmdQcjNhMy9SV0NtMTdDUjJMUHJFRCtSZ3pUcVciLCJtYWMiOiJmYmFlNjRkYjY4ZGFlMDk3YTgwMDIxOWZkOTczNTdhMmY4MjFiMTc5NGU3ZTNkOTA2YzcxODQwMzNhZDg2ODdjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:09:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNWeFBtZktPOFA1T29Pb0xrN1YwSEE9PSIsInZhbHVlIjoiTXF1MGFySnltQmV3V0RaejZOR2R0Zk9OMkU4UkV4Z1VxR3o4OHdQQk5sQmd3eFdQNkFoSWIwSjlSQjREcDduck1mS0NhQ1hSb3QrVkVZZDNHVmhCQ3JhUXJrUG9CZEVvOGJ5MDd2dG5uR0pTaXpuVHRQNTUrSW1oR1EvNkk5TzFZQkl5bDM4Q0tlY24vSGJ3Q3c5bFI3T2dIenFpZVU5cHd3Q2JjbUZJVDUrSy94U2d2MTkzNHB3OGR1RFI1a01qQmtTZEUvUHVYWnFOVXVmNm5sejk3MjFhZjk4clhpTzBBMmZUQkFmaVh6dzRoUjduV0ZjeEdCZjg5b0JhYUxXRldxUHphWG1HVFdSRjF2ZHdGL3RUQ2pNT0lVeExMRjZlNjJMTXRXV05XZkFjRWx6dkJpbHJJQnVackFYN0pWalQvWk9NcFVzZC9XOStSdXNNMmxRTTVFMHVsQ2VuaUFIK2NZcW52eU9HTngrcXZsUUJTZyt0S1ZiVVU5aHRRR2dpZjFBZEVINE90V2p6T2UvckZxQk1vWFgxZkNZOUhlZ1hwNThmRUE3TzJ4d1dIckR3eTZzNmdIb0tzQnkwTVZLaTFnNDZ1aFhaendGVHB0M2ZXT0g1ZWE0TnJSWFArQ0dPVWV3ZmR4dWhtaWV2UURMMjlKV3JYRmNLYndxWU9EY0UiLCJtYWMiOiI1ZWJlM2ViM2RhOTQ5ZTM4NmE5NzE5OGE1YzgzNWNiZWY4YWIzZDk2ZTc1MjU1Y2JjMTIzODNlMDkyNThhMTJiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:09:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjRPakJWVXVqWDBVZ2liamNZUFRwNGc9PSIsInZhbHVlIjoiK2ZnMnd0eWpmQ1hTZ0NhM1NEK05vYXlra2p5NnBDeHRZUktNaVJ6S05uWSs0c0RBcStkOHZsZjBBVU1oOUJNWjQ4aFdkSGVsb3VyZ3VSNTR2MWRTRzB2NURkSk84YUs4Sk4rMjBXelhmdFRNMDRZUDVkRjNDYW1MeFo0K0E5aTVoNnZiQ0lrYmx6TVNMOC9aN0dxSm9EVUZpSzVUeU5OOW81b2lGYklYa0JQL3ZtRTRCY1V4eWM3Z0xnNW1iMTE4ak5zbzQ2NE41RTBpU2ZuQ2M0enZOQWFXQmpHaGdhRTdXeTRLMWtvVzFvWnRGbDd1WkNtTWRKUFUxOVdTTDZ0ODVISTJvRGtwTmRjbUw0OVZIQWhEZjErWjJBcTlENGtKNmd0bkR3dEJqVWY0U0JWVGkwSklMVWYwQmc0ZkxmMllxaVBWc1RMSnN5aU42TnhmNm8yeVVURU9GWG5VampWTmlOSmhrM2R2RXdmQktMc3c5aE5haXVuaENuMll1Zmw2R3BMQnRwck0vOGw5V2lkbnlNZkg0TnFpRzg4OFk2RVpzdFB5SGdVSUp4ZnlaZGJiZHlRRDE2aWpaa3c0NFVJdG9MVGNJalpJUTV4K051UkcxVkFESHpocUI2bWt0UWpZZHZYVmdQcjNhMy9SV0NtMTdDUjJMUHJFRCtSZ3pUcVciLCJtYWMiOiJmYmFlNjRkYjY4ZGFlMDk3YTgwMDIxOWZkOTczNTdhMmY4MjFiMTc5NGU3ZTNkOTA2YzcxODQwMzNhZDg2ODdjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:09:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-924799277\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1869875693 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1869875693\", {\"maxDepth\":0})</script>\n"}}