{"__meta": {"id": "X6fedac682b4fbcef32f6ee2e33953308", "datetime": "2025-06-08 00:28:18", "utime": **********.801236, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342497.82434, "end": **********.801259, "duration": 0.9769189357757568, "duration_str": "977ms", "measures": [{"label": "Booting", "start": 1749342497.82434, "relative_start": 0, "end": **********.69087, "relative_end": **********.69087, "duration": 0.8665299415588379, "duration_str": "867ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.690883, "relative_start": 0.8665428161621094, "end": **********.801261, "relative_end": 1.9073486328125e-06, "duration": 0.11037802696228027, "duration_str": "110ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45161760, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022260000000000002, "accumulated_duration_str": "22.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.746051, "duration": 0.0177, "duration_str": "17.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.515}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.779536, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 79.515, "width_percent": 6.514}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7854202, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 86.029, "width_percent": 13.971}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1290939494 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1290939494\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-138153941 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-138153941\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-599006369 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-599006369\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-266239243 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNJeFdBRlZDT3A4c0lTSUNEdTZnK0E9PSIsInZhbHVlIjoiUVg1U3FIeGErL29BdlYzN1llZFQ2VUdBNmVEamUxQUVBT3cwVGFzWEVrMXBBUkVYQTdyVVlkOHIwRXd3NDFIUUUzOVYzank1dU1uZVVEVVhJcTNMMUYwYVgra3JSeDZBdVdDOEVyMDg0TnY5Mmh5WFJ0cVVlNFlFM2xBemNkbVlTNG9jWTFTSmtpVVdlMFQreERGd3lHUTViMkZCUWVxRUJKdTNqTndmM2ZPREFNZHNIb2lTSzQ1b1lacXhTKytORnhZWHd0bTRaN1IveDE3b1JIM3RwcTZnMjB0bWlsMDR0SkxIdituUFk4STN1Ukx0RDhxQjNZcUE0V3c1OEtpQW9tVjRuV0ZMVmtVUFp0TklYVmF2eDcwN1BBeGNKOWVidDMweHBneCtYUG5JMkZkTm4yZTYxRUV1TFVKdmtDdnh2NUlqTjFybUkyM0pSZ2Y5blRNb096Z2NJZnV5N010K1FPTlRoZ3dWcjRjbVp5dDlSMml4VWpVb1NCaUpiOGJBcDhaZ3JXbGlSU21FcmlMM3o3cE56WVMxcmxaTlQ3UlA5dXFFK0NiZlE0UTBicVlRVU9oVEYzMFZ0VE53dkVZRC9DT2dnRk9rbFB5M05LTUZRQ2ZnSlhaZEdXaDhCOVB1bmN6cHlJSFllQTRKWU56dTYrTFN2NW1QYWlTZC9VNzEiLCJtYWMiOiJlZGRmZjY2NDFiMWE0NjJiMjEwMjliNmMxNTE2YmE3YzRhYWQyOWU3MzE0ZjRjMjU4NTkzOWE1MDc1MTc2OWRkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImJubnk3TU5haWx0d25oUDFFV2Ruc1E9PSIsInZhbHVlIjoiK0dFN1VpQlhkWGZKeEl3QkxGNjlERkozTEpyMFBoQ0xGUE82cE1ocTk4clg2MmN6VldXZjQzTWFhSGU3Q0dEZHNSWGR3TEwvQy94VlVWOXFDa1lMZkltSGVObE1HdEdkQ2M0clJwZnNjN1d6d0RlY0srcUNLUmpROUswdVRER3lRN1J5dHY5R09yUGltdUdWOGZDNW9zMFpoZ1ZiU0hjcUIwdGF3RTdNYnVETlJaQnpFdEtWTlpSRDFuWHFRbmNxNmNsMTMzWDZGM2lCazZGVTdxTjNMYUtRN3JuTmR4bUdGOTBiQTViY0dIVlNFcHdkUmhhWTBSalZLRFdPODBiR1oxUWxaK0xtWXV3VEZPeHZWbW9JeDJySVBTV2U4VGZ4VEQxNlFjVmdEVmZPSXdjR0QxSmZyWk5wWXdicnhpQ2R0L0FvOFM3bSt5TmVqS0dQOFlQSHRPSkxUelJBUzRkakorQ1NnaVUwM0JhcXZlMC9UamRHRkJSSEJwWjcxTUJoREZtWFNEV3luQWxPc2UwL283YVM3UTBxYVYzbkJjSUpvWFRnTnBsMndoOGtzVHZsSVR2Vms0ZzAzamtHOExQNDZwQzZVQVc4eWlFK2lKa0FUWG5ZMk9uV3BCRElnenB2WUM4NmVFZGdwOE4xTGpCY0dSTnR3WHlvTWFTT2J4NkkiLCJtYWMiOiJlNDY1YTU1NzgwYTM1YzFiMTg2OWJlY2I0NzNmZmViNmI0NWNlMGIwYzRiZTI0YTY0MWZkYjA4YTExNmE5MGZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-266239243\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-580319803 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580319803\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:28:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im8xYVpleXprSjhYd0dFRkZkMUttNmc9PSIsInZhbHVlIjoiNEtSVDk1N2hnM25pS3JFdXZZYWVOUHovditMTStQbUM0SWRzTHpzdy9KTVRZTDNDTE5lOWlFWlorMHZtK2ZOVzFOc0FzdUU0TkxrVzdiREZoNVNQSytBRUJFRzdML0F0cmJFTExXQ1lSZG1McFZRb2Vwa2xzdUFzS2hrdFBqWHVzWFZhdTlNRFpwUFdqVUxmcG0yemtTWUtreVB6QVhQaDRRTTkvQlByTTZlREtsK1cxNjZjSTRIcjk5bnQxNzRvQ2lmU2hNUSs5VWtPaUhWSDFBOE9yQk9YcDJlN004RlZTaDI3YTlVNTB1N1MvWkRaazE4YktUUmZ2Z3VoZHYrV1dsenhNQkdXOW9nK3A3TUN4NlhSVTJPemc3eGtsSlA1aDAzOFRsd0dqVFFBcTIvczZQSTlyV3IzUjhMVVp4ZXhhaEQ4R0lDdnh4V2pXWldZSXBxVURocVVRbmFpcXJ2QjZnVmZUSDR4Y1RqeThsV3dDcHIvRzk5bFB5TmZIcFl3L083VDNhaXlhajRmU2M0cmtzY2lFcTlReHZ3blpxSzc1Y1F1SkNHRmo4czh6d1NCVU9Ba2RQV1BqYmpLVnJOZjdVclBCaDlBZHo0N1RGNDVGMkJCelZQRVB5dlcvbmg5eU5SQnRrQzVrVmZ0anRpUlRlUUcyM1JBbnhsRUNzUXciLCJtYWMiOiJhZDEyZmQxNzlkOGQ2ZmIwMTA3NjQ2Mjk5Y2VkN2Q3Y2NjZDY2NDVjYWRkMjA3Yzg0NDAyNzUxOWFlYjZkNWZkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InFBdzdmR3EvWnRrVE5HUGx6aVRBYXc9PSIsInZhbHVlIjoiUEpMZ1Q0cWMyKzdiOEFTTldtVnNmUmV3VlQ2bDVIYnMvNTR0a0xsUk9seFZBdDRTSis5a3BkT1VIVDNRZGRZL0I2REhuTkRveGNuVVFid2FpUG1pcnJnQXpIUjdUR3BsbGw2MGMxbXJKTHN0akllR3Yvc0xRcVhpckx0YXZpVE1Sa2cyU0FMZTJlUlNPMjNBYStnRmprM25pajd3UFVycEtlZmJpQmwySWtwdXNNMDlCL1VsZEdUdUVtcksrM3hPREhGZHVPZ1BTbjlFMVkrcVlzMTNXWG9qeG5wbzI0aTRmT09ZS1hvQ0NuT1FUTEtBaWhKdHNxMTBMcGJzRk5BME5PQ3Z2WVFUL0pvem41ZDZrUnlwdGlaRGtoS2tvb0RsM2NFcFJoU2xhOFFmY3JSN0FucU1nR0tvTVRJYndKam9qSGpHR2VXZnNpVGJnVWI5M29RcGlFVUtjejAwNFZXTFNyR056M2gzR1p0UkZwRW5VZk9EaC81b1lHQWwrWHRVL0VoZXdpakdGUEExbXVGYlVzelkxRWNDSkwyOWlNYmdRWGpmeFVJZUJ2NnN5RGNTeVY2b2owUGxmWE9FQUJQWlIxYitERjJSby82UWRWdE1LejdsK01vOHVnOFQ2cVIyTks4aDlSZ1JwRjROQUxaU2xSMDZPMURmcHBEOGpLMDciLCJtYWMiOiI1ZGZhZjE0OGQ5NjRhY2Y0YzgwOWRkYjE5ZTJlY2RjNDlmZTg2MDlmNGQ0ODc5ZTZiYmI1MmZlZTFkZDYwOWU2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im8xYVpleXprSjhYd0dFRkZkMUttNmc9PSIsInZhbHVlIjoiNEtSVDk1N2hnM25pS3JFdXZZYWVOUHovditMTStQbUM0SWRzTHpzdy9KTVRZTDNDTE5lOWlFWlorMHZtK2ZOVzFOc0FzdUU0TkxrVzdiREZoNVNQSytBRUJFRzdML0F0cmJFTExXQ1lSZG1McFZRb2Vwa2xzdUFzS2hrdFBqWHVzWFZhdTlNRFpwUFdqVUxmcG0yemtTWUtreVB6QVhQaDRRTTkvQlByTTZlREtsK1cxNjZjSTRIcjk5bnQxNzRvQ2lmU2hNUSs5VWtPaUhWSDFBOE9yQk9YcDJlN004RlZTaDI3YTlVNTB1N1MvWkRaazE4YktUUmZ2Z3VoZHYrV1dsenhNQkdXOW9nK3A3TUN4NlhSVTJPemc3eGtsSlA1aDAzOFRsd0dqVFFBcTIvczZQSTlyV3IzUjhMVVp4ZXhhaEQ4R0lDdnh4V2pXWldZSXBxVURocVVRbmFpcXJ2QjZnVmZUSDR4Y1RqeThsV3dDcHIvRzk5bFB5TmZIcFl3L083VDNhaXlhajRmU2M0cmtzY2lFcTlReHZ3blpxSzc1Y1F1SkNHRmo4czh6d1NCVU9Ba2RQV1BqYmpLVnJOZjdVclBCaDlBZHo0N1RGNDVGMkJCelZQRVB5dlcvbmg5eU5SQnRrQzVrVmZ0anRpUlRlUUcyM1JBbnhsRUNzUXciLCJtYWMiOiJhZDEyZmQxNzlkOGQ2ZmIwMTA3NjQ2Mjk5Y2VkN2Q3Y2NjZDY2NDVjYWRkMjA3Yzg0NDAyNzUxOWFlYjZkNWZkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InFBdzdmR3EvWnRrVE5HUGx6aVRBYXc9PSIsInZhbHVlIjoiUEpMZ1Q0cWMyKzdiOEFTTldtVnNmUmV3VlQ2bDVIYnMvNTR0a0xsUk9seFZBdDRTSis5a3BkT1VIVDNRZGRZL0I2REhuTkRveGNuVVFid2FpUG1pcnJnQXpIUjdUR3BsbGw2MGMxbXJKTHN0akllR3Yvc0xRcVhpckx0YXZpVE1Sa2cyU0FMZTJlUlNPMjNBYStnRmprM25pajd3UFVycEtlZmJpQmwySWtwdXNNMDlCL1VsZEdUdUVtcksrM3hPREhGZHVPZ1BTbjlFMVkrcVlzMTNXWG9qeG5wbzI0aTRmT09ZS1hvQ0NuT1FUTEtBaWhKdHNxMTBMcGJzRk5BME5PQ3Z2WVFUL0pvem41ZDZrUnlwdGlaRGtoS2tvb0RsM2NFcFJoU2xhOFFmY3JSN0FucU1nR0tvTVRJYndKam9qSGpHR2VXZnNpVGJnVWI5M29RcGlFVUtjejAwNFZXTFNyR056M2gzR1p0UkZwRW5VZk9EaC81b1lHQWwrWHRVL0VoZXdpakdGUEExbXVGYlVzelkxRWNDSkwyOWlNYmdRWGpmeFVJZUJ2NnN5RGNTeVY2b2owUGxmWE9FQUJQWlIxYitERjJSby82UWRWdE1LejdsK01vOHVnOFQ2cVIyTks4aDlSZ1JwRjROQUxaU2xSMDZPMURmcHBEOGpLMDciLCJtYWMiOiI1ZGZhZjE0OGQ5NjRhY2Y0YzgwOWRkYjE5ZTJlY2RjNDlmZTg2MDlmNGQ0ODc5ZTZiYmI1MmZlZTFkZDYwOWU2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1027319698 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027319698\", {\"maxDepth\":0})</script>\n"}}