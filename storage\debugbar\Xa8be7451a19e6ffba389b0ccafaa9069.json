{"__meta": {"id": "Xa8be7451a19e6ffba389b0ccafaa9069", "datetime": "2025-06-07 22:18:28", "utime": **********.27602, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334706.82364, "end": **********.276057, "duration": 1.4524168968200684, "duration_str": "1.45s", "measures": [{"label": "Booting", "start": 1749334706.82364, "relative_start": 0, "end": **********.089434, "relative_end": **********.089434, "duration": 1.265793800354004, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.089462, "relative_start": 1.265821933746338, "end": **********.276061, "relative_end": 4.0531158447265625e-06, "duration": 0.1865990161895752, "duration_str": "187ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45054448, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00726, "accumulated_duration_str": "7.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1877909, "duration": 0.0046500000000000005, "duration_str": "4.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.05}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2226748, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.05, "width_percent": 18.044}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.247058, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.094, "width_percent": 17.906}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1664583219 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334697734%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJPMTJOSlpUSTc5ZTlFeHBHVnh6RHc9PSIsInZhbHVlIjoiZWRQbDNyakplMEJxRndiNFp1NkJ5SzhPQkUzNk96RnhuUWpBNmZkSWpoS1N6WHFUaHN3SWtFUWJtTC8zalJId2ptKzd5WTQzU056endDdW5zd2EvNjFmWTNBZzdtMEp5d0xNKzErVDRXL2hsMm1Ca0tobitwTUdlVmdLQlBESG9CaHNhZnNldmNQTzE0dUlJQmZHdXgvYWIvRWUyUzBVa2dHZmdUZVdnWUxrWUQ0RU1uc0d6U3Z0MzRWa1YvZVVoZ1liY1NPMVVUUkNqZjh0cmVTM0FEWUlQOW04MmlaZVFXSjg4d0E0b0hFamdLMjdqZWxpTDRUekp0ckR1L2pBY1U4S0F3bUM1bDNtZ3hGSnZabE5GMW0xWW5nVnhlT21IemMwWnpqa00vNFFPN2NtZU8vN2c2YjNsbll4bmJpL2NXT2J0NmJleEFsbGFwV1Y3amFvZUVIcllhWmtFcHVrZTI5ZFl5OEZPaXlFUXlpTk9PakNNNitHM2t1K1Z4OE92VmRqd0VKbVJkdUliNjVVL2hLb3R2MzhGdS9NMnJiQmZhWTNYdlNsRjU2NElrN0x0RzlhZ3NFOEt2T2RsTzdUTU9JK1NlY28vWDF5Z0RRVUswSkdQdkVYUHpISXVvamswelY1YzREMGFkcHBRWjNMVGxHTlVXU0xPNEtaczl6d3MiLCJtYWMiOiIyZGQ1MDdhMmVkMWIzNmFjNjU2NDBjZjJkMjhjZjdhZjMzOTcwZmJlOWRkMTE4YTJlN2YyOWQwODVhNWM2NzcxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImRDbjVsbDE4dVhieXdkS0dINXV2bnc9PSIsInZhbHVlIjoiSWZZZEpyRHBtRW5CTi9EREdJTjV6TjhtZUNyMS9kUW1uWTZEYkxucHZPRk1tSDA4MkdPbTRRejZSTlhTMUdETVRqTzc5ODdwMnJvWlNyNDlRRXlvcTNuakxJazZHcTNEbWkvU1ZpVCtjNmV0UGtxZ0w4MVkrdDFJbXVpZENUYXBXZzc2eTc4RWRwWEVxVHRQYThBZ2wvRnJRSjFaR3hBTFY2bTBxd1A3Q08vdDVPZ0tvTEFVeW8rZ0ZYaU93WWd2TXo2bWVMckpYWDhlS3B1V2ZjTG9KeWpKL3VON1g2WmlkUlIva1R6V1h3a0pVbldaUVVBSXA4RDU4UGIreWszUXZOVGMzUUZDS0I2cjJGSDFSMkpBYkFZRFZPV3FoOVhiaW8zSjlLVHg5aHpFdFIwbXpKVkxjdXIzd0JVSUczeWdoWW5YbnNRN2VtVzJuUnhCQUxPeGN6QUszV3NOYnJ4SXFqdFdSYjhzU3IzVzd5MkQrRTA5VUVLUElHaElCQ1kxa3pPZldIMlFSUUZHWVlTZXZDR2ZraHRsZCtZeVVkeVJzWFlLV1kybUU0YXNUYWJYU29GbjRlbjgxV3BrbDk2eTl0L1RFL2FLN3VkZEErNFUvZkZad1ZWS3lEbVdsYmo3M1RZYTZNR3JYQ2ZlTlR3UWE0SGNKRXJaMEhodzB3ZXAiLCJtYWMiOiIxNjM2YzQyZWQyYWZmZjM0YjlhNDgzN2FiZjZiMGU5MzhiYzJjMDZkOGRhZjdlZjFlMmI0MGMxZWQzOTkxOGViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1664583219\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-114213937 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-114213937\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1080757268 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:18:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImM2TmFZUTJoWWQxTklBZHp4ekN0UGc9PSIsInZhbHVlIjoiYW95M255dUp0WENDMERtQ29HNEFEcWFHK0p2NXgxM0RQeDUxSEFKdnB4WXBWWFAzR0dNNEpGSE8xL1RjUHRBSENnK2N4a1ByazVtZ0k2cGF6dm80Y2x2a0QzZEF3bktUUmhBNit0cG1ZM0Y4bXR0TDB0aGlQYUFGZjdxalhCbnNDbVNLU3BOZjBrK2tVM2NnMElSQ1Y0d21neklBMG01VU80ejl4U2lFcmlaK01nSnZrT2xuOW5wNkZDNnNGd0k3Yml4NGVaVG1RemFCSWtlVnFKc0xhdVB0aHNoOXdibkF5STVjYTZZdTB6RDdPQXlOR1BtWXNvSFRRcmxNNmFuSXpRZkNHZFdYVkwwZFV6T1ZWWHpvdWdUOE40c3RqUWVsU3E0UXZxNzhXN0V5WTVHeng3ZkVOcXJJeDlkYmUyWTljK0RublliTTRsSmpnUFgrblF6N3ZZUTBSL01xMUNyM3AzOGtoODVlMUE5NEZYaEJzOXBtRGZiQjVEbC93S1MzaUozeUpSYkI4bUM4MUNoVmJFZkRNZzRXWXE3Nk5mRGxUK04rNXA4N0doMTF5SUJvM0lyeGl3am9YSG5OVjNHS0YvTGlXMGU0eWxPU1J0cWtrNjNDdGJtQkY4eXRpZWwrYkV3YjhkbFFOU2RaTk9PRmY0dDk0UUhmcTRVcmo3SDciLCJtYWMiOiI5N2Y0NzVhNTNmNWQ3Yjg4NDIwMGM5N2Y4OWZhZjVkY2EyMjZiMTIyNTY1MGM1ZjBlZWExMGZhNmM2NDdiNzQ1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlA5djRZZHhsRjNYZjl5MlRLaXN1RkE9PSIsInZhbHVlIjoickZLZ1lpYmlIL0tVS3JPc2kzcER1R0dYT0tURzl5amNjMyt3eW4wSndVNUkzNXE4Z0Mxd1hJbkpoc09Da1BhNFRuK0Z0QkFQbXpsZFE2MlExdXRKYlJOZlhIdlJCdENHaExRU21WMDVwYmVpaEIyOHoyaVhGRFhRSGpnaVVaeDhkU2RacEtOejVPbzR6OWJmeGpKMlhVazQxSzhNM295N3lTMm5GSjROV25DOGJ2TEltYXdLaGN4cWlkRVNvelI3WFpBZFA2KzNES0ZsSzBIOUVucDlPbjByNmc4SnVONEgyTi9HV1RZTXpRQjAxT045aStVaDR4N0haSVBYMEJ3OWN2RXkvTC9NczNkdW5tN3JWR243aXhoeUQ1YlRRMENCejNyeVFXODRieHVtOTAvK0dpT2RpR2t2NjhCYmV6L1JBTGdxbVUzZkh0VXlKUktJcDhmeFdTWkhKL1VhakdIcjBKZHdhd2IzakUrdUNEb0pIeWxYYmhLS2hzMTFaVktOQzlabU00OTdSNVpjeGdmMHlMa0ptTUxBZU1Sb1lhWU1XTFFDSTV4bTBHdW5sSGNaKzlKSUVaMUo2UnArMUNUakhZdjRUU0YyczBuaktJNGhoTUdVMnhWRUJPK3N4Z3Y5RVRrdUpJQmNhMWlFNWdiUHlQdlMvOTFqNmFncERyV28iLCJtYWMiOiIyZmZlNThiYmZjM2M1NGFhOGZjYTZkMmFiODFlNTBjOWFiNzAxZjU0MzAzMjlhOTE2ZWQ3YTNkM2QwNjRjYmYzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImM2TmFZUTJoWWQxTklBZHp4ekN0UGc9PSIsInZhbHVlIjoiYW95M255dUp0WENDMERtQ29HNEFEcWFHK0p2NXgxM0RQeDUxSEFKdnB4WXBWWFAzR0dNNEpGSE8xL1RjUHRBSENnK2N4a1ByazVtZ0k2cGF6dm80Y2x2a0QzZEF3bktUUmhBNit0cG1ZM0Y4bXR0TDB0aGlQYUFGZjdxalhCbnNDbVNLU3BOZjBrK2tVM2NnMElSQ1Y0d21neklBMG01VU80ejl4U2lFcmlaK01nSnZrT2xuOW5wNkZDNnNGd0k3Yml4NGVaVG1RemFCSWtlVnFKc0xhdVB0aHNoOXdibkF5STVjYTZZdTB6RDdPQXlOR1BtWXNvSFRRcmxNNmFuSXpRZkNHZFdYVkwwZFV6T1ZWWHpvdWdUOE40c3RqUWVsU3E0UXZxNzhXN0V5WTVHeng3ZkVOcXJJeDlkYmUyWTljK0RublliTTRsSmpnUFgrblF6N3ZZUTBSL01xMUNyM3AzOGtoODVlMUE5NEZYaEJzOXBtRGZiQjVEbC93S1MzaUozeUpSYkI4bUM4MUNoVmJFZkRNZzRXWXE3Nk5mRGxUK04rNXA4N0doMTF5SUJvM0lyeGl3am9YSG5OVjNHS0YvTGlXMGU0eWxPU1J0cWtrNjNDdGJtQkY4eXRpZWwrYkV3YjhkbFFOU2RaTk9PRmY0dDk0UUhmcTRVcmo3SDciLCJtYWMiOiI5N2Y0NzVhNTNmNWQ3Yjg4NDIwMGM5N2Y4OWZhZjVkY2EyMjZiMTIyNTY1MGM1ZjBlZWExMGZhNmM2NDdiNzQ1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlA5djRZZHhsRjNYZjl5MlRLaXN1RkE9PSIsInZhbHVlIjoickZLZ1lpYmlIL0tVS3JPc2kzcER1R0dYT0tURzl5amNjMyt3eW4wSndVNUkzNXE4Z0Mxd1hJbkpoc09Da1BhNFRuK0Z0QkFQbXpsZFE2MlExdXRKYlJOZlhIdlJCdENHaExRU21WMDVwYmVpaEIyOHoyaVhGRFhRSGpnaVVaeDhkU2RacEtOejVPbzR6OWJmeGpKMlhVazQxSzhNM295N3lTMm5GSjROV25DOGJ2TEltYXdLaGN4cWlkRVNvelI3WFpBZFA2KzNES0ZsSzBIOUVucDlPbjByNmc4SnVONEgyTi9HV1RZTXpRQjAxT045aStVaDR4N0haSVBYMEJ3OWN2RXkvTC9NczNkdW5tN3JWR243aXhoeUQ1YlRRMENCejNyeVFXODRieHVtOTAvK0dpT2RpR2t2NjhCYmV6L1JBTGdxbVUzZkh0VXlKUktJcDhmeFdTWkhKL1VhakdIcjBKZHdhd2IzakUrdUNEb0pIeWxYYmhLS2hzMTFaVktOQzlabU00OTdSNVpjeGdmMHlMa0ptTUxBZU1Sb1lhWU1XTFFDSTV4bTBHdW5sSGNaKzlKSUVaMUo2UnArMUNUakhZdjRUU0YyczBuaktJNGhoTUdVMnhWRUJPK3N4Z3Y5RVRrdUpJQmNhMWlFNWdiUHlQdlMvOTFqNmFncERyV28iLCJtYWMiOiIyZmZlNThiYmZjM2M1NGFhOGZjYTZkMmFiODFlNTBjOWFiNzAxZjU0MzAzMjlhOTE2ZWQ3YTNkM2QwNjRjYmYzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1080757268\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-15******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15********\", {\"maxDepth\":0})</script>\n"}}