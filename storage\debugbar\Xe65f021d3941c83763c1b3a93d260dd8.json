{"__meta": {"id": "Xe65f021d3941c83763c1b3a93d260dd8", "datetime": "2025-06-07 22:18:10", "utime": **********.729219, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.258103, "end": **********.729255, "duration": 1.****************, "duration_str": "1.47s", "measures": [{"label": "Booting", "start": **********.258103, "relative_start": 0, "end": **********.50136, "relative_end": **********.50136, "duration": 1.****************, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.50139, "relative_start": 1.****************, "end": **********.729258, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "228ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023180000000000003, "accumulated_duration_str": "23.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6073189, "duration": 0.019510000000000003, "duration_str": "19.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.167}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6673799, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.167, "width_percent": 6.255}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7022462, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 90.423, "width_percent": 9.577}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334686693%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImUyTUltMEFGMjQvNDdYNEtvZ3g5OGc9PSIsInZhbHVlIjoibWZTMGxZVWdFZUZ1cGRtRk1kREJsb0k0cTcvZXJndWlNSW40dXZUc3ZFY1VhaE5wakgxWGtOWnJmdE5NMUI0MUhNT2hXRWEyVjdIZHRmU3RtMGRiQnVNWW5sYVpKbWlNcG5wQzBwOGtPWUxYRVRQK0t3Rjk0S1lvQVRJc1ZaOWxXeUNwem9pb0txN0FrZ09WTmZ1SGFnTEhIMjJTNDIrVDJwaThnZzlaZnlnbVVuZGd3anU4TUlOL3RabXBRU3NHRUpmNzVuQnh5VkNITEh5Y3RrQUJERisvTXBoUGtCNzZESVo0cW83S0pFelpUdmxYbS9JdmREOXlQN2xZS0RXVHMzcUs2OEtDRHhlN2FnYWVxcmZkVDhEZlRVaFowK1NzWllDTVJQdkFSVlltK2RVQkY5Vyt5SkdpUlBiallqRlFRQklkd0NkeFFjOTVDNnhMM0p6aXB1MElWdjV1YmZwTVpwSFM0WStyOGVpYVY3M0pYcXF5VjRPSGZJcjViUVpxQzB2M0RmNWNxVGYwQlN0TDc4ZFM3V3pQRDlzV1o1alhtdjZJcXE3V3hoY0l1S3dLM29hUWQyaE5kdUJmNElSaW9hbzFINzNEcFg2TDdyazJnUnlRdUhVenFoSE5BNENWV09VNVI3Ris1S2tmTERVZG5Sb1NsZDlyNHBSdmoyckwiLCJtYWMiOiIyYTU1YzhmZTBmNTg4NGY3NmUxM2YxOGJiMzhlZmQzOGEzZjA1ZGNmZTBhYWYxNjJiOTY4OTNlMDVhOWY5NTZkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlByK3ZpNG5SL09hR3VJbHNwK1Bob2c9PSIsInZhbHVlIjoiZkxaS21SdTZXQko3ajdtc2VwcnFKaGVWcHZjaCtxL0JjMHZrSVpHQVAzTTF4Z3VxOE5NYno2ZkV1Y2Vyb2tmeHRsdlRwWHNId1F0bUM4RjNTSTFwQ0UxVjdySVI1b0dqUUlkeEg2OFFsK1M0aDlmUGhTQXBKUEV4ZXByRlZ1cERqMVBtaU1nNnZMMExrRFduSG0rK0hDVHB6YnMxTmRQZi9hMEZvN2VPbjJWeEIvbFV4a2hudU1FaWlRb0hMM2JNcXFDTWorVFlURFVxbDEzVmV6dkVKWU0zU0ZOcWpKcUhnZTNXS2d4bitUL0lybW1xUit6R2tYc0hGUEE3eWRiVSttMVhSbEJwMktidnVSVnJETE16VDRNYWRYU0MwY1l6TWdrZm0rTzk1V01wcTlIOFJiQlpPNUM5cllvcVVNdjZQZ25LNHZub29PZ3g0T1pTbWNYYUlpQWN5TmZyQk9wTW1qY3g2c2JGVFFtTVA3Y3lxRVA4SXlWbVVUNU1oU1lKQ2xhZ0JiaHo1RWFReTljVVpKVUxnN2psTGgyZVQrc3JmSm5ieTFIcmh4ZkgwbTdzdWdydGZzNlBYWkZzNG1MYjdIdVY1T3JZQWNKOXVhVlUxN0ljMWJCaFNMSGllUnRaeHpZT2ZWS3Q0R2xWcURHUjhnKzBVSFNCWWRqd0U4aWkiLCJtYWMiOiI0NzFiYmMzODcxMTY3NDE1ZmRiOTk5ZDMyZTUwZWFhZjQzYTAwNGQ1Mzg4Y2RkMjYyMjZlZDE4M2YxZDI0MzUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1863766621 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PmFRgUqysTWH0qB2ROlhITKoLIETZGW2KykVXtOx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863766621\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1336241380 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:18:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdQaG5yTCtjbEJrWitSOW8zQUk1Nnc9PSIsInZhbHVlIjoiSjJzKzRpeXVGdDNPeXdxRDF6MnNoaXF3K3d3TTEwOEVWZG1KTDZ4Y21RTlc2SXdiU1RWTnRFa1FESndFTUNrTmNpVEdCeEtSZHg2eTFwclBDcUlLeERjc0U1SDNBQXhKSXc1MFEwZEFFTDZNem5SU0IwOFhnclFUQm1hL1YvakU3QmYxSnNZU2tleExXb0EzVmIwd1FGQkVJeUR1MVM1NHYvdE9vUXZ6NFdJdW5GVjNvU3BBa3NpMkZlRlFGa1NXcTg1bjFoTTVxTlR4WHFOMjhmSytUd2UvdVp6QnJYcytra1FKTVcwbWdXNjdtcFdaTjBDOVdGTG5vanR5cmpJUlBONkIwRGxIYVNaZmZhdXJxVEVCTU9nOG9Vd1Jsd2RlZ3FRdzYvQlZzN2l2RzJCQ3FscVlmNkoyM3gwV1dGUFpNdjN5ekJuanZLSkFJM2pQa3NUL2RPMFR5ajZNQnVGV2xhQjc3SFB6OHg5RzN4b0ZJbjlOUy8yZzBHSEVWYkhIQXh3QkppYVVONFN3U1FZYjZubGorZW5ubmNQNnBPVnpJY2YzWHFDZGVQalpKTFRYek0vKzhXYThmTy9zMDFuZVRmbTB4ZVBnL01LMWxybWFzOUsya0VGRjhlaSt5bUFTY0ZKMXZDS1k0QzhMcDVXWWhJLzlZUDBuczZPSFRTLysiLCJtYWMiOiIyOWI2ZTdmODU3Zjc4Mzg0NTk5NTJkNGJhMDUxODNhYjAyYzA0OWNjNDlhYTEwYTRmMmQ2YThkZjUwZTEzNWMzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InhmbFA2bE1TSTZRdFYyaEJzVlFSMGc9PSIsInZhbHVlIjoicVkrS05oRXpUa3JCbnd1bWluelI1Q24rTkMzUTlBT0dXcGk2QnZzbEVzVVB1N0JiZkVXMDBEQnp5bzhzK3RWd09jSE8xR3lqcjRuejVwQVZBbzhleloxVnY2SDByOGZ2M0Q3VlRseS94WlRnQkk1dFMyNkYwbmY1ME9FcFdHVUhjNFRoMUlUdmVuZUhkOHNCTC9LNEczcENyRmhhazVoZEtoVS82b3p2Yzg5M0ZSb0tpMGtjWlJQNnlaSk9ROWVIVnlsZmw2VmwwZFR6dStWdWZHVHpCUkZnSFRWY3o4U2thS0llUXNRSk40VUVPY3FaVTJ2ajMzQmY1cyt6WnpNRDU5a2Y0S3ZiU0JpUk1WdEwvUzJ3L1FHaFhxWHBRSjNsMGx1SUlrOVdmQnYxc2pvY1dXcTJyS3JtcWhyQnJaN21PcmttTXNXd3I3TkkyNFMzY2k5NlNPb0NCUG1IRFlRc3V5UE1WaGFSaElZK3d1ZXQ2UGNMTVlvKzc3RTF2dVVSU3FXMk9NcHpIbGU0VW1IbGRQMGowQmNpZnR2eWpkRVlRUE43K1RvT2UzRmhOWGY3bTJxNkg2Q0p1cjBlclI0M1U4cy8yTE1wWWw2QnJnUzdQTlBVckdDVlhaTzdIdEtlUTJqelVxYWRrelZoQ1lSVGhsV3FpYzdjM0U0SFNtRXQiLCJtYWMiOiJjYmZkNDA0MDA5NjkwZTRlZGY5OWU2NDk5ZjllYmJiMjk0NTJlODdkMmMxMDE2MTBlY2FhZWU1NmIxZWY3OWRmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdQaG5yTCtjbEJrWitSOW8zQUk1Nnc9PSIsInZhbHVlIjoiSjJzKzRpeXVGdDNPeXdxRDF6MnNoaXF3K3d3TTEwOEVWZG1KTDZ4Y21RTlc2SXdiU1RWTnRFa1FESndFTUNrTmNpVEdCeEtSZHg2eTFwclBDcUlLeERjc0U1SDNBQXhKSXc1MFEwZEFFTDZNem5SU0IwOFhnclFUQm1hL1YvakU3QmYxSnNZU2tleExXb0EzVmIwd1FGQkVJeUR1MVM1NHYvdE9vUXZ6NFdJdW5GVjNvU3BBa3NpMkZlRlFGa1NXcTg1bjFoTTVxTlR4WHFOMjhmSytUd2UvdVp6QnJYcytra1FKTVcwbWdXNjdtcFdaTjBDOVdGTG5vanR5cmpJUlBONkIwRGxIYVNaZmZhdXJxVEVCTU9nOG9Vd1Jsd2RlZ3FRdzYvQlZzN2l2RzJCQ3FscVlmNkoyM3gwV1dGUFpNdjN5ekJuanZLSkFJM2pQa3NUL2RPMFR5ajZNQnVGV2xhQjc3SFB6OHg5RzN4b0ZJbjlOUy8yZzBHSEVWYkhIQXh3QkppYVVONFN3U1FZYjZubGorZW5ubmNQNnBPVnpJY2YzWHFDZGVQalpKTFRYek0vKzhXYThmTy9zMDFuZVRmbTB4ZVBnL01LMWxybWFzOUsya0VGRjhlaSt5bUFTY0ZKMXZDS1k0QzhMcDVXWWhJLzlZUDBuczZPSFRTLysiLCJtYWMiOiIyOWI2ZTdmODU3Zjc4Mzg0NTk5NTJkNGJhMDUxODNhYjAyYzA0OWNjNDlhYTEwYTRmMmQ2YThkZjUwZTEzNWMzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InhmbFA2bE1TSTZRdFYyaEJzVlFSMGc9PSIsInZhbHVlIjoicVkrS05oRXpUa3JCbnd1bWluelI1Q24rTkMzUTlBT0dXcGk2QnZzbEVzVVB1N0JiZkVXMDBEQnp5bzhzK3RWd09jSE8xR3lqcjRuejVwQVZBbzhleloxVnY2SDByOGZ2M0Q3VlRseS94WlRnQkk1dFMyNkYwbmY1ME9FcFdHVUhjNFRoMUlUdmVuZUhkOHNCTC9LNEczcENyRmhhazVoZEtoVS82b3p2Yzg5M0ZSb0tpMGtjWlJQNnlaSk9ROWVIVnlsZmw2VmwwZFR6dStWdWZHVHpCUkZnSFRWY3o4U2thS0llUXNRSk40VUVPY3FaVTJ2ajMzQmY1cyt6WnpNRDU5a2Y0S3ZiU0JpUk1WdEwvUzJ3L1FHaFhxWHBRSjNsMGx1SUlrOVdmQnYxc2pvY1dXcTJyS3JtcWhyQnJaN21PcmttTXNXd3I3TkkyNFMzY2k5NlNPb0NCUG1IRFlRc3V5UE1WaGFSaElZK3d1ZXQ2UGNMTVlvKzc3RTF2dVVSU3FXMk9NcHpIbGU0VW1IbGRQMGowQmNpZnR2eWpkRVlRUE43K1RvT2UzRmhOWGY3bTJxNkg2Q0p1cjBlclI0M1U4cy8yTE1wWWw2QnJnUzdQTlBVckdDVlhaTzdIdEtlUTJqelVxYWRrelZoQ1lSVGhsV3FpYzdjM0U0SFNtRXQiLCJtYWMiOiJjYmZkNDA0MDA5NjkwZTRlZGY5OWU2NDk5ZjllYmJiMjk0NTJlODdkMmMxMDE2MTBlY2FhZWU1NmIxZWY3OWRmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1336241380\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1570594308 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1570594308\", {\"maxDepth\":0})</script>\n"}}