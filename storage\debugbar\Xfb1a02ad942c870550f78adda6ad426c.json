{"__meta": {"id": "Xfb1a02ad942c870550f78adda6ad426c", "datetime": "2025-06-07 22:38:27", "utime": **********.028109, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749335906.304946, "end": **********.028134, "duration": 0.7231881618499756, "duration_str": "723ms", "measures": [{"label": "Booting", "start": 1749335906.304946, "relative_start": 0, "end": 1749335906.947182, "relative_end": 1749335906.947182, "duration": 0.6422359943389893, "duration_str": "642ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749335906.947197, "relative_start": 0.6422510147094727, "end": **********.028138, "relative_end": 3.814697265625e-06, "duration": 0.08094096183776855, "duration_str": "80.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43915680, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.004960000000000001, "accumulated_duration_str": "4.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.003958, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.492}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.014633, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 86.492, "width_percent": 13.508}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 18\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1752534636 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1752534636\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1492853460 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost:8000/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749335881083%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRnRTFpRHBjT3ozNVRmdElBYmF5U3c9PSIsInZhbHVlIjoiTVEyQ0V1V0xrVENSaVYrN1QyNEI2bUdUTW1yaWtmZGl5M3hPemU1UTVXVEZWeWl1SGdYY1JoNTdFNG56MkRhcmJuaHgvajlEYVJoOS9CZTBmZU5lZzR2dzM1RGZUWXI0dGFJd2hyNmRUU0dsclo1bi9FY1NkVjlQU1AxeGNNa0Q3a2E2MnNjS1ErRmQxMlQyWXlGcjc2RGtPTEE5cjdTV3lMQ2gxcEVzQ2ExNk5kWGF4eExodW92U3hSQUc3dzdZeFBoNDFRb2haampwK2ZVVEZ3My90VVVGZU4rakhBdnpnREU0bEhSRmlpRTlQaHRxaGpicStoL1p6QWtyL2R1NDl5WDc4NTliOHRDU2ZvN2wrK1kzNGV4TXAxMC9KdUhDK243enVIelVBdUFtUVhHZmIwQ0I4bzdEc29DT1Q3L1diejdoK1F3R3NSQXQwQ1ZzY0Mxd2tNbG9USHdsWlZGbkJWNmEvczVnLzh6ZmxTTFordkN0c0prbllYb3dEVHV5UG8wbllLYnJHUmdUdFg0N0wybTRSNDR5Vk05VUVxQzFMMitTMVMxL3RYbjY1RTE3aHJJb09lbjNPUmlzZks0amwwVUZWT1k1aUd4Wk0wQ3FsUjFudmVhWktOS3M5WEFnMXZkTG42b0laRmpKcGVkZ1JEVkZHdXViUnZ5ekJWK0kiLCJtYWMiOiJmYWJiYjRlNDM3YzA3OWI3ZjYyMTMzMjJhNzBmNWYzODllOTVlZGE1ODZkNGQ3Y2YzODE0NTZkMzE2NmUxNmY4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik50L1U2cEgwVkorNzVsczdtd21Rb3c9PSIsInZhbHVlIjoiNDRIS2xXTTE0cDVkZkR2SVRSeFd0T21hZGFsVnFPWkQ2Z3BoSkNjYWE1U0Rrd2p5SGorV3R2ZWZSN1krMm9OOWwrSitRd1JQVWpYY0pzcUh1dmtqempUN1pvZGpLOC9vNWZVY2FnVytreHZRbU4yTDdzMjhWOWU2NDBJc1lyVCtQSEJqamxRMk1qOEk5VmxoekVMOXNEbHZuMkFoNnhCSHBzeFJpL3FuOU16aFVYZlljT0dJNStkMHg4dktOREgzQXBWYU9JSGFab3dUZm91VnRPWnBWZEVHYUhEdUsyQTl2ZlA4WC9sTk5SbEQ1NkRkdEFWVndWZDBRNFFtVDN3WDk5bnF1YkRrMkQ4cjNnNWFPdmpRMDh0OHNBT1k5R0NOcDZ4QzBJTjkzN1NxMHI2VkdXNGNncDBodVFSeENxa0tNejRMRzd2RG0vYUxYVUMwa0NQNUt1Y1h5UUFzeStSMUo5OWNNVVRqUFlmWlZUK0ttK2I5RjlxUVNSckw3NUViT3F4QzlWRGl1eFdUS0w1cWRlRDhML202MkV4Nk5Sd0RjK1BXRXowWWFNTnMvT3VsK211VDlVUDV6THRtQTJYS2J2L1hKcDEzTEhWSGQvZnpNZkNiVWQ4V0lyMnVNRFBjR1NPS09QTm9md1REWkpoMHJjUUpIYjZpdU96a3B2M1IiLCJtYWMiOiJjYzY2ZDM5Y2ZiZTkwNTUyNzYxNzYyZThjOTNmYmY2ZTJiYjExYjNhOTkxYmNmYzQyN2MyZjc1M2NlZTJhYmU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492853460\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1013801279 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1013801279\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-201623914 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:38:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpvZWRjekdybjR0a3ZqUyt0bjJadkE9PSIsInZhbHVlIjoiUng3cFZ6VVdObmVMZEFoVzlNT2U3TnR6QU1sSGwrNGVLSno0VGFocjFOOUNkRkRneU5acHVHY01ZZmpHMnNsN21MRHhNdUdIYyt6dUU3aXJCaVUxZzl2Y1o3eWRiYjhuK2xmNWxFRXBLNElJRDAyTmNFQTloemFGejNLUWg0Y3RnTWtiL3gzZDd3R29iTGVvTG5xSkFEUjkrZWJMNEMvVFN4dkpMUmUxVklsRDN4NHdSL2RyczFLdnVuZ2NBZnY4MjlrK3BtcFpJZTdFb2ppcFYzelJ1akkySXB4OUkrKzJOa3kvdVBJUmJkeXVZWERFdllYRkJSWmlVdXA1akFvUUduQjE0Y1JHQUNOQU5hc3FmR3psdmlYVkh5T0wxM2kxc2Vta0k5TEZrUDBjT3FweG5OR08rTHg3UDdCcHNxLzkwc3kvaFdDbUZmZ0dabTlhZXp4YVlzQ3lIbVlDQWFJRGhPKzRpRlVlRmFMQ1ZxRHZyQ1RCeVVxSXdDaS80MklIYmNyRVFONHY2U3BCUXdqMlpucCswbHFUNXk3QXhXVVpqaUw5WDhSZG9wdWFDUkxDUG9RUlZaaW9wZ2hKd1Y3WjhNcFZ6L29vdFQxVWtoSlFvRWNFWlBuZVdJZk5pcWZCZmZ2ODZBa3pPNjRFQnlISnRQY0tKL0wrbUlpSXc3dzMiLCJtYWMiOiI1MTJkOWRlNTBmNTFlM2U5NDMxMTNkNWUwMGZlYjUwMjE4MzdkZWU4NGQ5ZTBjN2EyN2ZlNTVmNzg2MDYwZWRhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:38:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImtLZmk0aHZPSXFEditjVC9Zc1c5R1E9PSIsInZhbHVlIjoiSmozaFNRcHVZRlAxYXVGOGg3SVVSelk1dld0eGVxS1lHa2tvV3d0eTJZWGlIbnIvV1RQQTZKM3NZQUFaa0NNNjQ5NkllVmF4dzQxZER4dVQrYy8xdnUxaEhhSHkrY0sySVdnV21jMnI1b2ZoQnEvNkhqQXM5eFI3Y1pVNjc1ZnFvUDBIQ2pRTGJjZHNUQ3dVOWxnNjJqTldvZ0RoTTZFYjJoUVpQblhQRWdTSExUQVBaVUw2UFpuU0I2VWZkWGprNUhLSXRvTFBFV2JvdHR4TlNvZm5tQzdYRVJBWVQ5YmlRVnhPQVZTNmtTWFRkMk5EV2g0aTNIeVdBUTQ2WXY3clphellaeURSR2JlRzVNMnNhL2ZDUzVOOEVvanYwTUpoQ1NvQ3hyQ01aNTd5UVhwcjcxc0xIMDBmUmM1TnF2U0hsVUlmNDQ2bUY3ZElwRjdxWEd0c045T0VRUlJqWmRaR3JNK2JlOUJ1eWNqa2x2U3drUGY0WW05UXpjR3p5YW10TlhPZHZ0cHBTeUVZVXFMNTRPeEIybWRXUlFmWmxuUXFVQ2RGeHhpNjlDWkNYVHJ4WUJ0TldlOEZzVU8yUTU0dW83WlpPWE84NUZ4WjVKVWVHREpsOElQS3EvODY2T01lRkpXU2pwNXcwUWVPYWtldFA4UjdscFpkVUVlc2hNMHkiLCJtYWMiOiJhYTk4YWQ3NjUyMWY1NzViZTY2ZGQ4Njk5Njg3NmRkZWYwYTRmM2Y2NTc1NmIzZGRlNmY2MzJmMmNiNjQ5NTZmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:38:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpvZWRjekdybjR0a3ZqUyt0bjJadkE9PSIsInZhbHVlIjoiUng3cFZ6VVdObmVMZEFoVzlNT2U3TnR6QU1sSGwrNGVLSno0VGFocjFOOUNkRkRneU5acHVHY01ZZmpHMnNsN21MRHhNdUdIYyt6dUU3aXJCaVUxZzl2Y1o3eWRiYjhuK2xmNWxFRXBLNElJRDAyTmNFQTloemFGejNLUWg0Y3RnTWtiL3gzZDd3R29iTGVvTG5xSkFEUjkrZWJMNEMvVFN4dkpMUmUxVklsRDN4NHdSL2RyczFLdnVuZ2NBZnY4MjlrK3BtcFpJZTdFb2ppcFYzelJ1akkySXB4OUkrKzJOa3kvdVBJUmJkeXVZWERFdllYRkJSWmlVdXA1akFvUUduQjE0Y1JHQUNOQU5hc3FmR3psdmlYVkh5T0wxM2kxc2Vta0k5TEZrUDBjT3FweG5OR08rTHg3UDdCcHNxLzkwc3kvaFdDbUZmZ0dabTlhZXp4YVlzQ3lIbVlDQWFJRGhPKzRpRlVlRmFMQ1ZxRHZyQ1RCeVVxSXdDaS80MklIYmNyRVFONHY2U3BCUXdqMlpucCswbHFUNXk3QXhXVVpqaUw5WDhSZG9wdWFDUkxDUG9RUlZaaW9wZ2hKd1Y3WjhNcFZ6L29vdFQxVWtoSlFvRWNFWlBuZVdJZk5pcWZCZmZ2ODZBa3pPNjRFQnlISnRQY0tKL0wrbUlpSXc3dzMiLCJtYWMiOiI1MTJkOWRlNTBmNTFlM2U5NDMxMTNkNWUwMGZlYjUwMjE4MzdkZWU4NGQ5ZTBjN2EyN2ZlNTVmNzg2MDYwZWRhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:38:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImtLZmk0aHZPSXFEditjVC9Zc1c5R1E9PSIsInZhbHVlIjoiSmozaFNRcHVZRlAxYXVGOGg3SVVSelk1dld0eGVxS1lHa2tvV3d0eTJZWGlIbnIvV1RQQTZKM3NZQUFaa0NNNjQ5NkllVmF4dzQxZER4dVQrYy8xdnUxaEhhSHkrY0sySVdnV21jMnI1b2ZoQnEvNkhqQXM5eFI3Y1pVNjc1ZnFvUDBIQ2pRTGJjZHNUQ3dVOWxnNjJqTldvZ0RoTTZFYjJoUVpQblhQRWdTSExUQVBaVUw2UFpuU0I2VWZkWGprNUhLSXRvTFBFV2JvdHR4TlNvZm5tQzdYRVJBWVQ5YmlRVnhPQVZTNmtTWFRkMk5EV2g0aTNIeVdBUTQ2WXY3clphellaeURSR2JlRzVNMnNhL2ZDUzVOOEVvanYwTUpoQ1NvQ3hyQ01aNTd5UVhwcjcxc0xIMDBmUmM1TnF2U0hsVUlmNDQ2bUY3ZElwRjdxWEd0c045T0VRUlJqWmRaR3JNK2JlOUJ1eWNqa2x2U3drUGY0WW05UXpjR3p5YW10TlhPZHZ0cHBTeUVZVXFMNTRPeEIybWRXUlFmWmxuUXFVQ2RGeHhpNjlDWkNYVHJ4WUJ0TldlOEZzVU8yUTU0dW83WlpPWE84NUZ4WjVKVWVHREpsOElQS3EvODY2T01lRkpXU2pwNXcwUWVPYWtldFA4UjdscFpkVUVlc2hNMHkiLCJtYWMiOiJhYTk4YWQ3NjUyMWY1NzViZTY2ZGQ4Njk5Njg3NmRkZWYwYTRmM2Y2NTc1NmIzZGRlNmY2MzJmMmNiNjQ5NTZmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:38:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-201623914\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-16752776 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost:8000/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>18</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16752776\", {\"maxDepth\":0})</script>\n"}}