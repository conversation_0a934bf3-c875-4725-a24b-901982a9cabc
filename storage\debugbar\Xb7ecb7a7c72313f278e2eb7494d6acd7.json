{"__meta": {"id": "Xb7ecb7a7c72313f278e2eb7494d6acd7", "datetime": "2025-06-08 00:10:44", "utime": **********.832319, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749341443.782064, "end": **********.832353, "duration": 1.0502891540527344, "duration_str": "1.05s", "measures": [{"label": "Booting", "start": 1749341443.782064, "relative_start": 0, "end": **********.695901, "relative_end": **********.695901, "duration": 0.9138369560241699, "duration_str": "914ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.695919, "relative_start": 0.9138550758361816, "end": **********.832359, "relative_end": 5.9604644775390625e-06, "duration": 0.13644003868103027, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45269856, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02828, "accumulated_duration_str": "28.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.764891, "duration": 0.02705, "duration_str": "27.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.651}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.812752, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 95.651, "width_percent": 4.349}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1371877438 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1371877438\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1476579438 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1476579438\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1933150505 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933150505\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-485803018 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNDR2k2Mm5jUmdFUDBmVWdYRnJaY1E9PSIsInZhbHVlIjoiU2pVb1M5UW5XdExVMFVFZnRpZFZtaGJIc0ErdFB5TG03U1U0R2MwTHpLZjJocjkyVEQzenFUQnM4NGcwcEwxV1YrLyt3M3RiMFhaVVU5S2xVeU40ODVHVFAwWTVNWHo1SDZCbWVwMzR5ZlJ0bmtEQWVUREk5b01HNk1QbHdDWXZEQ0FrSVB1dEVvallURUpKejRSZE0vUWF6Q09ZRkdQendjeUtDSXRpbStaUlVJSGp1dmhQWWk4eHcrYTJXVFpOOEZxQmhHSis3VHhtWlhzYzdzVXNFbEVOWFJQL0p6TTN4SWdxdGZ2LzlDc09BTWx4SlZMNHNLY0ptTzFCZXBSM2kxYVZuN1Zqd0F5MEJBR09jblh2OG5qSkJzQlUwcE5GR2xYaUFIYW9Tb210c2NJYjdVSmFsdjEwSUJCWkFjZ0F2OUxuUHUwVHJMSDB5VHZqVndqaDY5YVFZVUVuLzFsM3QrNCtWSTYydysxbVNJMEMyVi9UdDNSUFRHM21jNUJXYWNtQmdSWEZCK0hESGFZNmFjeGpFTGFiSUpRdkU4QU9aUklDZVRlRUNZNzA2YnhMWmN2eXFpNXlVUmplS1p1OTVwY0R5N1RDc29qdFp4Y2NPL2RrRWhFajB3Q3p2dkI5ZzBWVDVyMk5SZWE3TkFMblJmakxsRWdnOEp0V1BhV0kiLCJtYWMiOiJkODU2MmNkZjYwNDRjMTI3Yzc1YzFiNzc3NmFmYmM0Y2JmYWFjNjA1MDU3OTE1MWVkOWJmMzE0YjAzNjVkZDVjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhyeXJ3TnR3M1dneVhGVEtPa3piR3c9PSIsInZhbHVlIjoiZXhjOENXNytpbmJEa25wY0FiK0ZoVEZBcjBGeVB3OUpGbkRCT2lTNmpkbDBjcGZoRWlTS2xjUTRWVVJBSDk5bmxoM2p0NVZDTWdSZytLaFNaYUJ6SnB2WDV0K2VSNllrcGNkZzdCd0MvMkFLRjhEdGJYbU5FSThjcHdPNUFPS0NGM0NEN0ZYWmFlQ0hUVzRZN0dqTkZ4ZkdBUTFxejdoVjdTWGliVmxCNWxTQStCMUl0TE5YVzBZK1pCdTV6V2tEODZTUlpWK2t2RnJYOWRjYmpWcnBrN1YyK1M3M2xCYU1rd1FvR3c5N25uQkliYy9UNk9oMDBQQXQweTllekt2VGNHUHJXWGNBN1djU1Zxa2JqdWhjWTFVK3NISEIwclpER096UmFPMjBDbEREUkUvRTRKMlBKL2FKcnN3ZGFKME5lSzRWbVVqdGhmYnlRL2NVRlZ6NlRsZndIRlpNR29GUVNMbFhaY3ZGRW5YOEVPbUhYcnc0NGRCMllSTWdxdS9RalUzMEVneHFNbC90ZDZvb0NNZ3ZtMTZRTW1FNkxHTnJZVFZIVjYreDliWjBNN2ZVa093NXBwTGNDNmxoS1Q4VkV0YlRoM1piakovakNMWjNDa1dWMjE3WWNFWG53RjhDU21JZnd1bm1sc1RaSkYyQlBqODN0WDEzTEMrcEZEOGkiLCJtYWMiOiIwM2U2NjBhODI2NzhiYjAwZjBiOGEyOTE1NzIxYjkzNjZjNDBmOWVmNjEwYWZjMzkyNDY4MDAzMGJhODg2Mjc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-485803018\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2071572030 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2071572030\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2095945244 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:10:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNPbFo0ZHpwb2VvQStCcm1LTjg0Qnc9PSIsInZhbHVlIjoiWGNBZUFrK0FockhUR082RDlWeXJuWmhrWWh1U0RKc1dUZitjaTMwdFJUckMyQVJQS1Q4OWRPcEtMbW1CVTB1bzBrTWRndllVTFJvWVdOQ1VLQjhMUmc1Q2lRR3BidzlEdkJwNytUVXJTR3NpTUhwSHBuSkJ6MmtmRmFxZWtsNUttSDJPdVFzbktSM0lpZDJVL2R5L1RRV0dlUnRpcVJXVTVoVnJPenRTL2JXN1BqWndXQk10RGw5bnljZHRjR3lnQ3ZScUVmazA1bUxpSDg1SzN2T29tU3ByKzduVEs3MTZtdXhPU1NwQ3dTNzlUSmJpdVZaSjc0Qm12cEM2cWptcUJaUTZBb1VDd1ZNSHRGKzZYdXFNWU04Qnpuc1JWa3J4dlFKR0IvQVFPanlsQWVqWDNyb1FQWTF0Ti9QWks3NXhhOW9HdnAzemdpRmNFQXhHdzJhUjlhNVBRa3daK2l4aUZzV0VZV0szL1VpUzh1RXAyRVlVekN6aE5wZDFNc3lodnN0MngyWkVxdkVrRUU2TVdDSlIwK05BZVZvZ2djK1FOTnRnZk40ZWxqMHF2Q2dUeGZYbkpPc1ZWbzVYWnpjN25FV1FTNm02bDFTYUhWVUFHNUQvSCsvQmUrY0NUczVDd3dubS9obXM4NUYvRjYwbVRJdllJMEcrRnZYK2NLRXMiLCJtYWMiOiIxMDg5OWRiNjA2NTYxZDYzZDg2ODMxYTIzYThiYWQ4NzA1NzBiZDA0NTgyM2ZmYWZhM2JlNzZmOWE3NjRiYWM1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:10:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlBYUGdqV2k0M1hNaFA2a0VPdGNmcFE9PSIsInZhbHVlIjoialpqRU9WUlBrYVEzRlR2QVpDajZtYTY4aTVSK1NJWjU2MUtZMGpuNHhENVBVOUp4dnpCT1E4bUFhNHNIVVp4N08zeGdvd3B3QmdpcFlqZU1qVkFoT1BXUXJLUld6WXJVNUc0WDBCcUorRGNVUGErelBGYWdta0tmNjA5MWJsb05GNThRSERHSGpCUkFQbURLUHRGSDIrbStQczd3c3ZaNW1vQVpGaVlmZ3lQVm9TTFJSdXBCM1RZQjVnQ0UrQWJVY0pLek9wZTBGSUlVcWZsNVpLWmk1Y3FhbnVqZzd5ZEpxTDhWUkpyMzlkK0VzU3JRYnU3L0gza2o5VXNVRDFWczVKSjFYKzlvcEk4VkVQc0Fjek9ZdGpnVG15MjFRV0J6c1NNcmVDdjMwQXhOK3JMRDVjWlc4NDlBQXZ2UFkxa2RPRVdTalFLQWY2YzNTcEdwQm4raVo1QUlkYm5zZUhOT2p6bTJWYkk1dW91QUptZDN2Szl0ZU1TVlFXTEV0anREQkFIWEpJRi9KbmJUWG1ZL2t1UThHUzZiTDJqOGJyZHdnckNRY21DZzM2Q1dZR2dNZ2tFMS9VcjRlYzQ4dTcvcUJmUmt0V245M2hLV0I0WTdINWNDa20wUGNkL3dYZDFHV25YYzE3UCtlOEpQT2VvREhoeGFDdGt0bExaZ1BYMVMiLCJtYWMiOiI4MzIyZjVhOWZiNzlkODg4YTU3NGRhZWE3YzA0MGUzNmM4ODlhZDQ2ODE4ZTQzYTQ3MGE2M2Q1NzU2MzA5MTM3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:10:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNPbFo0ZHpwb2VvQStCcm1LTjg0Qnc9PSIsInZhbHVlIjoiWGNBZUFrK0FockhUR082RDlWeXJuWmhrWWh1U0RKc1dUZitjaTMwdFJUckMyQVJQS1Q4OWRPcEtMbW1CVTB1bzBrTWRndllVTFJvWVdOQ1VLQjhMUmc1Q2lRR3BidzlEdkJwNytUVXJTR3NpTUhwSHBuSkJ6MmtmRmFxZWtsNUttSDJPdVFzbktSM0lpZDJVL2R5L1RRV0dlUnRpcVJXVTVoVnJPenRTL2JXN1BqWndXQk10RGw5bnljZHRjR3lnQ3ZScUVmazA1bUxpSDg1SzN2T29tU3ByKzduVEs3MTZtdXhPU1NwQ3dTNzlUSmJpdVZaSjc0Qm12cEM2cWptcUJaUTZBb1VDd1ZNSHRGKzZYdXFNWU04Qnpuc1JWa3J4dlFKR0IvQVFPanlsQWVqWDNyb1FQWTF0Ti9QWks3NXhhOW9HdnAzemdpRmNFQXhHdzJhUjlhNVBRa3daK2l4aUZzV0VZV0szL1VpUzh1RXAyRVlVekN6aE5wZDFNc3lodnN0MngyWkVxdkVrRUU2TVdDSlIwK05BZVZvZ2djK1FOTnRnZk40ZWxqMHF2Q2dUeGZYbkpPc1ZWbzVYWnpjN25FV1FTNm02bDFTYUhWVUFHNUQvSCsvQmUrY0NUczVDd3dubS9obXM4NUYvRjYwbVRJdllJMEcrRnZYK2NLRXMiLCJtYWMiOiIxMDg5OWRiNjA2NTYxZDYzZDg2ODMxYTIzYThiYWQ4NzA1NzBiZDA0NTgyM2ZmYWZhM2JlNzZmOWE3NjRiYWM1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:10:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlBYUGdqV2k0M1hNaFA2a0VPdGNmcFE9PSIsInZhbHVlIjoialpqRU9WUlBrYVEzRlR2QVpDajZtYTY4aTVSK1NJWjU2MUtZMGpuNHhENVBVOUp4dnpCT1E4bUFhNHNIVVp4N08zeGdvd3B3QmdpcFlqZU1qVkFoT1BXUXJLUld6WXJVNUc0WDBCcUorRGNVUGErelBGYWdta0tmNjA5MWJsb05GNThRSERHSGpCUkFQbURLUHRGSDIrbStQczd3c3ZaNW1vQVpGaVlmZ3lQVm9TTFJSdXBCM1RZQjVnQ0UrQWJVY0pLek9wZTBGSUlVcWZsNVpLWmk1Y3FhbnVqZzd5ZEpxTDhWUkpyMzlkK0VzU3JRYnU3L0gza2o5VXNVRDFWczVKSjFYKzlvcEk4VkVQc0Fjek9ZdGpnVG15MjFRV0J6c1NNcmVDdjMwQXhOK3JMRDVjWlc4NDlBQXZ2UFkxa2RPRVdTalFLQWY2YzNTcEdwQm4raVo1QUlkYm5zZUhOT2p6bTJWYkk1dW91QUptZDN2Szl0ZU1TVlFXTEV0anREQkFIWEpJRi9KbmJUWG1ZL2t1UThHUzZiTDJqOGJyZHdnckNRY21DZzM2Q1dZR2dNZ2tFMS9VcjRlYzQ4dTcvcUJmUmt0V245M2hLV0I0WTdINWNDa20wUGNkL3dYZDFHV25YYzE3UCtlOEpQT2VvREhoeGFDdGt0bExaZ1BYMVMiLCJtYWMiOiI4MzIyZjVhOWZiNzlkODg4YTU3NGRhZWE3YzA0MGUzNmM4ODlhZDQ2ODE4ZTQzYTQ3MGE2M2Q1NzU2MzA5MTM3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:10:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095945244\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1969287549 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1969287549\", {\"maxDepth\":0})</script>\n"}}