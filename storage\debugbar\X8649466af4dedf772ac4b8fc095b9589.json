{"__meta": {"id": "X8649466af4dedf772ac4b8fc095b9589", "datetime": "2025-06-08 00:10:20", "utime": **********.840498, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.018312, "end": **********.840524, "duration": 0.8222119808197021, "duration_str": "822ms", "measures": [{"label": "Booting", "start": **********.018312, "relative_start": 0, "end": **********.702064, "relative_end": **********.702064, "duration": 0.6837520599365234, "duration_str": "684ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.702076, "relative_start": 0.6837639808654785, "end": **********.840527, "relative_end": 3.0994415283203125e-06, "duration": 0.13845109939575195, "duration_str": "138ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48114848, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.024860000000000004, "accumulated_duration_str": "24.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.751452, "duration": 0.0201, "duration_str": "20.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.853}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7830632, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.853, "width_percent": 2.574}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.805697, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 83.427, "width_percent": 2.655}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.809045, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.082, "width_percent": 3.298}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8209481, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 89.381, "width_percent": 7.562}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.828368, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 96.943, "width_percent": 3.057}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1094370329 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094370329\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.818426, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1173267851 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1173267851\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2041678225 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2041678225\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1133346198 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1133346198\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1684551593 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZxZTFtZmhKMlB2Uk1vUmZEME4wMXc9PSIsInZhbHVlIjoiM0RDeDZNQnNYTkhFcmdBcVlWcTRENFNITFRPT2N5SXhWc3VGTTZsbG1tVVJLTjhYcExrc2p2WWhlWkt3aFlPa0JZcTVScWptV2tramliWEpEMVk4a3dBd01EMk9VdHNuSyt5QXlja0VoM2VnUFJudWpCZmF2Zmtuc2dmSE1PY3ZQbTd2bWc1dmdtbXpjRWduTDJzVHc1U2orSTlNSXRCcnh0SmNXNTJRb0FhakZCbGxHeWI3cTBPK1FPSWhSZDJCYXVLUnY4OFdLY3RKcDFmWTNjd1FOTXI3Sy9MWThwNVYrV0FYUkRmUGVIN0VoU3ZUWDl3OTc5dzBlZGc0dEd1Q2VKZWZadVUvZlpIVjhNekhHZTcxZzZHbjlRclZEbmw0bjBTZ0ZDOHFVMXZQcHBwakFuNENDR3Jna3BTSTJXWCsra0piMC85Y1JXa1dFOElNUmtJMWxKNUh1L2JpcTFFSzI2ZE10OHRtNzFnUTZ4NmNWTFFoVmU4Q2F5S0tIczkxMktmUHdJL2wyNGZRSGtNY2VZTG5jSkpHWXJVblFEWndFU0dVRGVWT0JTWGNRd3dNcHZNbVBFYWhqSTliYUlscmp4bjZPZUhVM1pmT2FBTlpSVTdYVzl3VnZmaXR1a05BUUV5VkdwY2w0SHpmTkRwT3h5RHBrTFR4dFNTWjZTekwiLCJtYWMiOiJmMTBjZTc0MGY2ODkxMDRkOWVkZjdlNWM3ODJiMGVhZjk5NDIyZmY2YjRhMTYyZmJiZDExODMxYzBiYTYwNWI0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Inp2aW9Zb3ArWVFFRloraE9QT0Z0d2c9PSIsInZhbHVlIjoiVEM5M1UyTmF6WFJVUkY1Tm1KY2J5V1BCblR6WDJiOE90cHEvZXNGVHZPSEdqSjkwUEY3bVBjck1YK3grRkZsMytycHdaL3oyWWhpN3ZOVzBOViswbkp3Ry8rUTQ4amptMlh5Yy9YWWlCUlhjbEdwNmdzL1ovQ21lZlo1bFdGZHhiYS9rWERDbkF3TWxVdUlYZzAzUklkRDdFVUp3RVVlcW5FdTJkYkNZYUNCMjgvR2I2a0xaaXdFbmNUZzI4QnNiYUMraWJ5eE1LZUtUbXN3Q2h1MkpuWTFjMmFCSnJ1NnIxOWdBaFdhYUNxWG9laTRTNWxxVDlFZEs5eHF6TmVXcHdhSXlZMi9PMUtIVHJ2dWRmU21nN1gzcU1OYjhwSFRLQ1J6cG9kWnArMVJXRy9iTVpaUk1rSHpUdDMxRmFhcUhFUjNkUWRMaDZIU3NTWWNjNUpVaGp2OVk4OSt1MVNucldiWU45WVR4T2VTUzN6TVFyUlN4VHVQWlB4SnQvZkp4NjR2QlNNd0ZRQTRpY29Jcm01b2hFbGNMK001djNrcVZCNmpQakJ0a2c0SzZQZjZSQmR3Qjl6ZHRJMTlRRDh4UzNWYXN0elNocUczbXRSNmxxdVZDOEZvODVCc2xZTFlxM1p2YzMrSXlWNVdMSk9TOTRRcmxHNVc1c1RPMW5BRU4iLCJtYWMiOiI2YjdlOTA0OGE0MjVmZDM2OGFkM2ExZDdjOTAzOWNiOGI3OTE5M2U4NmI1NzAzOGQwYTg4MmM0MzEzNTc2MTlmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1684551593\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1478204810 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478204810\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1655410247 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:10:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNDR2k2Mm5jUmdFUDBmVWdYRnJaY1E9PSIsInZhbHVlIjoiU2pVb1M5UW5XdExVMFVFZnRpZFZtaGJIc0ErdFB5TG03U1U0R2MwTHpLZjJocjkyVEQzenFUQnM4NGcwcEwxV1YrLyt3M3RiMFhaVVU5S2xVeU40ODVHVFAwWTVNWHo1SDZCbWVwMzR5ZlJ0bmtEQWVUREk5b01HNk1QbHdDWXZEQ0FrSVB1dEVvallURUpKejRSZE0vUWF6Q09ZRkdQendjeUtDSXRpbStaUlVJSGp1dmhQWWk4eHcrYTJXVFpOOEZxQmhHSis3VHhtWlhzYzdzVXNFbEVOWFJQL0p6TTN4SWdxdGZ2LzlDc09BTWx4SlZMNHNLY0ptTzFCZXBSM2kxYVZuN1Zqd0F5MEJBR09jblh2OG5qSkJzQlUwcE5GR2xYaUFIYW9Tb210c2NJYjdVSmFsdjEwSUJCWkFjZ0F2OUxuUHUwVHJMSDB5VHZqVndqaDY5YVFZVUVuLzFsM3QrNCtWSTYydysxbVNJMEMyVi9UdDNSUFRHM21jNUJXYWNtQmdSWEZCK0hESGFZNmFjeGpFTGFiSUpRdkU4QU9aUklDZVRlRUNZNzA2YnhMWmN2eXFpNXlVUmplS1p1OTVwY0R5N1RDc29qdFp4Y2NPL2RrRWhFajB3Q3p2dkI5ZzBWVDVyMk5SZWE3TkFMblJmakxsRWdnOEp0V1BhV0kiLCJtYWMiOiJkODU2MmNkZjYwNDRjMTI3Yzc1YzFiNzc3NmFmYmM0Y2JmYWFjNjA1MDU3OTE1MWVkOWJmMzE0YjAzNjVkZDVjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:10:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkhyeXJ3TnR3M1dneVhGVEtPa3piR3c9PSIsInZhbHVlIjoiZXhjOENXNytpbmJEa25wY0FiK0ZoVEZBcjBGeVB3OUpGbkRCT2lTNmpkbDBjcGZoRWlTS2xjUTRWVVJBSDk5bmxoM2p0NVZDTWdSZytLaFNaYUJ6SnB2WDV0K2VSNllrcGNkZzdCd0MvMkFLRjhEdGJYbU5FSThjcHdPNUFPS0NGM0NEN0ZYWmFlQ0hUVzRZN0dqTkZ4ZkdBUTFxejdoVjdTWGliVmxCNWxTQStCMUl0TE5YVzBZK1pCdTV6V2tEODZTUlpWK2t2RnJYOWRjYmpWcnBrN1YyK1M3M2xCYU1rd1FvR3c5N25uQkliYy9UNk9oMDBQQXQweTllekt2VGNHUHJXWGNBN1djU1Zxa2JqdWhjWTFVK3NISEIwclpER096UmFPMjBDbEREUkUvRTRKMlBKL2FKcnN3ZGFKME5lSzRWbVVqdGhmYnlRL2NVRlZ6NlRsZndIRlpNR29GUVNMbFhaY3ZGRW5YOEVPbUhYcnc0NGRCMllSTWdxdS9RalUzMEVneHFNbC90ZDZvb0NNZ3ZtMTZRTW1FNkxHTnJZVFZIVjYreDliWjBNN2ZVa093NXBwTGNDNmxoS1Q4VkV0YlRoM1piakovakNMWjNDa1dWMjE3WWNFWG53RjhDU21JZnd1bm1sc1RaSkYyQlBqODN0WDEzTEMrcEZEOGkiLCJtYWMiOiIwM2U2NjBhODI2NzhiYjAwZjBiOGEyOTE1NzIxYjkzNjZjNDBmOWVmNjEwYWZjMzkyNDY4MDAzMGJhODg2Mjc1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:10:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNDR2k2Mm5jUmdFUDBmVWdYRnJaY1E9PSIsInZhbHVlIjoiU2pVb1M5UW5XdExVMFVFZnRpZFZtaGJIc0ErdFB5TG03U1U0R2MwTHpLZjJocjkyVEQzenFUQnM4NGcwcEwxV1YrLyt3M3RiMFhaVVU5S2xVeU40ODVHVFAwWTVNWHo1SDZCbWVwMzR5ZlJ0bmtEQWVUREk5b01HNk1QbHdDWXZEQ0FrSVB1dEVvallURUpKejRSZE0vUWF6Q09ZRkdQendjeUtDSXRpbStaUlVJSGp1dmhQWWk4eHcrYTJXVFpOOEZxQmhHSis3VHhtWlhzYzdzVXNFbEVOWFJQL0p6TTN4SWdxdGZ2LzlDc09BTWx4SlZMNHNLY0ptTzFCZXBSM2kxYVZuN1Zqd0F5MEJBR09jblh2OG5qSkJzQlUwcE5GR2xYaUFIYW9Tb210c2NJYjdVSmFsdjEwSUJCWkFjZ0F2OUxuUHUwVHJMSDB5VHZqVndqaDY5YVFZVUVuLzFsM3QrNCtWSTYydysxbVNJMEMyVi9UdDNSUFRHM21jNUJXYWNtQmdSWEZCK0hESGFZNmFjeGpFTGFiSUpRdkU4QU9aUklDZVRlRUNZNzA2YnhMWmN2eXFpNXlVUmplS1p1OTVwY0R5N1RDc29qdFp4Y2NPL2RrRWhFajB3Q3p2dkI5ZzBWVDVyMk5SZWE3TkFMblJmakxsRWdnOEp0V1BhV0kiLCJtYWMiOiJkODU2MmNkZjYwNDRjMTI3Yzc1YzFiNzc3NmFmYmM0Y2JmYWFjNjA1MDU3OTE1MWVkOWJmMzE0YjAzNjVkZDVjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:10:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkhyeXJ3TnR3M1dneVhGVEtPa3piR3c9PSIsInZhbHVlIjoiZXhjOENXNytpbmJEa25wY0FiK0ZoVEZBcjBGeVB3OUpGbkRCT2lTNmpkbDBjcGZoRWlTS2xjUTRWVVJBSDk5bmxoM2p0NVZDTWdSZytLaFNaYUJ6SnB2WDV0K2VSNllrcGNkZzdCd0MvMkFLRjhEdGJYbU5FSThjcHdPNUFPS0NGM0NEN0ZYWmFlQ0hUVzRZN0dqTkZ4ZkdBUTFxejdoVjdTWGliVmxCNWxTQStCMUl0TE5YVzBZK1pCdTV6V2tEODZTUlpWK2t2RnJYOWRjYmpWcnBrN1YyK1M3M2xCYU1rd1FvR3c5N25uQkliYy9UNk9oMDBQQXQweTllekt2VGNHUHJXWGNBN1djU1Zxa2JqdWhjWTFVK3NISEIwclpER096UmFPMjBDbEREUkUvRTRKMlBKL2FKcnN3ZGFKME5lSzRWbVVqdGhmYnlRL2NVRlZ6NlRsZndIRlpNR29GUVNMbFhaY3ZGRW5YOEVPbUhYcnc0NGRCMllSTWdxdS9RalUzMEVneHFNbC90ZDZvb0NNZ3ZtMTZRTW1FNkxHTnJZVFZIVjYreDliWjBNN2ZVa093NXBwTGNDNmxoS1Q4VkV0YlRoM1piakovakNMWjNDa1dWMjE3WWNFWG53RjhDU21JZnd1bm1sc1RaSkYyQlBqODN0WDEzTEMrcEZEOGkiLCJtYWMiOiIwM2U2NjBhODI2NzhiYjAwZjBiOGEyOTE1NzIxYjkzNjZjNDBmOWVmNjEwYWZjMzkyNDY4MDAzMGJhODg2Mjc1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:10:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1655410247\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1392724605 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1392724605\", {\"maxDepth\":0})</script>\n"}}