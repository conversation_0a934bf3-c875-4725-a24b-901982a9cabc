{"__meta": {"id": "Xc9e6079dc90006977ebaf5d0a7df7bd7", "datetime": "2025-06-08 16:24:24", "utime": **********.985078, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.407728, "end": **********.9851, "duration": 0.5773720741271973, "duration_str": "577ms", "measures": [{"label": "Booting", "start": **********.407728, "relative_start": 0, "end": **********.905979, "relative_end": **********.905979, "duration": 0.49825096130371094, "duration_str": "498ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.905997, "relative_start": 0.49826908111572266, "end": **********.985102, "relative_end": 1.9073486328125e-06, "duration": 0.07910490036010742, "duration_str": "79.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45154312, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.005180000000000001, "accumulated_duration_str": "5.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.947138, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 63.9}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.963879, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 63.9, "width_percent": 13.127}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.973453, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 77.027, "width_percent": 22.973}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-462240196 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-462240196\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-408542476 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-408542476\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2061650365 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2061650365\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=1y4k55%7C1749399478878%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhkSStFZDNGc1dhYTVZTEhZdmFMd0E9PSIsInZhbHVlIjoic1Q4SHJrVkRqd3NZRzlLSDgrdWY0RDcwOXJMZjl4Ylp0cFZDWG01K1htUFZEN1cxdGRsNHA1dS9WM1RPZWtldHdqV3kvdmxUT2NGUXBuWml3STlQYXFVdk5BWVg4VTNaRlFTdEFZd1VTb2FFRHhzNlJ2N2pZWlpLbnNRQmtQNjNOTG13QTgwLy81VGJnQ1pTbVZBSkFKU2wyanIzWEJDMTlMdUlLQ1hMdXkzQlhaQ2h2aEV3NGkwMzhCbWdsanZZWVkxL3RiQ01MZ01zWlFXbmJ6RmJraEJDeFpaUC9UZ1JSaGxkRU1ucFFZbGc0Z0lENzdCSmxWbGJFdzBYR1FrTjNTbThIWnRGcGNFYzNBbUtUM21sb2lMSXJQbzZRQkdXRktRWnRtVGkzWm9jZVp2dTNqdzhCeGEvZFhtcjVwNGFCSDI0QnFHcW4vVzNRKzNlbHFWS3JsRjlPc3lEbTdVV0dCYUlwNFAydnljcmk5TTUrS2E2Z2diSEdyK0dHRTRLVjY3a2l6NkFJNjFzWjR4WGdKSVRsRnczWjlocXE1U0svbSs3N05ma0NVNVB1SkRFZDdNVlkzT0pLOUlkUFBMTDkwRTR4RXVwVTIwUDBJaFRUOHdMTm9vWjNpRStiWDhjZXFwcm02dTZxMUpyZTBaOE0xNFVtazBlQnpHRmpxbmciLCJtYWMiOiIzM2IwOWY0NDE3ZmQ5MzBmMTE0YjY1MTVmNDQ3YjZhNzMyNzI2MDRkZjljOWFiZWJkNmEyMWJjNGEyMThlZGYwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImFqWEVHZmJOb3o2bnQ4MWpFc0haOFE9PSIsInZhbHVlIjoiNmdOTTRBSjAwd2cyZVNxRCtydU1aN1cwajdRTFZyNDI1LzREZlNLcTRoNjgxcVMyZWFsdDFQSHlVdkhvV210cUN5b1lVUWc4UnE5clBjNjdlbUtGdUIybU9NTVE0elBKaldMSForT0o2Y0I2a1I5MVFER1RpUG9FZW9reXRtMldwR1FxQ1Y1NXZCN0ZHaXMyWlhnOHNyQVFnUWRVaEdNVUptRWJzdzErK0Y1SFVkK1VJcW5vUnZTS3cwSU92dmR6azNCcDllZzRuT3ErUmFyd0tTWE1Ma1RmcE5PM01EM0MxYXJmbHNoTDNqTVJDR1A3OXdyS0gxamk2RmttZ2xNYVYrVEdROWlxR0dHSDkvUjlVUjAxOVowZEpUYmduby92MTBkc082UTFNY2hqdnQvWkMzVFdtMlhXVWc3TGpiS2VvUWJzTHFZS3VhYmNxeXZCNXZXTEwrT3VoSG5EK2xrUWdxaWVxcjR6YlNDbzV6TExhTEJIVGZFancvYnkwR2NybmtLMnJOKzlJT3VKait2UVpvcmdjeTlxbkNja3Z6NENtL0Z2QldoWlFaaWdNc2hvamNRVWo5ZzdKb1QwYURzNTVmbmhzeUlPVHBSeUJYZjdCcXFsVFhrcHR6K1BRWWVwdWtuOEUvK0EwcTgyN1VHNVN0Tk9GYXRVLzN4S0tjSHUiLCJtYWMiOiJjNDc0ZDkwYjA0YWE1ZDI4YzFjOGZkZmIxM2I1YjI2OTg0ZjQ5OWE0NDZlNGY4MjdiZGZiOGYxY2JjMTYyYzVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-11492478 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:24:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1pZThQN1lZODJVWlNyS1VoRzdWWEE9PSIsInZhbHVlIjoiUU5UR2lFeVJ4YVl2amNMdlQrdXZicVlJS0NOUFlmcFd4UW9EcnNSQ3U2a3dqMjZPbWFJQVg2bE53Q2h1NWtCdERjaDZHaUt4bmkzdjRyVklGcUVhM051NXcrdEkwMTFCQXVDVmhGZTVHcjR1V3MwTHIvYjRYR1FxdWFoV0FkS3dETW1RMU41WEJpc2VFbzgzYXg4RzhqdG5WSDJRSUo0UVVRWUtpRFd3Wk54bkF3M256czdDZUcxc1dYQkFZSGx2NjdiRUw4K016djZLMCt0a0JUVUFjdWE2Tk1GRGNzMC9CcmpIZkVEYU5yRVlyZHZhQzlac3E0alZTTEY1MlVIT1hFK1l0VmpMZU0yQlN1ckhGSHJaZVR2a0lNZno5SG5xWWFDWUdPNEFCdE1VMCtXdG4xVEphQjI2UThjSytseDNxbmZRdzdETWFuT1NUenorN2JYRUVnWjRxNkh3OEdTZzExRzRKMG5NbmE1d3VINWtHVkQ3WTV5S3NwRnI3TDlIbmkyWUhiUDA0dG4wamhpbFk4c2pnODVmZ0ZvMS9YSFRoalRxZEFrS1VvTTFVQUFOSi9NVzVuZkV0WTI5bCt3V2NzUkRESWRZRTdleXMzMlgrZk9tWDB0NVZkMERyMmdmQnFvbUpmY2EyQUl2ZFpHckVrZFF6NjdUR0g2VWpRamIiLCJtYWMiOiI4OTY4NjMwYWFhOGFiY2IyYzZlOGY4MzliZmMxMWFjODBlZTMyNjZkZDY5MmNmMjg3YzQ4N2M3NTFjYWRkMGRjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRHZ1BIa1VsMldDb3F5SHlkZFRpbVE9PSIsInZhbHVlIjoiRkRjYUxTd0JxWVlyUnZvcFVtS1NBZDgzdmV2ZTQ4MTJLMW1TZXlRRW1jOWZpTEFrRVVlcEZUbFZhM0owdW1hdGplZXVXTVV0NWxkeTU1MnVyNEk1ODFLa2d1aTBWTm1lei8wckVDMTVHTElFUnZ4Sm0rOTMyVE16dUVPRmhvSVdBM0xHT09YZjkvS3g3QllhSkR4Vmw0OWFiSlJSNmtycGRIelBQVnJJQVl3QUROYVlmemxpUDVXenJvaFNxT1kxa0xHTGNNVHFRZlhPNDV4U1JERDZ0YllMK29CRy8zZVhacCtuZnVMKzVXV1dGOXU0TVNmL2VjN05QWjRUZzlFdHVGS05SUlV2eWJ4bjhpM3haWFlaaWJYZnhJYVdZblhvWnR4SkhEaW94d3UzS1JsOHJ4eGovR1FIdDRPMGVDekp4Q1k0WTJsZ0YreDFBa3Q4T1AwRHI3aDJ2VmpGQlQwSTZwc05NSTdkYnNjZk1RRUNtOG5uTnNZc2wzaW1rR1l0VlRvWkJqdHpLS1FhTmNLZkpHc0VDUHBveVUwOWkxVXh6WSt3OW5Ram5hYlhZZk1uYmJKRE1CV2FCcnVBYWpnRlZ2U2dPb2E1VGpWOXU0U1RILy9Rdm93T1p4RGRETHh1RnNVZlgyYnJybkdkNWo3WnhRakhqSHdpTEtIcVUvazkiLCJtYWMiOiJjZWM4Mjg4OGVmMWQ3NDRlM2M3ZjMxYjk3YTU5YjRmOGE3ODQ3MmNjNjU2Zjg1NTRmNjUwMzJkODk0Mjc0M2MyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1pZThQN1lZODJVWlNyS1VoRzdWWEE9PSIsInZhbHVlIjoiUU5UR2lFeVJ4YVl2amNMdlQrdXZicVlJS0NOUFlmcFd4UW9EcnNSQ3U2a3dqMjZPbWFJQVg2bE53Q2h1NWtCdERjaDZHaUt4bmkzdjRyVklGcUVhM051NXcrdEkwMTFCQXVDVmhGZTVHcjR1V3MwTHIvYjRYR1FxdWFoV0FkS3dETW1RMU41WEJpc2VFbzgzYXg4RzhqdG5WSDJRSUo0UVVRWUtpRFd3Wk54bkF3M256czdDZUcxc1dYQkFZSGx2NjdiRUw4K016djZLMCt0a0JUVUFjdWE2Tk1GRGNzMC9CcmpIZkVEYU5yRVlyZHZhQzlac3E0alZTTEY1MlVIT1hFK1l0VmpMZU0yQlN1ckhGSHJaZVR2a0lNZno5SG5xWWFDWUdPNEFCdE1VMCtXdG4xVEphQjI2UThjSytseDNxbmZRdzdETWFuT1NUenorN2JYRUVnWjRxNkh3OEdTZzExRzRKMG5NbmE1d3VINWtHVkQ3WTV5S3NwRnI3TDlIbmkyWUhiUDA0dG4wamhpbFk4c2pnODVmZ0ZvMS9YSFRoalRxZEFrS1VvTTFVQUFOSi9NVzVuZkV0WTI5bCt3V2NzUkRESWRZRTdleXMzMlgrZk9tWDB0NVZkMERyMmdmQnFvbUpmY2EyQUl2ZFpHckVrZFF6NjdUR0g2VWpRamIiLCJtYWMiOiI4OTY4NjMwYWFhOGFiY2IyYzZlOGY4MzliZmMxMWFjODBlZTMyNjZkZDY5MmNmMjg3YzQ4N2M3NTFjYWRkMGRjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRHZ1BIa1VsMldDb3F5SHlkZFRpbVE9PSIsInZhbHVlIjoiRkRjYUxTd0JxWVlyUnZvcFVtS1NBZDgzdmV2ZTQ4MTJLMW1TZXlRRW1jOWZpTEFrRVVlcEZUbFZhM0owdW1hdGplZXVXTVV0NWxkeTU1MnVyNEk1ODFLa2d1aTBWTm1lei8wckVDMTVHTElFUnZ4Sm0rOTMyVE16dUVPRmhvSVdBM0xHT09YZjkvS3g3QllhSkR4Vmw0OWFiSlJSNmtycGRIelBQVnJJQVl3QUROYVlmemxpUDVXenJvaFNxT1kxa0xHTGNNVHFRZlhPNDV4U1JERDZ0YllMK29CRy8zZVhacCtuZnVMKzVXV1dGOXU0TVNmL2VjN05QWjRUZzlFdHVGS05SUlV2eWJ4bjhpM3haWFlaaWJYZnhJYVdZblhvWnR4SkhEaW94d3UzS1JsOHJ4eGovR1FIdDRPMGVDekp4Q1k0WTJsZ0YreDFBa3Q4T1AwRHI3aDJ2VmpGQlQwSTZwc05NSTdkYnNjZk1RRUNtOG5uTnNZc2wzaW1rR1l0VlRvWkJqdHpLS1FhTmNLZkpHc0VDUHBveVUwOWkxVXh6WSt3OW5Ram5hYlhZZk1uYmJKRE1CV2FCcnVBYWpnRlZ2U2dPb2E1VGpWOXU0U1RILy9Rdm93T1p4RGRETHh1RnNVZlgyYnJybkdkNWo3WnhRakhqSHdpTEtIcVUvazkiLCJtYWMiOiJjZWM4Mjg4OGVmMWQ3NDRlM2M3ZjMxYjk3YTU5YjRmOGE3ODQ3MmNjNjU2Zjg1NTRmNjUwMzJkODk0Mjc0M2MyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11492478\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-509545785 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-509545785\", {\"maxDepth\":0})</script>\n"}}