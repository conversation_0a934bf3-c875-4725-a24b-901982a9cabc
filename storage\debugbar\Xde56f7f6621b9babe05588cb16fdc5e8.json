{"__meta": {"id": "Xde56f7f6621b9babe05588cb16fdc5e8", "datetime": "2025-06-07 23:56:05", "utime": **********.760963, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.167725, "end": **********.760983, "duration": 0.5932579040527344, "duration_str": "593ms", "measures": [{"label": "Booting", "start": **********.167725, "relative_start": 0, "end": **********.667047, "relative_end": **********.667047, "duration": 0.49932193756103516, "duration_str": "499ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.667064, "relative_start": 0.49933886528015137, "end": **********.760986, "relative_end": 3.0994415283203125e-06, "duration": 0.09392213821411133, "duration_str": "93.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45046240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01602, "accumulated_duration_str": "16.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.711529, "duration": 0.01402, "duration_str": "14.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.516}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.737992, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.516, "width_percent": 5.181}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7478962, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.697, "width_percent": 7.303}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1004582326 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1004582326\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-556452117 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-556452117\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1455478837 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1455478837\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1597818877 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749340523746%7C19%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Img1WFFmL1dGUVJrcUdMcUlhaGdad3c9PSIsInZhbHVlIjoiWG8vbFZxVU9mNEN0QXpMZXM0aUNnR3E4NGo1WEZTYXJmVTFTSzJYeWRYQ05kUHFWT3l6a1VMVGU4RUF5NDkxTVhweFk1V0hXWWJPR0Y3UUhIdGtkTmZVMktBVWtHZldrVGlJczBRZzB2cGxQUmIzNEhndm5TSjlaNmxqME1oM09PSUtQMW1OYmFVSTdmQzY1TlNGRXVRSEJYU0ZCb1VBeTBzMFZMMlZJemFjRlJhQ0FaUDhTQk11UmVmOHppV09WT0RaYTZJN2FRMk5neHhlN1c4YjY5RnVhaUh2ZEpVbFhIdE40UTZmcWFDT2lnbGFhZ0FCVU9lVHN4YVl5anI3VlVvRjJvc2pXRGpVUmdCcmxUUkd0elNYWlkzK0VBUm1kYUVtY3ljNnNNZDRvUkdlTm9xKytNTlV2MWRTVHRMOGV5OGlFOFMreHRCeHh1YXRLNlFCdDdJUmFtWGJhNHR6VGxPeUxOUnVLN2lCUGhvcEdFK29NRGVrOS8wM1JLT3orQnYrbDN3YWI2bEtEdVJpc09LYTdPR1RwdjRXejlocWduOG56T3lmc0JPQzFobVVEdG9MVkRmM3NDMUlGYXVaWmJ4VlZWMVphZXpwclpNYXlLK2xGOHhpY3VMOXZSYW5kYVh5OGVtb3JlZnpOTzFjWW51dllHYzVXYnpOYTlkaWwiLCJtYWMiOiJkODRjODRlZDYyNmI0NDZjOThkNWMyYTUxZWNkNTVmZjMyMDZiYzhlOWM5Yjg2ZDg5ODBiOWM2NzJjMWI1ZDBkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IllwbldnZlpBQXU0NHc3N2tIcWVlZlE9PSIsInZhbHVlIjoiU1ZibVhORnNDL3BIQXF5MVE1RWNOSVRndmI0YlpVRUtWZGRUSjkrZmpKQmlWOTR1aTl5alJ3RHEwdjBEL0JOcWozUjNtanRJYjkxNys2K1Yyelh3MVdWMGU5NGtPOTZOL0poaDJhdzgxdG1veW1PbGxVbVRuSGJRQjY3SitpditZOWt3TGYvdmVGL3dlR2szQTcxVENGWS9xb3o1bU10cThvT0RxcmR0Q1dKRGNBNW1tQkxXUTg3MHJaVUlOWlNQTEpGdjZFQWs5L2pMUCtuTFlQcDNMdWttdnczRDkwNHlBZWVZNVJad3FPT0ZqZnBxeG1GanE3SDZTVjUwaGRuWFVMdlY2UmhYTlRKUm5RcVFXRWVpMnBjYVhEMEVOWGpRMzNuZ2dtdjNaV0JnTXNtaUFnTFJNU0hqdVIrNm9Sd2E0TGtJdjBkejJyRFd5NHVQeERjZDFYbHJFcGM5aVoxbVJ0OXhzVmZXZTFSM0w2NGtIUS91TlovVWpoL2FYdTlyeDhPQ3pIbXNwaVlpM0JlcHlzUTJmejlBbktlWXJ0TmRMeWVoRkpFODhpbmloaGI2SHRrWTdzYUxSaHoxQXZlNHBBemlmK0liRlFNbFJhb0VzZ3REVEZpT0VHSSsxQ0F4dWpLT0Z2TEJCbXZIRlBXVWE2OUJxVkNSRTJrWTlIMTgiLCJtYWMiOiI3NmNkNGYzN2NkZmNjMTRmN2Y0MGY2M2ViOTQwYjdlOWNkYWMxMzhjM2U0ZDQ3MTVkMzFmMzAzNThjZGRjNDA2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1597818877\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2045331718 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045331718\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-574217391 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:56:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtXcEpZcWZ4KzdscTRqK3pFbmJPMFE9PSIsInZhbHVlIjoiNUI3MVVsdDBPR3pLK0NncGFQVXVyT2pRVjdqSHYyZ0o3eWQ0Y0ZlbGV2T09PTi9MRnpYWDQ1RlhDcC82UjZqNm5ld3lEdnJ6K2orMHhuYlcwNDhiT0l1VFlabWV1Q1Y2RTNrMzdzVG9ST3dIS2FHQ053WTczRW4zQUpIcTNaZEUva1ZibHZHb1U5azJrTEplMGNOTUZ0SGExeUdZUkdPSU43dFBGY2hXSGllMEpkL2xHSFJRNmxwaUo2b1ptQmxLemZKWDFvcUV6TkFkamIrbUljMjBWMmVRM204c0o0MmhDdXlxWWlTWmFBYk0zaEJYNFhoc1o2LzBkL3dsNVpNZDdqdGJpaklhL0tqRzg0SFdxSm9PYWxqazhrR0lIOXM4cEZuT3g4SzhlbGQ5WFp5M2hEM3ExWE9abXdTU2JPZVIzRzlXUEptUkllNWhsWVdDQVNYOGRGSTdIL3hBSzBhaWVvNmFSSG1uS3dvaDVBQXI2c2hnZng2dXRVS3RJeE5Ub0J3UVFGTGpTN1R6bEpBWFVCcDhQMjZraU1EM1VWck42cS9VRHYwVlR1L25SdENkZjJhdUJON1ZnSUJ5Tm1TdlIveWNQL3M2eHpySHZ1eUREdnhuaWZwNmFBQVBVaWV0bkJST0pWZmRzMnBzWXoycHFwV09UcU53QVI0Nm1uU1UiLCJtYWMiOiIxYWRmOGQyNzBhYWIyOWY5OGI0MjhiOGFkMWQwYmY4MGZhNTI2OWU0NTRmYmIwNDA4ZGE0MGE0YjI5YzlhMDZkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:56:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InNtS2p6SVFTWlJGeS9DdFVnS1NWMGc9PSIsInZhbHVlIjoibEt2WGcrRE9hMlJ1d2U3S255MmxReTgzakNobmc1Tkp2SkFpUWdjV1BDTmVVY25JWndNTjgrZlFiWm9BN1RsYzMwV01yNVpIVGZ3NzhYYjNiWXVLSDZFS0VNa0Q0VllmRVl1ZFV4ZnhVaHJqYXJnRkxJSit6bm5BQTFXdmplOURHV09JWmZNNEx3clA1dzY0K2JIbzEreU9SRzN0Z0tkc2dhU2dZSHllZno5WWdsdEEvek51cjMyNFYxZnZjNnN6b2w1Y3ZjWTlBUFlyZEpyMVo3c003eTVDMkh6RURaQkMxOVZ4Wkc3M1J2cnExRklhakJXMUhaN1BGVEN5VEJyQVdZQ3FISERpeVNJbkFrb3BtTkgreDNZMkplbzQyN0ppMFRhUXlTWDJ4aEhjRDl1VHIwUDVWcEhGTUdsaU5pV3psaHViL3VZa0NmVjA5YzQ0SW9Zdmw4RG8wWnV5ZFBGSnp0T0ZFSk1JL2FtUzdoYWYxS1E3MzdKb1YraE1ybHZGaG9ZZ0pvaGt4Z2luT0wzckJ3SnJCYnFWMk9wYlZzUmMxQXJsYTRHb0p5dlFGNHFmV2hEZmpoR0JPZHAyeUZ4blVRU05VN1hHLzJtZ0hKSjdwQTVSYng4Z0V5ZlNvRlVLNUFXT1hXYzNPdXdpWWY0Q3JYSUlRbnRibmJXdGQ3b28iLCJtYWMiOiIzNTQyM2U3NzNlNTVjZTExZmYwOGIzNGMzNzBhZmMzNTlmZWUwN2I5OGZmMzIxY2UxZDVjOGI5NDE1NDlkNmRhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:56:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtXcEpZcWZ4KzdscTRqK3pFbmJPMFE9PSIsInZhbHVlIjoiNUI3MVVsdDBPR3pLK0NncGFQVXVyT2pRVjdqSHYyZ0o3eWQ0Y0ZlbGV2T09PTi9MRnpYWDQ1RlhDcC82UjZqNm5ld3lEdnJ6K2orMHhuYlcwNDhiT0l1VFlabWV1Q1Y2RTNrMzdzVG9ST3dIS2FHQ053WTczRW4zQUpIcTNaZEUva1ZibHZHb1U5azJrTEplMGNOTUZ0SGExeUdZUkdPSU43dFBGY2hXSGllMEpkL2xHSFJRNmxwaUo2b1ptQmxLemZKWDFvcUV6TkFkamIrbUljMjBWMmVRM204c0o0MmhDdXlxWWlTWmFBYk0zaEJYNFhoc1o2LzBkL3dsNVpNZDdqdGJpaklhL0tqRzg0SFdxSm9PYWxqazhrR0lIOXM4cEZuT3g4SzhlbGQ5WFp5M2hEM3ExWE9abXdTU2JPZVIzRzlXUEptUkllNWhsWVdDQVNYOGRGSTdIL3hBSzBhaWVvNmFSSG1uS3dvaDVBQXI2c2hnZng2dXRVS3RJeE5Ub0J3UVFGTGpTN1R6bEpBWFVCcDhQMjZraU1EM1VWck42cS9VRHYwVlR1L25SdENkZjJhdUJON1ZnSUJ5Tm1TdlIveWNQL3M2eHpySHZ1eUREdnhuaWZwNmFBQVBVaWV0bkJST0pWZmRzMnBzWXoycHFwV09UcU53QVI0Nm1uU1UiLCJtYWMiOiIxYWRmOGQyNzBhYWIyOWY5OGI0MjhiOGFkMWQwYmY4MGZhNTI2OWU0NTRmYmIwNDA4ZGE0MGE0YjI5YzlhMDZkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:56:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InNtS2p6SVFTWlJGeS9DdFVnS1NWMGc9PSIsInZhbHVlIjoibEt2WGcrRE9hMlJ1d2U3S255MmxReTgzakNobmc1Tkp2SkFpUWdjV1BDTmVVY25JWndNTjgrZlFiWm9BN1RsYzMwV01yNVpIVGZ3NzhYYjNiWXVLSDZFS0VNa0Q0VllmRVl1ZFV4ZnhVaHJqYXJnRkxJSit6bm5BQTFXdmplOURHV09JWmZNNEx3clA1dzY0K2JIbzEreU9SRzN0Z0tkc2dhU2dZSHllZno5WWdsdEEvek51cjMyNFYxZnZjNnN6b2w1Y3ZjWTlBUFlyZEpyMVo3c003eTVDMkh6RURaQkMxOVZ4Wkc3M1J2cnExRklhakJXMUhaN1BGVEN5VEJyQVdZQ3FISERpeVNJbkFrb3BtTkgreDNZMkplbzQyN0ppMFRhUXlTWDJ4aEhjRDl1VHIwUDVWcEhGTUdsaU5pV3psaHViL3VZa0NmVjA5YzQ0SW9Zdmw4RG8wWnV5ZFBGSnp0T0ZFSk1JL2FtUzdoYWYxS1E3MzdKb1YraE1ybHZGaG9ZZ0pvaGt4Z2luT0wzckJ3SnJCYnFWMk9wYlZzUmMxQXJsYTRHb0p5dlFGNHFmV2hEZmpoR0JPZHAyeUZ4blVRU05VN1hHLzJtZ0hKSjdwQTVSYng4Z0V5ZlNvRlVLNUFXT1hXYzNPdXdpWWY0Q3JYSUlRbnRibmJXdGQ3b28iLCJtYWMiOiIzNTQyM2U3NzNlNTVjZTExZmYwOGIzNGMzNzBhZmMzNTlmZWUwN2I5OGZmMzIxY2UxZDVjOGI5NDE1NDlkNmRhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:56:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574217391\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1055274574 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1055274574\", {\"maxDepth\":0})</script>\n"}}