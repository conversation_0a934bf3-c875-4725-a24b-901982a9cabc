{"__meta": {"id": "X57f4baae61a3830e8c428bd6e4d362db", "datetime": "2025-06-30 15:44:20", "utime": **********.621726, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.244474, "end": **********.621742, "duration": 0.37726807594299316, "duration_str": "377ms", "measures": [{"label": "Booting", "start": **********.244474, "relative_start": 0, "end": **********.555242, "relative_end": **********.555242, "duration": 0.31076812744140625, "duration_str": "311ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.55525, "relative_start": 0.3107759952545166, "end": **********.621744, "relative_end": 1.9073486328125e-06, "duration": 0.06649398803710938, "duration_str": "66.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139368, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023580000000000004, "accumulated_duration_str": "23.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.582146, "duration": 0.022670000000000003, "duration_str": "22.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.141}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.612757, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.141, "width_percent": 2.502}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.615695, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 98.643, "width_percent": 1.357}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  1621 => array:9 [\n    \"name\" => \"ورق لعب اون\"\n    \"quantity\" => 1\n    \"price\" => \"5.00\"\n    \"id\" => \"1621\"\n    \"tax\" => 0\n    \"subtotal\" => 5.0\n    \"originalquantity\" => 14\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1744015125 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1744015125\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1714411515 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1714411515\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751298248439%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImltdkNaK0J1SUxtQ2luUnFtdFFsRmc9PSIsInZhbHVlIjoiVlBCMmR2UGJQclpSaEJBUkxTSXVpVDJwRVM2UzZ1YWIvTjNMM0lnVjdMOGo3dFFDb1dIL2QxZHNQWmpxTi9qU281eTh1VnlKNlBLSERQbWhFMFVNbkpHU2JWemFFS0lrYzZQYWlsV3pHNDEvbVJGeVgva1ZJZEwyaFdlVk1Eek82Q3dPOFFNQVBZTWZqYjJpSXFTYU9mSUR1V2xseFpmOTNxZlhOOG5pZGFtSVJJd2xHa3Fxbm8vVWE0Snk4SWZLWTBrZHhwSURMZ1lXdTloMEZEYlAwRm5oS2ViM2liQUJwT1J5b1dqdkZkcTJYWm9mQnI2Ti9Pb1lmMWUvdHpzci8zUm1WbkUzL2dvWmgxaU8xRlY1OXZyTnlYQkIyN2M5S2dtdVc5M3ozY1puL09oU1U2OTkrOTE0UUd6QmdzTDdrUzNYY1U3WTNZcytEb1pQRVIvMW5Pd3NtVko4MklIVWhyVmVCTmZ2aXREcnVhejF3WVNsajNoaGZqRDNETHE1bTN2RnBCZHhibmpuRmZrRHp1eExtc2FKNkJYNEI5eVd5M1AvQXo2bnUwTlQ2QWNzTEhzclJOaFBCMkJMMWhCeFdKRHVIZFU4cVZ4a3hLNy83aHV4K1BMQXpIckdST1BSM0EyK2tXU1lZVXgxTytCVHNUalV1b05Ub3VVWk9Db0ciLCJtYWMiOiI2NmM5ODE1ZTIzYWRhNzM3N2JiMDU1NmExMDE1NjI3NDljMTYwMjk2OTVhMTgwZTQ4MjhmNWRjZmEzM2U1NjA0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkF6aXAxM0J3anZqTU1Gc2hBcjRZbnc9PSIsInZhbHVlIjoiOU5TZXJyNHQrRW5ha1RlUGUzSmJlSDBVWmRxVTRUalY4ZU5YMkg2K2pPbUpQdU5LQUNTRTB2UUpHU2FIa1FCSjJFKzYzdnR2d0NhV3V4UVRMbXFqdDZzTXJzdURYZStEbHZmelpLK0VlV20xaUVyTWZPd0M0SXpndytGSlFDN2dFckZGZ1NyQ1psd1FENVF4UkxDaEFXVmlZMEhnbEpvcG9Yb1VhL3JzNU0wSGJ0UGp2K2hLZE9yWGR5dSs4SkpBcis0enBrVXEyQnhBK1YwdUEwcnRaOU5YVFBCZDg0QStlaFg1MzQ0NWRWY210SnpRSEYzRy9mSEk4NHNWTzdQemwwVzFoQ2tFelNydzM5cEsyYmdZdlBoenRvRGZLVlFDNWpLd2loYzNOWXlmWlNreWlnV2x6WDk0bm9YRG5WaE1WUGpRMWpuWU5CUGF3OFpTVWN3YjFRMGNsV0hGVHp5SXZNWUsvU0t0ckVlVjlOKysxTHE4WVVVWmZRdWc5YWF0ZnNHSDFKQmJvSkVaQmdjQ003Z3NvZjF6blJjQmZ5R1V2bDhBcXpROEVpL2haNXNmVGNOdjRmUWxNODRjK0lSYXlxN0plR0VXbFpTYlhJbnNIZGh5QmcwNVJIajl6VXZFVDBxSUVRRmR3T0Y1T01hNWZUU2hPd3I1aUoxS2cxREQiLCJtYWMiOiIyMGY2ZDNkNzhiNDhkNWU2YmJmMDJjMTQ0ZDE3MmE2NjU5ODI0ODk4NjAzYzg4YjQzMWZhNDQ0YjY1N2E5MDcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-722406787 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-722406787\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1307119004 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:44:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9KcmRsSncwZTE3dHlFS0VOZjZkb2c9PSIsInZhbHVlIjoiUEZLNmRtR2VrSXJJdnFIUGU1L3MxVjVMK1hOMDdZZmpZbEIzbWkrcXZNUHFCclMyOEJaYVlSZDQ0Ympjenl1ODJFRjViSmU0Q0NJSVdLRFdZT2lXTlUvVFN2cFNJWTFtU3RyUGVZdFJOS3dWZmdGTEJ1TWdmckNVOXlreEVWWTZLeTFaVStOV3lFdGpERk1YZkVUdmVYNkUwMzhrNWIxcWFTdXJoaEt3MXRWN1FZUTVBeHVWVWtaNTlnQTlnbjhsZTMreVVoeTltRG5CcDJZYUZjRHZYbE1PdktXWDkyRTRlcDdrQmY0NWliWmlyajZSVGtFay9ZQng1d29pemFSVjA1LzYvRDlGbWJZM0tGOWJiTkhLYmpoWU9waDBIM3pzQ2F3ZkU2ajJsWGpPd2NBYkI0OXR5QmFrMlFRVld4ZmMxVWc3akNpcFFDZ09iMndJbTdlaFROZ0NGOWNQZzJuR2xJbU5tMTh0NEVpMU5lQ3Y4cnlROG1xdm04RTQ3elp3LzRHckYwYVowSjVNTStCUUFjSUU3Um5pSkRDd0I0akRvV0pKSGZhMmRZNU11R05EazFTRitJNi84RW1HL2p5ZXAvSXR2UkNuOWZDSFdSSDZWRFZ1TForeXdselNybmNiY20rRzRxNDZVWWZLMFMzaUtyeXVBeHR2WXI4Z3pPdDIiLCJtYWMiOiJhYmZlOGQ3Njc4NTI1MDAxZTdhY2RmNTgwZTFjZmY0M2UwMzY0MjcxOTYzNDBkNjFiOGM3YjNkZGMyYjFiMDYzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkwwOFZKNDloVWJDcWxHZUFvNHgwS0E9PSIsInZhbHVlIjoiZzhwbEFTZXlFSjFtZmkreXdRMjJzWURXS2NDU1VJRWw1eGs0ZmRkdWdDR3YyU3hReHphSExFajYzaTY4aHlUTXJ6KzRuS0Y3MWl4S1V4UEJhTGJZbWo2Q1d6ODlxSkFJRGRDQmtlalRsWlhZY0MrUGlUUjhOM0tUZElhTGdFbzZkd1pSQWdZV2l3NExlRVc0ZHk5Vm93ek9yeDRZUmFrN2pkcytPM1Y0enpxd3pWUWFoT0F4c1doSGRPdis4a3FSY1IvdTlvK3JBeUVvVUQwWjcybnYxaGYySjNaOTNzeUpJTkxNSjlJWlpsZ2ptME5xOVV0WHFHMXhPUzJua0Z0QUdhTGFkVGp1Q3gyZTE1MmhNa0IxTi9mbTV3MzF5T2RuT0FRZFU3N0NXZVpONzJ2eDFFU2V0Sk5OUXMwSVZWaEpPNGdvbFlVVjZTMW9oNkJ4SFlWMGsxMGhwTjlXMHJvUGJ0ZGM4MXMvQStYaVRJSUcvSGt5MFl4b3A4eEorbERtNzVGa3F0OHNsR3h0VXNvbGcvMHFNT3lyQm16dzhLMEppVnNrM2FCUmRqSDByUzBjRHpQSmQrRzVYbVlKd1VxQTAvY2xXUlEyajg0VS90QXlWQlpLUkxtNXdBMFowOWxBd0wvVkJ4QmZIL0lSWWh3dnhjNTg0ZDZKRzRhWHVBVmYiLCJtYWMiOiIzMTNhZDg0NWVkOGIxYjViM2IxYmRjOGNhMWEwODdlMTc0Y2VlMjBkOTljYzQ4N2E4NWNkOGRkZDIxODJkMDQ3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:44:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9KcmRsSncwZTE3dHlFS0VOZjZkb2c9PSIsInZhbHVlIjoiUEZLNmRtR2VrSXJJdnFIUGU1L3MxVjVMK1hOMDdZZmpZbEIzbWkrcXZNUHFCclMyOEJaYVlSZDQ0Ympjenl1ODJFRjViSmU0Q0NJSVdLRFdZT2lXTlUvVFN2cFNJWTFtU3RyUGVZdFJOS3dWZmdGTEJ1TWdmckNVOXlreEVWWTZLeTFaVStOV3lFdGpERk1YZkVUdmVYNkUwMzhrNWIxcWFTdXJoaEt3MXRWN1FZUTVBeHVWVWtaNTlnQTlnbjhsZTMreVVoeTltRG5CcDJZYUZjRHZYbE1PdktXWDkyRTRlcDdrQmY0NWliWmlyajZSVGtFay9ZQng1d29pemFSVjA1LzYvRDlGbWJZM0tGOWJiTkhLYmpoWU9waDBIM3pzQ2F3ZkU2ajJsWGpPd2NBYkI0OXR5QmFrMlFRVld4ZmMxVWc3akNpcFFDZ09iMndJbTdlaFROZ0NGOWNQZzJuR2xJbU5tMTh0NEVpMU5lQ3Y4cnlROG1xdm04RTQ3elp3LzRHckYwYVowSjVNTStCUUFjSUU3Um5pSkRDd0I0akRvV0pKSGZhMmRZNU11R05EazFTRitJNi84RW1HL2p5ZXAvSXR2UkNuOWZDSFdSSDZWRFZ1TForeXdselNybmNiY20rRzRxNDZVWWZLMFMzaUtyeXVBeHR2WXI4Z3pPdDIiLCJtYWMiOiJhYmZlOGQ3Njc4NTI1MDAxZTdhY2RmNTgwZTFjZmY0M2UwMzY0MjcxOTYzNDBkNjFiOGM3YjNkZGMyYjFiMDYzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkwwOFZKNDloVWJDcWxHZUFvNHgwS0E9PSIsInZhbHVlIjoiZzhwbEFTZXlFSjFtZmkreXdRMjJzWURXS2NDU1VJRWw1eGs0ZmRkdWdDR3YyU3hReHphSExFajYzaTY4aHlUTXJ6KzRuS0Y3MWl4S1V4UEJhTGJZbWo2Q1d6ODlxSkFJRGRDQmtlalRsWlhZY0MrUGlUUjhOM0tUZElhTGdFbzZkd1pSQWdZV2l3NExlRVc0ZHk5Vm93ek9yeDRZUmFrN2pkcytPM1Y0enpxd3pWUWFoT0F4c1doSGRPdis4a3FSY1IvdTlvK3JBeUVvVUQwWjcybnYxaGYySjNaOTNzeUpJTkxNSjlJWlpsZ2ptME5xOVV0WHFHMXhPUzJua0Z0QUdhTGFkVGp1Q3gyZTE1MmhNa0IxTi9mbTV3MzF5T2RuT0FRZFU3N0NXZVpONzJ2eDFFU2V0Sk5OUXMwSVZWaEpPNGdvbFlVVjZTMW9oNkJ4SFlWMGsxMGhwTjlXMHJvUGJ0ZGM4MXMvQStYaVRJSUcvSGt5MFl4b3A4eEorbERtNzVGa3F0OHNsR3h0VXNvbGcvMHFNT3lyQm16dzhLMEppVnNrM2FCUmRqSDByUzBjRHpQSmQrRzVYbVlKd1VxQTAvY2xXUlEyajg0VS90QXlWQlpLUkxtNXdBMFowOWxBd0wvVkJ4QmZIL0lSWWh3dnhjNTg0ZDZKRzRhWHVBVmYiLCJtYWMiOiIzMTNhZDg0NWVkOGIxYjViM2IxYmRjOGNhMWEwODdlMTc0Y2VlMjBkOTljYzQ4N2E4NWNkOGRkZDIxODJkMDQ3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:44:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1307119004\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1621</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1608;&#1585;&#1602; &#1604;&#1593;&#1576; &#1575;&#1608;&#1606;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1621</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>14</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}