{"__meta": {"id": "X9c1d4ee5912b844f0b28b245d25ed96e", "datetime": "2025-06-07 23:28:41", "utime": **********.73658, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749338920.859161, "end": **********.736609, "duration": 0.8774480819702148, "duration_str": "877ms", "measures": [{"label": "Booting", "start": 1749338920.859161, "relative_start": 0, "end": **********.628249, "relative_end": **********.628249, "duration": 0.7690880298614502, "duration_str": "769ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.628265, "relative_start": 0.76910400390625, "end": **********.736613, "relative_end": 4.0531158447265625e-06, "duration": 0.10834813117980957, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45058432, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00656, "accumulated_duration_str": "6.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.685179, "duration": 0.00497, "duration_str": "4.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 75.762}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.70707, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 75.762, "width_percent": 12.195}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.720213, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.957, "width_percent": 12.043}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1864460900 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1864460900\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1145577616 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1145577616\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-967830806 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-967830806\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749338884662%7C14%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImV4YmQ5V3BkL0YxOWxFQU5Pc3psdkE9PSIsInZhbHVlIjoiZUd3bDZ0UndiYXFPMUNsNXlsMElSQzZUYVEzbUR2M2t2cldkRW5ZQ21Sb0hMTUF2eDVPMC9wZUJkVGVUMjIvaFlKL2tONjdGNGgzRkJ1Wi9WaTBacjlMazlRVkFQMzJaWmhmQUJiN1RCdzExQ3N1VytWQlcyOXE3MEE4WUMxOXNRTkgvL0F3VUFqSnRuT0hQNjR5dnN2dTNSVGd4ZG13eGVaLzBBRW9EQXZaV1EwbWk2RWgxaFladUVjaHBReGlFWlFYVEhacWZZaGJjZFBuSjFPMGp4SUpZSlphZ3FsWmI1ZTgyZ1dleTF2dE51ZlBPY0NmMHVBcHpsLzhUMHk1ZzlQMFpvTHY4b2RIRk8xclhwaHZlVjd3Vi9Cek5sUmZkQURRUng2WXQyUEJRWEhuTC9TMWExYTN6dy9hMmdidjBoTGJ0WTJSeDV1alVCcjZrQ3M1Ykp0Wi9mMlZGeDRqeUNvRFlBdW13VkJqOFBJZlZPY0owYTR0bGtCY05mVkJhd256VC9qZkNXdWxuVHNvWm9jbWFmQ2hOMlh2WEhZNTlDZklRVzJhNWx5REgwcU02YzBPMzdEY3dsYmpteFZKYkMzNk0yVW1Tck9BR1pNM3VFRDM3T1d5TXp0VXg5QmlvVVQ1Z09hSVdROW8rMzVzSkoxMWNJZ3hYVUxGYlkybm4iLCJtYWMiOiJkMDM4ZDRlOGFlOGFmOGRiNTY1OWM2NGY2MGI5MWFlMTZmZmEzOGIyYmY4Y2UwYjM3OGFkZTYzYTYzY2U1YzkyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IngxcG9NcFhJTUlmb3QxV2NKQmxVTVE9PSIsInZhbHVlIjoiSDNKWGplb2xBUXZrYmQ4eTRuamFUaGtWeEp3blZCNGFuS29XT0tFNWJoR1hFamRzL00rNk9Tb2hyR2xnN2ZvOHhrS2FUOExTdDlKVC9EenJEOFdjaVBQenFDMVRPbGpHY2gzaWNWdkxmbU1SWnp0NXdxcTVtY2dDYUtLVytEVjc2R3h0Vng5Rm01ZWVVYVAwbWF2dldwZVVvK3V4WUxjaDZpMEpXQmhLZ1YxOVZhTUZhbFpWbW14ZlBMbmVUTEovbDF3U1hvdHY5V2wvd0hvYTFPcEJreWJnWERGNXhLa29YcWM1NEU5a1h1SHpjMXFydzJ4MTF3TDA1bjBNQThNbE90OURiYTc5bFFjd3k4b1FpMUN4enBCWnRaQnZlL3RQcEszeXR1VlZManRHczBuM2RsQ3RWSGZnaTRWV3JaRk9TOGVlV1kreTUwWXNXb3pGVEJZa0NNdGUzZXk4dGl3MFhqa1lldWZDQUNhK3RjVTd3MjJSeExVVVNiQitycFRyN2xOOEtMQmcrTTN3TzA0VE55SWE0ajV0V3d6akNhdmkwYXdzU3huMmlYK2lCS25lT2hYN2JxbjRJWXhiMzdCZnd3ZmNwQXc3clVVdjZlaWYyQTZNTVZWQjJkMlpqTlA4RzRORDdkVnJsRW9Dd1VxNWNwYXVQZ0FuQW1qYnJoVjMiLCJtYWMiOiJkMWIzMGUyNTljMWNiZWViMGQ0ZjIyZTFhZjM5MDI2MTU1OWMyZmNmMzgxOWI5ZDFmNDE0NTQ3NjY3NDkxYmZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1046935757 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:28:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhtZzBPZWZkNkNoQXhjeUpXdmt5eEE9PSIsInZhbHVlIjoiNGJUNTlDZEduNmFodGR1bE5CUzA5N0ZLbjBOMVlhY3YxSzhTSVM3Nkx0WW5pZjRGdnNsa3Fvc0pJc1cyMGlMcVB2MFNIU3h5R1BjYVlnZjZ6Z2h0Ump0cmZTQy96WitmZ3JHY3RBTDNOamhZRzB4NDlseDFmMm1ETU1VYUJNZEwvQjVMc04rWForTkg2MHdJTGxIYlBFR0pQaktiN3dMeWZ3RmgyeHpzYWRpUjlxVkNmQTAwYWJNdzFqalpoQXVEdERyYVdkUkdjaHZmVk1tdUUzYWowWXVpUXNId29DdGY4TDhSa0J2WE1lNmxURU9vQVFwNExzanpVVGhnTGN1VlFlZkVacFMzSEkvb3BhYjg2Y1VsMWRrZzBXRUxxRVBiTVZmdVZDaTJEMVlIWFcvM3B4TXlNdjNyVHhJTUpjNlJMM3hHbFUxZUVxMnc4azBaak5WZ1Q1TEFFTXI5NXdFMXV3dCtOMktsazBINGhIR0p6enZyL0o1c2VsdmdReUVkWWxydjc4NEx1U3VzZUQ5WUlqV09QL01uZFN4Y0N0ZS9NR3puUGZhRFJDNS9vaTcrOE9JK3MySVNUa1VjVkVodTFycVhhUXJjS2hNck1HaitsUG8rR1Jza2lMcTlPd0grK200VU5aOTdUQkdQeHhhd042WTVYNEtKMm1obmI4QzMiLCJtYWMiOiI0ZjIxNWQwN2JlYTA0NzMzNTUwOTU1NGVlYjAwNWU5YWM2MGE0YThiYzUxMDZjZDMzMTA2ZjRhYmQ4Y2RlNDIxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:28:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlhsZFQ1MG0weEk5OTNad2gwdmFTMWc9PSIsInZhbHVlIjoiVlg3S3VyV2grVGxsNzBBOGVMZGhmM0ZjR2RmVDdrdnljVHZ6YlA1V0xTUkVpQmtIbG9sVThrMDkvQUFSLzV2ZlFjZ2dabWV6Q0owaElTTUdCSjAvNzRxT0ZXTGk4TGZiVjFKUmZvOTNYQlQ1U21uZHRsK0hrditQM3pUZVpSNFhwQ1FJeEpveFpTU1gvVDRoN3gwY2czS0VHcGF3NmlkUzF0NVlURWJmWE5acFpmM3c2ZTRlVnAxMlBTMVVKSC90TU9oeVNIclRWN2M5VDNxSGszVnBDR0daR0FrT0dQWnJlSVNNSFlQMnJTaW56N01La3NqTnlONTEwb29OWXRGUnMvTEhuMWxlaHFLLzdmbUI0SHNYbjMxb1pDUnFHazdwSTFMOWpQc0RBMEJGMkVIL0phaGx2c2NrYUU0UURiWnBlaWZITlpJcGE5NmIxemRtYnY2SC95R3k1MUdQOTVYUVdTQ242a2JpU29yaDNOWStYa2IxMHE3akVUS3JJNS9HbEVHUFh1WUQwbmptQlFlWXBOTjhJZEZpWHp4TzRtVE9EQTE3SkNhaEN0aW9lN2RRRmMrbFN5elFyaU81UnFFVnBhaGpPUXNTN0lYTEdlU1lhMlhPZzdIS2VjZ3hqS29NZG1tRENzcFpvYU1NUThBdmNqbjJMOUVqY1dzdzZ3WmsiLCJtYWMiOiIxMjFlNDg1YmQ2MmUwNjU2NjgzMjQ4OTllMDAwYTUxN2UzYjQ5ZDgyYzFjYWNkYmM3ZjEzYzhmMGI5YWIxMjc4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:28:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhtZzBPZWZkNkNoQXhjeUpXdmt5eEE9PSIsInZhbHVlIjoiNGJUNTlDZEduNmFodGR1bE5CUzA5N0ZLbjBOMVlhY3YxSzhTSVM3Nkx0WW5pZjRGdnNsa3Fvc0pJc1cyMGlMcVB2MFNIU3h5R1BjYVlnZjZ6Z2h0Ump0cmZTQy96WitmZ3JHY3RBTDNOamhZRzB4NDlseDFmMm1ETU1VYUJNZEwvQjVMc04rWForTkg2MHdJTGxIYlBFR0pQaktiN3dMeWZ3RmgyeHpzYWRpUjlxVkNmQTAwYWJNdzFqalpoQXVEdERyYVdkUkdjaHZmVk1tdUUzYWowWXVpUXNId29DdGY4TDhSa0J2WE1lNmxURU9vQVFwNExzanpVVGhnTGN1VlFlZkVacFMzSEkvb3BhYjg2Y1VsMWRrZzBXRUxxRVBiTVZmdVZDaTJEMVlIWFcvM3B4TXlNdjNyVHhJTUpjNlJMM3hHbFUxZUVxMnc4azBaak5WZ1Q1TEFFTXI5NXdFMXV3dCtOMktsazBINGhIR0p6enZyL0o1c2VsdmdReUVkWWxydjc4NEx1U3VzZUQ5WUlqV09QL01uZFN4Y0N0ZS9NR3puUGZhRFJDNS9vaTcrOE9JK3MySVNUa1VjVkVodTFycVhhUXJjS2hNck1HaitsUG8rR1Jza2lMcTlPd0grK200VU5aOTdUQkdQeHhhd042WTVYNEtKMm1obmI4QzMiLCJtYWMiOiI0ZjIxNWQwN2JlYTA0NzMzNTUwOTU1NGVlYjAwNWU5YWM2MGE0YThiYzUxMDZjZDMzMTA2ZjRhYmQ4Y2RlNDIxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:28:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlhsZFQ1MG0weEk5OTNad2gwdmFTMWc9PSIsInZhbHVlIjoiVlg3S3VyV2grVGxsNzBBOGVMZGhmM0ZjR2RmVDdrdnljVHZ6YlA1V0xTUkVpQmtIbG9sVThrMDkvQUFSLzV2ZlFjZ2dabWV6Q0owaElTTUdCSjAvNzRxT0ZXTGk4TGZiVjFKUmZvOTNYQlQ1U21uZHRsK0hrditQM3pUZVpSNFhwQ1FJeEpveFpTU1gvVDRoN3gwY2czS0VHcGF3NmlkUzF0NVlURWJmWE5acFpmM3c2ZTRlVnAxMlBTMVVKSC90TU9oeVNIclRWN2M5VDNxSGszVnBDR0daR0FrT0dQWnJlSVNNSFlQMnJTaW56N01La3NqTnlONTEwb29OWXRGUnMvTEhuMWxlaHFLLzdmbUI0SHNYbjMxb1pDUnFHazdwSTFMOWpQc0RBMEJGMkVIL0phaGx2c2NrYUU0UURiWnBlaWZITlpJcGE5NmIxemRtYnY2SC95R3k1MUdQOTVYUVdTQ242a2JpU29yaDNOWStYa2IxMHE3akVUS3JJNS9HbEVHUFh1WUQwbmptQlFlWXBOTjhJZEZpWHp4TzRtVE9EQTE3SkNhaEN0aW9lN2RRRmMrbFN5elFyaU81UnFFVnBhaGpPUXNTN0lYTEdlU1lhMlhPZzdIS2VjZ3hqS29NZG1tRENzcFpvYU1NUThBdmNqbjJMOUVqY1dzdzZ3WmsiLCJtYWMiOiIxMjFlNDg1YmQ2MmUwNjU2NjgzMjQ4OTllMDAwYTUxN2UzYjQ5ZDgyYzFjYWNkYmM3ZjEzYzhmMGI5YWIxMjc4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:28:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046935757\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-283311917 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-283311917\", {\"maxDepth\":0})</script>\n"}}