{"__meta": {"id": "Xf9faa4f9f4e45a22bc76877212209607", "datetime": "2025-06-08 00:30:54", "utime": **********.264722, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342653.124939, "end": **********.264757, "duration": 1.1398179531097412, "duration_str": "1.14s", "measures": [{"label": "Booting", "start": 1749342653.124939, "relative_start": 0, "end": **********.092007, "relative_end": **********.092007, "duration": 0.9670679569244385, "duration_str": "967ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.09203, "relative_start": 0.9670910835266113, "end": **********.264761, "relative_end": 4.0531158447265625e-06, "duration": 0.1727309226989746, "duration_str": "173ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45399144, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.032529999999999996, "accumulated_duration_str": "32.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.187171, "duration": 0.0294, "duration_str": "29.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.378}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.240531, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.378, "width_percent": 3.228}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.249037, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 93.606, "width_percent": 6.394}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1319301048 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749342593005%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii90bHdPUWZvaTlDdXNnRWw3OVpSUUE9PSIsInZhbHVlIjoiaTR3L2o2bkdKUFQ2RGJOY2VwNC9jRzZkeC9CZGJrdlJmNlR1ei9oN1lVR3ZjWER1VDV4eHVyNDRjbE9oSWhwNFlEWXJiN3NPQkptSUJLYlBlaC81VjJubEZ2VDFxUE5YM0pFMU9QQkpRYVJVenZTb3pMNlE0MlJCRlVPT2pTUVZoZWdlNVAwWmw1aWplQTIycStsMVMwb3lGNWZHbVdhc1h1MDY1dVR1MmZYOVNNVHVwTlZCY012dlYwSllDcHpvSkdhM3gzL2VNeFlwZGdxYnhKdk5mNmt6WG5oMFIxSlBhOW9lYmxFaTFsSVlrb2IyME5WVWJGZk9QOGJsQzZBY3JrMnFtdWlEVUs3bVZFeXFJM0R5KzRBcXpodUVDY2ZKSDNqMEdxOU1La2hFRklBaUsxNjhzeVZYL1RBR0RINGV1NEFsMk1KcUNVaktMd3hZdlphU25qRllDNHR2MDBmV3FWYzV4VytMdVp2MzhBQUg5QXRNdHFlcDV6ejJiNFZEeUo1UC9GSmZUd1VrZDZFbngwaVg3dkYzbll6NGtQU0ozdVZ2c2hoOXY4VlJvWVF2RThtWlBoOTFpbHQwTTVOWWZZWFFhSDE5R1o5WWNld0ZjK29WZERnMXUzcktDcElHZEpnUXA5M1orMUNCRWNBaUVNSTl4SUNXWUFCK2NtMVkiLCJtYWMiOiI4NjNjMzUzZDFlZTM5OWViNDIzMDk4ZjkwYTQ2MzIyYjIyYzIyMTY5YjY3NGE5YTQ3OWYyMTM3OTkzNzczM2MyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikc1bmI0c1VER1FoNCtVMUVwWm1hb2c9PSIsInZhbHVlIjoiWXZ0ekY0Rm5nSytlTDFYR0tPeDJWODUvKzZGUk1WdDRnTmplSTNISkxpdnFOYkVBZXRMK3NRZ2RuR1N3cVRJbG95YkRrL0ZDcXFjcGpDWVdzRnlra0xoMHRoeUttYnhZSmZBT2pJSkRkZ3VMSVhpNFRVc3BrQWkyUmdZYW03VS83RjJjRGVIWlJRYzZjQXBuWFhmRG0xaTZTalloU2xncVJxWEc2ZkVOd21HUVU1NU43WWl6OFBZNng0YmJhdGVyOXJTbkp2eU5mZXJDY3hERkJUcEpTcUxXSTNDU0ZRNVQ3a1pDU1RyNmRXYTNwR2g5eW9iSVlhcWJFalI1bWZPbkQxTlFpYVd6Mm5yYmV6aG9ERW9jR1RMdVYybjR2eDA1UEpSR2NQR0tlSkk4TTQwOUZDbXJ6dEVCMXB5V0c1L0xDTzMyREkwY2xiTC9xWWQwdUx1dCtSbi9JSlVackpzVzhITXdPb3NFdWlGRVpscGp2QzhvZTZzOTBhUXZYOWNOZXc3cVI2WTM4bzlFMWovZ0VmbDhRVlAva3RhV2RWcGdxSU9qdDBwRW01d2dhRzVsWTRpL2dLWWpTd1BDRkpSTjA3U0ZxU1p4bE1GaHk2OU50UGVUcmxETWlWVHFXYjBrcE1aVzVwd3FqY3h5bysvdUZXU1NDVHU0QTJPTzc4UXoiLCJtYWMiOiI5ZWIxNmFiZGVmYmMzNzJlOGE1MzRlYmI5ODMzODU4NjgwYTFkNGEwZjI4MDkzNDU0OWRhMWIzZGE0M2M5MzA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319301048\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2083789797 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2083789797\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1297224786 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:30:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhBeTMxcmpRQ0xuNm1XV04zRjhNU1E9PSIsInZhbHVlIjoiRFZkOFRyalpCaUh3dThDczhqUmlsWFBnR1Y2a3UxK1loa3huWVRIMjJtZUFsRzhLSVpod3d5MG90SzY2NkROMGRlMWMzU1VmU2RIKzVBK3k3QzVQRk50NlRBQk9wdVhFNVkrdHRESllXRUhNZENpQVhEVkllS1JaSkVDcTNjU1ljQld2MlF6NEVsM0pXSzdVc3MyaWRYVktrR09OVXpKaXVncmFIWEprNEF1MG1QTTF0Vmc0UXlkcEFNSkVsTWF2MHFma25ubUs4eThFV0taYkxTeDQvTXFNZUZtUVlKMzFMK2wyTE5QSWpYWWVRWThhR1FaOHdvZi90Tkp2KzJmR212SXJBd3VFQjFFSnVMajdsQjRKeFFYNjRCRkxKNk16QmFGUXZwUTFIVUpudnd0eVd4VWpPRk8xS0FqWFlkazhPMEVDb0RBS0E2b1AzazZScEVVZThRcTdXRnpndlpaSUh3emd3aDlrZDFNODBOZDRSNzc1ZnpHcVN4ZVFnL25LeitYQXJacCtXaUZYRkViY05jS0RpaVUvZ2thT1NrajNiVHhQMXUwNE53NFo0dFR0czZCcUovMXJKYzJvVDNUV0F1OVA4cWk3WVNXaTVwb053K0pZa2N0WkxkQ2ZjTndYTk5rS0VuU2J2UXY1MlhtUWlrcnQ1Nk9ValpPNWRyejgiLCJtYWMiOiIxMzUyMzRiNGVkOGQ4Njk5YWQyMWUwNmVlNzJiNWQxMzk5ZTNjNmVhMDZiYTc0YWQ1MGYzMjZmYmRhNmM0OWUzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkR3OXFsTlJVcUU2eWcwUUgySVFkM3c9PSIsInZhbHVlIjoiQ1F3MXNrY3pnb3lzck9RSDdqdkp2NkNBTFlyM2N0cnFDeitjS3B2aFFRZXByUyt2RDZSTTcrS0JSVDVpbnlyc3B5SDFRdTFNWENNby8yU0xhcDNwR294Y0FxcDlOK1pCakJERFN3dUNaY0FNS3ZyY1JkR0o2OXZWek1hZXdUSnpXblloeGtHK3k1NDI4NzhpS283TjNFcVlCc0pjOTlsMkkyOFJIc3U3MkI5TnAyWTQ1RlRob3FLSmhndlA3aVQvN2RKUnY1dEh6TGdvK1lPcWJzb01tVVRHN2ZzUCtWYWdXc3pIaEpydTJoVk0rZmYvM0FHR09rN3JMcDFuRjZ6Y0pqSVU3K3Bkclh6SlM5TG9xRisrL3dtZm1WMit2SFNwWmh0M2p1Q1JsRUNrcnRsZDdoUGoxS3ZIUXJ2TFZzWE05TjMvckZIQ2lmRVI2ZENPWUM5SUIyK1ZvZXFoYXNJbWlRRWFXMlVUQVFrRlNyL2hDV2xnQ1pVdXNEV0plbmNwVUVPZDVFSS9KNzIwM3VaMVBWL3R6dWd4SG1CazB1ODBodWhwMzFEdWtiTHNIaVAwbWhpMXVoYzZmR2dYZGl6RHQwaWQ2Q1h1Nll3bVFUS1N6eGFrVG5KSmszZjZlQ1BvOVBudXBMZmNEU21ZR3lGS3BXaEpQY3lsOUcwQTFiU0wiLCJtYWMiOiJmNjAyMWI1NjM4MzFjZjA3ZWIzZjQxZTI1MjlkMWZiNWZmODJmZmE3ZWU3MTk0YjNjMjc0ZTI0YWI2OTI4NWU1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhBeTMxcmpRQ0xuNm1XV04zRjhNU1E9PSIsInZhbHVlIjoiRFZkOFRyalpCaUh3dThDczhqUmlsWFBnR1Y2a3UxK1loa3huWVRIMjJtZUFsRzhLSVpod3d5MG90SzY2NkROMGRlMWMzU1VmU2RIKzVBK3k3QzVQRk50NlRBQk9wdVhFNVkrdHRESllXRUhNZENpQVhEVkllS1JaSkVDcTNjU1ljQld2MlF6NEVsM0pXSzdVc3MyaWRYVktrR09OVXpKaXVncmFIWEprNEF1MG1QTTF0Vmc0UXlkcEFNSkVsTWF2MHFma25ubUs4eThFV0taYkxTeDQvTXFNZUZtUVlKMzFMK2wyTE5QSWpYWWVRWThhR1FaOHdvZi90Tkp2KzJmR212SXJBd3VFQjFFSnVMajdsQjRKeFFYNjRCRkxKNk16QmFGUXZwUTFIVUpudnd0eVd4VWpPRk8xS0FqWFlkazhPMEVDb0RBS0E2b1AzazZScEVVZThRcTdXRnpndlpaSUh3emd3aDlrZDFNODBOZDRSNzc1ZnpHcVN4ZVFnL25LeitYQXJacCtXaUZYRkViY05jS0RpaVUvZ2thT1NrajNiVHhQMXUwNE53NFo0dFR0czZCcUovMXJKYzJvVDNUV0F1OVA4cWk3WVNXaTVwb053K0pZa2N0WkxkQ2ZjTndYTk5rS0VuU2J2UXY1MlhtUWlrcnQ1Nk9ValpPNWRyejgiLCJtYWMiOiIxMzUyMzRiNGVkOGQ4Njk5YWQyMWUwNmVlNzJiNWQxMzk5ZTNjNmVhMDZiYTc0YWQ1MGYzMjZmYmRhNmM0OWUzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkR3OXFsTlJVcUU2eWcwUUgySVFkM3c9PSIsInZhbHVlIjoiQ1F3MXNrY3pnb3lzck9RSDdqdkp2NkNBTFlyM2N0cnFDeitjS3B2aFFRZXByUyt2RDZSTTcrS0JSVDVpbnlyc3B5SDFRdTFNWENNby8yU0xhcDNwR294Y0FxcDlOK1pCakJERFN3dUNaY0FNS3ZyY1JkR0o2OXZWek1hZXdUSnpXblloeGtHK3k1NDI4NzhpS283TjNFcVlCc0pjOTlsMkkyOFJIc3U3MkI5TnAyWTQ1RlRob3FLSmhndlA3aVQvN2RKUnY1dEh6TGdvK1lPcWJzb01tVVRHN2ZzUCtWYWdXc3pIaEpydTJoVk0rZmYvM0FHR09rN3JMcDFuRjZ6Y0pqSVU3K3Bkclh6SlM5TG9xRisrL3dtZm1WMit2SFNwWmh0M2p1Q1JsRUNrcnRsZDdoUGoxS3ZIUXJ2TFZzWE05TjMvckZIQ2lmRVI2ZENPWUM5SUIyK1ZvZXFoYXNJbWlRRWFXMlVUQVFrRlNyL2hDV2xnQ1pVdXNEV0plbmNwVUVPZDVFSS9KNzIwM3VaMVBWL3R6dWd4SG1CazB1ODBodWhwMzFEdWtiTHNIaVAwbWhpMXVoYzZmR2dYZGl6RHQwaWQ2Q1h1Nll3bVFUS1N6eGFrVG5KSmszZjZlQ1BvOVBudXBMZmNEU21ZR3lGS3BXaEpQY3lsOUcwQTFiU0wiLCJtYWMiOiJmNjAyMWI1NjM4MzFjZjA3ZWIzZjQxZTI1MjlkMWZiNWZmODJmZmE3ZWU3MTk0YjNjMjc0ZTI0YWI2OTI4NWU1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297224786\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-20******** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-20********\", {\"maxDepth\":0})</script>\n"}}