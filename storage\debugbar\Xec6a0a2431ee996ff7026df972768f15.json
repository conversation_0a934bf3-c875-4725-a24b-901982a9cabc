{"__meta": {"id": "Xec6a0a2431ee996ff7026df972768f15", "datetime": "2025-06-07 22:57:26", "utime": 1749337046.005525, "method": "GET", "uri": "/add-to-cart/3/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.238057, "end": 1749337046.00555, "duration": 0.7674930095672607, "duration_str": "767ms", "measures": [{"label": "Booting", "start": **********.238057, "relative_start": 0, "end": **********.85124, "relative_end": **********.85124, "duration": 0.6131830215454102, "duration_str": "613ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.851259, "relative_start": 0.6132020950317383, "end": 1749337046.005552, "relative_end": 2.1457672119140625e-06, "duration": 0.15429306030273438, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53596064, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1236\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1236-1493</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00825, "accumulated_duration_str": "8.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9168649, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 39.152}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9347782, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 39.152, "width_percent": 7.515}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.960022, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 46.667, "width_percent": 15.394}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.964357, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 62.061, "width_percent": 15.879}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1240}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.97353, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1240", "source": "app/Http/Controllers/ProductServiceController.php:1240", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1240", "ajax": false, "filename": "ProductServiceController.php", "line": "1240"}, "connection": "ty", "start_percent": 77.939, "width_percent": 10.909}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 3 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1244}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9810839, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 88.848, "width_percent": 11.152}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2005468478 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005468478\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.971976, "xdebug_link": null}]}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 2\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 20.0\n    \"originalquantity\" => 17\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/3/pos", "status_code": "<pre class=sf-dump id=sf-dump-2064140312 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2064140312\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1589704988 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1589704988\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1235814876 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1235814876\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749337003065%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhabE92UDZNY0MyMXI1alpPL0ZlM1E9PSIsInZhbHVlIjoiam1oUVhLS1ZuT2RsZ2l6SnVlVTBxcGdFanN1MnZYS3N5TEdrT0JrYm5aY1Npd1c0aGw1b1BDOFFxazVmQThSN2xQQXd4TlJwcW5pTWoyOEltZ3BEdlROUEV5b0tHL3JJd1c1b3lJS3RRR2RMR0VNYlhDTk41U1FuamZrd0NaZEo3ZnNjRGo3dnB5bHBnWWdwaGxGeTdrVjlEd2Qra0VaWVRnbkp3NEw0ODM4dzg1ZUxrdm8xYnlIWXBkZW12RzAxMFA3dVplRjF3TDhlWkxXZnZIUzNBY1lxbzc3ZllZVHM1TGM5aXM2eWJwcXgvUTVuK1RzMUxkQ0tlNUowYlFmVFZjbFJtMnB0K3ZBbmwrN2tTcndhY0xhK0poclRIeFZVQnduTVlNUmVzbFFLS2RCeWdUTU9IVE1hUjRzNnFxcU9yRU84aVgxSDV3dFlSSzZFMHIwT3o2RVBNY1hjYzVpV1ZFWXpGd0VEeXR6WjZQMzhRMEljbjZYSy9VVWliUnZHLzVqUzVBbXV0cU9ibUJCTTMxN3djRmc4b2xDWm54MVduWElZemRoYkNJcTh6YnRhMlRua3RKNTJtUU9oZGtyNVVxQnFEZGFHbTRRQUVnL2w1OTIzM0RJWXJMMHl6NlYya3hENzlYdWdsaTc4L1p0eXA2UWw0cGRRL3kyU0NwZDgiLCJtYWMiOiI3ODg2NTc1YTg5MTJjODgxMjVhMjU2MjE2MzYyOTkxYmNmZTczZDUzOTM0ZGE3ZjZhOWZiMTAzMWVlZjE5NzljIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkM2bWhwMVBUSlRLQ01WZ09DOXpNb2c9PSIsInZhbHVlIjoiSzJkQTJrd2RlVU5zcTcrdjJsTjNyRVp1a1ZqRUZSUjZyMDgxV21pUm5RRFN6dHF6aUlya2x2VE4yWnByWTlPYXEraVRaQlpSV1RWT2lGQVB5WjRsT3NiQWozZTlRQTQvNzRLTjQweExDVk1jYUh3MHBHOGFLY3BrQkVOZjJiQXVuWkZUMWJ4UTRRVzV4Rmtpa002S1E0MzhyczFyN0lKOTE5U09yRWVFSGlVQzlTTEY3Vlc2dC82OXdOeXRwSEE5UzU5RlhZVXRqRzFJTGFvNXp1eVYxTUQvK3FMVnNMS1ZCRHVGeU5CRkJxbU5CZ084S0c1R0ZwNVNKWlBsV0x1aWVmYnVKS0kwWTdSbHFpS0RtUlJtOEpUSWFwZmxzVkZRNUxwSGZ1bnBUUjhPb2ZwV1I5QVlYWWNOSVdzTUhvdzZjOG42ZStaRkZXWUtiaXdISFpYQWZtRExlZTFWUjVaWjRlbjVmWGI5cVV5d01DQ0doZ3AwZVlGL2J4aXJRY1JycTNVckRCTjMxT29ka0tpVkN2RGtVMzJwWitsQy9rYXJFSVUrcGorMEJ1VkpkTmlCVUJERWx2d3NiMGFVRzRBTHhmSk83Q2ZSZDEwSXlXd1dzNkhlTGVBN3U1TU1qRFgrTG1TT1pJWW53QVpKdzRud0lXWXhGWHo2di9UczZwSHMiLCJtYWMiOiI3ZTBhODFiNDlkZDA1OGRkNmVmZDVhMWY5Zjc4NTdmZjc4ODFkNmMwN2ZhYjdlMTU1ZmM3ZmQ1NTQ1YzdhNWVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1112652965 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1112652965\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1962811924 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:57:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZqS09GblBhZnQ1OFhqTWJDNUJaMnc9PSIsInZhbHVlIjoidytMaU01aU1jcUFwY0NnOFY1WFVTbERnaTIrVWhiZ2tkczRyODkwWHpPbDJ6SWNIeXdQSEpFTUtKZXNERENmcHZBQWZ4RGEyb29iU2xIWWF0T0VCZytKTVZSSmtwSmljSWpjTFk1ZFRWSFFHL2huYzNzUDIvZHhSWHVKVmgxREFGMVBRTDRra043Rkd3Wk14ZHpTNUlFRm1vdWs4bllKM0hNZ01tSjNXVDlUNzNPMEpOaEdUQWxuK1Q1eEtxdVllclo4TnhIR3dyRW5LUlRaT2RoTkNVN0MzMXJkTWRScCtISGJSSTB4TnZyVUJHY08xVVJ3Y0hBeGRYbUZKT0M3b29vaHVicFpJYnhyTHpHSlpKdHIrWHFGS2ZKeTk2TFU4QXNDVzR5L2dtMzJJNDIzZEx1bjFXVEF6eDN2M05yY3Z1MWpwOVR1M1IyWDg1UlA5K0prcnNVb1FZZlNyNVB3TU1NQU00WWRraTQvMkdycnY0SENaSlFJSjJNQmFaKzFPb1RBQjNUR2dMR0pJN1dZRndkRXZaQjJ6TDZZWWVObTdtbS9xMXBFVFhmWmx6U2ExUlprbHFDNUp4MmhBbjJ5VnR2c3hyM2R3SzVPekdtVUVlSU9SOHFqZXpvUFJXYjJTc2xWQmptYzFON0Q3TmUrQjltbEM4YXpWanJLUkQ3aXEiLCJtYWMiOiI4MTg5ZTU2YjQwMjA3MGE5NTEwOTIwNzQzYzU5OTcyZjc1MjA2ZDZjYWQ1NjNkMGNkODhhZmE1ZjlkMGU2NzBlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:57:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imx6K3IweG9nMzNrd1gzZDg1UWtXZWc9PSIsInZhbHVlIjoiU1NNS3o1eml2MTRmaVNMbC93b0ttYUlLSXloMnFyVy9nVmU5dGdYZXBsckkvblRxcFJINVRKRDNnZjJoeFJtb2tFeGlJTzlsYjZDWmMwZlh1K05JamFLNmlzcURFUkJlYlJQYVNQRFkvMDlGcFRoS0d2NW5FcXY2RnVlM3YrQUgrZSt1bG1nQzdMMXFpVmhEalFhK0V5RENaYUgwcXJYb1ZFUGpIOFRTbWJkblBPQ1dnNXplU0RiUHZLTTgyR1B0ejNDMnZSaGZoNC9qc0xRaUtSMmdDencvYVlhTzdnNC9TRzFyMjFscHhnVE14VjFXKzdTNGNaaWx6U0Uvanoya1p1Z2Vic3VvYUF1Mko2SWZGSEllU2pCYXJQdFdnamZGaDdDVlpOWGwrY1dlR0wzMG5JQ1lyTmhmTWkySkI5eUtpcGQ3YTJkcGM0dThYcU5wNHZJZ1puektISnF6aHlseDErZDdNa3B6eGtTRmUyN1RtYUtjY3VTMU5xNXVBb3BkY0JZUVQ2OFg1WGxuZmVVZlZ3N2JMVFB5N2pnenpyTGdWam5NUUovbjhHN1dlZ0JzNzk2dmdkaVgyTHdPbkdtK2k0V1VaN2xiQ0YySXNrQWJkMVZ6MHhuQUMzOVByL0lCemZQQjFhSHhXZGxHVEVPN3JwMVMyMnZOYXJWRXBSRDYiLCJtYWMiOiIyMGFlYzkzMTM5Mjk3MjJjODZhNmFkNDgzNjg4YjNhZDAwOGM2YWJmYWViNGRjNTQxYmM5MGRlOTcwYTY2NGM2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:57:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZqS09GblBhZnQ1OFhqTWJDNUJaMnc9PSIsInZhbHVlIjoidytMaU01aU1jcUFwY0NnOFY1WFVTbERnaTIrVWhiZ2tkczRyODkwWHpPbDJ6SWNIeXdQSEpFTUtKZXNERENmcHZBQWZ4RGEyb29iU2xIWWF0T0VCZytKTVZSSmtwSmljSWpjTFk1ZFRWSFFHL2huYzNzUDIvZHhSWHVKVmgxREFGMVBRTDRra043Rkd3Wk14ZHpTNUlFRm1vdWs4bllKM0hNZ01tSjNXVDlUNzNPMEpOaEdUQWxuK1Q1eEtxdVllclo4TnhIR3dyRW5LUlRaT2RoTkNVN0MzMXJkTWRScCtISGJSSTB4TnZyVUJHY08xVVJ3Y0hBeGRYbUZKT0M3b29vaHVicFpJYnhyTHpHSlpKdHIrWHFGS2ZKeTk2TFU4QXNDVzR5L2dtMzJJNDIzZEx1bjFXVEF6eDN2M05yY3Z1MWpwOVR1M1IyWDg1UlA5K0prcnNVb1FZZlNyNVB3TU1NQU00WWRraTQvMkdycnY0SENaSlFJSjJNQmFaKzFPb1RBQjNUR2dMR0pJN1dZRndkRXZaQjJ6TDZZWWVObTdtbS9xMXBFVFhmWmx6U2ExUlprbHFDNUp4MmhBbjJ5VnR2c3hyM2R3SzVPekdtVUVlSU9SOHFqZXpvUFJXYjJTc2xWQmptYzFON0Q3TmUrQjltbEM4YXpWanJLUkQ3aXEiLCJtYWMiOiI4MTg5ZTU2YjQwMjA3MGE5NTEwOTIwNzQzYzU5OTcyZjc1MjA2ZDZjYWQ1NjNkMGNkODhhZmE1ZjlkMGU2NzBlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:57:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imx6K3IweG9nMzNrd1gzZDg1UWtXZWc9PSIsInZhbHVlIjoiU1NNS3o1eml2MTRmaVNMbC93b0ttYUlLSXloMnFyVy9nVmU5dGdYZXBsckkvblRxcFJINVRKRDNnZjJoeFJtb2tFeGlJTzlsYjZDWmMwZlh1K05JamFLNmlzcURFUkJlYlJQYVNQRFkvMDlGcFRoS0d2NW5FcXY2RnVlM3YrQUgrZSt1bG1nQzdMMXFpVmhEalFhK0V5RENaYUgwcXJYb1ZFUGpIOFRTbWJkblBPQ1dnNXplU0RiUHZLTTgyR1B0ejNDMnZSaGZoNC9qc0xRaUtSMmdDencvYVlhTzdnNC9TRzFyMjFscHhnVE14VjFXKzdTNGNaaWx6U0Uvanoya1p1Z2Vic3VvYUF1Mko2SWZGSEllU2pCYXJQdFdnamZGaDdDVlpOWGwrY1dlR0wzMG5JQ1lyTmhmTWkySkI5eUtpcGQ3YTJkcGM0dThYcU5wNHZJZ1puektISnF6aHlseDErZDdNa3B6eGtTRmUyN1RtYUtjY3VTMU5xNXVBb3BkY0JZUVQ2OFg1WGxuZmVVZlZ3N2JMVFB5N2pnenpyTGdWam5NUUovbjhHN1dlZ0JzNzk2dmdkaVgyTHdPbkdtK2k0V1VaN2xiQ0YySXNrQWJkMVZ6MHhuQUMzOVByL0lCemZQQjFhSHhXZGxHVEVPN3JwMVMyMnZOYXJWRXBSRDYiLCJtYWMiOiIyMGFlYzkzMTM5Mjk3MjJjODZhNmFkNDgzNjg4YjNhZDAwOGM2YWJmYWViNGRjNTQxYmM5MGRlOTcwYTY2NGM2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:57:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1962811924\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1068568417 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>20.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>17</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1068568417\", {\"maxDepth\":0})</script>\n"}}