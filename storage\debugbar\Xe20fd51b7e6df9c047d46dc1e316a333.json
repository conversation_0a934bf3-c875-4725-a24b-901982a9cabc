{"__meta": {"id": "Xe20fd51b7e6df9c047d46dc1e316a333", "datetime": "2025-06-30 14:55:56", "utime": **********.287222, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751295355.664805, "end": **********.287237, "duration": 0.6224319934844971, "duration_str": "622ms", "measures": [{"label": "Booting", "start": 1751295355.664805, "relative_start": 0, "end": **********.112681, "relative_end": **********.112681, "duration": 0.4478759765625, "duration_str": "448ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.112692, "relative_start": 0.4478871822357178, "end": **********.287239, "relative_end": 2.1457672119140625e-06, "duration": 0.1745469570159912, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45062000, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02454, "accumulated_duration_str": "24.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2297761, "duration": 0.02224, "duration_str": "22.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.628}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.262136, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 90.628, "width_percent": 2.73}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.271176, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 93.358, "width_percent": 3.464}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.278571, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.822, "width_percent": 3.178}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-295206277 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-295206277\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-536900861 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-536900861\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2118009614 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2118009614\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-926676182 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=2uy8by%7C1751295328208%7C1%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVrMjh2RnVLdkpxWEdGMlV2cUljMHc9PSIsInZhbHVlIjoiVDgvSFI4V0FGUHNaM0tzeWFCQmluRkJHL2ZhOUpZbjlBeXE2YUpOTmprZys2NjNmNzBtbFNLMjJuYjQwQlAvNXc4c05IYThGb3JidU5jckk0TWNjVjZ0UE1qdUFhdUpWZ29aYUtsa3JhWm1nN2JCaHo0OENIRzlUYVF1Y3dEbWY5OStUQ1h6OWIrZWhrdUVUWjdTcERQM1Vwc1cwTHdWWmlPYzdndXVFR2FmZ2l1K2lpSDNVSzZHdFc5emIwZXFNeTFOVnRXbzZySjlOZ1BzYzFlUEsxVnlOdGFlRDczWWxRV2U1TkhHRnFvVkdCMEl6V05TTUxlWm92SStnb2Z1UGN1Wmt4eGxSTVRDcUFSSklXcCtlQmtyWm50cFJMazdOQ2RxVjd6REdwWTdVN3FvM1pJbGVObXhELzJYemtOdUhkeXBqRWJCaU4weUlpOHNURDN0M0dEUmxKOHlzYmZiMUlZVnNaVWtVdS91aUhuTnlHcmNFQ211NE9lZzQyRVBxYmRrT2ZjM3czNmVQdDdhY1k0bmxwVW5MZXExdUhaWDdURnNIdDVRWFB0b0VTaFlTVEJOK1Btb1lqUnJSeElRc0kyaWdzeEhjSVdEQTBrbktIYi83ekMzQjFpZ1NHUFcyWU9JeUJGa2pVZnBBQ2xJaVJGTVB4YURpZG8wRW5KVG4iLCJtYWMiOiIxNTJlNmNhNGQ5NzRkMGMwOTc0YzBiOWM2ODYyZGY2NzZjYWY4Yzg1ZmNmOWYwYzc1ZGViMWQ2MmI2NDBiMWMxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkJhR2hDVy8yN0RQc3ZOallWS0FNZkE9PSIsInZhbHVlIjoiRU5memlEcllmbnlmeXVrWTR6LytDc1g0enVDZFpmMjQ3QVFqR3JCaFMxdGNVemxadVRORVFFT0owNUNuVmx0ai9EREczc3ZscWJORmRyTkZ0L0ZFZWg5cU5NckNBeVUxbitGSjB4RmUwNjA4NXptSzBHMkg1MVBER3MxTE5ySnQzdCswZE1rSWlLdEtjNFlXZWt0RXlTeHhITzdyTDVxSkwrQzdJdzY0Y001L24wNktMbHdzL2k3Q0J0bHIvTnV5V2tsbUplaXFiL0lQUy9rSG5pam5ObGZSWEo3NDRtVW0vNENFbDJaWHlndngwSEFBTytKclk3UGZUMkhlendkZ3EwZkNCVFcwSllHTXp6c1UyTEFYQng4QXZSK0ZpRHBhWGpFck9NdzMybDNMMHoyMHRkdXhUdzczSkdIekdhc1g0MnRXUWo3UE9OeUw3OURLK1FkSlZ6andEVUhLNXVPd3JMOVFPWXN6d0ZtQ1pLczVyTHZ4czFmSWVFSTF2TnpXSFhEa0lQTEt3cFlmUDlhc0JjZGVkayt0ZVkrUEVQWG51ZXNhRnJNWC9KYWRRWHJOTkNqQUdLRWlkakZtd05mZi9nUkxvcUFyY3VwbW4xa1Z6U3hkRTE2U3B4a1pYTk1ETTU5RVFoM3pxYm4zOWFtY1YwTGhpRTVXVFN6bjFHY0UiLCJtYWMiOiJlZmYxYTNiMGNiZWY3OWNmZjljN2M4NTljMjM1NTk3ZjUyMzZlMTNlMjA3MWE2YWYwMGZkMjA5ZDFiNTc3NGFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-926676182\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-91916619 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rf3imG1lPKr2xfPRA9rNVzJMke6dPWK1Wb6HvZ8N</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-91916619\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1386937675 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:55:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkIrdy9ISStpU3czN29tOGVqMFJNWGc9PSIsInZhbHVlIjoiUXZDdkdvVDNDQlpUUlNHeFJBZ3UwTmhLbW1mMDJtQ2k4OTVoYVJRNDh2UkttSDhadVhMd1B4L2tUTHRWSGljc3JJN3V1TGthNmRyd1BnenlsOGVsWkhWbVdXOE9keU5QT01aU0JIKzEyWnU1WmFYUlJ2OHpwdmdpMXdNTEdqWEhhMGswT29IdmJqeHJrQzdYb0RyeGltN3Nxc0tsTUxVQWo0ekdGakcvRHN4TFU3TllaVlM0SnkrYUhISUwzWWFURWtHVms0VGpXbG42dHB0YjRnOWE1UVAyaDh6TGJ3UG8rRzh3UEhuZ1MvWEQwSWVQRVI4SEZER2VmanVpOVFXYjZuc3BwODdDQ0ZDeXEzeWxrWmtJSGR1K2xkMGJaTWpQeXM1dTBCNmV2dm4xRHRUSUJ1dldvejY1R3hvb245TVpidXIycUZmT3Q5R3lGdUY2bkpqS0ZvcXFjR2tLZy9hRnIrTGxTZEo1dVAvK3gvNjNGUnR6ejVZTUlsbFpGOUVuT0dUTTJBTW95ZUVacmR5MmhkNFdPbUI5Y3lia0hiWmxoRnhyM0owQkZmN2ZmWnpjOHVXWWpINTF1NmtoUTFGWllOWnpwSEJnMCtudXhxMTZzblNpZUJnQWgzSEp3a1BTS05EWlIvK2ZrUmRxZnpnNEpTVEZYUFhSYVBQWEJWZnYiLCJtYWMiOiIxY2M3YzZlNTY5YWQxODYxMzY3OWQyYThlMzI3OTBhOGMzNTU1MzNkMWJmZDlkYjI5YmVlNWU2NDQwYmNiYmNjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:55:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlYxZWhNTFlxbHdlN2xzRE9xV2UxTUE9PSIsInZhbHVlIjoiaHE2bHl5VE1nMHkwQkJZcG5kV05lN05wQmFIdHdnOXNsU094c20vU0RPNHE1Z1JlakNFN0E2bFZZMEk0YjJkNThhYzZGV003M21YTjBEUEZlVHJmeVBzMXZ3bm84QVM4MGd4S1RkNk9KNmhpc1ovWkJxZUFDeC8yanI2MFFuWFFOYXVLMG1wOUhseDNFeHBwT2xWN1J3Y2NNMDBTWXdVQzRRdnk1dFhKRm5CaWZQWkNxL2pzY3o2eTRFWkJIazNxeCtjSEpjU282QkZmWEU3SU8xUXFWWStZaHFWdEhVSVpZdFlSUEttWFMybEJEZ2NZMzNvcks5Ynh6WmNHTTJPZmFnNjc0andLbTlRdmJrYnZxSnFEZkhpWXYxRmRtaXMrV3hFd0E3aWlOUnBIMDA2SklhQWQvNTBYdUpZUDByTmNKbE1SUTFXRitOUUxRM29nc2ZMRUU4N0NjeTVZRk5VQ05jblMvSUt1cE92YXZ6bjlNNlNidC9pQXFaOGpxZFlWL2I0VS8yeUdaMEgvZnZtRTR5L2NndDAzUVRTb0NPZDRqY1E3TVhxWU1YV2FIdXlXMnJZOFlxL2ZwWTBLTmh3ckVNeWRoOFUxY1F3eWRrTTVjOHFUQTMrZ0pVR0duSS9ndzZxaHlPVW5KbFRQenBLUUdFaXZJemdqWldGbnpNWWoiLCJtYWMiOiIxZDg4MWMxNGVjZTkxMmM1YjljZDlmZTE2ZWI0NGE2YmIyOTMzNWYzYWI5MjM5MmI1Njc4OWNmODYyY2VlNjU5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:55:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkIrdy9ISStpU3czN29tOGVqMFJNWGc9PSIsInZhbHVlIjoiUXZDdkdvVDNDQlpUUlNHeFJBZ3UwTmhLbW1mMDJtQ2k4OTVoYVJRNDh2UkttSDhadVhMd1B4L2tUTHRWSGljc3JJN3V1TGthNmRyd1BnenlsOGVsWkhWbVdXOE9keU5QT01aU0JIKzEyWnU1WmFYUlJ2OHpwdmdpMXdNTEdqWEhhMGswT29IdmJqeHJrQzdYb0RyeGltN3Nxc0tsTUxVQWo0ekdGakcvRHN4TFU3TllaVlM0SnkrYUhISUwzWWFURWtHVms0VGpXbG42dHB0YjRnOWE1UVAyaDh6TGJ3UG8rRzh3UEhuZ1MvWEQwSWVQRVI4SEZER2VmanVpOVFXYjZuc3BwODdDQ0ZDeXEzeWxrWmtJSGR1K2xkMGJaTWpQeXM1dTBCNmV2dm4xRHRUSUJ1dldvejY1R3hvb245TVpidXIycUZmT3Q5R3lGdUY2bkpqS0ZvcXFjR2tLZy9hRnIrTGxTZEo1dVAvK3gvNjNGUnR6ejVZTUlsbFpGOUVuT0dUTTJBTW95ZUVacmR5MmhkNFdPbUI5Y3lia0hiWmxoRnhyM0owQkZmN2ZmWnpjOHVXWWpINTF1NmtoUTFGWllOWnpwSEJnMCtudXhxMTZzblNpZUJnQWgzSEp3a1BTS05EWlIvK2ZrUmRxZnpnNEpTVEZYUFhSYVBQWEJWZnYiLCJtYWMiOiIxY2M3YzZlNTY5YWQxODYxMzY3OWQyYThlMzI3OTBhOGMzNTU1MzNkMWJmZDlkYjI5YmVlNWU2NDQwYmNiYmNjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:55:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlYxZWhNTFlxbHdlN2xzRE9xV2UxTUE9PSIsInZhbHVlIjoiaHE2bHl5VE1nMHkwQkJZcG5kV05lN05wQmFIdHdnOXNsU094c20vU0RPNHE1Z1JlakNFN0E2bFZZMEk0YjJkNThhYzZGV003M21YTjBEUEZlVHJmeVBzMXZ3bm84QVM4MGd4S1RkNk9KNmhpc1ovWkJxZUFDeC8yanI2MFFuWFFOYXVLMG1wOUhseDNFeHBwT2xWN1J3Y2NNMDBTWXdVQzRRdnk1dFhKRm5CaWZQWkNxL2pzY3o2eTRFWkJIazNxeCtjSEpjU282QkZmWEU3SU8xUXFWWStZaHFWdEhVSVpZdFlSUEttWFMybEJEZ2NZMzNvcks5Ynh6WmNHTTJPZmFnNjc0andLbTlRdmJrYnZxSnFEZkhpWXYxRmRtaXMrV3hFd0E3aWlOUnBIMDA2SklhQWQvNTBYdUpZUDByTmNKbE1SUTFXRitOUUxRM29nc2ZMRUU4N0NjeTVZRk5VQ05jblMvSUt1cE92YXZ6bjlNNlNidC9pQXFaOGpxZFlWL2I0VS8yeUdaMEgvZnZtRTR5L2NndDAzUVRTb0NPZDRqY1E3TVhxWU1YV2FIdXlXMnJZOFlxL2ZwWTBLTmh3ckVNeWRoOFUxY1F3eWRrTTVjOHFUQTMrZ0pVR0duSS9ndzZxaHlPVW5KbFRQenBLUUdFaXZJemdqWldGbnpNWWoiLCJtYWMiOiIxZDg4MWMxNGVjZTkxMmM1YjljZDlmZTE2ZWI0NGE2YmIyOTMzNWYzYWI5MjM5MmI1Njc4OWNmODYyY2VlNjU5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:55:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1386937675\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2128580758 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2128580758\", {\"maxDepth\":0})</script>\n"}}