{"__meta": {"id": "X11230fabea2af84b1e1d4641c224d960", "datetime": "2025-06-30 14:56:48", "utime": **********.58671, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.088209, "end": **********.586726, "duration": 0.4985170364379883, "duration_str": "499ms", "measures": [{"label": "Booting", "start": **********.088209, "relative_start": 0, "end": **********.52412, "relative_end": **********.52412, "duration": 0.4359111785888672, "duration_str": "436ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.52413, "relative_start": 0.43592119216918945, "end": **********.586728, "relative_end": 2.1457672119140625e-06, "duration": 0.06259799003601074, "duration_str": "62.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45047264, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.004059999999999999, "accumulated_duration_str": "4.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.553304, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 51.97}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.56438, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 51.97, "width_percent": 10.345}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.57223, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 62.315, "width_percent": 16.256}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.578672, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.571, "width_percent": 21.429}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-112121558 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-112121558\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-455695723 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-455695723\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-183096827 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-183096827\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-865987742 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=2uy8by%7C1751295356058%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhucDg4ZldJMXNocHNTVzJybkJqWUE9PSIsInZhbHVlIjoiSmwzeE9QRlphQ2ovd2VHb3VUODJpT2JLWG5WNk9WVy9YMHhRN3pobjNNZGxib3NldmdzNDlpZ3ZDeHVKQWFEeVkzTWtjYTB1NHVnc3lvK0dUNzlQWGRXMjkzczRuSllTTnM2SjVmMzNlcmR1cUI5aTBZL0J2MGlXdWFTQk1yaHFoL21OdlFFb0tUTjVnMlg3T2hnbEJyM0VYSGU1eXNZeTgvd2tBRVhGUnlLQXZqK1FJSU1Fb25rMS81bnB2SHhpVkhWOWRZQXZWRjNMd05LZG9RWlpYQktZSHVEUDg1bWRtTmZvQmFFb3VpNFN1L3hOSXNDSzdqUm4xYUVZMFdvUmxGdlJzdmNnTHFPeWlzVUJENnVYbHl1M2dCd0UycGpBc3h3cFFtajhFdlh5Ti81OGtHeE51TndJYzFQODNsM0IraktsSDZFdzBCUW03QzhCS0ZzUE1FTjFKc1JQR200b0d3MGYwbE1WWUtqOGVXOE9oR1lOa0theE5JZGtyekJGNXZIYkU2TElJdWt6aXE4TDM2dS9XaEF5TGtaM2ZnY2Rhc2ZQNFZHeUJDQzRXTjlHK2RVZDNsTGszdUpJMHg5QUhMOGZOZkUyMzFXdzd4c0RmUXBqK0luWG5vU3JpYTJpV1NpeHdVK3JHaHFtYzNNN1lhVitxVkNwMExvRjZFdnQiLCJtYWMiOiIwZGZmY2M0Njk2NjJhMThjM2YzYzA5MmQ2YjE5ODJhODM3MmRhYjgxMzQ1MGJjNjkyY2UzNjYyYTNiMzc2ZmIxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imh1cnNad05wdS9nV0l2d1Jac0E1bGc9PSIsInZhbHVlIjoiZGNWT3FtR2k3K0k0UDZiVi9MVmM5T05EelgwOGNJdmdkNThCRkQ2Z3Zqa0FKQ3hpRmUweE1vY1hjQ0hOR2xMRDM4MjRHQ2l6YVNQMytXV2gvcTlpUE1IbE1DekhQNWFHWUMrQWZWWnBrRGR3S2M3M2M3OEYrYWZ5Yy9rRjQ5VUJ0SEVEMVZtek02aEluS05lcFU4RXN5UDZrRU4xZDR1UUpjdXRUWGdTOEZZcnYrVnFLUHlwNG1BNGZ3anZ1TmdXYlQwZ0diZUxoazFocFZ1SnR3SGJGZ3hyM05CbTdDU0U2RXRqRmVTY3VGbUUrNHhOdWl4dHVIcXZvRnB3dHFxbmxrclVxanJGdWJMNXhGYUdkbjYrRTgyWHJzTWI0Q01RZkJMK2JDZHZsUUxIUGpzTHRJdnJSWk9jUWVGUzdUUVVObW1kQUFLbTk5Tm1CNHZuVHBVeG9ZeXBwbnRUSjZaSlNwRmhnSUdLY0ZTVzFHdzREWHlPTVN3ZngyY204cTN4MFd4bTB4NlZjcnQ4TDhPU2pxbVNQdVFFS0pVckM2dnNNOUZtWjkzQWxGQi9sdUdqaDlOT1pFWkpXYW9TR21nOEpyZ24rREdDQTJLYUVxTzNId2xyV3FuQ0luZmoyVks0bUJYY3NBZlFaVmcrcm9WTHZIMDNrbCt6S3haSTM0RWsiLCJtYWMiOiIwZDc2OTkxZDNjYjBmMGQwOTA5NTZkY2Q1NzZmMTIwOTRhNWQwN2ZiZDg2MzlhODBjOWY0OGI3OWRlMWU1ODAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-865987742\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2069574811 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rf3imG1lPKr2xfPRA9rNVzJMke6dPWK1Wb6HvZ8N</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069574811\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1309379751 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:56:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1TZml2cGNWZERIUEZYOUtVTXVPSEE9PSIsInZhbHVlIjoiaFlpUjFQd0J0MmU2aE40YXVwYjJuVk4vR2ttM0pSWlNiZmtvbFV1TDE5dU9DOWt1YTNVOVpzbmxCZ1pDSXhPQ1JvbWR5Tm9wYmpScE1SZHZKZGF3V21lTXNEMWhZMGtZVWxTRXNtcmUwZWpURXVMeGNtd25IY3lOSnhHTkE3czd2eXpCN0pua3ZBNFFEMXZpQ01ETnZDMTRoN3JOblZWTHVPbldJQ3Azd2s2Vk83cUpyTmdoakk1UzBFS2xRR1o1MFdST1FhTHZoNE9MNW9udk9VOXdOcWlsS1hWODZuM0NuNWZDa3Y3MEZvK2UxYmViV1haYXR6clQ0d0FhS2cvVzFmWTV2WGF0SXJxQktnZlpkWDhmOS9yVk1GelplQ20vdUJGV2dkSXMvNXhINGFFenVsZUlBb0ZwNzAxUlpJRUpGQlUzSllub09EMVBCSk9HcVZjWURPTWVsK2hhUVkwK1QvcDA0Q1huTEp4UEJkazBpVFdETTdLdWxUNFVyTGtOb1RmNWNZRkJuMVlyVHlJczU0NDhVa2Zsck9sdUtCSDNKSkNIdnhUQmM2dnBmQ0l0Q0Y3V3VCT0dvdGZnM2RmTG9KbEEweWlpZ3hEbHZ0VjNJeUVsSzEranE3Myt0ZEtZNUN5VnFORDA3QXN6L0E4NnJxUnQ2ZlBxMEVUTTRORzIiLCJtYWMiOiJiMjAxOTYxOWNmODI5NDAyMjUwYjcxNWI4NjIwMWY2NzFiYThhZWNkNDA0ZGYwNTNkZjZhMzBmMDUxODYyNzA4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:56:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjR3WStWeU1VNXY3eFZySVdVL1A1UlE9PSIsInZhbHVlIjoiaFhxY3ZRS092NGJzMHVBaE55akwxSFRqWHU1cG1hSnRBajAyenhYeXkzbnhVeWtMcXh2WFhUWWxXMnE0YmxncXp6bEovWjdYbGU4WkYyNEdocDB1aTAzUEd5aHJpMCtuNkUxTksydUxwUTZpRk1YZ1BvM0dQUGJaQlRlTTM1VDA3N29TNDBxejdQUGRQTlJ3WGxlZEtoeDZhKzZrMjlFUStYU3UwMDFDTExrYVZzUTBiaWJzM0diMTBqMXJCT1dCNFd5aGFBRzdGc2dMdUlpNUJ3QmR2ZFdCM3gwNWhEakdweUgwMUNhL2JGNFZ0Wk5ta09sd05zdVEyZFQ1dVFIYVBFbktvTis1cGd1VE44U25pRXRibDNaQVNNVnpOdFNjUWRWL1J6a2c1WUt5L1greWJwUFpyNzdVMnlWZzFBM3ZMRWZiSmpWN1A4VkJMcHNiNU1rOWZncTl5L2NkdWpRMXhnTk5ENWprSHgyZDJyWVlCRlpGZmNaKy9FTVo3NUVMKzJ6Ym1ZeGpkRUoxYTc4ZFNnbnpmMktETWVZWDgraHlQM3BXOUhCdVNPM2RYU3RTVU85bWxUa0NJQlUyMlE2Qk1JblNMRWtOK2FlMXQrR0FaUGszNlFYQ09wd2JYdXp4QzBhdTY5ck9ualVrMXJ1Tm1IUXQrOVRMYmxjM1V3NTEiLCJtYWMiOiIxYzExODJkNTcwNWFiNWM0MTBmNTJiNmRlZTAwNjg1NzA0Nzg3NWVkNDNmMjNlZTY2YzAxMTk1NDRmYTI3YWExIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:56:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1TZml2cGNWZERIUEZYOUtVTXVPSEE9PSIsInZhbHVlIjoiaFlpUjFQd0J0MmU2aE40YXVwYjJuVk4vR2ttM0pSWlNiZmtvbFV1TDE5dU9DOWt1YTNVOVpzbmxCZ1pDSXhPQ1JvbWR5Tm9wYmpScE1SZHZKZGF3V21lTXNEMWhZMGtZVWxTRXNtcmUwZWpURXVMeGNtd25IY3lOSnhHTkE3czd2eXpCN0pua3ZBNFFEMXZpQ01ETnZDMTRoN3JOblZWTHVPbldJQ3Azd2s2Vk83cUpyTmdoakk1UzBFS2xRR1o1MFdST1FhTHZoNE9MNW9udk9VOXdOcWlsS1hWODZuM0NuNWZDa3Y3MEZvK2UxYmViV1haYXR6clQ0d0FhS2cvVzFmWTV2WGF0SXJxQktnZlpkWDhmOS9yVk1GelplQ20vdUJGV2dkSXMvNXhINGFFenVsZUlBb0ZwNzAxUlpJRUpGQlUzSllub09EMVBCSk9HcVZjWURPTWVsK2hhUVkwK1QvcDA0Q1huTEp4UEJkazBpVFdETTdLdWxUNFVyTGtOb1RmNWNZRkJuMVlyVHlJczU0NDhVa2Zsck9sdUtCSDNKSkNIdnhUQmM2dnBmQ0l0Q0Y3V3VCT0dvdGZnM2RmTG9KbEEweWlpZ3hEbHZ0VjNJeUVsSzEranE3Myt0ZEtZNUN5VnFORDA3QXN6L0E4NnJxUnQ2ZlBxMEVUTTRORzIiLCJtYWMiOiJiMjAxOTYxOWNmODI5NDAyMjUwYjcxNWI4NjIwMWY2NzFiYThhZWNkNDA0ZGYwNTNkZjZhMzBmMDUxODYyNzA4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:56:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjR3WStWeU1VNXY3eFZySVdVL1A1UlE9PSIsInZhbHVlIjoiaFhxY3ZRS092NGJzMHVBaE55akwxSFRqWHU1cG1hSnRBajAyenhYeXkzbnhVeWtMcXh2WFhUWWxXMnE0YmxncXp6bEovWjdYbGU4WkYyNEdocDB1aTAzUEd5aHJpMCtuNkUxTksydUxwUTZpRk1YZ1BvM0dQUGJaQlRlTTM1VDA3N29TNDBxejdQUGRQTlJ3WGxlZEtoeDZhKzZrMjlFUStYU3UwMDFDTExrYVZzUTBiaWJzM0diMTBqMXJCT1dCNFd5aGFBRzdGc2dMdUlpNUJ3QmR2ZFdCM3gwNWhEakdweUgwMUNhL2JGNFZ0Wk5ta09sd05zdVEyZFQ1dVFIYVBFbktvTis1cGd1VE44U25pRXRibDNaQVNNVnpOdFNjUWRWL1J6a2c1WUt5L1greWJwUFpyNzdVMnlWZzFBM3ZMRWZiSmpWN1A4VkJMcHNiNU1rOWZncTl5L2NkdWpRMXhnTk5ENWprSHgyZDJyWVlCRlpGZmNaKy9FTVo3NUVMKzJ6Ym1ZeGpkRUoxYTc4ZFNnbnpmMktETWVZWDgraHlQM3BXOUhCdVNPM2RYU3RTVU85bWxUa0NJQlUyMlE2Qk1JblNMRWtOK2FlMXQrR0FaUGszNlFYQ09wd2JYdXp4QzBhdTY5ck9ualVrMXJ1Tm1IUXQrOVRMYmxjM1V3NTEiLCJtYWMiOiIxYzExODJkNTcwNWFiNWM0MTBmNTJiNmRlZTAwNjg1NzA0Nzg3NWVkNDNmMjNlZTY2YzAxMTk1NDRmYTI3YWExIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:56:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1309379751\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1888657434 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1888657434\", {\"maxDepth\":0})</script>\n"}}