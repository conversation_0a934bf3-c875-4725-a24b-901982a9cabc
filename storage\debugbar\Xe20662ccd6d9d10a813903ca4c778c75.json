{"__meta": {"id": "Xe20662ccd6d9d10a813903ca4c778c75", "datetime": "2025-06-07 23:06:16", "utime": **********.670963, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749337575.776937, "end": **********.670993, "duration": 0.8940560817718506, "duration_str": "894ms", "measures": [{"label": "Booting", "start": 1749337575.776937, "relative_start": 0, "end": **********.555757, "relative_end": **********.555757, "duration": 0.7788200378417969, "duration_str": "779ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.555775, "relative_start": 0.7788379192352295, "end": **********.670997, "relative_end": 3.814697265625e-06, "duration": 0.11522197723388672, "duration_str": "115ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45403352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00519, "accumulated_duration_str": "5.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.624847, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 68.979}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.649346, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 68.979, "width_percent": 16.956}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.657009, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 85.934, "width_percent": 14.066}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1549839228 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1549839228\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1711623920 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1711623920\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-66302834 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-66302834\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1636447760 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2811 characters\">_clck=erumib%7C2%7Cfwk%7C0%7C1984; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; pos_system_session=eyJpdiI6ImVNL2JmZ05iY3huemxuNWZOWkJqeHc9PSIsInZhbHVlIjoia2taNWtSajhLZmtWVmhqZzM2a1VRQ1ZpNk8zdVZaUHlpUGlZVkRwREY2dy80cVN5Rk55RGZldFJIempxdmFVSGdtRDVQRkxyMEtpNU84THFWSlpJQWVjL1p0MzZnSFBQbTFFanNPcytGU1R1cjV0bDAyQkl1RURud1JKeHBNVjZRcUN6WGF5cDhZYlV0UVBGVXY4dURZYTdIY0dZcHdUb2JPcHM0V3FuSFNHblJLSTVwTlZJazU1SG1pZzl6aGZMbHpkOFRTMnRGWDNKSVVrdWNIa29wcGlUMTFtWTI1bTducU92LzZIcXNFamFDZUVlN1FTVHB3bXJSWHh5MFVEVmYzZ3ZRL0EvNHg4bjVNUWhjTFJPTWlGL05GeDNUbUt4d3NJY29rVXVOVGxka2Fkc1cxR0Q4UGxpWGVGVTVMUnU0UTQ1bXBXcmE4ZGIzTnpuWjNtMnQ0ZEdsbjIzOERhTWpFaHNtbHFLRkZQcW1ZQW1zeXdINmNmMERIWDcyZFhTenUrWklDQllEa0k3TUJkM3Uxd3E5UWhZRU1vT283ODNXU3VSVVBvTFZGK04zVDE0MU5YdXNqZVdjS0VJVi9POVJRRU80dkxsYmRpZWlkYXlwV0g1cEZvTys5ckhNUUZFTThmc2VrQWp5RmhWMTUzdlNYY01henM1aXFRUFZFRHoiLCJtYWMiOiI3ZmVhMjFjNGMwODdjZWJkNDZjNzY1NGVkZDYxODkxZTUwOWExNWIzYTE4YmVmMDhiMzQ4YjkzNjM1NDE3YzY5IiwidGFnIjoiIn0%3D; _clsk=vcxus7%7C1749337562427%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhyK29TazA2dEg1RUdwMXU3WGpWZUE9PSIsInZhbHVlIjoiMnlRMnVvOTl3V0JpSEljaFZRY2Zvb0JnckZyb0lveldERXN3Tk52TnByL1hJVGVab1dnVEhkRVpwWUNUMG1YaDlTQkgvL1pQNnJmcXVSc2tCVWdjM1JyOUhUSThRNHpkOTNtMXJuSURML29tbExMUC93RXFqSllPZUI0N1RqL1laQ0RvQzhDbHRSeWhFRVFCSWpCYzgvSnJPOS9TRzVGbituKzNuNFRBQzhVdmRHRmo2QlBJREthWHYxSy9MM2tJZXN6YldVVE14MG5nRVdZSUd3bnd0d0tQYlJja1BKMXh2RVhhZloyM1hNcUNYYUJEeEhwU1l4QmZKQWtXRFljblVBTDc4bitJZU8yNjlOc2lmWEhYMVpEQ3grRWtqTWFBU0RkVjVla0RwR0YrNkZUZHNQaTVuY0xFUnp4SzJtNDBmUytSRFhZMzg1Q25vNEY1RWVyMjQzRHZrRm01VlZhb1lBb0EzaEJib1Q4RlQwNjJpblY2YmFMMnhhbkhqd0ppS2tTYUh0OVlseHU1d2p1UEZXRzRseVFkSEtZTjdvRTNpVG5BU2I1c0h6SDNtN3M3MExaSXhQeVN4N2N4aVhvYkhOTjJRUkJaZG8wQnkwNmpaejBsVmt6bk91NGhSWVZOZktvVjFGaC9LeklIN2lzZUl2eFU3TGdzQWtpMDN6YXgiLCJtYWMiOiJiYjg3NTFmYzJmNjI4MDE5MjFkNjhlODNjYmQyYTExOTdkODJlMTI1NDQ4ZTBmMjdjNWNlMWQyMjdiZTJjNThhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjNTOEpKV1BleDNZbVhoQ2prVnAzbUE9PSIsInZhbHVlIjoiNVl0bEIvYk1ZQzB0Vmd4Q3ljcDhXVzNzTXEwOXVWbFI4TmF1NWJjdnBoVW0vM25NUVB2dHZ0YVRaNDdzOWpEWko0aFdUQmZST3MwZmJjWXoyRjZaOFF0bjJlVjRpbUJSZmRxRTVObVFDMU4yWVUzZDlpLy9ONGIyMXU4MWRwS09SOTZHd1hRMmkzWVZsc3FybDVlSGZKTENFSFovMFhCRDRZS0NYQjFpNUJ5dm1vWm5sMSswVUJKRDVNV0pQKy9NMkFaVU9McmF0dE8xU0ZtVVp0UXFLSCtVZlR3YVY2eWlMR3NrK2l5N1BhT05yb1F2TFdsQytkdG1sS2lMMnRWUUtHTnc0NStKalN3VGc3NnhzMS96M2tkUWFWZ3ZjNCtJbTZOZ0Mxd0NnRGVidzFubW52R3FzbmR2dGtPU3BCQ3NxUVdCVHREdHlFcno3UG1HdmJsN1J1RmFKNDZWdEVTV3FtaXV6MFQ5ay9Zc2VqcCtQZDVaNHYwdWptZU9kWlQxMzlCSjF5cVNmNDZHNzhVdG1hWkdoRTMvL1d1TVNaaTkxL0FMaGlER1BmNnFKOUUwbElKdXo4YUdiMWcrSjh3ejBRaUsvSjJWTEZWa0VobTY0OHRocWREdTg5bHBYdGJNQXpGVlJSTE84bEQ1NjRwaTk2elN5T1hPQi9GVEdmUGgiLCJtYWMiOiJhNWQxYzI1M2YxOTZiNGQ4YzdjZGUwZGM3YzJhZWYyM2EyZDNkMWU4ZjkxMzM1Y2E5NzNhNjNiN2FiMGU5MDNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636447760\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1099968197 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pos_system_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k8SQqyhwBkgpW9lptEbMoTN8iNDNeZ6DzX94m50O</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099968197\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-541847615 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:06:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtzMjlHV01NQnB1c2gvN1ZHZ3NSR3c9PSIsInZhbHVlIjoiU0tibkZRa3drNVBubnB1N3UyQjFlV0xrR0o3Z3B1S0xicHdaNlhOaTBMRko2WGY4c2FFTnl0ejhoRjFUVDFPd2ZHTy9qWXovVElzU3RQL0NucjA1K0wxMk5QRWwvZVU3dkt6MkRadXR3cnRZVWZ4bFozV2RQVDBKRFhGK29ST2ZJU0tLUkdHdnlHRXVBR05wZGdzZERwTnZsdGdTeUw2OUgvQSs3b25TOUg5SW1ZT2NTODFlNTdnWHRacUI5d09DVHozVkwzY3R2QVQwdXozVE5xS2srL2lQTHRqY2ZEWk5oRTNKRHloZ0xWT0NVc0JjalJQOG4wckZDNEw5cEdrS0hZM0FDTEpjVlFjQTR5VHRtZXQzV2E3WVlNQVdWZE91U2hmQm9xZ3Vhck9oZy9ibnVUQ1BXaHFkdUE0TW5XcVltbE10U0JoMlh6K1JqYlE0QWF0MUdQRlZTcEdGeUJTZm12U3FmNFZOUVJYTkNOWi9HMTFqUFpUN3hxb1NEL2hvTDRwTkJTdzE2T3VVQ2lTZCt1R0hrN25yeHU5aUNMT0dEcE1ianBCUkYzbjZ0WlNIdlZ2dlFrYkFlMk9PZHNXTTVhcUptajBFQkIyOGdUZG94V3VMd1hGL01RM3V0YUVaVjVhVkQ0MHpxRFVrb01QK3k0Uk9LMEZsRk1xUHpaeVYiLCJtYWMiOiJhMDU5NmU3ZWI1MGRkM2FlZWE0NjUxMzUyYTQ4NGM1MzdhM2YxYmNhZjRhY2YxNzdjNDBhMDFkN2RiYWE1YTQyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:06:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImQyNU5vSG5keWVhZkFZOEhDT01IMWc9PSIsInZhbHVlIjoiSHJvNVdqdmthTnB0Z0xzNjNLL3lQNVpMNk9jdVM2Z2RsczdSbUV2YkpaRGlVVDNzcjJQVGkzWENrNE56aXZ4QnFERmJQQUpEWkNQVHNCZXVwTGpwcU5lMXE0YzB5SjZqNUh0ZElCNS9SckprSUNTMjg4dTFBRGdaSEhCTWRxT1l6WXI5M3lYT3NGSlV4blVLTmxxSzBncVhMNmovV0xNQmVDR0Qxb1M1Qmx3UXhnYkptZVFQZ013a0duQmR5TXYzeXJuUDhsSzVoYWR4YUY5OExuTmhmaGNkeHBpQ3VUS2tNRjRnUlNIZ2tENFRpd0htZWd3aGFBWmRzWWt0cXlTUUNxajg2SjF0ZzQ3VkkzWkxEWGk0cGJyY2p2aFVhcHg1VGppbmRIbE5RY3ZENGY2MVpTK0NWYTA1MEFoUWVObXl6WG14Y1BjWFF0V2E2cTB1NmhaUkVjVjIwUVVYZmU5NGI4QmJUdHBYS2lvQVFqSlAvcG9kZWhVMmpCaDg3aTJ5cUVTZ2YwS1E3R2wrT2dZblJIYXhQYVd4U3lpM2NNUkVJVVp4YXk3QkpiLzVpNXNHd0taRmtTTlFPWHhqSGxEc24rVzFzcGxtVWViTlBucFJGL2JTUlVJWjdXdDZFTmVKcHJ0bjI5UFZSdnIyN3g4TXFLR3hMYlhVbk9mbUFpSnciLCJtYWMiOiJjNTVjMGI4YmRiMmFhZGM4ODk1YmRkMGZkMmJmMmI0ZTM3NjMwYjEyZjZjMTA5ZTYwYmQxZGMwOTQzNjczMjlmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:06:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtzMjlHV01NQnB1c2gvN1ZHZ3NSR3c9PSIsInZhbHVlIjoiU0tibkZRa3drNVBubnB1N3UyQjFlV0xrR0o3Z3B1S0xicHdaNlhOaTBMRko2WGY4c2FFTnl0ejhoRjFUVDFPd2ZHTy9qWXovVElzU3RQL0NucjA1K0wxMk5QRWwvZVU3dkt6MkRadXR3cnRZVWZ4bFozV2RQVDBKRFhGK29ST2ZJU0tLUkdHdnlHRXVBR05wZGdzZERwTnZsdGdTeUw2OUgvQSs3b25TOUg5SW1ZT2NTODFlNTdnWHRacUI5d09DVHozVkwzY3R2QVQwdXozVE5xS2srL2lQTHRqY2ZEWk5oRTNKRHloZ0xWT0NVc0JjalJQOG4wckZDNEw5cEdrS0hZM0FDTEpjVlFjQTR5VHRtZXQzV2E3WVlNQVdWZE91U2hmQm9xZ3Vhck9oZy9ibnVUQ1BXaHFkdUE0TW5XcVltbE10U0JoMlh6K1JqYlE0QWF0MUdQRlZTcEdGeUJTZm12U3FmNFZOUVJYTkNOWi9HMTFqUFpUN3hxb1NEL2hvTDRwTkJTdzE2T3VVQ2lTZCt1R0hrN25yeHU5aUNMT0dEcE1ianBCUkYzbjZ0WlNIdlZ2dlFrYkFlMk9PZHNXTTVhcUptajBFQkIyOGdUZG94V3VMd1hGL01RM3V0YUVaVjVhVkQ0MHpxRFVrb01QK3k0Uk9LMEZsRk1xUHpaeVYiLCJtYWMiOiJhMDU5NmU3ZWI1MGRkM2FlZWE0NjUxMzUyYTQ4NGM1MzdhM2YxYmNhZjRhY2YxNzdjNDBhMDFkN2RiYWE1YTQyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:06:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImQyNU5vSG5keWVhZkFZOEhDT01IMWc9PSIsInZhbHVlIjoiSHJvNVdqdmthTnB0Z0xzNjNLL3lQNVpMNk9jdVM2Z2RsczdSbUV2YkpaRGlVVDNzcjJQVGkzWENrNE56aXZ4QnFERmJQQUpEWkNQVHNCZXVwTGpwcU5lMXE0YzB5SjZqNUh0ZElCNS9SckprSUNTMjg4dTFBRGdaSEhCTWRxT1l6WXI5M3lYT3NGSlV4blVLTmxxSzBncVhMNmovV0xNQmVDR0Qxb1M1Qmx3UXhnYkptZVFQZ013a0duQmR5TXYzeXJuUDhsSzVoYWR4YUY5OExuTmhmaGNkeHBpQ3VUS2tNRjRnUlNIZ2tENFRpd0htZWd3aGFBWmRzWWt0cXlTUUNxajg2SjF0ZzQ3VkkzWkxEWGk0cGJyY2p2aFVhcHg1VGppbmRIbE5RY3ZENGY2MVpTK0NWYTA1MEFoUWVObXl6WG14Y1BjWFF0V2E2cTB1NmhaUkVjVjIwUVVYZmU5NGI4QmJUdHBYS2lvQVFqSlAvcG9kZWhVMmpCaDg3aTJ5cUVTZ2YwS1E3R2wrT2dZblJIYXhQYVd4U3lpM2NNUkVJVVp4YXk3QkpiLzVpNXNHd0taRmtTTlFPWHhqSGxEc24rVzFzcGxtVWViTlBucFJGL2JTUlVJWjdXdDZFTmVKcHJ0bjI5UFZSdnIyN3g4TXFLR3hMYlhVbk9mbUFpSnciLCJtYWMiOiJjNTVjMGI4YmRiMmFhZGM4ODk1YmRkMGZkMmJmMmI0ZTM3NjMwYjEyZjZjMTA5ZTYwYmQxZGMwOTQzNjczMjlmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:06:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-541847615\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1219613879 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nGvH7PAdnQSQFvqDnEAqIqmomRwl7awlTV1H21K5</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1219613879\", {\"maxDepth\":0})</script>\n"}}