{"__meta": {"id": "Xda231f8453f95bf140e7b6f7810e9569", "datetime": "2025-06-07 23:27:02", "utime": **********.893981, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749338821.962857, "end": **********.894011, "duration": 0.9311540126800537, "duration_str": "931ms", "measures": [{"label": "Booting", "start": 1749338821.962857, "relative_start": 0, "end": **********.775763, "relative_end": **********.775763, "duration": 0.81290602684021, "duration_str": "813ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.775786, "relative_start": 0.8129289150238037, "end": **********.894014, "relative_end": 2.86102294921875e-06, "duration": 0.11822795867919922, "duration_str": "118ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043592, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.015439999999999999, "accumulated_duration_str": "15.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8340368, "duration": 0.01343, "duration_str": "13.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.982}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.865103, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.982, "width_percent": 5.246}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8784811, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.228, "width_percent": 7.772}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/debug-product-creation\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2047741081 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2047741081\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1163747511 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1163747511\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1610273833 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1610273833\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-727626746 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/debug-product-creation</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749338815716%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNheFNPTmpSNjhDT2VzTmZxV2FjREE9PSIsInZhbHVlIjoib0hMZHh0UU9qOGhiNUN3UXZHSWlYQ1A3T2JkNmwzeDNGcEZ6akFHYkk1RW1CWkxEMXVGU3MwWVJqdEtEWHRMejVsWkRRdmZLeUxpYzJ3QlRwS2NET1pXMEFVaG50ZUxTb3dyRTYyc0RnSXVJc2gxb1hhQncyZVlHekxmczBqTXgyTUNQWWYwTXdFTzRBVThSQXRPVGFFMTFVN092cUZhcjU3Rm10YmZ3K2xneCtxQnV5UzJrOVZxaWdUY3BJQlVxWHVaUkJNbWNtalI4a1RyRm1wNmg4UUNVc1cyRTQ2dVpNdTA2dmk1Qks3SDBoMVRJSi9xdmxyc3U1TlhYdU9MN2RYTEJiNzBDTFhzWXRNSElzT3NVNzloUHd3V3B4MXNZUG05YWNhbUx1TFoxRlliRk90Tyt6dmpyTDZ4NjJxYlQ1L0tTQ0cwbmc0UjlJaGZRRU9PMjlSNU1YaUhiWlQ3UlFMcEJRUGtOMjlIYkorSXJuQnhUb0FGbFB0Um5scERQVElyczM1SFZ0M0VSRE9haEN2N0RyZE05cFhrVnZ3eGZaOE9FQlcwcEIrS1NIZndYYk01KzBFVVNuTU1xZElJL1pGSlNxYkp4d3pHUCt2alphUGlncGZ2bnFPNTM4VHFQanFFQjBBSkErVlozdlpJdXdrSnhOT25TUStyY1MvU3UiLCJtYWMiOiIyMjk5ODBhNzVkMDk5MWZmYWYzMDU3MDg2ZmU0NGE3ZWFmNjcwZmI4ZjZmMWI4ZWRhZmZlYTBmZmE1NWE5NDM3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InNCSTZTV3BCcFJZWDNrODNrbHdYMnc9PSIsInZhbHVlIjoiNnkvQ3QrcWs1S3czL1ArZjNFZnNxbDhGYVhSSWhseTNjbGptM1FhZkZGcCt0ZS93NGhnVllOMTJuVWRUd3NYMW1UOERVT25lWWpYNmdhS2FqWEV4NzFSY2MvQmVReWRsRmdhT2pjOCsxRWVNVzZkb0lDWHBMZXgrbjgydWNxU3pRY2hsckxBK3doR202S2VXT3g2QW4wcStPK21EK3dSNC96ZzVaSGZZZk50S05MMTRjUk1SLzRLb3NubEc1WjlZaHkrbTJvZWJsVlJrdG5FNDZ0cFo2eGtZRXB6dHExSlkvVVN2ZlFWRDN6ZWxqamxDUEFZeFFzbHcwc1JKY1g2VFR1UVBBSWVoSmpUOERRSTYvakpzVWtRbmw2WER3Z3pSWFl3K2krYTRZdTRLbDlZWlBjdlE3dWZoQTdhbW1VS1VoRlo3R20zRHhKdGl4SGk1Yi9sR2tLam9CbENUQ0lxeUI5REJuV1JaeTlIckVZWEJjdmpXclh0c1VCWE9KeTNzSmZNd1A3Y1p2bTF3a2FSMEdrNEVobUhhSjVYZmxCMlpaem95YncyMFExbkJadUh2dzJ3eEloQXdsRVlCZGUzTXRaMEEvaE1TK05PYVJtbkpWVnFUZGpoOGlMblhPb29oOU8xbDF3aXQ0U2VVcFlWdFJtaVl5ZGhqQnczVkRDWVMiLCJtYWMiOiI2MWE2YmI0YzgxNTk3OTc5ZTY1MDUzNGE4YWVjNGEyNjE1NzE4NjFkZjEzYjM4M2ZmMTU5NzI4ZGYzNTQwZGM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727626746\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-526077630 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-526077630\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1755418183 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:27:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNqNjgwOGVjVHJrcjFtekJnQVJXb1E9PSIsInZhbHVlIjoibGJnOWQzYkpxSllQZzRUZGlPRXFHK0E0SHY3NFBNakQ3TitvdEhLRWdTeTBWSGtUSFp6R3E1M3Q3bTRsVExCS1VxZE5JWmZoWGVpTTVXOG5XaUVESlZ4S1NMK2h6dFRGcklWY2d4anlxSDBHZ0E4K2lFSVVCQUMyNkV3UTVwZEM5aVBqQ1NtQmIzclpqSHhkajg1V0pyTEtZalo2YWw2eHdaZWYyYUt4bFNWVlQ5VlFDczlmRXpEbUhTcklSeUlYU3ZBTk1hbjdFS0RCdWF6Wk5GS3JsK3h3OS9LZldHUHlocC9LT25Ddzd6M25VV0s0bG9WM0hkK3NhczEzVXhnMnl5WjV2bk1JTGhzWEZmNDltOTUxZTFlMGRYcFN2bHZEQ21TR2VlcHBDSVJVQlJHNE9jS1pTVnQ4VEtNa2NTbWZNU3lLSmZnVXZwV3Y2d1hiRnRKdjlJV2lMU29PVnBGdnpjTzhRb2c0SHpUbzkwYW1oMnc2S1hLRC83Y2NUbmlYS2UxU3JzRVlUTndLV3c2RlVKWFhXdncvMFNTMDJhSzI0LzFOM0xlbUVDSGpYQjIrTkE1T3hScmRHQm9zUDBtOHdKSVJyODNmMTlNZFhXTDRjNHl1Qm04K3hKTGVtVUdzV3NzTWdyVjJYMjVvdUlibnR4WTZSQThQZ0hzUFdzTGIiLCJtYWMiOiIzNTkyNWUwYzQxZmU0MWEyMzc2NmNkMjlkYWEwYTZjMjE1MGNhNDY5MGJmMjg1ZjZlYzEwNjFjMzY1YzhhY2E0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:27:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlB4dTE2OHNSa3ppTnZ6QkVOclI5UEE9PSIsInZhbHVlIjoicFFLbW1kOTNyRmZxN1FkV1ZSV0VyeVgxdzRtM0VPd1JFOVlhME44bE9RY250OHJNaVRrNjJ0NW1Bd1BmQ2dSQnZxZ25YT3hMMzJiTE1yWXpJa3FYanFGcVZNcG9aSkdPQUM5Qy8xdVhXd0tIRUhNcjlyQW9FL0ZabTlYNU95aytwZHBLMytxZFM3Tkx3NkszT1BLVTAvZjlEZDVyVjRpRDNUWGZva2lybTJvV1E3Q0x2RWpwTEtHQXYraE8wN2ZGdUZreHMxcGxZai9tQmZFVDd4ckQ4MjZYNFgwL3VKWUdZS1Erc1lsNTNZcCtHdVRpQ0YrZHlScndwUTJIVjdsdncwSERBUGUzQjJPRGVQRzN5eVErbEdJc0ExSEZUZjg3VFRUREtZMEQrM3c1aXNmV21UNk9CTG4zZUdCSHFBRXkxOVN6NnZyWnZCbHNraEdWdndPbDdGYm1aMi9adUNXWHp0alhxWWhjV05mZTBQdUlIY2JkNlhCQU1CcWlTaXp1OEJYallJN0g0djRkc3BsRXBDWFl0TFhBbmNoY3hiTVE1WGlQTHhEME9wYTQrZXBqeS9LaDhtTXVDcG9McGhnbDVhQmVLU3YrSHZuZkVsUFZYZmQrWEFVc1doWUZYeEMyRG5JNS9CU05qQXU1SDMrOWFGWVJzZituZ2JEUW9RYmkiLCJtYWMiOiI4YzM0ZmZmMmY3NTMwODJkYTA2NWYwYTE5ZmQ3YTgwZWQ4ZGYwNmEyZGUwZWMzYzYwYTYyZDQ1NjVjZjQ0YTgzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:27:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNqNjgwOGVjVHJrcjFtekJnQVJXb1E9PSIsInZhbHVlIjoibGJnOWQzYkpxSllQZzRUZGlPRXFHK0E0SHY3NFBNakQ3TitvdEhLRWdTeTBWSGtUSFp6R3E1M3Q3bTRsVExCS1VxZE5JWmZoWGVpTTVXOG5XaUVESlZ4S1NMK2h6dFRGcklWY2d4anlxSDBHZ0E4K2lFSVVCQUMyNkV3UTVwZEM5aVBqQ1NtQmIzclpqSHhkajg1V0pyTEtZalo2YWw2eHdaZWYyYUt4bFNWVlQ5VlFDczlmRXpEbUhTcklSeUlYU3ZBTk1hbjdFS0RCdWF6Wk5GS3JsK3h3OS9LZldHUHlocC9LT25Ddzd6M25VV0s0bG9WM0hkK3NhczEzVXhnMnl5WjV2bk1JTGhzWEZmNDltOTUxZTFlMGRYcFN2bHZEQ21TR2VlcHBDSVJVQlJHNE9jS1pTVnQ4VEtNa2NTbWZNU3lLSmZnVXZwV3Y2d1hiRnRKdjlJV2lMU29PVnBGdnpjTzhRb2c0SHpUbzkwYW1oMnc2S1hLRC83Y2NUbmlYS2UxU3JzRVlUTndLV3c2RlVKWFhXdncvMFNTMDJhSzI0LzFOM0xlbUVDSGpYQjIrTkE1T3hScmRHQm9zUDBtOHdKSVJyODNmMTlNZFhXTDRjNHl1Qm04K3hKTGVtVUdzV3NzTWdyVjJYMjVvdUlibnR4WTZSQThQZ0hzUFdzTGIiLCJtYWMiOiIzNTkyNWUwYzQxZmU0MWEyMzc2NmNkMjlkYWEwYTZjMjE1MGNhNDY5MGJmMjg1ZjZlYzEwNjFjMzY1YzhhY2E0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:27:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlB4dTE2OHNSa3ppTnZ6QkVOclI5UEE9PSIsInZhbHVlIjoicFFLbW1kOTNyRmZxN1FkV1ZSV0VyeVgxdzRtM0VPd1JFOVlhME44bE9RY250OHJNaVRrNjJ0NW1Bd1BmQ2dSQnZxZ25YT3hMMzJiTE1yWXpJa3FYanFGcVZNcG9aSkdPQUM5Qy8xdVhXd0tIRUhNcjlyQW9FL0ZabTlYNU95aytwZHBLMytxZFM3Tkx3NkszT1BLVTAvZjlEZDVyVjRpRDNUWGZva2lybTJvV1E3Q0x2RWpwTEtHQXYraE8wN2ZGdUZreHMxcGxZai9tQmZFVDd4ckQ4MjZYNFgwL3VKWUdZS1Erc1lsNTNZcCtHdVRpQ0YrZHlScndwUTJIVjdsdncwSERBUGUzQjJPRGVQRzN5eVErbEdJc0ExSEZUZjg3VFRUREtZMEQrM3c1aXNmV21UNk9CTG4zZUdCSHFBRXkxOVN6NnZyWnZCbHNraEdWdndPbDdGYm1aMi9adUNXWHp0alhxWWhjV05mZTBQdUlIY2JkNlhCQU1CcWlTaXp1OEJYallJN0g0djRkc3BsRXBDWFl0TFhBbmNoY3hiTVE1WGlQTHhEME9wYTQrZXBqeS9LaDhtTXVDcG9McGhnbDVhQmVLU3YrSHZuZkVsUFZYZmQrWEFVc1doWUZYeEMyRG5JNS9CU05qQXU1SDMrOWFGWVJzZituZ2JEUW9RYmkiLCJtYWMiOiI4YzM0ZmZmMmY3NTMwODJkYTA2NWYwYTE5ZmQ3YTgwZWQ4ZGYwNmEyZGUwZWMzYzYwYTYyZDQ1NjVjZjQ0YTgzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:27:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1755418183\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/debug-product-creation</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}