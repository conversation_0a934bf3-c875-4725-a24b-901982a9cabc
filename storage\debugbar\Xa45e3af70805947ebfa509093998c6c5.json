{"__meta": {"id": "Xa45e3af70805947ebfa509093998c6c5", "datetime": "2025-06-08 00:08:55", "utime": **********.643083, "method": "POST", "uri": "/inventory-management/update-min-quantity", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749341334.986222, "end": **********.643105, "duration": 0.6568830013275146, "duration_str": "657ms", "measures": [{"label": "Booting", "start": 1749341334.986222, "relative_start": 0, "end": **********.513404, "relative_end": **********.513404, "duration": 0.52718186378479, "duration_str": "527ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.513415, "relative_start": 0.5271930694580078, "end": **********.643108, "relative_end": 2.86102294921875e-06, "duration": 0.12969279289245605, "duration_str": "130ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46703832, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST inventory-management/update-min-quantity", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@updateMinQuantity", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.management.update.min.quantity", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=205\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:205-242</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.019989999999999997, "accumulated_duration_str": "19.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5565732, "duration": 0.0159, "duration_str": "15.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.54}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5842469, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 79.54, "width_percent": 3.052}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '5'", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.596434, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 82.591, "width_percent": 3.452}, {"sql": "select count(*) as aggregate from `warehouses` where `id` = '7'", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.6008239, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 86.043, "width_percent": 3.052}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 214}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.61223, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:214", "source": "app/Http/Controllers/InventoryManagementController.php:214", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=214", "ajax": false, "filename": "InventoryManagementController.php", "line": "214"}, "connection": "ty", "start_percent": 89.095, "width_percent": 0}, {"sql": "select * from `warehouse_product_limits` where (`product_id` = '5' and `warehouse_id` = '7') limit 1", "type": "query", "params": [], "bindings": ["5", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 217}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.613776, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:217", "source": "app/Http/Controllers/InventoryManagementController.php:217", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=217", "ajax": false, "filename": "InventoryManagementController.php", "line": "217"}, "connection": "ty", "start_percent": 89.095, "width_percent": 7.454}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 217}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.621195, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:217", "source": "app/Http/Controllers/InventoryManagementController.php:217", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=217", "ajax": false, "filename": "InventoryManagementController.php", "line": "217"}, "connection": "ty", "start_percent": 96.548, "width_percent": 0}, {"sql": "insert into `warehouse_product_limits` (`product_id`, `warehouse_id`, `min_quantity`, `created_by`, `updated_at`, `created_at`) values ('5', '7', '4', 15, '2025-06-08 00:08:55', '2025-06-08 00:08:55')", "type": "query", "params": [], "bindings": ["5", "7", "4", "15", "2025-06-08 00:08:55", "2025-06-08 00:08:55"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 217}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.621638, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:217", "source": "app/Http/Controllers/InventoryManagementController.php:217", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=217", "ajax": false, "filename": "InventoryManagementController.php", "line": "217"}, "connection": "ty", "start_percent": 96.548, "width_percent": 3.452}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 217}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.627158, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:217", "source": "app/Http/Controllers/InventoryManagementController.php:217", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=217", "ajax": false, "filename": "InventoryManagementController.php", "line": "217"}, "connection": "ty", "start_percent": 100, "width_percent": 0}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 228}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.633662, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:228", "source": "app/Http/Controllers/InventoryManagementController.php:228", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=228", "ajax": false, "filename": "InventoryManagementController.php", "line": "228"}, "connection": "ty", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/inventory-management/update-min-quantity", "status_code": "<pre class=sf-dump id=sf-dump-19635696 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-19635696\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2048790242 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2048790242\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1555561001 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>min_quantity</span>\" => \"<span class=sf-dump-str>4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1555561001\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-702430383 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">90</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749341329306%7C29%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImN3Ylh1NERWdFloWlVNU0c3Rkt4TVE9PSIsInZhbHVlIjoiSXlOVGI0aytkUXBnNFd6N01iZUh3UmxNeW5CeVJxRkp2eVQrTkREZDJTc2xQS1N4VTU5SWRkcE02TWZYbDlyWXZCWWJrYU9wYjRLZUxXK3Uza2RLZFJucFhHV0UycXlUTWZFZDc0ejdib0NnRnRXV3pWbjRuRUtJbXRITGFPK1ErL2FTNXF1WktwYmRIRms0dWZaYmtxblRqUmZVbjZiaEQ5c2ZjNFVMTi9JT2RPekNOWW82emJxODUwVE1RRTlnQ05vdEZNcU80VjlpNHd0UlB0Wk5zd2ZadnJ5MmZjd1kyMUVnazZGaE9Zd0tsUUtBdFVPZXRCRkNGNkF4dGxWQmJVa01taXBva2I5Ui9nYlI1MkhQdWtOdi9hMCtTUTZaT1BZS1NiRUVPWUNUaGd6aDIwa292T3lqZTRZeDFXNk5IOFk4Wjh6VmdGbFhqb3VKMVpqOWtlWXEvbHpGTzVndERDdndvRk5Ia3kva0RYbThEQjVWQXl0R0R2M01zeVlFUDhBRnZNUnJJVmlQZkd4cGdRRUxjRkd0VVhNa2gzZmNJYnhMQkMrbXlFYm1JL05UNmppeXJ1U3RQV2k3SmlVOVJLU0dhaU4xUStsbHV4OEI0QktjVVFhUTA4S0xjZGVCSzFUVzhwSVlSYjdQUTIxd3FHNlhibUp1ODI2V0hGLzgiLCJtYWMiOiIwODU2M2VhOWNlNmRmOTA2NGY3YjEwZjY1YzM1MzJhNzU1MDk1NTU5NjYwNzRiZDQyOTkzYzg2MDFhZDA1NzUzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikp2MURBM283S0R1N0hBdWp4Q2dVSWc9PSIsInZhbHVlIjoiUlVOTG5kUkNLZGNrenk4OFBKaEFlNkptTXpaY2p2K3ZKY0JQVEcrQ1gwODl6eEsyTVREWEhzb1hrR3ZPUWhWeHhOTXRQVWNjRXhRc202aWFDTDA0SEhwbDkrbXhTdEZiZVFFSzRIQUJoWlhCSzZmNmo0RXhhWVdnQkxiMGY1Y1cvRTRTbWZLTngzTCtPT242OGN2YnZxOXdyMnZDWi9lVlJSR3A3SUtPbFBBR3pBWWx2UkljS1BlWWFJR3J1cTdvVzFobXRudWt5YWhZZG8wOWUrenNSTHJ3czBJM09JVkVJQXdiM3pPeW9VeGhaVEVhRDE3cVJaYTFWaGlXS3dHaUlhT1MrWTRsVWcxV01paDBuREt4ZWlrZUQ3YWFlRmx5aWVVMUdUYVlnMmtJTGppdTEyQ2VJYW1TUFBiN2RmNm1yeDdiUE0wZXBjRHhUYUgyYkZFK3NjV3lxTlF3RXlwU0xzRi9iMjFSTFV3S2JvUkhNcUxHWDFaUVVNYkFBdk4wdk9ubXhVRGRsVjdvMWFNTE1SUVY0MS9la09zTnlEcy9adEpqTlRiVDFVamUxdjFhR1Z2c2RNb3lmeG1tNGRtbTdFaXM1K1pBYktXZ3hlQlBxMGhGOEFhTkt0U3FXZTNxN1FLUzZGRHVPeDZGTXNad1BEZ3ZIOXZvMGtDblNsUGsiLCJtYWMiOiI1YjgzZDNmYjAxYzhjMzZjZDhlMGQwYzVlMGU4ZDU5MzE1NTQ5MmNmZmJkMmJhN2MwMjI5ZWQwN2Y3M2I4MTQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702430383\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-349258265 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-349258265\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-300629085 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:08:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImR1UTNlOXBtazI4YU9rNW1FemhKbUE9PSIsInZhbHVlIjoiY0QrRUpCMmF3QXliYVlFSWpKczZVOGNoUFBOb29oOHU1eCtUQzhYVjFSNlZPVnVpcCtleHdlR2ZNUm5aUGVZWG1Ub2cyY2dOS0paQkRBdmRJbG1iamxwT2NieElQRjFpL2lQOVFwTE9IRWR4V1FCNXRBUXVlUGRkRS9qNXpOUm0zZHpQZTRrZEZOWXQ4VWVGNElFTXpMWVBDT09raVhnSjhlZHlmU2JVVXA0ZURuL2xBNDEzSzJZMEkvRnhUQmlJSEFQTGZJYUFXRkxOZmsrL3ZNZTBPZ1YxdjNVNUtSN1ZHQTYvQkF1YnFqNUpGUXhMMllvb2x0WUw0aFUyeEJrdllEMEJyemtVOGtackk2U3hPWGtHVHhtZytPQ0ZSY2pGbndRTG9EOVhLN0RzcnE4TXA2am5tRXJWL2w4OXV6N2JTZHFIdkFuYVhyMkErWWdJdnFQVElOS3FXS1IxY0RQVUtvK0hHUFdUc2ZsazF2TGVXaTRZQzhXVXIwYk00T1RDWUtsZkorWHczL0VwcFdUeGpac2ZteDVWUEJzcm96dTgzT1BLWjdPdHY4VGJoWU95U2o2T3J0ckJqVlplUG9jUElURTFsMGx2UUM0bTAvT09ONFY3dHRSeDlVYitNNk5CZ2tWU2tiQ2VNRVYvSjJHanNsQmdZV3ZJSXd1RjhPajMiLCJtYWMiOiI3ZGVkZGRiZTJmM2VlZDM0OGE4ZWY3N2YzY2QxNzhjOTM4MWI0NTBkYmU4M2Y2MTU1ZDk4Nzc5ZDVhYjg1ODVkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:08:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlVoOXYyZlA2YVFRSEZTa05mY215Vnc9PSIsInZhbHVlIjoiQmJqTEpuemVlc0VqdTRmVUpCSGNFbWxyakVnTzFEc0FGU2tnNmI4K0xnWVVpMjVEeDMySnl2aDViWGVzL1p2Nmg1QjhSSXB6U01wRFJFWHc1RVduNnp3VytUQStEVUh2L2dNNzRyMHlLR2pWUnVKQlN6dklKYzhDOTY2UHd3YXZ6dU9CSDJMV1k1eHRJRUwwWE1kdytYQk1rSDRzQk05ei9Hb3BSS1YwaGJWeEpsT0wwSG95TDR6b1FTZ1RkZFYvMXRncUFBMU50bUMzd0NyS0hDaUpRNjZJZXJaTGlWVzVKLzVFbk1Xc2xubTNLbENjcjFhOG9UQ1VLSGxtbWF5STVpUGI3Q3NSN3BoM25oK2J2aFd6aVIwSWRRMTNrOXp1WW95YndDeXZEWFErZFV2SDVlSzlDeVNFUnhJa01zVjRETVd6RHFRb0xGWlplelQxeTZBMXpGNVJiR0FtTVl6VXBZdDQ0eHR1dENHVTJWS2VTV1hzOEVGMUd0amtSeHJqZHd6L3ZuRHk1N05RdFR2UUhheFpPNzVILzlTdnRteWVaYkxvWlJHR29oaUdTTDl3SjVhKzh2ditRR09ValFZWGtNTDFsR2gyY0ZCcm1PTGx0eDk0dHpMOHZ2dFZSRExVZkdVYm9hYjIyUnBpTnNiZmxheHE2Ti9naVBIamhoakoiLCJtYWMiOiJlZDhmZTg0MDkwZjc5YTBhNWY2NDJlODE1MDA2OTE4ODAzY2Q3NWVhMWJmMTkxZWQzNjUyMDgxYjA2NmVkMTExIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:08:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImR1UTNlOXBtazI4YU9rNW1FemhKbUE9PSIsInZhbHVlIjoiY0QrRUpCMmF3QXliYVlFSWpKczZVOGNoUFBOb29oOHU1eCtUQzhYVjFSNlZPVnVpcCtleHdlR2ZNUm5aUGVZWG1Ub2cyY2dOS0paQkRBdmRJbG1iamxwT2NieElQRjFpL2lQOVFwTE9IRWR4V1FCNXRBUXVlUGRkRS9qNXpOUm0zZHpQZTRrZEZOWXQ4VWVGNElFTXpMWVBDT09raVhnSjhlZHlmU2JVVXA0ZURuL2xBNDEzSzJZMEkvRnhUQmlJSEFQTGZJYUFXRkxOZmsrL3ZNZTBPZ1YxdjNVNUtSN1ZHQTYvQkF1YnFqNUpGUXhMMllvb2x0WUw0aFUyeEJrdllEMEJyemtVOGtackk2U3hPWGtHVHhtZytPQ0ZSY2pGbndRTG9EOVhLN0RzcnE4TXA2am5tRXJWL2w4OXV6N2JTZHFIdkFuYVhyMkErWWdJdnFQVElOS3FXS1IxY0RQVUtvK0hHUFdUc2ZsazF2TGVXaTRZQzhXVXIwYk00T1RDWUtsZkorWHczL0VwcFdUeGpac2ZteDVWUEJzcm96dTgzT1BLWjdPdHY4VGJoWU95U2o2T3J0ckJqVlplUG9jUElURTFsMGx2UUM0bTAvT09ONFY3dHRSeDlVYitNNk5CZ2tWU2tiQ2VNRVYvSjJHanNsQmdZV3ZJSXd1RjhPajMiLCJtYWMiOiI3ZGVkZGRiZTJmM2VlZDM0OGE4ZWY3N2YzY2QxNzhjOTM4MWI0NTBkYmU4M2Y2MTU1ZDk4Nzc5ZDVhYjg1ODVkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:08:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlVoOXYyZlA2YVFRSEZTa05mY215Vnc9PSIsInZhbHVlIjoiQmJqTEpuemVlc0VqdTRmVUpCSGNFbWxyakVnTzFEc0FGU2tnNmI4K0xnWVVpMjVEeDMySnl2aDViWGVzL1p2Nmg1QjhSSXB6U01wRFJFWHc1RVduNnp3VytUQStEVUh2L2dNNzRyMHlLR2pWUnVKQlN6dklKYzhDOTY2UHd3YXZ6dU9CSDJMV1k1eHRJRUwwWE1kdytYQk1rSDRzQk05ei9Hb3BSS1YwaGJWeEpsT0wwSG95TDR6b1FTZ1RkZFYvMXRncUFBMU50bUMzd0NyS0hDaUpRNjZJZXJaTGlWVzVKLzVFbk1Xc2xubTNLbENjcjFhOG9UQ1VLSGxtbWF5STVpUGI3Q3NSN3BoM25oK2J2aFd6aVIwSWRRMTNrOXp1WW95YndDeXZEWFErZFV2SDVlSzlDeVNFUnhJa01zVjRETVd6RHFRb0xGWlplelQxeTZBMXpGNVJiR0FtTVl6VXBZdDQ0eHR1dENHVTJWS2VTV1hzOEVGMUd0amtSeHJqZHd6L3ZuRHk1N05RdFR2UUhheFpPNzVILzlTdnRteWVaYkxvWlJHR29oaUdTTDl3SjVhKzh2ditRR09ValFZWGtNTDFsR2gyY0ZCcm1PTGx0eDk0dHpMOHZ2dFZSRExVZkdVYm9hYjIyUnBpTnNiZmxheHE2Ti9naVBIamhoakoiLCJtYWMiOiJlZDhmZTg0MDkwZjc5YTBhNWY2NDJlODE1MDA2OTE4ODAzY2Q3NWVhMWJmMTkxZWQzNjUyMDgxYjA2NmVkMTExIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:08:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-300629085\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1978526544 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1978526544\", {\"maxDepth\":0})</script>\n"}}