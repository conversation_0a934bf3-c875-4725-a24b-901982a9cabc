{"__meta": {"id": "X9305dd1be3417195ed70d1492a4f24a8", "datetime": "2025-06-07 23:22:08", "utime": **********.844035, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749338527.761318, "end": **********.844074, "duration": 1.0827560424804688, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": 1749338527.761318, "relative_start": 0, "end": **********.708759, "relative_end": **********.708759, "duration": 0.9474411010742188, "duration_str": "947ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.70878, "relative_start": 0.9474620819091797, "end": **********.844079, "relative_end": 5.0067901611328125e-06, "duration": 0.1352989673614502, "duration_str": "135ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00764, "accumulated_duration_str": "7.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.780109, "duration": 0.00511, "duration_str": "5.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 66.885}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8089359, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 66.885, "width_percent": 12.565}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.824811, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 79.45, "width_percent": 20.55}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/support\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-809206566 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-809206566\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-347913006 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-347913006\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1929070767 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929070767\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1430110828 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749338519557%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdYY05xaXYrSnhtMVhiZDByU2tDT0E9PSIsInZhbHVlIjoiZHdnMEVDN01nalU1dTFRUk5YQ3QyRjBOVk9zYXdOZWF4RzRtWVdPUUlBcnJYMExNQmdKSmRZVFdoQm8xRmZwZ09xOFJha1U2b2lxWlBGUWdVcE1Fa09jUStMV1lVRnA5cHZjNFFvUmYydDZBVTZ3ejU5Z3FwU3V3OWVwaktMd1BFM3ZmY0tXZkl4MUJteDBqYlVUMVJrU1dJRFcrRlRiVW5pWEsxYWY1azI1azN0eXdHWThRQmp3aE0rbEpDcVgyTkI3WW5ST29GdDRZOHJ3cnRWOUloRmRnZjZ6OS84bGR3Q2lMQVFNbzJENGtrVmQxU04zQzRId1NBWEpNMEJiN1gyVGI4SzkxSTNzY2VLM2JOVmQ1eFdjRnpBNi9ibnZSbW5vYjJDNDIzUXIzMjNBN28vcDdNdTY2NjhNVVhaNWMzVlA0clZUWUtKcG5OWVp2ZFhWRzFxaTJyMXl0YTFyUUM4QzlmVGNEd2ZDWVl5VjN1QmVjRzE3V0JBUzB6eFgxN08raHhIc0JYKzlhY2svTThOVFpyYXZKaCtTVjZZZmgwMnhhYmtadk9oMHExWU5GL1A4WTZDVDNjanNKSk9vbkRDaWo4ZVNCaGRWWHgxNHdMY3BpcDBxU1RJWVlZang0cGtKYzk4Rlhkajg1Uyt3NDNOWFF3cDZzTFhDSjRLU0ciLCJtYWMiOiJjNzYxN2I0OTMwNDBjNjIwOWY1MmE1NzllZmVkYmZhZWFkMWM2NTJmMmQ3NjEyZGJhZWJlZDRlMTEyZTdhZDljIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IktIb2REQ3paNFBzdUEwNTVpcFo2ZUE9PSIsInZhbHVlIjoiUVVBQkFFaWpUSnVaQVZMLzNIWmh2WllpV3BKZSt6RHE1UEdVNnpnU3JIQ2d6aDJWb0R1bStZNEJqaFhmckhSWUxGOWJ6VFBicTZIWTZsRkc2OCtqUkIvTDdsZkdLbHl1TXJURmlubU5LL3QwRFA2MjNCeEYyeW92cm51Nnl4VWhZQ1VKRmpJN1UxREd1bUcvWGNuZ1lJU1hIa29lNm9TK0pNbGlTTG5jRHB6aG9vWFFwM1FIdDZ2UURFNjVYWTROaGtWaXkrOFlQMlIrTmZwWmZXajFTelcxdWp5U3FwZUJQTEQ2ZVRTbTNCVFljYTUzM0FPSWlvVzhxZFUvazdHdlYyN2tvdFAvek9DUnZ0eHlSWXNYZEFROSt5Uk1kZEpvTk1UWDFOM1lOZ3JDeEVFWVMxeFc3QndicUh5bzlKbStxS0JDL25uK0ZmRVdpSlkwT0dSVXlJbjhLSzZtNnp0ekhURzl0ci9OU3doTGdaU0hIaTYvaDVIcVF5UWM0SkhEY0tUV0Z5ODRnQjBaQldra2JLMlpnVWorM2l4dVpUVkZMbzRPdzE5N0thOXVUTTRURnVNZmJENHlBOStUVDRNYjh5RFVQWWVZRXUxcmd3VHBzS2pHUE15K21WTGZZdFVIM2FsOC9WNGdhaWZzam12TGFKL3J1Zmh3L2tzV3N4RGIiLCJtYWMiOiJlYmQ1MGRhYmIwZmNmNjQxNjZlYjcxYmEzNWJkYzAzOGE0MjFjYTljMjc5YWQyOWJhNDlmNzg5OGM1YjFiNzQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1430110828\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-622750905 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-622750905\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1061563362 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:22:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpHZ0xSQk9TR0JsRnhuNWplY3gzTUE9PSIsInZhbHVlIjoicHBQbm1PZk5iSWNuKytpYVVReDdrNjNFYjV2T1BWWkRuVytlKzJYMnRBYUZ3c3JSSEt6WHNaa1ZCb05TV2hTYjF1Z095QW02ZllhU2FiclVJc0YvVXN3THplT0ZKaUdNd1pvWFRzVWZ4REY0RStieEhNSzVLV1FvSm1DZnRXNFNPSG5DWnlLemFackdWaVRxc1NxY0h2dEQwWUh3UFZscjAvRUFkdzlyWXFJVXBPVjZMVGZYSFptQit3OEFBa29FcWc5WHJKVGJ5UGZwWkh4eFhwK1lkYk1HSkE5SDM0VmE0YTl6UFRyVzZvSXN1NWFsOUh3N3Ird2xlalM0V09kdUtEZDNFOU9lMHkrVFpuaVkzbCtpTzgyRlVhOWJhQTRBZjhFSk12RnRCYTJOanZZNENHRStkY1J6eUNsZUQ3VlJza2hoNmh0ek5aU1lSUWllNEMyS3JkbXJmWW9TWFVxTjZTeThLR1o3d0ZMZUEvS1VHWXRZejVBa3hKUjcwQ0t1VmNDcVlBVXM0YnNablpJZFUyWWVweFJGYmF4Ukh4ZVkzU040TGV0YUNWMmJzd1RYRlZON01zdjgvWmZWMUV6MDlEOWY0TlJhTlBvRTdGc3pEUkI0TG9nVDVEUUNXUWpha3puMkp6U3FFZkVOSFgvamU0aWVCVUE5K2FXZi83S2giLCJtYWMiOiI4OGVmMmFmZWQyYWJjMjAwNTAwYmM2ZDdiMWRmZTY4Mjc3Y2I3ODNjOTZjYWI5ZjVmYTZlNTg3YjdiN2IxODg2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:22:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklBL0IzNUtJa3FlZURjNDU0UDRKQXc9PSIsInZhbHVlIjoieVBRMTFON09QWkhHUmdha3l1UlAvQXBzUUU4QU5vWTZlcDIvUDNjLzFJRjdHR0YrZVVCbTdzL2RpbnEweHZlQlBUREprRXRuYUxSamk5UmgvMjRHWDhsLzRpcWZ6cjBqTWNVTGpJNVE4czhXckxoSTFzRHFYaXZPTHN0Z3NJSENzOHhESUJnakpURUY2TzlMUlE5TlR0MmVWc3htN1BDVUJXTExnMHQxTFVEQ2kzMzdtOWFBTElTNm52N1dBdnN2bjRVWHovTENOM29mMFhEMjJCcWJqL3NKK2ZJWVVOdERYZElmOHRBODZ1aG5qTTVVTmozWU9PcG1pUjFKS2FQenNGSkhpMEoycmFmSkZrMC82a0FGL0hVZFY0YXJqU1B0NjdVSEVFUjZwYmYrRGtnOW9mRnltSzVWWndDd0lyYnhUSTlDNngrZTNhb1UyZGIrNWszeVpEZnFMY0ZNL0d4R3RyVTIzRXRCWXB1TlFwck5mYThFcTdTR3VHT1JJNUhzRzJMTml4TTJvYi9sdU9rNjArTE5haGZyNmNhei8zcVlQZlNJNGhnVVM0V0JUL3p1QktuQUQ4M3RvanhkRXNxK2JzdS9ySWRNb3NqTjU0Y0FnYUx1aFo1cjlick9qSFZTRDJwTkZPUmw1UVI3NEh6dWp5UEVoR29qNjRzWFdxVngiLCJtYWMiOiIzMDIzYjkzZDZjOTE1YzRlMDVjYWNiNzhkN2U0NGY0Zjg4YWIwYjk1MWQxZmUxNDdiZGM0NzMyNzZlOGQ2MGUxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:22:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpHZ0xSQk9TR0JsRnhuNWplY3gzTUE9PSIsInZhbHVlIjoicHBQbm1PZk5iSWNuKytpYVVReDdrNjNFYjV2T1BWWkRuVytlKzJYMnRBYUZ3c3JSSEt6WHNaa1ZCb05TV2hTYjF1Z095QW02ZllhU2FiclVJc0YvVXN3THplT0ZKaUdNd1pvWFRzVWZ4REY0RStieEhNSzVLV1FvSm1DZnRXNFNPSG5DWnlLemFackdWaVRxc1NxY0h2dEQwWUh3UFZscjAvRUFkdzlyWXFJVXBPVjZMVGZYSFptQit3OEFBa29FcWc5WHJKVGJ5UGZwWkh4eFhwK1lkYk1HSkE5SDM0VmE0YTl6UFRyVzZvSXN1NWFsOUh3N3Ird2xlalM0V09kdUtEZDNFOU9lMHkrVFpuaVkzbCtpTzgyRlVhOWJhQTRBZjhFSk12RnRCYTJOanZZNENHRStkY1J6eUNsZUQ3VlJza2hoNmh0ek5aU1lSUWllNEMyS3JkbXJmWW9TWFVxTjZTeThLR1o3d0ZMZUEvS1VHWXRZejVBa3hKUjcwQ0t1VmNDcVlBVXM0YnNablpJZFUyWWVweFJGYmF4Ukh4ZVkzU040TGV0YUNWMmJzd1RYRlZON01zdjgvWmZWMUV6MDlEOWY0TlJhTlBvRTdGc3pEUkI0TG9nVDVEUUNXUWpha3puMkp6U3FFZkVOSFgvamU0aWVCVUE5K2FXZi83S2giLCJtYWMiOiI4OGVmMmFmZWQyYWJjMjAwNTAwYmM2ZDdiMWRmZTY4Mjc3Y2I3ODNjOTZjYWI5ZjVmYTZlNTg3YjdiN2IxODg2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:22:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklBL0IzNUtJa3FlZURjNDU0UDRKQXc9PSIsInZhbHVlIjoieVBRMTFON09QWkhHUmdha3l1UlAvQXBzUUU4QU5vWTZlcDIvUDNjLzFJRjdHR0YrZVVCbTdzL2RpbnEweHZlQlBUREprRXRuYUxSamk5UmgvMjRHWDhsLzRpcWZ6cjBqTWNVTGpJNVE4czhXckxoSTFzRHFYaXZPTHN0Z3NJSENzOHhESUJnakpURUY2TzlMUlE5TlR0MmVWc3htN1BDVUJXTExnMHQxTFVEQ2kzMzdtOWFBTElTNm52N1dBdnN2bjRVWHovTENOM29mMFhEMjJCcWJqL3NKK2ZJWVVOdERYZElmOHRBODZ1aG5qTTVVTmozWU9PcG1pUjFKS2FQenNGSkhpMEoycmFmSkZrMC82a0FGL0hVZFY0YXJqU1B0NjdVSEVFUjZwYmYrRGtnOW9mRnltSzVWWndDd0lyYnhUSTlDNngrZTNhb1UyZGIrNWszeVpEZnFMY0ZNL0d4R3RyVTIzRXRCWXB1TlFwck5mYThFcTdTR3VHT1JJNUhzRzJMTml4TTJvYi9sdU9rNjArTE5haGZyNmNhei8zcVlQZlNJNGhnVVM0V0JUL3p1QktuQUQ4M3RvanhkRXNxK2JzdS9ySWRNb3NqTjU0Y0FnYUx1aFo1cjlick9qSFZTRDJwTkZPUmw1UVI3NEh6dWp5UEVoR29qNjRzWFdxVngiLCJtYWMiOiIzMDIzYjkzZDZjOTE1YzRlMDVjYWNiNzhkN2U0NGY0Zjg4YWIwYjk1MWQxZmUxNDdiZGM0NzMyNzZlOGQ2MGUxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:22:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1061563362\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1526960522 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1526960522\", {\"maxDepth\":0})</script>\n"}}