{"__meta": {"id": "X98f1d38a2c13860956906b5fc8c723cc", "datetime": "2025-06-08 00:30:38", "utime": **********.605462, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342637.656494, "end": **********.605499, "duration": 0.949005126953125, "duration_str": "949ms", "measures": [{"label": "Booting", "start": 1749342637.656494, "relative_start": 0, "end": **********.417352, "relative_end": **********.417352, "duration": 0.7608580589294434, "duration_str": "761ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.417367, "relative_start": 0.7608730792999268, "end": **********.605503, "relative_end": 4.0531158447265625e-06, "duration": 0.18813610076904297, "duration_str": "188ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48114848, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.024110000000000003, "accumulated_duration_str": "24.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.484024, "duration": 0.01684, "duration_str": "16.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 69.847}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5204918, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 69.847, "width_percent": 3.774}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.556538, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 73.621, "width_percent": 6.387}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.5624752, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 80.008, "width_percent": 7.01}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.577646, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 87.018, "width_percent": 8.544}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.584871, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 95.562, "width_percent": 4.438}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-788566170 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-788566170\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.573913, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1726041471 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1726041471\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-842061050 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-842061050\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-24588456 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-24588456\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1142172093 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749342593005%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhzTWlQMW5nY010bzdCaG5YQmhuUEE9PSIsInZhbHVlIjoienFaM1lxUjZIZDNiTE94MVFGVUN1VFIyQlNabUpka3RYdlA3cFhoRDM0MWIvczdFc0x5UHVCWE83T01rcU1kbC9uVFQ2ZHpmUlR2TFFoYUZMVnBkQTlZOGUwbCtFNmI0clJ4TFFWbGVtb3BTQWRhWDdIQ1VscWJ3RkFqS05lenhnTlpnT2tsTDBGWTZyUlJEZGhkOGtZTmlmWmc2dkFrUEYvcG81RVNkcEIwN2xTbjNMRjdIRVhWeFZaODE5SXFENGtwRzVJbHFLQXN0ZWZqYTlpeDhJWmZ2bVlBMk9xUk1jeGg1WUtDd1pJS2RqVTRZTVEzUWpzVFFiNkVsdEFEbXBaaTVLZk1Ld3BXajBSeTVUdUYrWjZrMWhnS0pHUmdoSW5Nb1BiNDB0QWd5WWtraEZFTXE4R05tQnVrbS9qSmdKREtNbVgybjQrK2g2TnNDa0lyekRXR3BWbXk3R0dQVkhmMi95NE5uSFpQd1VyQmZ3blhuc0s3QlV0ZjBYUHdlaTNFZnlEL1ExNkg2QnpQaDVJVTdjeWpvdFV0OW5TdXRIeXdlRGRpK1VOenBzc3NuMEpKYkdSYVJWZHhVYit5ZHlaeFRsMjhjMWU1ZThtMU5rNXpyTmdFNi9JM29PNGZkblp3blFFa2gzT2RVUi9VTXFaTUtDZXlyem42OCsxVmEiLCJtYWMiOiJmNTY4YzQ4YjE0MTg0NzhkMmFkMGRmYTg3OGZkMzQ5Y2IzZmU3Y2NiNmY4YzgwOTUyYWVlMTJmYjBhNDMwYjE3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IktkVkZ6bmNXMmpETWl1eE1iSFl6bHc9PSIsInZhbHVlIjoid05mMnczWEN1dWZTOVJMN0hUZkJRb0xYYjMzdU9uMDFyemp3QkpRblQxb2ZkZUF6aEZ1VXh4NU1KM1lNSFl5N3ZJM1cwYVVvL3FzbGF4VGsySzVjZ0VzZk5pUzRhMlNqMnZYVUVUMkRUNzV5UUx0SVQzVFYwSXhiYUN5MWZpTHhlU2R6TXFrNlNZcGVtaE43S0NEUDFQUHBkMHJEUFNoVnlzWUVZdTZZWFBHWVNHZzdYWVRqTnh6aTVzZER5ek44Q0xqN3hVRUJmVlhrQWNFVlJKL0tKREVIamFra28xTW1nUUFJTzJ2Y0FTblRzVU16Zytvby9qQkpXbjhhcWMyZEgvRXdWNGNmSmJEVllNZmRRYUtVMXE2SU9icXErUlBWYmZBbE03d2tSWXhzVkVIS1pPQVFjUnVtQzVNQ3lFSG9kaENXM1VxQlpMd3R4aTA1dEtzMkNJQjJ0MWtKRDdxN1d4c0JQemMzMEJLdUdTNTV1SHVXZjd1cVhzd0d0TVQ4a1REOW02UHhNQmVnZTVkaHpZU1ExZ0VZV3RvdUlEL2lsWC9jZDlVclRLUzgzdWpHaCtrN2Q5TUVsaFQvdTZHTlpDR2xseGNCdnVvZmk0MGNpaGxoY0VkT21jc20zZi8xc3lVSEhvenJoY0E3bFZkVy93eHpyYzFPYlN0QlZPdTgiLCJtYWMiOiI0NTUzYjdiMjIyMjk1ZDdkYzNhM2Y5MTgyMzFlZmY1ZmI0NjA4ZTI4ZjE3ZTcyNGFjNDE5NDFjODhjMWIxZTA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1142172093\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-94560046 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-94560046\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2008277217 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:30:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRZZHgwS2krTkplTk9TakRhMnd6cEE9PSIsInZhbHVlIjoiM28rZlY4K1BmRG5jL3Fub01ldksrQUpsRnhTc1AyVVU4THBsb2ZjMTU5NDhVRVVJUDR1SDBETWVLZHI1VitMeVF3NXA0bW5MZWxSa1JiejZHdndteHlVT3lzR1VxZUdrTnhJckJ4SFBONzdYWGhuRnJTVlNPcGRVajVyZGxNQUZTMjdlaDVnSkJGWUYxMGQ5b25tRUp6WjlvRS9TSjZ6T1FHZ3J1cE5Vbnp4NnRXTHlZS2NReHFMNkhtc1NxcEtDcnV3MVhpTXNGMkhJSytzYlAzZHpvemJ2MmlJRFNlK0pjR3VvNlR5THNpTy9XVU5HU0VSaytHZ21HZ1plTmhDRHYzaW9CUmZSUWNWeEFtd0xHQWRLN1dBVUhFWkZNM2VadTRFQzZMVldCdmtPODNKY3pxTEV3akdLQVFEVkNuZzNwZVk1elJUV0YyRmp3OTZ2K0tGaEt6TUQrNmlPU0RyOFYzajFIZlQzWDFsMmttS3RXd3pmZXBLakh4cnZzaXBFcFBBeTZGSjVUTmdNK1hPeU1CM2VhVVJhanBpNHBuVlVPYTVWd05QQzF2aUdQSGJoRlFPNnoxV0QrZHRmTUd2VlR6WWFqR1lVdzR0cDgvdlgyQ3VnTDVqeDR5M24zRGJpbHc0ZWdjSTNOaUJJeXo0Q0l3OUpTeFVqdXkyNlhoVVUiLCJtYWMiOiJhYmJiMTg5NjYxMzVhZDhhMjQ4YTljYjQwNGQ2N2FkMDljZGJjYTI4MDhjNDY1MDQ2YzM1MTQ5OWZmY2JlOTljIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik9BaFJXcElHWTVRZXdValhkdlV4b2c9PSIsInZhbHVlIjoidEhRUjROdkJaU1VNcm83RkQvR3lud0FpSzBpWTcxSndad25mbFZqQkg2WEZ4N3l3K0J2N2tYaFRralZnYU5odXFHRGZDOUt2aXFLSHAwN0xxOGw2SkwxbG92N3pqT3hITWxRN25PM2VRbU5NZGd4RHNqVm91WVpETVBQRVEySVV4V0pUay9WQVJZcVpJWkFxS2I4cFpIcSs4d1ZtbW11aHVZQzIzaWM2RUN0V3Fwa3VnY0Z1anVyb1lTNTBrY1FxSGtKSlYyU3lKdWY1ak0ydTUwbUE1ak9nWXkxN0E2dkxoYUVOZmZXYVhTNndJT3I5V3BEOFFFREtiZEEwczI4b2ZxS1FpLzlZbjl6elZrSkl3QVRvZk52T0pGbWNucndrK3QzNzgzempTTVF2Y1loaXhUaXVlMHRiNUsxaUtpRDg0ZWxEbWFnTjYvMWI0WXdhK0I5aVlRT1Z0VWpSSkFXSTl1N3VYZnZQaFJFWGRhK1ZMVngxYnNTamN5U010cVVMK2hzbzVQTUYwRHJmT203UEhhemxYVFZIQTNJY0w5WDFBcXFZMTNSV0xVdVNYR2dHVG9EVDFpak5qK1JZODRST1o0OXJuUWd3SXNQSWJEWldTY1Uwak1WNktESmtOVGxsZ3FSdnlNSkFQdkVHTTNFUVFFQnVOalJqR2RXYWRvRmwiLCJtYWMiOiI4NDRjYmY4NGJlZjE3MjBkNGQ4ZDZlYjk1YTY5MmM1NzhlZDI1MzliNzAwNmRhYTgxOTZhNTJmNDY2YzE0MjM0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRZZHgwS2krTkplTk9TakRhMnd6cEE9PSIsInZhbHVlIjoiM28rZlY4K1BmRG5jL3Fub01ldksrQUpsRnhTc1AyVVU4THBsb2ZjMTU5NDhVRVVJUDR1SDBETWVLZHI1VitMeVF3NXA0bW5MZWxSa1JiejZHdndteHlVT3lzR1VxZUdrTnhJckJ4SFBONzdYWGhuRnJTVlNPcGRVajVyZGxNQUZTMjdlaDVnSkJGWUYxMGQ5b25tRUp6WjlvRS9TSjZ6T1FHZ3J1cE5Vbnp4NnRXTHlZS2NReHFMNkhtc1NxcEtDcnV3MVhpTXNGMkhJSytzYlAzZHpvemJ2MmlJRFNlK0pjR3VvNlR5THNpTy9XVU5HU0VSaytHZ21HZ1plTmhDRHYzaW9CUmZSUWNWeEFtd0xHQWRLN1dBVUhFWkZNM2VadTRFQzZMVldCdmtPODNKY3pxTEV3akdLQVFEVkNuZzNwZVk1elJUV0YyRmp3OTZ2K0tGaEt6TUQrNmlPU0RyOFYzajFIZlQzWDFsMmttS3RXd3pmZXBLakh4cnZzaXBFcFBBeTZGSjVUTmdNK1hPeU1CM2VhVVJhanBpNHBuVlVPYTVWd05QQzF2aUdQSGJoRlFPNnoxV0QrZHRmTUd2VlR6WWFqR1lVdzR0cDgvdlgyQ3VnTDVqeDR5M24zRGJpbHc0ZWdjSTNOaUJJeXo0Q0l3OUpTeFVqdXkyNlhoVVUiLCJtYWMiOiJhYmJiMTg5NjYxMzVhZDhhMjQ4YTljYjQwNGQ2N2FkMDljZGJjYTI4MDhjNDY1MDQ2YzM1MTQ5OWZmY2JlOTljIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik9BaFJXcElHWTVRZXdValhkdlV4b2c9PSIsInZhbHVlIjoidEhRUjROdkJaU1VNcm83RkQvR3lud0FpSzBpWTcxSndad25mbFZqQkg2WEZ4N3l3K0J2N2tYaFRralZnYU5odXFHRGZDOUt2aXFLSHAwN0xxOGw2SkwxbG92N3pqT3hITWxRN25PM2VRbU5NZGd4RHNqVm91WVpETVBQRVEySVV4V0pUay9WQVJZcVpJWkFxS2I4cFpIcSs4d1ZtbW11aHVZQzIzaWM2RUN0V3Fwa3VnY0Z1anVyb1lTNTBrY1FxSGtKSlYyU3lKdWY1ak0ydTUwbUE1ak9nWXkxN0E2dkxoYUVOZmZXYVhTNndJT3I5V3BEOFFFREtiZEEwczI4b2ZxS1FpLzlZbjl6elZrSkl3QVRvZk52T0pGbWNucndrK3QzNzgzempTTVF2Y1loaXhUaXVlMHRiNUsxaUtpRDg0ZWxEbWFnTjYvMWI0WXdhK0I5aVlRT1Z0VWpSSkFXSTl1N3VYZnZQaFJFWGRhK1ZMVngxYnNTamN5U010cVVMK2hzbzVQTUYwRHJmT203UEhhemxYVFZIQTNJY0w5WDFBcXFZMTNSV0xVdVNYR2dHVG9EVDFpak5qK1JZODRST1o0OXJuUWd3SXNQSWJEWldTY1Uwak1WNktESmtOVGxsZ3FSdnlNSkFQdkVHTTNFUVFFQnVOalJqR2RXYWRvRmwiLCJtYWMiOiI4NDRjYmY4NGJlZjE3MjBkNGQ4ZDZlYjk1YTY5MmM1NzhlZDI1MzliNzAwNmRhYTgxOTZhNTJmNDY2YzE0MjM0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008277217\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1288176867 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1288176867\", {\"maxDepth\":0})</script>\n"}}