{"__meta": {"id": "X84aab40ff9b1d7e7d01483eabbd52782", "datetime": "2025-06-08 00:03:47", "utime": **********.373781, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749341026.5299, "end": **********.373819, "duration": 0.843919038772583, "duration_str": "844ms", "measures": [{"label": "Booting", "start": 1749341026.5299, "relative_start": 0, "end": **********.254846, "relative_end": **********.254846, "duration": 0.7249460220336914, "duration_str": "725ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.254862, "relative_start": 0.7249619960784912, "end": **********.373823, "relative_end": 3.814697265625e-06, "duration": 0.11896085739135742, "duration_str": "119ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45052592, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02654, "accumulated_duration_str": "26.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3077278, "duration": 0.024460000000000003, "duration_str": "24.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.163}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3456252, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.163, "width_percent": 4.484}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.356221, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.647, "width_percent": 3.353}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1914564732 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749341009015%7C24%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im40a0FiYTNnRjl1U1FGRHN2Y1M5V3c9PSIsInZhbHVlIjoiQXJMU2lWc21kTUVsZG8xK2diamtEaGpMWDJTWllvbG1pQ0d6NCsyWHVPUW51endCVnovQ0VXUXVKcG11eHk2eFJGVUpNQm4wYk05ZGVqSTRxczY0c3crSUxsOEZ5ckMrK2R4RzhBbVUyMDRQMW5qUGlRRUZ2STJLZ0JpcEpkZ1R1ZUxLTUtNWXNoNmR5VEptOUtsL0MvTHpHSERqTVllMDFDVm84Rm1aRERVb1RCMXFYZTZKTER0cEhiQm13TGpWc0hGVWFybGFjTGN0ZDM5cEM5amF2SUI5cHZOcjZTQnFHODNSeTdqUjBMNGxEazIxQlVtWXlrQk81RzRsWjlLelA1N2h1NisvQjc0b3pvQkpBQ2gzTTFMK2xPT3lLZVNqT1A2OFdQVVpsU2pQUFhEb1ZoVXYwYXhJWDJJdmduVmp5U0x2TWpWMW9ST2kvL2dxbGxnNWZ6eDlVS2tMSWJXV1BFaktKeFc0cnd6TjlaYmpYZ0NGTGZoVjZYeVNCdENWR05PZHZqZXJjZjdvZDM2TVN5UVZwUlVjd2pJeGdxck0yUG9OL2pRd2JORG5vSmNVNkRod08xODZESkI4NE04VC9iQ2E3alN1S3RkOG9adEJOVTRObXJCRVZIRW9yVUQzeWR5VGxMWlhMdndzR1BMQlVZaysrMUxGaDVsTkR2eGsiLCJtYWMiOiJlYmU1ZTQzNmU2NjkyYjYyMzc1MTAxNzhlZGU2ZGExNzFhNDc5Mzg1MjU4MWUxMzFjZDE4YWRlYzIxOWIxMjRmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImVobDVkV1ZoWGN3NG95SzhBTGhDT0E9PSIsInZhbHVlIjoiSDA2aEE3c1JhRXFqZ0VOK1RsU2NvWGJNaWFhb3JUM1VkT21iRFo2eVNYMnQyclVteTF5N1NOWGZNS1VMVmIwSVp2Z093ZnBzSElnb2pPR1FRUVhqR29qNGRsVnVlOVN4TFZjUnNnMURiTitqYVQ4T1YvbGp0RmxqVEp2YlVkeWV6a054VmpwN01LaGVxRjhza09maHRGWlBPWFYxZXJ6UHRwTS9ob1BUZXhvQUJOWkI3Wk5Xa3dBSlBUQ2orMDB3ZldMTi9nZVpQNjBzRTg4QkUzYUJFK3lVUFlTM0lCczVIY1VkejN3dlBhZmtqcngrWmRuSWtPQXhIZmxWOFNBV3F5Ri9pTWdBSHZlamN1SWVvS3ZqdUhlY1BzaHVmU2dYL2NxUVQ0RkI1Q0c0Y3ZNYVNqbUhaY1VVQ2lNRnpnOEEyNytVUFFxYjFlanJVRlBaWDZKWUk3cGtpZ3IyamtzcWdXNmxEaFQ4UTZLK3JzNjAvK2ZTMzVNYXM0VGN4WkZOdEs3ZDIxT0J6ZUJjRGt3UFlMRTlVSGd0eFVjVVJTNlNrb1BndVlzWklOUmEvc2d6YSs3bmdPOFVoYW1VcWJ2Vm4yK3NyYTZFT3ZDU0orWG1pYStPcXhQYy9TNXRBZ0U3VHcxNlZLc1FjY0p6UlNXUnlYempOMmdlQ2x3c21nMXoiLCJtYWMiOiIzOGZmY2MzNmI2NTYxODY2YzdmNTU2NDZmN2Q4YTFmNmZhY2Y2YzgxYTI2YTNhNmNhYWRjODc5NjQwYTQ1OTY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1914564732\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-750417837 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-750417837\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-552227810 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:03:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imk1dE9iV3VFM0o3VDFuaXVXMC9RV0E9PSIsInZhbHVlIjoiOUFuTUZMb0dxT1oycXAwTUN0U3FNVWhBWmdkS1lsUGgzVEYxcU5sY1dPdWthS2QxMmJ4a1pKMXd2SEtJWFNueG9yOUk5VVB6dHhFamNCZkNCR1N0cXBCSXlhZ1Ftcm9JcnNEVHlJSU5MTmkzYVJRSXFTVzU1ZWlWZEZnK3VpenhuNFVqVklUTXBzUWhyZTFxRlBodjN1VkppK2lWVy9GV1VxdWFDYnZRVmRyQmxrajl3Yi9kQ2hwa0xId1FFck5GUDA5MzA4elZXMlhPSFo2ZGlPUXFBNGhJV0FhU0FHRThVQkxrc2tlSEJLK2xDOW1TMkNocTFsM0ZzODE5QTlNK3VQVStOOHU2b2lTTXRBNWpMd0MwVDVOVktJZ3hRSENSWkw0VTFVak4xaksvTzJrMFpMQmRLdFhVQ2oycUh3RUhUNGdzQ0ZwZnNnUDNSQkUzUDFEZkxYMXRldWxyMFR6RFBlQnRyaklXd1UwMmREQnZ0VDUzdHJ2Y1ZXSzIzN1dja25VcVBYK0NaZU14VWVFR2lPZW9vUFZJU09PSjBjcWJqMlppemRjbCs2blFFSi80dWY1ZWtMZnJ0SWpRTXJ1MHltQkd4VDdyUGNzN3BUVmE2Q3gvdyt0NzBibXVwQnhzQ2ZNQ09ZUlVMTUViMDBMM1VqZHRsMVJsQVNhRU5WSFciLCJtYWMiOiI0ZGFkMmFmMTAyNmUwNTcwMTdkZWFiMDFkNGVkZDhiMzBiODIyMDhkZmE0NWFiYzNhNDkwZjUwYzQyYzM4ZDBhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:03:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InpMbFJzMGI5UEs1bk1PeDg3RkNESkE9PSIsInZhbHVlIjoiNWpYeW9KeHAwT004c250aFlKdXppdWdSckhrckxJcUNteHoxMzlaYnNpdDVYTytsSTFUcU1RN0xINGNmMDAyQTBSdnhPamZCVVRNa1hIK0svWFhJTUE5YmdDekVnZk5ib3hWUThuMjArS0hJTXdJZHlWUkdOZ2FoUXZSYnRBZWdDbGdwcHpaNlJSSnBkQ0taNzUxVTlMZis0SGRDMjlsMWhKR1UvSmh5ZTNFdXM5NFRQVW5qZzYyUEFWeEJTVVVQYzhlQlhLMnhyWmp3eklDSzl3ZEZ1N0E1U0kyaU1sUytmY0FzaXFDZmQzT29vbFhsbTN3MERib1V1bmpTZXRGYmdPejNjblppSGx0YmE0R2pSWUhqOGJPZmZpaEdmYkgyMkNoYmJOYnFyZXRoSXplVUJMWkVuTGlLY3BBYVYzNkxHaE1iYUFPRXVMK2RCUndIZ1VvcXVTQUp1ZjVJMndxWU9weEJaMHdjbnZVa25kTk9qSDRtTXozRlBUZVVERDA3ME0vZ3BiWk1lUGk5QXBTMk5Bbm5OVGp3M1RpY1puUmZUenl3NDlCUDRwZEZQMUdvVUY5S1B0bWRVeE0xMmpaclpqbVhCZmdUMHBiUW5iOWs4d3R4UG1iaGlJMDRaSnFXVmV1QkJxeFZPOTliekJOZ3dVTzJYQ0JyYzltK1N5WWoiLCJtYWMiOiJlODA2MDkxNTljYjFmNDJlNmM2ZDIzOWU1Njk0NTM5ZDk1MzEyNDY4ZjAyYWVlNWNhMDdmYzBkY2Y1NzhkMmMwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:03:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imk1dE9iV3VFM0o3VDFuaXVXMC9RV0E9PSIsInZhbHVlIjoiOUFuTUZMb0dxT1oycXAwTUN0U3FNVWhBWmdkS1lsUGgzVEYxcU5sY1dPdWthS2QxMmJ4a1pKMXd2SEtJWFNueG9yOUk5VVB6dHhFamNCZkNCR1N0cXBCSXlhZ1Ftcm9JcnNEVHlJSU5MTmkzYVJRSXFTVzU1ZWlWZEZnK3VpenhuNFVqVklUTXBzUWhyZTFxRlBodjN1VkppK2lWVy9GV1VxdWFDYnZRVmRyQmxrajl3Yi9kQ2hwa0xId1FFck5GUDA5MzA4elZXMlhPSFo2ZGlPUXFBNGhJV0FhU0FHRThVQkxrc2tlSEJLK2xDOW1TMkNocTFsM0ZzODE5QTlNK3VQVStOOHU2b2lTTXRBNWpMd0MwVDVOVktJZ3hRSENSWkw0VTFVak4xaksvTzJrMFpMQmRLdFhVQ2oycUh3RUhUNGdzQ0ZwZnNnUDNSQkUzUDFEZkxYMXRldWxyMFR6RFBlQnRyaklXd1UwMmREQnZ0VDUzdHJ2Y1ZXSzIzN1dja25VcVBYK0NaZU14VWVFR2lPZW9vUFZJU09PSjBjcWJqMlppemRjbCs2blFFSi80dWY1ZWtMZnJ0SWpRTXJ1MHltQkd4VDdyUGNzN3BUVmE2Q3gvdyt0NzBibXVwQnhzQ2ZNQ09ZUlVMTUViMDBMM1VqZHRsMVJsQVNhRU5WSFciLCJtYWMiOiI0ZGFkMmFmMTAyNmUwNTcwMTdkZWFiMDFkNGVkZDhiMzBiODIyMDhkZmE0NWFiYzNhNDkwZjUwYzQyYzM4ZDBhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:03:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InpMbFJzMGI5UEs1bk1PeDg3RkNESkE9PSIsInZhbHVlIjoiNWpYeW9KeHAwT004c250aFlKdXppdWdSckhrckxJcUNteHoxMzlaYnNpdDVYTytsSTFUcU1RN0xINGNmMDAyQTBSdnhPamZCVVRNa1hIK0svWFhJTUE5YmdDekVnZk5ib3hWUThuMjArS0hJTXdJZHlWUkdOZ2FoUXZSYnRBZWdDbGdwcHpaNlJSSnBkQ0taNzUxVTlMZis0SGRDMjlsMWhKR1UvSmh5ZTNFdXM5NFRQVW5qZzYyUEFWeEJTVVVQYzhlQlhLMnhyWmp3eklDSzl3ZEZ1N0E1U0kyaU1sUytmY0FzaXFDZmQzT29vbFhsbTN3MERib1V1bmpTZXRGYmdPejNjblppSGx0YmE0R2pSWUhqOGJPZmZpaEdmYkgyMkNoYmJOYnFyZXRoSXplVUJMWkVuTGlLY3BBYVYzNkxHaE1iYUFPRXVMK2RCUndIZ1VvcXVTQUp1ZjVJMndxWU9weEJaMHdjbnZVa25kTk9qSDRtTXozRlBUZVVERDA3ME0vZ3BiWk1lUGk5QXBTMk5Bbm5OVGp3M1RpY1puUmZUenl3NDlCUDRwZEZQMUdvVUY5S1B0bWRVeE0xMmpaclpqbVhCZmdUMHBiUW5iOWs4d3R4UG1iaGlJMDRaSnFXVmV1QkJxeFZPOTliekJOZ3dVTzJYQ0JyYzltK1N5WWoiLCJtYWMiOiJlODA2MDkxNTljYjFmNDJlNmM2ZDIzOWU1Njk0NTM5ZDk1MzEyNDY4ZjAyYWVlNWNhMDdmYzBkY2Y1NzhkMmMwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:03:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-552227810\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-14******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14********\", {\"maxDepth\":0})</script>\n"}}