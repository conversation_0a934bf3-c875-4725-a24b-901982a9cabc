{"__meta": {"id": "Xd97b421773e0ac49667b26b62dc096d8", "datetime": "2025-06-08 15:40:19", "utime": **********.801852, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397218.925878, "end": **********.801876, "duration": 0.8759980201721191, "duration_str": "876ms", "measures": [{"label": "Booting", "start": 1749397218.925878, "relative_start": 0, "end": **********.695368, "relative_end": **********.695368, "duration": 0.7694900035858154, "duration_str": "769ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.695383, "relative_start": 0.7695050239562988, "end": **********.801879, "relative_end": 2.86102294921875e-06, "duration": 0.10649585723876953, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45499272, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02694, "accumulated_duration_str": "26.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7458432, "duration": 0.02506, "duration_str": "25.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.022}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7841818, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.022, "width_percent": 3.563}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.790027, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 96.585, "width_percent": 3.415}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-343957375 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397215476%7C9%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9ObFRNcnpzYi9LUmppQ0daU1dLb1E9PSIsInZhbHVlIjoiUndhcU90OWdzWUlrblBPL2o2RllYaG16U21KUVZBOExPSktQNm5xZzkrM0ZQNTNTMis5QkkvVXpTRVpBeWdkVE56Y1UrdDcyTWtjVmFOa0t2ZDJ6L1RoVzVQVU9EZmx1bzZWaVRWZ3kyb2JoaWRJTENSL1RXV205V3dFdkUvTm1Ndm12eCtZTVVDSjkvUnJuVERxOTFSRGEwVVRadGY4ck1pMENab1VNQkM3QVZFd1crVUVRTndMS0c1c3I4YWMvNEU4WDVybjFtYlU5eS9QMUh1dXVLZ09FbnZBNEM0eFNtaEY4UkR6UEpKT1dhcGE0bGR3dUpOdmJ1OFh4T0RqWWpBcDkrMndsRENvVytrOG1VeVZSK1FWUlZJMGdZUEJGaytnTVc3NU9YOWpGMUdRbU9nRXc3eFBlK2RnejlLVkIzeXpGQjZNNVNSMEVodHVRdUZYbW5lNFk2cE5FOFgvZGU5SCt0WHFPVXdtWkZ2Nkcwa0QrZ2d4T2YyNFUzQjgzQ09TYU8xSEZoRTI4a1RmQVJyN0JKdDNLYmdCOGVTbGUzdWt6OG9heWF2Z0Z4QnJiS2hPRmNBNkJWR3ZVMWhRbmRNbXZWU01ucVkxRnRwZU9yTHRveTYvU2l2VG94eW82VXdvMXRkQVoyc0lsQUN0eXJPRlJZYWNMOXMzbmhFZDAiLCJtYWMiOiI1NDVjMDM1N2UyNzFhMzJmZmFiNmM4Y2UyOGUwNTcyNTAyOTcwNTQ1OWFiZmNjODYwNGRjNTU5YTJkY2JlNWI1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im40bE1PS2VkTmFTc1VISzEvUWdOR2c9PSIsInZhbHVlIjoieUZ6N1Fac1JXZFpDYmpZMGFha0tTMEtsTVlDNlJ4WGZkUmhxTzVsTGM1WWRnbGFhWVFYYmY3K2U4ck9xZUx2bTB4K1ZVRGxOQjFnaFg2TmhRUXQ3aEhuQ3ZlOC80b0daTFEwKzliVVpXeVo5V3ZJNm1tWTJQUkh0c1p4WEM4Z05mQmxMV1RyTTNMQnZHUituaDY2UDZwRkxQVnpJU1cwUS9IMUNObng5TTc3cFgrTDY2cE0xM0dORDFPN0JJY040bFppK3pRMVk2L00xU1JUOEhuOFlUYXpwekxZaWR3TlZPckNvSHNhaUNWWlRnOHFzbUNGWmZqeWxBSmtFS3dkcUd3dW9KUmlOVDFodHZmSVB5VE1mdXlvdmVpQStTc09yQXhHUG9IQkU5L3dEYkcvbnNIVmNXOWRtSFdjK2Jwa3JNK3hDRGIzUWZnOFNWRjhZZ2d3QjFDZlJDSUJzZ2NmVU1tWTFyL3MvZXk1eXFRUW1pKytOb0lyUFhzSnhIR1BvaTZMRnBPV1hFbWNod0JCNk5TUEdqdmxVb2x5NmFITHhaS29HR1lDcDlHRkFsTWJEWURJWCtjbWZpTW9CeXV4ZTRZS2dLWGpQYmlMaW1lRDJoQ2NJL2I4NExPdm1id0grWHdSK3FaUk16Zmw2WWRpR0J6dC9ybUYrWisxU2I0aEIiLCJtYWMiOiJlODIxYTBiZTc0MjhkMjc2Mjg2YWI4MjM5Yzk2ZDg5NDgzMTM4MTE1NjU0ZDIyNjdiYmQzMTlmOWZmZjc1NDM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-343957375\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1636576827 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636576827\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-930611556 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:40:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxMQ1BXb0hLTjJEQ1pubTl3anhyY1E9PSIsInZhbHVlIjoiY1pIMzNJNkpXSnBEaDFQWjJ3RGtiTk1GZGlkamxia1BEalpjaGlpR0MzTzlzaEpFclJWVWo4TGNDRm5CdGlXdCtaNS81YVYzRDdjaUxZb2N5QmdSUnFtLzZsWUJ4SjJQQWJrN3FRb2dGdk0yRlZPWmFNSmNUQlNEZ3I0K1lkOW5obFY5NXd0M3JyU3hwQjg5YlliZ0RlN2x1NjhuY3NpbHc2SmF5OTBremRxM2hpMkptNHh3cGhVVVVubFBYcUxrMUVpWndwMzU5aXZ5UWhHUDNVUlhrZjB5bkN6czBXVnRjTkV6elVNTzYwcVNaTUd3UVgyaUhLWFlpUEc5S2tVZjB4RVYyejVFUko4TXJFRGpsT2hEeUJZcXNFUTlUd1dGMmFqcjZIWXMzV3RmVEozZ0toaWVGVGVSbllNWGw0cWJ3MXQxY3hrdkZBcSszcE11a2p6NEJZQWtXb0IwYnVSazYxSktHYVdKdGFRdU43aHJSY0paRDUxdk1vOUVuNWNzUDYwdExrVytsbERxNEs2QnBid21OWmlSWC9ZdmlQQllnL0FTdzIyckg1ZWdNemhJZGs2Q25lR2xIOHpEbldoQmYxQ21jc3FWNDBWcHFrc3JuSUJkVlE0UzhNVUtsUUhOYzNoUlVidmJvZEs1TVQrS2E2Ly9CUW1UMmYyUVkvaU0iLCJtYWMiOiI0YTI0MjU2NWEzYTBmMzMwMWQxYzI4NTU1NWYxZmY4YzhiZGZiNWNhYWE4Y2VhOTZkOTAwMWVkN2YxN2RmMjBkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:40:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IndsWWtDWmFIZGY4V0NzMkpFekwrMFE9PSIsInZhbHVlIjoiNUwxUFU5bVNVdWI1Y3RCWForbzdSQ0Y3T1ZRZWJuQ3Fpb21YWnRucmFlWGNCeFVLd3NheklZL3hFMG5PZktEU2UwMGhtRnBHR2xTZEpRMkZoNjh2SWR2cVBxWnVqVDJ4TFU1SW1EM3FRaDV4UkxEUHN6Qy9sU2I0ZmdWK2gwNTFyaG9CSTFHK3dmdTdsSnJ5SlBrN0xVNjZvejVTVFF0MHFoU09nWDN5ekJKak9ESVR5UW1LRzNRZHh6d2ZQY0s1SDF2NndqUDJqY2tPTGFDNjc2RWJob2ZtcXBQRXhIZ05qQjY5L01PS2RvaE5Gamh6UzlOTjAwRW5OTmJUMnVqM3pSK0JTdWE3NjdtajZ3cDAvNDFzUkpVc0hjM2RIMXdiWnVlMllOZHR1Uk1lRnptNHdGanF4WmFKeHpJLzZ3OFplMHZMU0pGeUtkL1RaTmRkQ01vRmtOc3Eya2JtR0NiRDRVa2xPME9KRXdjanpsMmQ4a1I1VXZJbjdwMWw4U0hNdFFXc1JmSjJHK056bmhJcmZXVGJVNGF5cUV4d2UwZ2ljUzhXR01MdmJrVFUrYmdndU82Q0ljWTF1cU1XdFk0YnFibk1COUlFcllLVTIxa2ZoTkY4NllKeFdtNHZqL0szTnJiLzRqV3JGSTdFZjI1cURQZ09zT05LL3RRWW5kNE4iLCJtYWMiOiIyOGZhNmIwYmY1ZmFkYjY1NTdkN2U0ZGVmNTVkMzBmYWI0ZGU1MmQ1NThlNjUzZDkzZjk2YWZhN2E0YmRkNjBmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:40:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxMQ1BXb0hLTjJEQ1pubTl3anhyY1E9PSIsInZhbHVlIjoiY1pIMzNJNkpXSnBEaDFQWjJ3RGtiTk1GZGlkamxia1BEalpjaGlpR0MzTzlzaEpFclJWVWo4TGNDRm5CdGlXdCtaNS81YVYzRDdjaUxZb2N5QmdSUnFtLzZsWUJ4SjJQQWJrN3FRb2dGdk0yRlZPWmFNSmNUQlNEZ3I0K1lkOW5obFY5NXd0M3JyU3hwQjg5YlliZ0RlN2x1NjhuY3NpbHc2SmF5OTBremRxM2hpMkptNHh3cGhVVVVubFBYcUxrMUVpWndwMzU5aXZ5UWhHUDNVUlhrZjB5bkN6czBXVnRjTkV6elVNTzYwcVNaTUd3UVgyaUhLWFlpUEc5S2tVZjB4RVYyejVFUko4TXJFRGpsT2hEeUJZcXNFUTlUd1dGMmFqcjZIWXMzV3RmVEozZ0toaWVGVGVSbllNWGw0cWJ3MXQxY3hrdkZBcSszcE11a2p6NEJZQWtXb0IwYnVSazYxSktHYVdKdGFRdU43aHJSY0paRDUxdk1vOUVuNWNzUDYwdExrVytsbERxNEs2QnBid21OWmlSWC9ZdmlQQllnL0FTdzIyckg1ZWdNemhJZGs2Q25lR2xIOHpEbldoQmYxQ21jc3FWNDBWcHFrc3JuSUJkVlE0UzhNVUtsUUhOYzNoUlVidmJvZEs1TVQrS2E2Ly9CUW1UMmYyUVkvaU0iLCJtYWMiOiI0YTI0MjU2NWEzYTBmMzMwMWQxYzI4NTU1NWYxZmY4YzhiZGZiNWNhYWE4Y2VhOTZkOTAwMWVkN2YxN2RmMjBkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:40:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IndsWWtDWmFIZGY4V0NzMkpFekwrMFE9PSIsInZhbHVlIjoiNUwxUFU5bVNVdWI1Y3RCWForbzdSQ0Y3T1ZRZWJuQ3Fpb21YWnRucmFlWGNCeFVLd3NheklZL3hFMG5PZktEU2UwMGhtRnBHR2xTZEpRMkZoNjh2SWR2cVBxWnVqVDJ4TFU1SW1EM3FRaDV4UkxEUHN6Qy9sU2I0ZmdWK2gwNTFyaG9CSTFHK3dmdTdsSnJ5SlBrN0xVNjZvejVTVFF0MHFoU09nWDN5ekJKak9ESVR5UW1LRzNRZHh6d2ZQY0s1SDF2NndqUDJqY2tPTGFDNjc2RWJob2ZtcXBQRXhIZ05qQjY5L01PS2RvaE5Gamh6UzlOTjAwRW5OTmJUMnVqM3pSK0JTdWE3NjdtajZ3cDAvNDFzUkpVc0hjM2RIMXdiWnVlMllOZHR1Uk1lRnptNHdGanF4WmFKeHpJLzZ3OFplMHZMU0pGeUtkL1RaTmRkQ01vRmtOc3Eya2JtR0NiRDRVa2xPME9KRXdjanpsMmQ4a1I1VXZJbjdwMWw4U0hNdFFXc1JmSjJHK056bmhJcmZXVGJVNGF5cUV4d2UwZ2ljUzhXR01MdmJrVFUrYmdndU82Q0ljWTF1cU1XdFk0YnFibk1COUlFcllLVTIxa2ZoTkY4NllKeFdtNHZqL0szTnJiLzRqV3JGSTdFZjI1cURQZ09zT05LL3RRWW5kNE4iLCJtYWMiOiIyOGZhNmIwYmY1ZmFkYjY1NTdkN2U0ZGVmNTVkMzBmYWI0ZGU1MmQ1NThlNjUzZDkzZjk2YWZhN2E0YmRkNjBmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:40:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-930611556\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-8******** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8********\", {\"maxDepth\":0})</script>\n"}}