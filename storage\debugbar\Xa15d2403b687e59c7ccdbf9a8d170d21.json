{"__meta": {"id": "Xa15d2403b687e59c7ccdbf9a8d170d21", "datetime": "2025-06-07 22:36:38", "utime": **********.905978, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.07212, "end": **********.906008, "duration": 0.833888053894043, "duration_str": "834ms", "measures": [{"label": "Booting", "start": **********.07212, "relative_start": 0, "end": **********.820036, "relative_end": **********.820036, "duration": 0.7479159832000732, "duration_str": "748ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.820053, "relative_start": 0.7479331493377686, "end": **********.906012, "relative_end": 4.0531158447265625e-06, "duration": 0.08595895767211914, "duration_str": "85.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43911696, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00425, "accumulated_duration_str": "4.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8800979, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.765}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.892003, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 79.765, "width_percent": 20.235}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 19\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1806844772 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1806844772\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1830661341 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1830661341\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1123083743 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1123083743\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749334872942%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkM4dUNoTktGakYvc2taeC91emRVNVE9PSIsInZhbHVlIjoiUDdWT1pDS2d5ak0rYWVkUTBnVHZNWkd3Z2ZwUGQ2Um5Rc2RXdzRUUVpxeWpDbmFyVDJjajRkb25kTDdKeHkrOW1LeXU1WmkzakpWMk5YRjlFbWtuZzhuQldOVFhyem1rNUxGTlFsWldNdmYrTkJrMzQvOXoxWnQ0cDlES0gyNU8yOEk2cVAybWhLcll4c0xnTEY2SnBUaTBXWTJqMFZzcEovRXdSeEZmaytCSEUvRWZFVXpjYWZCRFR5VThoQ3NpeEQ1VnpWNXdncVdYZHByckRSTE9DRkFMRE1pWUlMT0M1Qm1takpuWk5pc3VvSDc4aDNjWkVES28xQzM2WWg3cExUZURveDN5SCtJaDBWL2dPUTRQM0ozRlpPbGlUaWJMU3ZzOFNkNTc2UUVvRksyQkNlazhFMEdSNWE3K3FZcW8yZWtzMlllODJiQ3dVQWJzcjdqNDJGWXFvVzZtMWFxTFhnendzVG9PNFJwM3oyNWhScWtVV3l0cTA4OEsvcDdkV0RrdjJEb2xDNEJqbWplSkk3K3hLUHBadE5McUIzNGdDNFl1c2c5VGdMRkhsVTVYQ2pIYzJ6eEVDUEJqOE5JTS9ONnVOZlhHakZFN01lNDBGZ2UyRlh1WldSVGRIbUhmQW9QRlN3dHpFNHl2UlRJSklOdlVPbjhiUkJ1UW1iRlQiLCJtYWMiOiJkODRiMWM4MDk5YzA2MDIzM2EzZTNhNjY1MmM5MWJlNjk5MmVkM2NjZmMxNWY5M2Y0NWZiY2ZlNTViYWZjYjU0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImRjVnpYcEZsaTJwdWRjNlNQNCtMa2c9PSIsInZhbHVlIjoic3Y4QUxCT3llMkhJbnBJMVdWRDQ5Q0x5VFFQRitwOERwVDFSSlhMcCtYRWhnTktaVlBVTjhpamZjdjBjeFdjN0dLU1doeFJtRVhWNCs5QUc5WW1oZWZOcXJIV3VidldvY1JFa3RkMXkzb0k4cDV6NFZ5OGZmQzg1SVVsTmxvN3VKNHEya1locmRiLzlwVldhRytXVWREWk9lSmJoblhZeTR2NlY4SU94MnUzZFdaZkdhOXZXc3RBOEJmRnZLWCtoSXFKUlo4UDNUWWhSRHJOd3YvNG9EcGdST21WSWpzdWsvKzQ2cFR5N2RxZVBHbGJTck56ck1aUHo3MWlvNDY1TkRaVkR2cGViOGMxNmJqYXhGTE9MZ29qZzYwU1U3clBTQnFaemUwRDJZSlR6eHVIaE9RZnVzUVpKOUNldUVnU254VlRkYm05WVlScjMzZlZlSnZqTmxtWEVIU0EyelVzWU5mUWo1RXdQa1pONWJMdE5oWk9FY2ZwZE94YlZveG9GOWtRS3ZwTEsyTjRGaU05RHp6YXcyTVlaOHRuVjVrbm5JQ3l3REJSL2Q0VUYrSHVwVk9EOTdHMzlseWtSYXdJRFhnSHNRcGhZNmRUc25PeUxnd01OTCtWdmtTblNhdHJHdzJyZTdmMzdKdGhDVk5sb1pKK1czNTNQWFhRSnVBYTIiLCJtYWMiOiJjNTljNmFkNWE5YzFlYWI5NmFhZGQ4MGYyNTUyNmIxMmE4MzgyYjJhYmFhYzJkZGM5YjA3M2EwNjMwNDg4MjZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-231828109 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-231828109\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1897507213 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:36:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhONDlLbHYxYk5LZXNJUWxaRWxLYkE9PSIsInZhbHVlIjoidHNEVitwdktjQVBDSjc2eCszem5rWXc5a0lSS0R5THdFWlh2UE1CUXFCY2lMVUlVd2QwV2MvbXZrcjJ1Nk5FcHZncDRFamRKYnlKcm9oMVlOeFYrS0dER0dtb2VuVDc2Mnd1RXhHY25GYTVTYm1sb2FFSDNDRk5Dd0pnNVI2a1gvK1RYb3NiZ2tqSXRlcDAzS1RyOFpKMlZqQXdvUm1PcW91cFM2VUNpRW05M3hpbk5QQlNCS21NVXZDZUdnSFlLaW51eXFTejRDcnQxNGJRb1ppaVNNdU83Qk9hb25zbTdObWZWbWd3eVUrK2RwRnQ4YUhXSTVuaSs1TlBKdk5HdDdNcVhTTzZTYkxia0lVVmVPSVhscWNLWUp3WEMxVG5MSDVPTmVVelFqZmVFb0Uwbi8zYm9yWFZVdjl0OTV6TUx4UlgyZnJOdnR2TDJ0RUtvOEpLZGlJd3JPelROTzk3WEJVZWQzTHJiVHJPMUExVEhJeHJYc3ZaUWNsWVlvdTU4dFhYQnVIeWhtb2NYbVAyYTlHNlBYRHA3L2VyTE9iMjdrQzErWmZkMThTeGtENDdaRHJCYkdNeERTbXV0NjliaDFhYkZTdXNsRHQwbVNuS1kzZ2UrcE1HTDVTSVNnQXFndjFyc0hzMnJEN0d6UEx5K1JtVkNld01zLy83ekVkdy8iLCJtYWMiOiI0YTQ5NmIyZWNiNzJkMDk1MTRjYTE1NzAyZTI5YmY5MGNjOTdkMGVlODgzYWExMjJkYWM4MWFkZGM1MjBmMDEzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:36:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImxxMkZQVkl3dzdKY2VSRFJsalB6bGc9PSIsInZhbHVlIjoiUVBnR0dMUVFFK3NVekp5U2c4TW1lNU03Q2t2YkQyQU9FeXR0NU9rRzROaHFqL2pqcHlUMDRXZ1JJbE5GWE9kL3N5NTBMVlN1cGpHaWVXUlpIaXN3ZURsN2pSdXl0Vk51c3NVMjBCaTBMUWovazY2N0NKbCtNTzFjYldMUzhSMHhCWEJ2VHFsVjlTeGs2Wks4cm5xOTFSTElnTUZBS2NIZlF3cHJDN2wrRkRuVTZzSXN2S3NMNkkxNVFFUDJRcXgrbEF1cVFGS0tmNlhpVm0xaHhWRlIwZ0t0RWJRZzEzbkFxcHZXekVNclErTUMvRjJqTC9vazIxUndaSXhreWtRc0pqZld5VTlxSWtRNHR3ancyNDNrQ0hRU0UvQVV1UVRGbGtyL0dvNUs4US9neER5M3J0WnIwbEhsdkhxd2V3M2tPcFFmbkJjNnhmaE5oakJYL0dEMUtZNHBieUpyODNkczYwYWo2ZVNtUUJsbmMrYnNiWElFOStMTDg4dThzZFg3dHlGS3dQMjlEMFkvbGRhSnVCNURkQVZpZXVvS0FmUGR2Y3UrWWR6cUEvWTZKeitNTm5DdEhXUDRjLzNsTWZDOWRoOFBhOE9ZU002Mk5idWV4Z0poV09kM09kYkhTZ2ZzS1dUckJLT1FTejYrT0UwVWlHNzFxd0dMNUxMNWgxTFQiLCJtYWMiOiJmMjEwMzg0YTk5ZDBmNWYwN2M1ZmY2MjE1MWQ3NDU1Mzg3NGRlZjRlMTI1ODA2NWFhZTRhMGMxY2UzY2M0NzcyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:36:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhONDlLbHYxYk5LZXNJUWxaRWxLYkE9PSIsInZhbHVlIjoidHNEVitwdktjQVBDSjc2eCszem5rWXc5a0lSS0R5THdFWlh2UE1CUXFCY2lMVUlVd2QwV2MvbXZrcjJ1Nk5FcHZncDRFamRKYnlKcm9oMVlOeFYrS0dER0dtb2VuVDc2Mnd1RXhHY25GYTVTYm1sb2FFSDNDRk5Dd0pnNVI2a1gvK1RYb3NiZ2tqSXRlcDAzS1RyOFpKMlZqQXdvUm1PcW91cFM2VUNpRW05M3hpbk5QQlNCS21NVXZDZUdnSFlLaW51eXFTejRDcnQxNGJRb1ppaVNNdU83Qk9hb25zbTdObWZWbWd3eVUrK2RwRnQ4YUhXSTVuaSs1TlBKdk5HdDdNcVhTTzZTYkxia0lVVmVPSVhscWNLWUp3WEMxVG5MSDVPTmVVelFqZmVFb0Uwbi8zYm9yWFZVdjl0OTV6TUx4UlgyZnJOdnR2TDJ0RUtvOEpLZGlJd3JPelROTzk3WEJVZWQzTHJiVHJPMUExVEhJeHJYc3ZaUWNsWVlvdTU4dFhYQnVIeWhtb2NYbVAyYTlHNlBYRHA3L2VyTE9iMjdrQzErWmZkMThTeGtENDdaRHJCYkdNeERTbXV0NjliaDFhYkZTdXNsRHQwbVNuS1kzZ2UrcE1HTDVTSVNnQXFndjFyc0hzMnJEN0d6UEx5K1JtVkNld01zLy83ekVkdy8iLCJtYWMiOiI0YTQ5NmIyZWNiNzJkMDk1MTRjYTE1NzAyZTI5YmY5MGNjOTdkMGVlODgzYWExMjJkYWM4MWFkZGM1MjBmMDEzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:36:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImxxMkZQVkl3dzdKY2VSRFJsalB6bGc9PSIsInZhbHVlIjoiUVBnR0dMUVFFK3NVekp5U2c4TW1lNU03Q2t2YkQyQU9FeXR0NU9rRzROaHFqL2pqcHlUMDRXZ1JJbE5GWE9kL3N5NTBMVlN1cGpHaWVXUlpIaXN3ZURsN2pSdXl0Vk51c3NVMjBCaTBMUWovazY2N0NKbCtNTzFjYldMUzhSMHhCWEJ2VHFsVjlTeGs2Wks4cm5xOTFSTElnTUZBS2NIZlF3cHJDN2wrRkRuVTZzSXN2S3NMNkkxNVFFUDJRcXgrbEF1cVFGS0tmNlhpVm0xaHhWRlIwZ0t0RWJRZzEzbkFxcHZXekVNclErTUMvRjJqTC9vazIxUndaSXhreWtRc0pqZld5VTlxSWtRNHR3ancyNDNrQ0hRU0UvQVV1UVRGbGtyL0dvNUs4US9neER5M3J0WnIwbEhsdkhxd2V3M2tPcFFmbkJjNnhmaE5oakJYL0dEMUtZNHBieUpyODNkczYwYWo2ZVNtUUJsbmMrYnNiWElFOStMTDg4dThzZFg3dHlGS3dQMjlEMFkvbGRhSnVCNURkQVZpZXVvS0FmUGR2Y3UrWWR6cUEvWTZKeitNTm5DdEhXUDRjLzNsTWZDOWRoOFBhOE9ZU002Mk5idWV4Z0poV09kM09kYkhTZ2ZzS1dUckJLT1FTejYrT0UwVWlHNzFxd0dMNUxMNWgxTFQiLCJtYWMiOiJmMjEwMzg0YTk5ZDBmNWYwN2M1ZmY2MjE1MWQ3NDU1Mzg3NGRlZjRlMTI1ODA2NWFhZTRhMGMxY2UzY2M0NzcyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:36:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1897507213\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1684614361 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>19</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1684614361\", {\"maxDepth\":0})</script>\n"}}