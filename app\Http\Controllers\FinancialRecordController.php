<?php

namespace App\Http\Controllers;

use App\Http\Requests\FinacialDeliveryBill;
use App\Models\DeliveryFinancialRecord;
use App\Models\FinancialRecord;
use App\Models\PaymentVoucher;
use App\Models\Pos;
use App\Models\ReceiptVoucher;
use App\Models\Shift;
use App\Models\User;
use App\Services\FinancialRecordService;
use App\Services\FinancialTransactionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class FinancialRecordController extends Controller
{

    protected $financialRecordService;
    protected $financialTransactionService;

    public function __construct(FinancialRecordService $financialRecordService, FinancialTransactionService $financialTransactionService)
    {
        $this->financialRecordService = $financialRecordService;
        $this->financialTransactionService = $financialTransactionService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $openShift = Shift::whereNull('closed_at')
            ->where('warehouse_id', Auth::user()->warehouse_id)
            ->first();
        $is_sale_session_new = Auth::user()['is_sale_session_new'] && Auth::user()->can('manage pos');


        if (!Auth::user()->can('show financial record')) {
            return redirect()->back()->with('error', __('You are not authorized to perform this action'));
        }


        //return the oping balance form if he has the permission aka sales.
        if (Auth::user()->can('manage pos') && $is_sale_session_new) {
            return view('pos.financial_record.opening-balance');
        }

        if (Auth::user()->can('manage pos') && !$is_sale_session_new) {

            $financial_record = $this->financialRecordService->getWarehouseOpenedFinancialRecord(Auth::user());

            if (!$financial_record) {
                return redirect()->back()->with('error', __('No open Shift found on your warehouse'));
            }

            $receiptVouchers = ReceiptVoucher::where('created_by', Auth::user()->id)->where('shift_id',$openShift->id)->get();
            $paymentVouchers = PaymentVoucher::where('created_by', Auth::user()->id)->where('shift_id',$openShift->id)->get();

            //return the table
            return view('pos.financial_record.index', get_defined_vars());
        }

        //display the delevery fiancial record if he has the permission (delivery).
        //open a new delevery finanacial record if there is no open one.

        if (Auth::user()->can('manage delevery') && Auth::user()->type != "company") {

            $deliveryFinancialRecord = $this->financialRecordService->openNewDeliveryFinancialRecord(Auth::user());

            if (!$deliveryFinancialRecord) {
                return redirect()->back()->with('error', __('No open Shift found on your warehouse'));
            }

            //get all vaoucher send to the user
            $receiptVouchers = ReceiptVoucher::where('receipt_from_user_id', Auth::user()->id)->where('shift_id',$openShift->id)->get();
            $paymentVouchers = PaymentVoucher::where('pay_to_user_id', Auth::user()->id)->where('shift_id',$openShift->id)->get();


            return view('pos.financial_record.index_delivery', get_defined_vars());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    public function SetOpeningBalance(Request $request)
    {
        $warehouse = Auth::user()->warehouse_id;

        // التحقق من وجود المستودع
        if (!$warehouse) {
            return redirect()->back()->with('error', __('No warehouse assigned to user'));
        }

        $validated_opening_balance = $request->validate([
            'opening_balance' => 'required|numeric|min:0',
        ]);

        $shift = Shift::create([
            "shift_opening_balance" => $validated_opening_balance['opening_balance'],
            "is_closed" => false,
            "created_by" => Auth::user()->id,
            'warehouse_id' => $warehouse,
        ]);

        FinancialRecord::updateOrCreate(
            ['shift_id' => $shift->id], // Search by shift_id
            [
                'opening_balance' => $shift->shift_opening_balance,
                'created_by' => Auth::id(),
            ]
        );

        User::where('id', Auth::user()->id)->update([
            'is_sale_session_new' => false,
        ]);

        return redirect()->back()->with('success', __('Opening Balance has been set successfully'));
    }

    public function closeShift(Request $request)
    {
        $validated_shift_id = $request->validate([
            'shift_id' => 'required|numeric|min:0',
        ]);

        //update the shift and close it
        Shift::where('id', $validated_shift_id)->update([
            'is_closed' => true,
            'closed_at' => now(),
            'closed_by'=> Auth::user()->id
        ]);

        //update the user sales session
        User::where('id', Auth::user()->id)->update([
            'is_sale_session_new' => true,
        ]);

        return redirect()->route('hrm.dashboard')->with('success', __('Shift Closed Successfully'));
    }

    public function financialType(Request $request)
    {
        $data = $request->all();

        if (!isset($data['payment_type']) || empty($data['payment_type'])) {
            return redirect()->back()->with('error', __('Payment type is required'));
        }

        try {
            // Process the payment using centralized handling
            $result = $this->financialRecordService->handlePayment(
                Auth::user(),
                $data['payment_type'],
                $data['total_price'],
                $data
            );

            if (!$result['success']) {
                return redirect()->back()->with('error', $result['message']);
            }

            // Create financial transaction record
            $transaction_data = [
                'cash_amount' => $data['total_price'],
                'payment_method' => $data['payment_type'] === 'network' ? 'bank_transfer' : $data['payment_type'],
            ];

            $this->financialTransactionService->createFinancialTransaction(
                Auth::user(),
                "sale",
                $transaction_data
            );

            // إنشاء الفاتورة النهائية مباشرة
            try {
                // استدعاء طريقة store من PosController مباشرة
                $posController = new \App\Http\Controllers\PosController();
                $createRequest = new \Illuminate\Http\Request();
                $createRequest->merge($request->all());

                $response = $posController->store($createRequest);

                // التحقق من نوع الاستجابة
                if ($response instanceof \Illuminate\Http\JsonResponse) {
                    $responseData = $response->getData(true);

                    if (isset($responseData['code']) && $responseData['code'] == 200) {
                        // إرجاع شاشة النجاح مع معرف الفاتورة
                        return view('pos.payment_success', [
                            'success_message' => $responseData['success'],
                            'pos_id' => $responseData['pos_id'],
                            'pos_number' => isset($responseData['pos_number']) ? $responseData['pos_number'] : \Auth::user()->posNumberFormat($responseData['pos_id']),
                            'payment_type' => $data['payment_type'],
                            'total_amount' => $data['total_price'],
                            'payment_data' => $data
                        ]);
                    } else {
                        return redirect()->back()->with('error', $responseData['error'] ?? __('Payment processing failed'));
                    }
                } else {
                    // في حالة إرجاع redirect أو view
                    return $response;
                }
            } catch (\Exception $e) {
                \Log::error('POS Payment Error: ' . $e->getMessage());
                return redirect()->back()->with('error', __('An error occurred during payment processing: ') . $e->getMessage());
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    public function opinningBalace(Request $request)
    {
        try {
            // تسجيل مفصل للتشخيص
            \Log::info('Opening Balance Request Started', [
                'user_id' => Auth::id(),
                'warehouse_id' => Auth::user()->warehouse_id,
                'is_sale_session_new' => Auth::user()->is_sale_session_new,
                'has_manage_pos_permission' => Auth::user()->can('manage pos')
            ]);

            $is_sale_session_new = Auth::user()['is_sale_session_new'];

            // التحقق من وجود المستودع
            if (!Auth::user()->warehouse_id) {
                \Log::warning('No warehouse assigned to user', ['user_id' => Auth::id()]);

                if ($request->ajax()) {
                    return response()->json(['error' => __('No warehouse assigned to user')], 400);
                }
                return redirect()->back()->with('error', __('No warehouse assigned to user'));
            }

            // توحيد الصلاحية المطلوبة مع method index()
            if (Auth::user()->can('manage pos') && $is_sale_session_new) {
                \Log::info('Returning opening balance view');

                // التأكد من وجود الـ view
                if (!view()->exists('pos.financial_record.opening-balance')) {
                    \Log::error('View not found: pos.financial_record.opening-balance');

                    if ($request->ajax()) {
                        return response()->json(['error' => 'View file not found'], 500);
                    }
                    return redirect()->back()->with('error', 'View file not found');
                }

                return view('pos.financial_record.opening-balance');
            } else {
                \Log::warning('Permission denied for opening balance', [
                    'user_id' => Auth::id(),
                    'has_permission' => Auth::user()->can('manage pos'),
                    'is_sale_session_new' => $is_sale_session_new
                ]);

                if ($request->ajax()) {
                    return response()->json(['error' => __('Permission denied.')], 403);
                }
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        } catch (\Exception $e) {
            \Log::error('Opening Balance Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if ($request->ajax()) {
                return response()->json(['error' => $e->getMessage()], 500);
            }
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    // Route للتشخيص - يمكن حذفه بعد حل المشكلة
    public function debugOpeningBalance(Request $request)
    {
        $user = Auth::user();

        $debug_info = [
            'user_id' => $user->id,
            'user_name' => $user->name,
            'warehouse_id' => $user->warehouse_id,
            'is_sale_session_new' => $user->is_sale_session_new,
            'has_manage_pos_permission' => $user->can('manage pos'),
            'view_exists' => view()->exists('pos.financial_record.opening-balance'),
            'route_exists' => route('pos.financial.opening.balance'),
            'current_time' => now(),
        ];

        if ($request->ajax()) {
            return response()->json($debug_info);
        }

        return response()->json($debug_info);
    }

    public function finacialdeleveryBill(FinacialDeliveryBill $request)
    {
        try {
            $validated = $request->validated();

            // التحقق من وجود نوع الدفع
            if (!isset($validated['payment_type']) || empty($validated['payment_type'])) {
                if ($request->ajax()) {
                    return response()->json(['error' => __('Payment type is required')], 400);
                }
                return redirect()->back()->with('error', __('Payment type is required'));
            }

            $result = $this->financialRecordService->handleDeliveryBill($validated);

            if (!$result['success']) {
                if ($request->ajax()) {
                    return response()->json(['error' => $result['message']], 400);
                }
                return redirect()->back()->with('error', $result['message']);
            }

            // Handle different payment types
            if ($validated['payment_type'] === 'split') {
                // For split payments, create two transactions - one for cash and one for network
                $cash_amount = floatval($validated['split_cash_amount']);
                $network_amount = floatval($validated['split_network_amount']);

                $cash_transaction_data = [
                    'cash_amount' => $cash_amount,
                    'payment_method' => 'cash',
                ];

                $network_transaction_data = [
                    'cash_amount' => $network_amount,
                    'payment_method' => 'bank_transfer',
                ];

                $this->financialTransactionService->createFinancialTransaction(Auth::user(), "sale", $cash_transaction_data);
                $this->financialTransactionService->createFinancialTransaction(Auth::user(), "sale", $network_transaction_data);
            } else {
                // For regular payments (cash or network)
                $transaction_data = [
                    'cash_amount' => $validated['total_price'],
                    'payment_method' => $validated['payment_type'] === 'network' ? 'bank_transfer' : $validated['payment_type'],
                ];

                $this->financialTransactionService->createFinancialTransaction(Auth::user(), "sale", $transaction_data);
            }

            // إذا كان الطلب AJAX، إرجاع شاشة النجاح
            if ($request->ajax()) {
                $pos_id = $validated['pos_id'];
                $payment_type = $validated['payment_type'];
                $total_amount = $validated['total_price'];
                $payment_data = $validated;

                return view('pos.payment_success', compact('pos_id', 'payment_type', 'total_amount', 'payment_data'))->render();
            }

            return redirect()->back()->with('success', $result['message']);
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json(['error' => $e->getMessage()], 500);
            }
            return redirect()->back()->with('error', $e->getMessage());
        }
    }
}
