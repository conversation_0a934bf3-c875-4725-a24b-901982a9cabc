{"__meta": {"id": "Xf1cea2476bde8c70ae4199781e1a0f2c", "datetime": "2025-06-07 23:22:17", "utime": 1749338537.003643, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.066444, "end": 1749338537.003672, "duration": 0.9372279644012451, "duration_str": "937ms", "measures": [{"label": "Booting", "start": **********.066444, "relative_start": 0, "end": **********.861418, "relative_end": **********.861418, "duration": 0.7949740886688232, "duration_str": "795ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.861447, "relative_start": 0.7950031757354736, "end": 1749338537.003675, "relative_end": 3.0994415283203125e-06, "duration": 0.1422278881072998, "duration_str": "142ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45054616, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01709, "accumulated_duration_str": "17.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.940377, "duration": 0.01472, "duration_str": "14.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.132}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.972389, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.132, "width_percent": 6.554}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9878569, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.686, "width_percent": 7.314}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2074215242 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2074215242\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-768110993 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-768110993\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-975515425 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-975515425\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749338528301%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNQY0pXbGc5TDFWelJ3Q21ET0R0eUE9PSIsInZhbHVlIjoiTENNNUJ3aXk4T294dVdJK2lhZXduZzVTRG9RTjVDaldpdlNKRkZTN2NYMGpiTFJraUJhWUVOU1FjUjM3Y3V0dlZyZTI4UC8vaEV6dHlmall2YXNkQlJHZGNROStQZEVkZUQ0eEVxSmo2VnRlVFNPV0haaGEyTzUra1M4dmFwREtwUGZyajdmQmRpUHlXV3JPRjJMbXZ5d25FUGt6RlA2ZnVDZkVDU1p5T0kzME4xbGJyQjJ4WHdCRENIM3kvcTBiYlZzVjVYdUN1ckluTE5sTkhLTUlDQ0dxZWdLcXlGdHFhOEJrd3I4QnhDZnR4NlNzZm1YdmZIUm9CTHdIc0Y5cWRJT2hYb2prSFZ6M0UzV2p1TG5zU09IZXNiUTl2M3p2Z1hyWmFObUF3ZlZzTUw1bEpwNnZqMDhrSVdKay9UZ203dTlPdlpLaFpzeHhxdkpkc0xoZzRHWkVVMmdNTGhseXdZU0hITWtjTVltbTYrTm1aT2F6bWU3aEpweFROM1dmc0RncnJuRjVNZjR1eGNXaWluTXM1em9DYjhiU0N3NmR2bk5UbWxpdGpORisxdzc1M3pJS2d0SGRZeU1JMWpCRzhVaGcrekU1OC93eFNEUGhEbHprTm1oZ0tRbElkelQwT2x0UFBYbmRvajJQN1gwK2F1WGs5TW5iRkQ3NXRSNHIiLCJtYWMiOiI1NjkxMTI2YWIyYTdhMWFlNmViYTBlNTkxOWRmOTllMzFlNmY0OTBjZTg2OGUwMjM5OTk4ODVjNjIwZmMwNTE1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjN6UWVVRTFiUFhJVjlkQW5CNERvNVE9PSIsInZhbHVlIjoia0w5NHhWelJJM3BsV2ErWm1vWVIzNkd6QnN3ZWdKRFljbUw3MnVpTUtlZWNMbE5DOTAxVE4xcTVCalRPbm9yUVFXTUVocTFObHp3aFFTRGVRWEZHRGJYTk5TTytuTWdUcWd5VDRYTm13RW1SMis4UlNZQ2FNWkRDQTRnamorM1lqTWVhZjNyOXRuWDlaait1RkMrazA2cHhNQ0xzaS9janVpQ2VIWEVobG81dHVKdjY1bmFTa2xtTUZjRHJlK3E2d3hhL1VvUm5NU2JJOUk0SllYeWJOV2J4Y1pURFU0SEsxRFVvNEI5Q0l4SUFZRytvSkthNGRMdlVncVNIeGNjZnVmUmNqLzhFLytqa3ZJQUZWMHB0LzhGMVZwZjlCYlBCSXhwSElzRW9kREtleSs1SzA2RmVFUEU4NXpiVnhTT0ljT0ZVVXBFeFFiSk5aMmkyRkdGQ2lHZ1JJREU2eGp2MzFYZWppQk5qeWQ5TmY0dnJqcXpJUkhENkRaS3AwanlZYkpOUEJKSE51dWlZbDFHdDl0b0tnUkJ2NHBCbzVmemU3MHJBNytOMElOQ05hY045U0ZITUM0MmVTMjNSdGtnV2Y0dzVwTzArREo5Q0tvK04vODFnSWFUVWNEeFRXNmM1Q3JWWUc1UGZxT09xcXk1bVJuRkZWOWNnT1Y2Y3RFbHYiLCJtYWMiOiI5MjdiNzY4Njk3NjdjYzE5MjQ0MmU0OTI0YzY0N2JiZjA5OTUzNjVmNzBhNGE3N2MxM2ZiYTVjMjU5MWU1MWRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2010193556 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2010193556\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:22:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdQZW81MzNWc25rbGplOU1aOVJhd0E9PSIsInZhbHVlIjoiakhURjZyNjNpdE1PaVlrZWVXbnhtRmxZang5em5jb3A4UXk4WHc5K2o0RC9valhPSzZpbklTRW9nWE1JWXRBZEx5M1J1dE5BLzdyajYvNWROOGxLRFFjRWJjUW16d1hvOG9UZTFRa0VlaGFKYVhjdEROTjJlaGE5TksycUJYc0JqWllpeHdsenRNeVVZRHNNRHl2ZGJVUnpsWW0rL0dVcXVmUU9WQ0RIYW9haVdRYkpoN2NNbzc2aEZTeVUvOFdWaWtyeDdjY0t1a0h1OFBLZUhIQlhEUDBiQTUzTjBvSEJqdUIwMVRTWjNBeHB6dEpOOFMyWndOM09ZOTZ3Wm82dTA4SnlCNGZ3bktKT3VhY0hGQWY5bkJFajlDMmtzNm5FMkFCZVhIc1A4OUx4SWdzamFwNXpvUkNxU2EyUVVwVTJIcWt2RmRiaDByb2J5N0J0QWNsUEFaQXRJcUFRNEdWZ1k0RFlPZEFrdFN2MDAxYWUrVkQrenZIbVVyeWE1WUVEUXRGemg0NGZGSWpOTW1KalJFR0VLMU1TL3c5ZjN3QWVmZlU4UWtLWVZ6eTJCTG1mOUwvQkdtVk5HeGlZa3hndm5oWVVoR3R4MTR5b21BOVZGUVJLYnkzSjB6ZkxRRlljcFhiaFZ3SkJ0WVhLTGdEYkFPN3hSYkNTTEN4Smg4QjkiLCJtYWMiOiI5NTI5OGE2NDlmNzA2YzEyZDFjNjkxOGRiNDQwNWY1NjBlZWMwNmNhYTBmZjA2ODA5YWJhM2Y2NGRmYzAzYTZlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:22:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjhXYTJKOFc3cXFqSUN6V3NtNzg5eVE9PSIsInZhbHVlIjoiT2JKcm5PTWx1bDJRaHoyNzF2MlZYYjNsb0h4QytYS05PQXJaTnVlNGJaZStFNHNsMncwZnlQOGJYREZGTXJWZVYxZUhxWFpZZGQ3UnEyWGZLN2FBNnFDVTlEREZuRTU0S1FxME1JRmx4aFNwblhONmRWT1VlZXVDSTMxbSswREZkZFJCMGM5ZWxXZ1k0SjhCVG1RLy84VXV4TEUyV0tIZE1QNHgrZWtrbGhSZUFSaEx2WVc2OTVManNqcS9LVWtNc2dzc1dVMld3SWZmV1NkUHZ1dWc1ZXBSTndoLzcwNUNUT3c4dmNZM0ZiWEZpblY4UXBMbkZTcDIwTW92U1ZJRFhDTnB4N3hEMzdXRXFuRFhZVGpmeFZiN0lFekVpOS9oRzRLWnd0cHRpQi8wUTZFTVJqaW5CTXVSK1NnR1ZMd24yL2libXp4RUlIQUtCNWlOb2lQdFB2TkgvMmRrb2JOR2wrZmFlN3o4K3BPV1N1d05aYXpBWUFILytyYk8zNDVFaWtUVS9Lbm1zSWFaaXp4KzFKMUdUV1NLYUtDdzNaYmtGSTlNOFZGMWZuZVg5dWRTeGtodDNkbWlCVEFyRm1sd0pVVEoreXpUYWMwZzBCbXFFZ2NVSlhmWFNJVjA5VnUyZGxnaExRRmxDMm1SdnB2VmFJYU1GNXZDc3RlNFNON20iLCJtYWMiOiIwMThiNGYxNzViZTBiMTdmZDJhNzRkMzM2ODQzZjYzMzFkMDQ5MWE4YzFhOGYxN2JiODgwZDM1NWQ4YzdjYTVjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:22:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdQZW81MzNWc25rbGplOU1aOVJhd0E9PSIsInZhbHVlIjoiakhURjZyNjNpdE1PaVlrZWVXbnhtRmxZang5em5jb3A4UXk4WHc5K2o0RC9valhPSzZpbklTRW9nWE1JWXRBZEx5M1J1dE5BLzdyajYvNWROOGxLRFFjRWJjUW16d1hvOG9UZTFRa0VlaGFKYVhjdEROTjJlaGE5TksycUJYc0JqWllpeHdsenRNeVVZRHNNRHl2ZGJVUnpsWW0rL0dVcXVmUU9WQ0RIYW9haVdRYkpoN2NNbzc2aEZTeVUvOFdWaWtyeDdjY0t1a0h1OFBLZUhIQlhEUDBiQTUzTjBvSEJqdUIwMVRTWjNBeHB6dEpOOFMyWndOM09ZOTZ3Wm82dTA4SnlCNGZ3bktKT3VhY0hGQWY5bkJFajlDMmtzNm5FMkFCZVhIc1A4OUx4SWdzamFwNXpvUkNxU2EyUVVwVTJIcWt2RmRiaDByb2J5N0J0QWNsUEFaQXRJcUFRNEdWZ1k0RFlPZEFrdFN2MDAxYWUrVkQrenZIbVVyeWE1WUVEUXRGemg0NGZGSWpOTW1KalJFR0VLMU1TL3c5ZjN3QWVmZlU4UWtLWVZ6eTJCTG1mOUwvQkdtVk5HeGlZa3hndm5oWVVoR3R4MTR5b21BOVZGUVJLYnkzSjB6ZkxRRlljcFhiaFZ3SkJ0WVhLTGdEYkFPN3hSYkNTTEN4Smg4QjkiLCJtYWMiOiI5NTI5OGE2NDlmNzA2YzEyZDFjNjkxOGRiNDQwNWY1NjBlZWMwNmNhYTBmZjA2ODA5YWJhM2Y2NGRmYzAzYTZlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:22:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjhXYTJKOFc3cXFqSUN6V3NtNzg5eVE9PSIsInZhbHVlIjoiT2JKcm5PTWx1bDJRaHoyNzF2MlZYYjNsb0h4QytYS05PQXJaTnVlNGJaZStFNHNsMncwZnlQOGJYREZGTXJWZVYxZUhxWFpZZGQ3UnEyWGZLN2FBNnFDVTlEREZuRTU0S1FxME1JRmx4aFNwblhONmRWT1VlZXVDSTMxbSswREZkZFJCMGM5ZWxXZ1k0SjhCVG1RLy84VXV4TEUyV0tIZE1QNHgrZWtrbGhSZUFSaEx2WVc2OTVManNqcS9LVWtNc2dzc1dVMld3SWZmV1NkUHZ1dWc1ZXBSTndoLzcwNUNUT3c4dmNZM0ZiWEZpblY4UXBMbkZTcDIwTW92U1ZJRFhDTnB4N3hEMzdXRXFuRFhZVGpmeFZiN0lFekVpOS9oRzRLWnd0cHRpQi8wUTZFTVJqaW5CTXVSK1NnR1ZMd24yL2libXp4RUlIQUtCNWlOb2lQdFB2TkgvMmRrb2JOR2wrZmFlN3o4K3BPV1N1d05aYXpBWUFILytyYk8zNDVFaWtUVS9Lbm1zSWFaaXp4KzFKMUdUV1NLYUtDdzNaYmtGSTlNOFZGMWZuZVg5dWRTeGtodDNkbWlCVEFyRm1sd0pVVEoreXpUYWMwZzBCbXFFZ2NVSlhmWFNJVjA5VnUyZGxnaExRRmxDMm1SdnB2VmFJYU1GNXZDc3RlNFNON20iLCJtYWMiOiIwMThiNGYxNzViZTBiMTdmZDJhNzRkMzM2ODQzZjYzMzFkMDQ5MWE4YzFhOGYxN2JiODgwZDM1NWQ4YzdjYTVjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:22:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2016688034 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2016688034\", {\"maxDepth\":0})</script>\n"}}