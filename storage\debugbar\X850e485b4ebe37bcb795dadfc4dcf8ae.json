{"__meta": {"id": "X850e485b4ebe37bcb795dadfc4dcf8ae", "datetime": "2025-06-30 15:35:04", "utime": 1751297704.000425, "method": "GET", "uri": "/pos-payment-type?vc_name=6&user_id=&warehouse_name=9&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.177167, "end": 1751297704.000449, "duration": 0.82328200340271, "duration_str": "823ms", "measures": [{"label": "Booting", "start": **********.177167, "relative_start": 0, "end": **********.839139, "relative_end": **********.839139, "duration": 0.6619720458984375, "duration_str": "662ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.839155, "relative_start": 0.6619880199432373, "end": 1751297704.000453, "relative_end": 4.0531158447265625e-06, "duration": 0.16129803657531738, "duration_str": "161ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53323496, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.bill_type", "param_count": null, "params": [], "start": **********.994955, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/bill_type.blade.phppos.bill_type", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Fbill_type.blade.php&line=1", "ajax": false, "filename": "bill_type.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.bill_type"}]}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1420\" onclick=\"\">app/Http/Controllers/PosController.php:1420-1528</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.008230000000000001, "accumulated_duration_str": "8.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.89955, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 31.592}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.91707, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 31.592, "width_percent": 9.235}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.939507, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 40.826, "width_percent": 11.908}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.9428189, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 52.734, "width_percent": 11.543}, {"sql": "select * from `customers` where `name` = '6' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["6", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1431}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.950574, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1431", "source": "app/Http/Controllers/PosController.php:1431", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1431", "ajax": false, "filename": "PosController.php", "line": "1431"}, "connection": "kdmkjkqknb", "start_percent": 64.277, "width_percent": 9.235}, {"sql": "select * from `warehouses` where `id` = '9' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["9", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1432}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.953989, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1432", "source": "app/Http/Controllers/PosController.php:1432", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1432", "ajax": false, "filename": "PosController.php", "line": "1432"}, "connection": "kdmkjkqknb", "start_percent": 73.512, "width_percent": 8.627}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 527}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1436}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9592762, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "PosController.php:527", "source": "app/Http/Controllers/PosController.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=527", "ajax": false, "filename": "PosController.php", "line": "527"}, "connection": "kdmkjkqknb", "start_percent": 82.139, "width_percent": 9.842}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1515}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.981386, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1515", "source": "app/Http/Controllers/PosController.php:1515", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1515", "ajax": false, "filename": "PosController.php", "line": "1515"}, "connection": "kdmkjkqknb", "start_percent": 91.981, "width_percent": 8.019}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1602647928 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602647928\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.948905, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1675393353 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1675393353\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.958164, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:2 [\n  1839 => array:9 [\n    \"name\" => \"ميداليات شكل محتلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"1839\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  1877 => array:8 [\n    \"name\" => \"cleaner Mop\"\n    \"quantity\" => 1\n    \"price\" => \"15.00\"\n    \"tax\" => 0\n    \"subtotal\" => 15.0\n    \"id\" => \"1877\"\n    \"originalquantity\" => 4\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1582860193 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>9</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1582860193\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1041106293 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1041106293\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-471485716 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=ffd61i%7C1751297684545%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpGQlBhcjU1dHRzdkppUGYxcVMzRHc9PSIsInZhbHVlIjoiOUJ4Z0pUS1dYM2d5M3dBSjBGN3FOQ1doRy9BT1E0bk9CNEFSbzdGTFB0Y2VLNjdzWjUrSzY1L2FUVG1hNWljaUZXTlpOLy9BRFh2c3U1TlU2Z2d2N3d1UGx5NU05a25ScWhabGR1amRKbUMwRnJ5bEdUa1pVTGJmS3hPd0hZK3hWK2Y5NmV1YzVQTlZvbWt6NU83TTY0SXdZMFRmVGQ4eTIzTlZWVmluYXZ1My9scDJHcVRsN3lKeDhOeWtZdEZQeTUra3BDNW1EK0tlQkd6S091TW1URmV1VnE4SFVCWEhnWkpGOWFSNWZxa0VybmkvQldFd0ZFMXhqSWhxdUdrWks2Z1RXOTNpNUFwSG9OcTN0Y1VCcTJmamxNc2NBR1BFMTlsOHExK3hsRFlPTnZxRyt1bS9nSXRTNWYvN1M4czJYVE51VEZ3amtLeTkxenJ3NGJTNzhhVERZWWw5YXozNmxIbTZRZ0lXZFBDdGZVQXA2WlNLU0VzV1YyajVmS1kvWkw1dnVVSmdxaWJtU3VMWFpYVzVUOS9lNDcxeVlHdW1EcTJNRHBaQkxrcHc2d1dES1cxODl3cjIveHRnNjEwQVg0WGdudndxS2hqeCs0R3RwV0EwdWJSS0I1RXBFeHBjRkZoZXNobDJqVk12UVJyQ3pkekhFN2ZjYzFaWjJHamgiLCJtYWMiOiJhMjUwMjFjOTEzMzYzMTM3OGQ0Y2M5ZGQ2ZTI5MWNmYjgxYmIwYjNjZTdiMDY4OGI1MzBjNWJlZThiODJhOGRlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik8vdTN0YkRMUkRIeGxWbzZlWDE0bUE9PSIsInZhbHVlIjoiOW9rMVUzOE04ZUU0L3dQeTlIWjQxYVdhaUlSaDJSMVdLb0l0ZjRobW1qZmZIaVZjRE9mdno2bXFtdXdRbGRXMjBqcHU0RUtjWDlFY2U4OEhnaHNuNEZROWh3NVFQRHN5MEpZTVpsR3hKSzM1YjZrUzZleFdSZ1VxNHM5c0k3UUV6ZkhNYythc3E3SHlURkEzajY3VUJ5eVFvQ1psbmFRaXNvY2g3TnlVeFpsVGhXQVZncWo2NzhGZzlsUWdMa2QxZk54eUt3aXJMakUzZTBDM3J0QXVSaWdtVmJ3QVlzTGpycTNWdVZ1aERPWEh5MnlDbmZoWnU1czFXVzdlck5YT0VuYnZHVDllSncxYVZFNzVJbXp4ejVkWkNYYmZOSVA4RVZrZkc5Q3ovV2FhcHpwMXVnNTBWcXZzZjZQMFlmQllBZmpGRld6S25Rcmg4RjV3ZHlVRVZBZUszdzVPU0VmUkVGa0k1Z3B5K1RyWVBZc2lqOEhnWStiU2V4di9sV1gwR3pIdHVqVmRWMkxEUzZEd2paVGU2SGxrVU14Y1BkanpKWGd1bWJtNzVqYmpjbnNhWS90U3NXdml1aDJRYlQ0WnpSakZaVDdQaFZiTXdUVjlSWVhEMmJHSUhiTmFPblVXWEE0WU13YVJ6SE1IUS9maTA3V0Y3NFVRK1EzeWdtNTQiLCJtYWMiOiIxMDAwYzZlZWRhZDA4NjIyOGRmNDcyYTg4NDk4ZGE2NDg1MmQ4YzZkNjM5NzMzZTMwMDVlMzc0MzRjNjE5N2MwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-471485716\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1203057251 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1203057251\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1087149613 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:35:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNqUnJvTTB5R3NGMzdkWmV0SVB3aEE9PSIsInZhbHVlIjoiTDF4MHJEV2hGc2ZxaWU1eXpKL1JVOHVKZ3dZZGljTlZFWC9ydTIzTzNUUk4vdGFPYkZvU0dTSlRadmp0UC9XN2VPT0M0NkFXMGpwQ3FMUE5hd3lJVk8zK25OYUx6RVBteWZpZ1RwSTdTeGJYVG5KTllGMjdJczBiM21wU2hKVGt4dGh1WEJrd3V1VmN0L21TWGlmbXdkTzF6Vi8wMWdCdXNYOEpVVnJHOW1GeW1HcGdQLytmckxZeUJoSlJQUndxdFkzTWpLSDdsVjVFd2VPTGlqSUtqMDdnWW1jRFpGazJoRXdWTk50Y2xPUllkVGtJTkNmYTZVdExTblcwL0Y2SkR5SjNtOGp5TEhnMVlGSnhQQzdUUXBaWVAzcXV0emtjYXU3cEp4Mm9nVENFbzQ4Z2FXbG5sL3Zqb0xaTWtjSHNKcDZoZFhvQXExRHlOWFBNKzh0bms1VkVsUzdranl5YnFjQ3Z4M1ExQVBsSEVLaE5VZ2E4YzFtU0QrTEVaSzdaYk5mWUJyTDFhNXdyVS9MYk1lTjF0Z2FRU3hjSFFLRFptVjFZTWxMY1lxS25BT1d6eGpTdFRTL2VBTGZqVjd3MjIvd1VPOGxLUStQNjR0TG1kNTl4T2hLSStxaVBFTnduSFNkdFVKTmVOdjFxNVFxMklRUzZYNUhIRUptWDFIWGEiLCJtYWMiOiJlNjRmZDFkNjk4NzM0ZTM5YWE5YTZhMGVmYjIyMjMyYTU1MjdhZGUyNzBjNDg1NWQwMWY5NjVhN2QyMDMyNTc1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:35:03 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkM1WFViWFNMVTBEeXY1V2hhUUoraXc9PSIsInZhbHVlIjoiMnFkeDNMQmFkVmlmRGh4T09BWFg0bGxNUmZ6KzZFNWo2dWdTemVOMitSMVlKWENQV2U0K2JmbTJFL3h6c1ArMkNpRlk3T0ErSW1UcHdzcGgzdGVIcGlaM05YazhPYXZoeHJzSEljekM4UnJ3eHB1YUY4VW1RMW9DcUVRQk9lbzdUaWxwbXZDMklWVW5McVRXRldzRHhRbmhLZ0J3dEpXTzIxYkxFN3ZmYUcxeTZPa3NOVHZXdVBBR1o4UjVXVWVlaU91dUQ0bnRreDVmTEYzMllPOTVwdGNzZ2dXV0ZPSjlmUXZnVzhKcm84MjJ0QUtjMFd5WFFTcWxIanBNemZVZWVZaFNGT3htNkk2elVVWmdjTE5lNzNvekZ5cVBhdm9QV3Y2NmVvUHRXOXVWdjhqZVIwTHVobTN5TkExNm5PUEZXakl0YW1QQ3RmN0EzQUZKQ2lvdzNSRlBKQThlZmEvemNhVjdLb0JMWk12V3VQUVEvRVMyakhCY3U0Wnd4VDZFWHJVcC9MdVVSR0k1dVkraGFsK1pDV1NJcS9TTVZTeElXQm15L292K0JPZ2tIL0hLN0VXNlVLVXJ1NmJKREJKeERHV2lQVE5JWjhPQWJ0RjM2clJodWV3S0RsRnZBWng1SER6QllTQ285VDhWRXdiZEkrdWZWWGhVbDN1d2xOSnciLCJtYWMiOiJkOTA3ZmRmYjI3YmE3NjljMWMzNmVjYzFjNWM2ZTE5OWE5YmFmMDY1ZjE1YTM5Y2E1MDhjYTUxNTFkMWZlZGM1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:35:03 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNqUnJvTTB5R3NGMzdkWmV0SVB3aEE9PSIsInZhbHVlIjoiTDF4MHJEV2hGc2ZxaWU1eXpKL1JVOHVKZ3dZZGljTlZFWC9ydTIzTzNUUk4vdGFPYkZvU0dTSlRadmp0UC9XN2VPT0M0NkFXMGpwQ3FMUE5hd3lJVk8zK25OYUx6RVBteWZpZ1RwSTdTeGJYVG5KTllGMjdJczBiM21wU2hKVGt4dGh1WEJrd3V1VmN0L21TWGlmbXdkTzF6Vi8wMWdCdXNYOEpVVnJHOW1GeW1HcGdQLytmckxZeUJoSlJQUndxdFkzTWpLSDdsVjVFd2VPTGlqSUtqMDdnWW1jRFpGazJoRXdWTk50Y2xPUllkVGtJTkNmYTZVdExTblcwL0Y2SkR5SjNtOGp5TEhnMVlGSnhQQzdUUXBaWVAzcXV0emtjYXU3cEp4Mm9nVENFbzQ4Z2FXbG5sL3Zqb0xaTWtjSHNKcDZoZFhvQXExRHlOWFBNKzh0bms1VkVsUzdranl5YnFjQ3Z4M1ExQVBsSEVLaE5VZ2E4YzFtU0QrTEVaSzdaYk5mWUJyTDFhNXdyVS9MYk1lTjF0Z2FRU3hjSFFLRFptVjFZTWxMY1lxS25BT1d6eGpTdFRTL2VBTGZqVjd3MjIvd1VPOGxLUStQNjR0TG1kNTl4T2hLSStxaVBFTnduSFNkdFVKTmVOdjFxNVFxMklRUzZYNUhIRUptWDFIWGEiLCJtYWMiOiJlNjRmZDFkNjk4NzM0ZTM5YWE5YTZhMGVmYjIyMjMyYTU1MjdhZGUyNzBjNDg1NWQwMWY5NjVhN2QyMDMyNTc1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:35:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkM1WFViWFNMVTBEeXY1V2hhUUoraXc9PSIsInZhbHVlIjoiMnFkeDNMQmFkVmlmRGh4T09BWFg0bGxNUmZ6KzZFNWo2dWdTemVOMitSMVlKWENQV2U0K2JmbTJFL3h6c1ArMkNpRlk3T0ErSW1UcHdzcGgzdGVIcGlaM05YazhPYXZoeHJzSEljekM4UnJ3eHB1YUY4VW1RMW9DcUVRQk9lbzdUaWxwbXZDMklWVW5McVRXRldzRHhRbmhLZ0J3dEpXTzIxYkxFN3ZmYUcxeTZPa3NOVHZXdVBBR1o4UjVXVWVlaU91dUQ0bnRreDVmTEYzMllPOTVwdGNzZ2dXV0ZPSjlmUXZnVzhKcm84MjJ0QUtjMFd5WFFTcWxIanBNemZVZWVZaFNGT3htNkk2elVVWmdjTE5lNzNvekZ5cVBhdm9QV3Y2NmVvUHRXOXVWdjhqZVIwTHVobTN5TkExNm5PUEZXakl0YW1QQ3RmN0EzQUZKQ2lvdzNSRlBKQThlZmEvemNhVjdLb0JMWk12V3VQUVEvRVMyakhCY3U0Wnd4VDZFWHJVcC9MdVVSR0k1dVkraGFsK1pDV1NJcS9TTVZTeElXQm15L292K0JPZ2tIL0hLN0VXNlVLVXJ1NmJKREJKeERHV2lQVE5JWjhPQWJ0RjM2clJodWV3S0RsRnZBWng1SER6QllTQ285VDhWRXdiZEkrdWZWWGhVbDN1d2xOSnciLCJtYWMiOiJkOTA3ZmRmYjI3YmE3NjljMWMzNmVjYzFjNWM2ZTE5OWE5YmFmMDY1ZjE1YTM5Y2E1MDhjYTUxNTFkMWZlZGM1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:35:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1087149613\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1462266565 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1839</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1605;&#1610;&#1583;&#1575;&#1604;&#1610;&#1575;&#1578; &#1588;&#1603;&#1604; &#1605;&#1581;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1839</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>1877</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">cleaner Mop</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>15.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1877</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1462266565\", {\"maxDepth\":0})</script>\n"}}