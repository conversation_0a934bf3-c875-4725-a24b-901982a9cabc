{"__meta": {"id": "X70edff616f2698feb95e9039a58baa29", "datetime": "2025-06-07 22:37:55", "utime": **********.201175, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749335874.240227, "end": **********.201203, "duration": 0.9609761238098145, "duration_str": "961ms", "measures": [{"label": "Booting", "start": 1749335874.240227, "relative_start": 0, "end": **********.060517, "relative_end": **********.060517, "duration": 0.8202900886535645, "duration_str": "820ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.060557, "relative_start": 0.8203299045562744, "end": **********.201206, "relative_end": 2.86102294921875e-06, "duration": 0.14064908027648926, "duration_str": "141ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45131896, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00557, "accumulated_duration_str": "5.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 26}], "start": **********.158349, "duration": 0.0047, "duration_str": "4.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.381}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.183363, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.381, "width_percent": 15.619}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1848860583 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1848860583\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1083584679 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1083584679\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2021735093 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2021735093\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-206846093 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749334872942%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxDajZiVXRId3NPNFBSWWpGTnhuS1E9PSIsInZhbHVlIjoibXc2NXhFREZxeFhtUlU0a2pxRzgxaDNWS1BrUDdiaUcrdk5rdjJrT0F2Tml1RUlreVBpd2lUNURGZ1gxSVM1ZXRpbjRRODUwaklROHlTZzdvdkNxcXBMRnh4N0ozVDJWZ1Q3NUJLcXd3ZFpmWEU3TGttRjJIS2dCMGdveVozSDhxSmd4Ymd1NzlRazY5MHJQMUQ3NHIweGl2RDdNdG5yUUt5MUdiWStFTkZMV2VxS09Cb1JvT0FscTNUNG1ZYlVaSWUrQ0xidEptbnVPSTBwQzJFL0RQSnBwQVQ1UHV1WXNEV0pDTk9WZlpGcFFqbjBLc29EVVRGZXFueWEreGREcjA2NHUrQmZ6ME5KYXhNbVdCcXFUWDdtYjlCbTZweWI0Mml5Q3N5QjZaSEdrdnp1ZEVFYkJOdTA1M0U2YW1xTndNbUVCdjlRalRUK05ma3JMVndrR2djZ1UzNkl0c0luU2xLVEgwbUNBbUYwMldqTFd5Rzh0eEVmcFdrNXhBdjBWMzlndjhvUXJkSFBnVytrV0s3S3VhNTFKaTZieUVQbVg4MWxWRkRsVFdCNnlxbFk2djBTUUlNQ2psdlcrTnBsWVhlRWxFOGtlZG1OOUt4UEZNY3FzQXEzRUFZRUttV0RjS2liV3M0aWNYSG1uUWExN1pTTkJIYm1aNkFSSTU5QS8iLCJtYWMiOiJmNWFiNmU5NWJkYThmNGIyZTliODEwNzUyYjBmZjhhZTMyNGIxMDBjOTMyZTNmMTM3MTQ5ZWIzNTViM2JmZmUyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im9OQWJLajZDTFh3V0NDcnRKanp0WUE9PSIsInZhbHVlIjoiSURDZW1SVks1WDhPbVdzMTdHLy9mWHZEeFR3L3NtendJMFpMWnBzakZKdDA4U1h1VDRaMVpKbUJRcm5ERDNGc25ZN3VpejJRZzVxSmhZcWNhVkt5ZDZoa3puVXg1dkFIMTFoZXhBVitmcnJVS3A5WVBVQ0NnRnVXNWE5aUxDeGFJZnRhZkJubkdiTUh1cWlxQXpCbmlQSDlCTVJQaTZVMFRvQzlVOWZvMjJFanJLcmxIQ1dLNWsxOG9hVDB4SmtiM3ZNRkF6NkV3RU9zQW93WHhaaEw1UDY4a2V2allpWFRIQXJ3b2c4YlVzazVBWnF3SFgzSUpUQVVkZTNYcTdqdzgvYU1DRnVvT0t3WWZsNm1SK0xGVXZjd2FRbXcyN2V3SHlWZ3FDVjJSck5MNHVXS2FMTktOZm5MMTJrVnIrUW1haWg5dmdWaVZBM3ZuVklmLzZIK3NIQkZUQWlFdnFnak0yeEhEeGlQd3hucklTanA1VWxJbXc2bWZlczNFM1Yva0hyZE14ZGlsOUI1aHc5U2kwRG8rRnVaNTVPd1RSSWc1N0txejJCUncyc0RMRFpNRCs3RHA4YWIwbDJ6aUN5NDY2YUFpcnA5cWkvMHYvK1N1S2VPMThqcEVxd0h0bjRldkdpTE5ieXBnSEo3SVhieG9RTzBkdVd4RC8reG5laGMiLCJtYWMiOiIxZTY0ZWNiNjIxZWQ4N2FiMDczMTVhZDE5YWQ4NmRhMzA4MTVmOWQ5NjA2NTEzYjY5MTBjY2Q3YThkNjVjODdhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-206846093\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1636979691 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636979691\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1045812675 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:37:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFiK2ZMQnAxa1FDWjVTOFVidmF6T3c9PSIsInZhbHVlIjoiM0tvVlZBeVIxWi9VajUvUUZmdE1nQmUyZStNNDJabkNFZkxPdzEyZUpYRjlEcHpIZDMwVkduNVJMYzdtUjJPV1ppMEM3NS9tRGZ6VkU1aWFGUHMwQ1NOZWRKNE1USXhWWlBsTEcxdW1ZSGJlVjRLNFJiNE1MdCtjL3lteHd0eEpMZ0Q1T0Q3Zk03Q0VSRHZ5NUFlZFBLWDU2RnNwa0FoNzV5ZWkveG81UFp1QXpzK1RFTzhUQnpmTXpxNnVkRkFlYUQ2UHl1azArMk02QUtjM3FYak5VQkZ5czlxcnVsa09qTm5qNElXUm5IRmNSckYzbTMxcmlXTnBYNklwNmx2TlNMNW9SendxSnBCN2tvSndsUWJLejZZdDJEaWgrVTAyOEplM3JBS2xYNllqMExIdHNoZEgxSTVsdG5IcHc4d3ozL2tHN0ord1VKTXcyWTN0VWhDSFRscFpQOEUrU1FmN1p1dkJiTHdPaEZFeW1MenNsUjRNdUhXTFFjTDBpSkVIdzdJU2dWOC93OGpTT2VMK3RVdWU2Z1JDT2dqdTZWWjdOWkpFVmc1R2hrTDdsTGtpY01UdWcvZk92T1llQUxjMTNWalQ3YXNWNW5IVUk1L2hZbzVZY1VLdlJHUmx0WFB1aTE5aWRPYnlVL0lhV2ZKckpXZ2lRN2huQzNlRlBjYXYiLCJtYWMiOiIyNDE4ZTg2NmFmMDdiMWYyOTAwZDQ0Y2U4NDQ2ZmVmZWVlNjNiMjYyMzE1MTEyZTkzZTM0ODhjZjYyNTY1MGY3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:37:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZKQWFzNHVQcWxyWEROYVNwMHlqbEE9PSIsInZhbHVlIjoiUWtIOTc2cDZKOXFDUHJaaGR4cUVkeHhWeGdLb0pEQjdmb3VsWUE0R1lhbzRxWFEzaUU1OEJXNGlNQlhyTjBhaVFqUmJIM01mSW9kdXkrVG5rOWFoaHZNNHN5Vkdoa0pQZ1BXN0o0WTRMelpsVFV4MittVDRPam5vMnp4ZzJIYnhLUyt0T2JQUDlrS1Q3dHJjUVVpcGN6TGdGM3lmeXV0Z0lSZHdYOFhXZC8wak9zRlNoYVh1Z05DU1NGRjUxMHk5MU9kbHRQbDN2VW5NL1BWREFTRW05TVgvUmplQ1U0b1BlZjQ2cDZwVWo1U1lmU2Nnd0pqTUZXcDdFMXRQZm9rb3JYRXlITElVdWdHSjZzcUFPWkJqb0xqVlRyaUpXVlBHVTdBbTNqamw2Q0lZVW1VTHBQRmsrSWZ1ZTNHeEVKWVc2cmcwNyttOVB0cGtJUkFwUFp5UkZhUm12Z2djd21GTVQwZHVOMkpnQUdNZ3M0c0ZGL09ISHZHbm9KU0NEcHJQTmtEbHlDaVpFOGFVMzY2bGZ5MzlSeU1uQnpUa0lvS1NtMzVjTVJCUm42R0h1VVB0d3MzQXJSYzNsazVJb3VZa21oNXZkWGVHaENJa3JZUm82Yi9ZRGdnZnJ4eDliSitnVHJ6UmQxQXJvMytKY0RFZjZ5NXJ1Y3B4emcvcnFURUIiLCJtYWMiOiI4NmUzYzRmYzZmNGQ4ZDA3ZDMzNDIwYjM2ZjdkNWQyY2ZlMmQ2NTdiYWJlYmQ0MzQ0OGJhNzAwMTViMzhhMmEwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:37:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFiK2ZMQnAxa1FDWjVTOFVidmF6T3c9PSIsInZhbHVlIjoiM0tvVlZBeVIxWi9VajUvUUZmdE1nQmUyZStNNDJabkNFZkxPdzEyZUpYRjlEcHpIZDMwVkduNVJMYzdtUjJPV1ppMEM3NS9tRGZ6VkU1aWFGUHMwQ1NOZWRKNE1USXhWWlBsTEcxdW1ZSGJlVjRLNFJiNE1MdCtjL3lteHd0eEpMZ0Q1T0Q3Zk03Q0VSRHZ5NUFlZFBLWDU2RnNwa0FoNzV5ZWkveG81UFp1QXpzK1RFTzhUQnpmTXpxNnVkRkFlYUQ2UHl1azArMk02QUtjM3FYak5VQkZ5czlxcnVsa09qTm5qNElXUm5IRmNSckYzbTMxcmlXTnBYNklwNmx2TlNMNW9SendxSnBCN2tvSndsUWJLejZZdDJEaWgrVTAyOEplM3JBS2xYNllqMExIdHNoZEgxSTVsdG5IcHc4d3ozL2tHN0ord1VKTXcyWTN0VWhDSFRscFpQOEUrU1FmN1p1dkJiTHdPaEZFeW1MenNsUjRNdUhXTFFjTDBpSkVIdzdJU2dWOC93OGpTT2VMK3RVdWU2Z1JDT2dqdTZWWjdOWkpFVmc1R2hrTDdsTGtpY01UdWcvZk92T1llQUxjMTNWalQ3YXNWNW5IVUk1L2hZbzVZY1VLdlJHUmx0WFB1aTE5aWRPYnlVL0lhV2ZKckpXZ2lRN2huQzNlRlBjYXYiLCJtYWMiOiIyNDE4ZTg2NmFmMDdiMWYyOTAwZDQ0Y2U4NDQ2ZmVmZWVlNjNiMjYyMzE1MTEyZTkzZTM0ODhjZjYyNTY1MGY3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:37:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZKQWFzNHVQcWxyWEROYVNwMHlqbEE9PSIsInZhbHVlIjoiUWtIOTc2cDZKOXFDUHJaaGR4cUVkeHhWeGdLb0pEQjdmb3VsWUE0R1lhbzRxWFEzaUU1OEJXNGlNQlhyTjBhaVFqUmJIM01mSW9kdXkrVG5rOWFoaHZNNHN5Vkdoa0pQZ1BXN0o0WTRMelpsVFV4MittVDRPam5vMnp4ZzJIYnhLUyt0T2JQUDlrS1Q3dHJjUVVpcGN6TGdGM3lmeXV0Z0lSZHdYOFhXZC8wak9zRlNoYVh1Z05DU1NGRjUxMHk5MU9kbHRQbDN2VW5NL1BWREFTRW05TVgvUmplQ1U0b1BlZjQ2cDZwVWo1U1lmU2Nnd0pqTUZXcDdFMXRQZm9rb3JYRXlITElVdWdHSjZzcUFPWkJqb0xqVlRyaUpXVlBHVTdBbTNqamw2Q0lZVW1VTHBQRmsrSWZ1ZTNHeEVKWVc2cmcwNyttOVB0cGtJUkFwUFp5UkZhUm12Z2djd21GTVQwZHVOMkpnQUdNZ3M0c0ZGL09ISHZHbm9KU0NEcHJQTmtEbHlDaVpFOGFVMzY2bGZ5MzlSeU1uQnpUa0lvS1NtMzVjTVJCUm42R0h1VVB0d3MzQXJSYzNsazVJb3VZa21oNXZkWGVHaENJa3JZUm82Yi9ZRGdnZnJ4eDliSitnVHJ6UmQxQXJvMytKY0RFZjZ5NXJ1Y3B4emcvcnFURUIiLCJtYWMiOiI4NmUzYzRmYzZmNGQ4ZDA3ZDMzNDIwYjM2ZjdkNWQyY2ZlMmQ2NTdiYWJlYmQ0MzQ0OGJhNzAwMTViMzhhMmEwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:37:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1045812675\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1490069279 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1490069279\", {\"maxDepth\":0})</script>\n"}}