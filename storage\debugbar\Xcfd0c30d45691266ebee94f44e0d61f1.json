{"__meta": {"id": "Xcfd0c30d45691266ebee94f44e0d61f1", "datetime": "2025-06-08 00:40:50", "utime": **********.382898, "method": "GET", "uri": "/users/17/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749343249.696743, "end": **********.382923, "duration": 0.6861798763275146, "duration_str": "686ms", "measures": [{"label": "Booting", "start": 1749343249.696743, "relative_start": 0, "end": **********.237422, "relative_end": **********.237422, "duration": 0.5406789779663086, "duration_str": "541ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.237438, "relative_start": 0.5406949520111084, "end": **********.382926, "relative_end": 3.0994415283203125e-06, "duration": 0.14548802375793457, "duration_str": "145ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51392448, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x user.edit", "param_count": null, "params": [], "start": **********.365595, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/user/edit.blade.phpuser.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fuser%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "user.edit"}, {"name": "3x components.required", "param_count": null, "params": [], "start": **********.37264, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 3, "name_original": "components.required"}]}, "route": {"uri": "GET users/{user}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "users.edit", "controller": "App\\Http\\Controllers\\UserController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=235\" onclick=\"\">app/Http/Controllers/UserController.php:235-250</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.011770000000000001, "accumulated_duration_str": "11.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.279114, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 33.645}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.29581, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 33.645, "width_percent": 7.901}, {"sql": "select * from `roles` where `created_by` = 15 and `name` != 'client'", "type": "query", "params": [], "bindings": ["15", "client"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 238}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3010309, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "UserController.php:238", "source": "app/Http/Controllers/UserController.php:238", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=238", "ajax": false, "filename": "UserController.php", "line": "238"}, "connection": "ty", "start_percent": 41.546, "width_percent": 5.098}, {"sql": "select * from `warehouses` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 239}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.306024, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "UserController.php:239", "source": "app/Http/Controllers/UserController.php:239", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=239", "ajax": false, "filename": "UserController.php", "line": "239"}, "connection": "ty", "start_percent": 46.644, "width_percent": 9.856}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.33194, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 56.5, "width_percent": 5.692}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.335631, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 62.192, "width_percent": 6.372}, {"sql": "select * from `users` where `users`.`id` = '17' limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 241}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.342921, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "UserController.php:241", "source": "app/Http/Controllers/UserController.php:241", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=241", "ajax": false, "filename": "UserController.php", "line": "241"}, "connection": "ty", "start_percent": 68.564, "width_percent": 7.052}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'user' and `record_id` = 17", "type": "query", "params": [], "bindings": ["user", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 242}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.347687, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "ty", "start_percent": 75.616, "width_percent": 12.489}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'user'", "type": "query", "params": [], "bindings": ["15", "user"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 243}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.352478, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "UserController.php:243", "source": "app/Http/Controllers/UserController.php:243", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=243", "ajax": false, "filename": "UserController.php", "line": "243"}, "connection": "ty", "start_percent": 88.105, "width_percent": 4.418}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 17 and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "user.edit", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/user/edit.blade.php", "line": 100}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.375136, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "user.edit:100", "source": "view::user.edit:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fuser%2Fedit.blade.php&line=100", "ajax": false, "filename": "edit.blade.php", "line": "100"}, "connection": "ty", "start_percent": 92.523, "width_percent": 7.477}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 13, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2135804817 data-indent-pad=\"  \"><span class=sf-dump-note>edit user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2135804817\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.342145, "xdebug_link": null}]}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/users/17/edit", "status_code": "<pre class=sf-dump id=sf-dump-1486881423 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1486881423\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1243902016 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1243902016\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-121478718 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-121478718\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-520757609 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749343246363%7C41%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJwQmROSGVTbE5nN21qZW5OSlNmVHc9PSIsInZhbHVlIjoiQ3ZrOGxJYUhTOFFUMVNhTnhTZ3o4VFAvTkk1dGRHbW5ibnc1WUdmaGxUZDAyNHF2ZmpQa1FudjVNYmhBZTE4cUxPQStIRE80djFyVTV6Z0tqcFBqZm1saGJJNVdSNTdkQ01yYm5zNkJud0cyZEt2YitiWElNSnhyOVRDYWp0RjgvaHdEbVc2YU44eUZNSzFZR2RLQ3NDekxscklQeWNJSll5WXFxbGxMQkEva1BOWnV6ZXFhS1h6bS9CZkRzN08vWkwwR3VwUU0wY00zNjFJdnQ4SVF5OTA5bld5UnZwSzBsWUZNVGtwSjYrUEtYdE1RS2ttb3NLdTJzVGhIN01sS1VzNE8xMGhSSGVxTHRDVSsxNTRncW56R1pVZVAvamN2YWo4UHI4dDZOeENpQW8vdHFyMTAxL0Z2RHBYd1RDc001Rk8xa0l1andqOHNjclRkVFl0by9pbWlyeUlha294bkw2MFhiOHJ2T0h4cnRQbzdFWnJoWG9adnlHOUNDODhxeGR3QzFpQldXNnlDTUY4amIwb1VxbWxMcEpwMjhaSGIxRzM5NjBQU3dtWnVqbzlHYUo2UW9MaFREWE9VbVVjcmRhTkZ5Zjc4ZUQ4MG1qaWpqbjlkWnlGQk9UbTBvVzMzRi9wbTZEQVJpQU1RWlhUMUt6YU9ibUNXbHpPeTRLd1kiLCJtYWMiOiI3YmY0OTNmMmExMDIwNWMxYjE1YWJhZjY1ZjBmNGQ0OWVhYWE0OGYwOWUyNjhkNGFmNGU3Y2RhMmQ1MGY0YTgzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InIvcUNVb1NCMkMyVFQxUkpkSkN0akE9PSIsInZhbHVlIjoibGJiaFVQTGlhNXdVcVg1OXRMSWdXNVU0MGRNMDJrdU1XS2dva1E1OHZZd0JyOGhPY09NNE04QzE3ZkJXZnNnMTdSOW85R2J3L0Vub21IeW1CQWhlOGh0U05oMlp6TTdkSlRhb0QyQ3pETnZFcTk3SkJHVy9wMnQ0bHcxMHJlVzhqY3ROQy9TTGdyUHpqb1RXMjdkY29CYmlSR0krL2xlZnJOSGdDVXhHWlhTalNaUHFacEVGRnZnUzA5ZTFUVGhiK3JnMmlmMzFsaU1saHpObUpxNlVCMUtYOGtKdno3SFBhTllKMWQ2dlZ6bENlU24rUW9GWFNQRWNmbHNwTVhCR1ZUbGVpMzFiaHl4RytqK3pYcjRYSHF3UjRZdlpQS2s2VzVMWlU1cmRtaGRxK3hUbUZSWDhWT01HR1RaaWhOd2xWd3lZOVRzdmhOeUQxL1c3eWhEWjgvb0NKTlRzTTFDRm9rMFIreE9YSU5hUXgzTHY1ZDFqdmpORFRXS2FHL0w3YVlyR3BWdUxjUC9MVGxHUXM2NDVLV29mQVBsV3k0amhDSkEzMzRubmZ5TWhTTldUdElrbUgxNTdmZmFjazMvSjIzSGgzWG4yaFdwRllmNTd0RVFKcDdOUG1GQnBmcWpRZ1dITy9RbGxkQ0dKaXZSUy94WTU3VFo3MjZvUGFMR1YiLCJtYWMiOiI3YmFkNzJlM2FmYmRkZWFlM2YwNWEyYzM5MjEyYzgwMTk5YTQwYzk3OWU5YTMxOTg4ZTI0ODE5MTQxODdmZjdmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-520757609\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1066257191 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1066257191\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1540259629 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:40:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5XNjBaVzA1eEJPbEVLeHlrV2tNK0E9PSIsInZhbHVlIjoiN0hnY1pTNG1oOCtGS242emtpOEpMelUxVjJyM3UyRWxBVXNsNExETGJpeVJIeEVESXd5WlVXK0tzcXB5NWlTbTZVYXJJUXZ4LzVsUWFHcXUzVis4RzdXY3U4KzUrSmg2aUNuTFY2K1JQcURmbVBtck9NbkwzK05WODhHOWIwV094ODR2SlFUenQyT1hoVXVyOC9nOE9CdTkxTkdIbEtIZXl3aFpOUm5PVjhGallHeURlN2diN2NBcDljQWZOR24yNythNHN0bUJmOXEwVVNMdzJjc3pnOGFKSzhaNjhhOUpjQmlsMURMWXBxWUxadmlRTk5FUnpzYmpNNW1LYWFZby9TZldnR0lKOS9Nb1M4bjYwdnlZSTJkZDZQMFhjK2h2SGlOcCtIWkFqVmNLd1lrL0lqWW8zOHF5bXdBS2ZYd1UyeVlXZGEyelRDSHJGMm9SR2gwU2xuTTl4cGZCSGdyQ1luRXRqbWZLaEFBczJlNFZGQ2tNOTlmTm1mY1JtKzNmanJBVEEwR0VObTFxL3I1RGx2RVdLVmZPVVpCeEtxSm5mZEh3OW4vM2RjZEdMZ3o4b3lDMGZmRXNCMHBWenRzQnA2V3hYbVM5V3l4R0x4Sm9UeDRsZGRhZ1R3cENXanc2V0Nid1ljOEVSZDlnb2ZBS1AvS3JteXR5Qnd3THZVdzIiLCJtYWMiOiI0ZGZiOGIzYzg5OGNiNDYwNWYxYjRlNTk0YTgwMGRmMjU1NWJkM2ViN2Y4NGZjNjhmNjk1MzU2Y2ZlYmQ5OWZhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im1YalhwUDRCU2ZSVTAwOVZIc0duZGc9PSIsInZhbHVlIjoiT0huUnhTV3hONW9ReHVPSVgxU3laZFRGTU5YenFzT01ncE5KWWxXZURuZkgyQm5taWh3c2dEVjhKNGQ1M0R6c2NWMjNYZENPR1hFeUVSVmJLSVBpbWxWTlhUcUJzOXk1aHZ1TDZQMVN4WGRwYXBYV05QdGgxQmxSN2U2NC85NUI5VlpLckR0T21leTJDcUF6MWFrUEhZMi9OZkRML1BuNEwrTTRoUVRhQ25DSFhCdUJhWk9uN05ZN0VqcUZwUjdUVlNkam9xRXYxYVBLNGFVVnJkNkdrbWVJR2ZLdkRtNVZwYUNoQ2xDMjFpZVNyUW0zM2lwaThuV1hNREJHUkJnRlZ3ejJuRW9jcFQ3MnBJTmdxYml1S25JUkZNR09hV01RZ0pHVmNkZ2RYMjhDelZaWHBhZmhlZDNabEFnVkdIRHFibjU4a1dGclZVeFdFdHc1S1lkeWdDUzBGNGlvdUM5blMxK05udTI5Vmk4RGNZYjlEUVlISWlNRVR3cFRMV3pqMVpLYXdnempIcE5DUktMZGRnWi9IdlZOOVpTUVhTUVZINFUrUEhjNWh5K1pGOXpSQmhwSUdLbkM3TktHcnhXTnNqdDdkc2w0cnl1U3RFSi9Ia25FR3NBbWwvOFNzT0IydU1NV0pXdVFpQWFweHFBTDZsWHhtb3pVb3hiWmUwdXMiLCJtYWMiOiJhMGQ1MTMzNWNiNzZlNDYwMjhkZWNhYjM5YTZkY2E5NzE2NzBjZGU0NDI5OWRlN2JiYmMzMGMxYThhNmVlMzE2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5XNjBaVzA1eEJPbEVLeHlrV2tNK0E9PSIsInZhbHVlIjoiN0hnY1pTNG1oOCtGS242emtpOEpMelUxVjJyM3UyRWxBVXNsNExETGJpeVJIeEVESXd5WlVXK0tzcXB5NWlTbTZVYXJJUXZ4LzVsUWFHcXUzVis4RzdXY3U4KzUrSmg2aUNuTFY2K1JQcURmbVBtck9NbkwzK05WODhHOWIwV094ODR2SlFUenQyT1hoVXVyOC9nOE9CdTkxTkdIbEtIZXl3aFpOUm5PVjhGallHeURlN2diN2NBcDljQWZOR24yNythNHN0bUJmOXEwVVNMdzJjc3pnOGFKSzhaNjhhOUpjQmlsMURMWXBxWUxadmlRTk5FUnpzYmpNNW1LYWFZby9TZldnR0lKOS9Nb1M4bjYwdnlZSTJkZDZQMFhjK2h2SGlOcCtIWkFqVmNLd1lrL0lqWW8zOHF5bXdBS2ZYd1UyeVlXZGEyelRDSHJGMm9SR2gwU2xuTTl4cGZCSGdyQ1luRXRqbWZLaEFBczJlNFZGQ2tNOTlmTm1mY1JtKzNmanJBVEEwR0VObTFxL3I1RGx2RVdLVmZPVVpCeEtxSm5mZEh3OW4vM2RjZEdMZ3o4b3lDMGZmRXNCMHBWenRzQnA2V3hYbVM5V3l4R0x4Sm9UeDRsZGRhZ1R3cENXanc2V0Nid1ljOEVSZDlnb2ZBS1AvS3JteXR5Qnd3THZVdzIiLCJtYWMiOiI0ZGZiOGIzYzg5OGNiNDYwNWYxYjRlNTk0YTgwMGRmMjU1NWJkM2ViN2Y4NGZjNjhmNjk1MzU2Y2ZlYmQ5OWZhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im1YalhwUDRCU2ZSVTAwOVZIc0duZGc9PSIsInZhbHVlIjoiT0huUnhTV3hONW9ReHVPSVgxU3laZFRGTU5YenFzT01ncE5KWWxXZURuZkgyQm5taWh3c2dEVjhKNGQ1M0R6c2NWMjNYZENPR1hFeUVSVmJLSVBpbWxWTlhUcUJzOXk1aHZ1TDZQMVN4WGRwYXBYV05QdGgxQmxSN2U2NC85NUI5VlpLckR0T21leTJDcUF6MWFrUEhZMi9OZkRML1BuNEwrTTRoUVRhQ25DSFhCdUJhWk9uN05ZN0VqcUZwUjdUVlNkam9xRXYxYVBLNGFVVnJkNkdrbWVJR2ZLdkRtNVZwYUNoQ2xDMjFpZVNyUW0zM2lwaThuV1hNREJHUkJnRlZ3ejJuRW9jcFQ3MnBJTmdxYml1S25JUkZNR09hV01RZ0pHVmNkZ2RYMjhDelZaWHBhZmhlZDNabEFnVkdIRHFibjU4a1dGclZVeFdFdHc1S1lkeWdDUzBGNGlvdUM5blMxK05udTI5Vmk4RGNZYjlEUVlISWlNRVR3cFRMV3pqMVpLYXdnempIcE5DUktMZGRnWi9IdlZOOVpTUVhTUVZINFUrUEhjNWh5K1pGOXpSQmhwSUdLbkM3TktHcnhXTnNqdDdkc2w0cnl1U3RFSi9Ia25FR3NBbWwvOFNzT0IydU1NV0pXdVFpQWFweHFBTDZsWHhtb3pVb3hiWmUwdXMiLCJtYWMiOiJhMGQ1MTMzNWNiNzZlNDYwMjhkZWNhYjM5YTZkY2E5NzE2NzBjZGU0NDI5OWRlN2JiYmMzMGMxYThhNmVlMzE2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1540259629\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1748036026 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1748036026\", {\"maxDepth\":0})</script>\n"}}