{"__meta": {"id": "X7c493dff855b93b4e896051982c76543", "datetime": "2025-06-08 00:08:13", "utime": **********.297587, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749341292.757196, "end": **********.297608, "duration": 0.5404119491577148, "duration_str": "540ms", "measures": [{"label": "Booting", "start": 1749341292.757196, "relative_start": 0, "end": **********.21871, "relative_end": **********.21871, "duration": 0.4615139961242676, "duration_str": "462ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.218721, "relative_start": 0.46152496337890625, "end": **********.29761, "relative_end": 2.1457672119140625e-06, "duration": 0.07888913154602051, "duration_str": "78.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45176624, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01602, "accumulated_duration_str": "16.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.257016, "duration": 0.01474, "duration_str": "14.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.01}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.283244, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.01, "width_percent": 3.683}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.28807, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 95.693, "width_percent": 4.307}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1247710496 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1247710496\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1296703773 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1296703773\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1851764308 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1851764308\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1428791918 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVMSTJFOGgvTll2MUZEeVcrVWxYTmc9PSIsInZhbHVlIjoiaUJ6WE9lazJVT2ovUG1JRjFxYmN5QU1NazFMZVBYcXVMcitROGxpWG9mc2UrckcrVGY2cVFvYzZ5NE9vcTRhdDFoenAvdlhVYzEvblk0WWlubXpmR3ExUUhoWS8rSGtFSUlwZWRkRDZGVUhQY3lJMlc3Wm91M0FmNk96L2t5YUJZM0NXSjcxTk91d3VVNmNJN3Y2dU4vQ1RtQmw4eXJwVWRYSU1FSjhHdEw0NVV3MmtGTW53cWNmRGloRzIzMGo1NktBWWVKVEErZ3FjNUhmY1crL0RwNDNDQjdGSlhLbEE5R3NpeXFZNExxNGdZL0hhODZyZllXQWZTb3Zmemd1YUxsRjRqWGlVdWo1N0hkYVVNTVBONWpMY01OMG50c0Exb09DU29oM0RqTHZOM2kwMk1DdHJiVE1aOXhUSm1BcEFzNXljUVV5SWlyS3FjYzc2emdHQ3YxRlQ5NUIxMGoweU1YSSt6NHIrSjljSWNSL1lMbk9oTHROVzlUUmdENnhZWmxPeFpqeHVPbEk3LzdtR3hBKys3ZGxhWUZqejAwN1BnSVRyUk1VWFgrMDNnZ1FoV3UvSm9xL0ZMeW9MWC9CdlJlZUR6d0ZHeExtNHRJTjJmWFFBODAxS1NZNE9OU3NFOUxFdUhCNm5zUnYvcm5LYUlVSUhJOTJXQUVYb3B5bE8iLCJtYWMiOiI3M2JkOWYwMzA1MTNkMjc0NGE5NzAyYThmNTUzMTA5ZTg5M2ZiNTE3MWJjNWIzNmUzODI5MzRlZDUyYzk3NDZlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InlBR25lOUo3bW44OFRkZmcvWDVwVkE9PSIsInZhbHVlIjoiWGRsUjQzNFF0NnF4VVQvZEtqN0ZlZ01UUk1ZMk05L0s3L0E4TjBsak9kNE9nRXV5RFVTWmQvMS81ZkpycnhRTklCTTZheXlvdnFDbHhtNmxLaGJhYTRRT3VQUXlpU09DdFFrS01xbUZGOXdndHRhZXlCUHpnTUhGdUo2VFVUOU1PMEozQXQvNHVkZG5zVnlzUmpwdEtzaUVIajJpTTRWaFVyT1VmZklVWmlEc3BPeDQ1aHZMcEZKME92em5qNjVUTmVINjlRdFNEdFNtdk0vbWxDK2U0ME5tQUpyQWpiNnpQR0tGVXlWOHIrbzNxTTg3cjFmL0dRM3VBV1JKWDUrN1hkVElLNGNjWEhLdjNralAvcTBkblZkZURXNW9JVm51OCtiUmoyYzl0dUNVNnlzYTMwMmdWYVgrVEEyKzR3Zy9tWUJYYlFyUFUrdXRCTEZPMWlkTVQzSjhwQWpodkVqQTQramJxMXVReXlzaFVKVGZxcHI3Umh6aml0WmRRZkZ4ZlFPK0IybEJKQnJxR1NSTkpEa1RmSFByTVYwNjhLOUtSaEQ3Mk4weTJyc0pJRzdwQXpSSHhPNTJGSUVNVnRMNmJsNm5PNllmUExJVUJETW1OMnYyU000NGpsNE94aXFqSVl5ZnhON3pyOCt0TEJFOG9Da3J2S0hYWmRkbVMyeWMiLCJtYWMiOiIzMjczMmNkZDk4ODM0NDk4Y2U4Y2JiNTBkY2I5YTMxNjk1MTk5M2VjMDYxMDBhZDYzYWQwMmJlNGMyNmUyMDZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1428791918\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-236319758 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-236319758\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1454518542 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:08:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBoVnJ3aHBLdkgyemN6TllRNWFFaWc9PSIsInZhbHVlIjoibFhqYWVyZVhHcWxEOFF5MGpsOEljMXlzSi9mcUNaT1l0OC8wTUR5ckdsOUFFaGw3MHArcnBMQTlUWWJqcVhFaWxGR1p4YnhGMUxubjl2SHhWT2oxd1R0a1U5ZE9YVlNOMVZQWHZHcW9sQ0owN0lUbUsvZmdReWwyVXp4dEZjZTZGNE1qWWNGTWpJZC8zRThPYmp0QUV4dCtQS1RkZkROQjZTN0NzY3dBNktNYk5nUFI1TUd5azBLd21pOVN3TENkYmxVRU5JazRIRkp4ekZkYVo2K3BzdmNYTVUzc1dXTUt1RzJDeGNJTEFNVkNvZmgzcHF5dG5zZU9JRDNRL1RidkM4eTZuM21xSGU5SXVzRU9rK1VHcHRkanN4bWR5cWFaK1ROUDh5Ym9NZkJ1YVBkRzJuUHpGanArV3hqcWErcW9mTjE4TUlJUXpGemNOelk2T2xiWkNGUFcydy9QNzY5dUhDNThCdyt1N0hKYXQ5OE5CSDNBOHJOeXNpTUQvUUZvOGs3bzB5eTBKa1dOV1ljNG5zdDRGSzM1eTh2VGRmMW9OSkZsYzNJTlM0VUl5cnRSa2xnMW1BWndUWlFpNWx0RWRkcHR3NCtUS0svdm9sUzh4Q09tTlVCZm9JcklUWkNKcUk3MndtLzVLODRhWUdyU2x1eU1nbnRvQjBaTHBGSkQiLCJtYWMiOiIyODMxNTIzMDZjNjZjYjg3MzNmNWNjOTMyMDFmNzcwMGZkMGFmZjg0ZjFiZGJkODlkZGUyZTc3MzMyMjNkYTQyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:08:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdYYkR5YktxdUhMNUdSTWx6ODBGelE9PSIsInZhbHVlIjoibDhVMTBNaE9ycnZTVndEeFVIYlVUZXFRRlR5MUE2a3pySGNqSGJaaWMwQzdrQmgyaUpJVndzNW9nTnZYd1BmbWlNNVVvZnhUVnQyQU11UjZjSUNOTWlQWTMrY3F1MldFaUNaZTJFOUJxMWxkM3VJM3YyejlZbXByYXpVSWQ4S25FaWtLQmFPWm9ybFZYMTgyMG8xRlRnOTNYVXF5MmxmWXg4UnlOR0VkK0c0Mld2bkJGa1FEY3RVMU0yNnNHY2w5cFpCQnVnOHVpcDdXRTZlcFlXQW9EVDhiZW54VzgrODQzbnhZclVCU2oyUTBldDQxYmlPNWtORHdQbE83ZE9kMVJnT0dKRjhDRGZ6K0orU1BSVXBaNVpUQVFJNXNWQ0FEYzE3OWhNanJucS9EYmROZEdLZFBVZ1lvMFFTWE0yRUFVazVPUis0U2ZMT0Z3SFJ4ODAyRDZKUEtUcDFaQmVQQWpybk9BUG5jQVJJdCt2M2NUbXUzVXpJMVFpZjZ6aFE0dGxnbXFxVUZTbVgxREMwZzJ1dStyczdlcWdYL2VYUFRUaCtZeHRSMCtkOVRUL2drOWVlUy9Pd0ZCTzI0ZG0xdm1mSWZRcjBjS1k5L1RQZm5NRys2c0Z0NDc0cE91cHgrdDh6WjJyWThpeUtzU28vQ240OUlCWE1VTWNrZUJzbGQiLCJtYWMiOiI5YTZmOGEwMGZhOWZhODUwNmIzMjllZWU1NmVmNjA1NDRkMGY0ZmRkMDA3OWJlOGQ3MDRiYWY3ZDM1YzI1ZGVhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:08:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBoVnJ3aHBLdkgyemN6TllRNWFFaWc9PSIsInZhbHVlIjoibFhqYWVyZVhHcWxEOFF5MGpsOEljMXlzSi9mcUNaT1l0OC8wTUR5ckdsOUFFaGw3MHArcnBMQTlUWWJqcVhFaWxGR1p4YnhGMUxubjl2SHhWT2oxd1R0a1U5ZE9YVlNOMVZQWHZHcW9sQ0owN0lUbUsvZmdReWwyVXp4dEZjZTZGNE1qWWNGTWpJZC8zRThPYmp0QUV4dCtQS1RkZkROQjZTN0NzY3dBNktNYk5nUFI1TUd5azBLd21pOVN3TENkYmxVRU5JazRIRkp4ekZkYVo2K3BzdmNYTVUzc1dXTUt1RzJDeGNJTEFNVkNvZmgzcHF5dG5zZU9JRDNRL1RidkM4eTZuM21xSGU5SXVzRU9rK1VHcHRkanN4bWR5cWFaK1ROUDh5Ym9NZkJ1YVBkRzJuUHpGanArV3hqcWErcW9mTjE4TUlJUXpGemNOelk2T2xiWkNGUFcydy9QNzY5dUhDNThCdyt1N0hKYXQ5OE5CSDNBOHJOeXNpTUQvUUZvOGs3bzB5eTBKa1dOV1ljNG5zdDRGSzM1eTh2VGRmMW9OSkZsYzNJTlM0VUl5cnRSa2xnMW1BWndUWlFpNWx0RWRkcHR3NCtUS0svdm9sUzh4Q09tTlVCZm9JcklUWkNKcUk3MndtLzVLODRhWUdyU2x1eU1nbnRvQjBaTHBGSkQiLCJtYWMiOiIyODMxNTIzMDZjNjZjYjg3MzNmNWNjOTMyMDFmNzcwMGZkMGFmZjg0ZjFiZGJkODlkZGUyZTc3MzMyMjNkYTQyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:08:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdYYkR5YktxdUhMNUdSTWx6ODBGelE9PSIsInZhbHVlIjoibDhVMTBNaE9ycnZTVndEeFVIYlVUZXFRRlR5MUE2a3pySGNqSGJaaWMwQzdrQmgyaUpJVndzNW9nTnZYd1BmbWlNNVVvZnhUVnQyQU11UjZjSUNOTWlQWTMrY3F1MldFaUNaZTJFOUJxMWxkM3VJM3YyejlZbXByYXpVSWQ4S25FaWtLQmFPWm9ybFZYMTgyMG8xRlRnOTNYVXF5MmxmWXg4UnlOR0VkK0c0Mld2bkJGa1FEY3RVMU0yNnNHY2w5cFpCQnVnOHVpcDdXRTZlcFlXQW9EVDhiZW54VzgrODQzbnhZclVCU2oyUTBldDQxYmlPNWtORHdQbE83ZE9kMVJnT0dKRjhDRGZ6K0orU1BSVXBaNVpUQVFJNXNWQ0FEYzE3OWhNanJucS9EYmROZEdLZFBVZ1lvMFFTWE0yRUFVazVPUis0U2ZMT0Z3SFJ4ODAyRDZKUEtUcDFaQmVQQWpybk9BUG5jQVJJdCt2M2NUbXUzVXpJMVFpZjZ6aFE0dGxnbXFxVUZTbVgxREMwZzJ1dStyczdlcWdYL2VYUFRUaCtZeHRSMCtkOVRUL2drOWVlUy9Pd0ZCTzI0ZG0xdm1mSWZRcjBjS1k5L1RQZm5NRys2c0Z0NDc0cE91cHgrdDh6WjJyWThpeUtzU28vQ240OUlCWE1VTWNrZUJzbGQiLCJtYWMiOiI5YTZmOGEwMGZhOWZhODUwNmIzMjllZWU1NmVmNjA1NDRkMGY0ZmRkMDA3OWJlOGQ3MDRiYWY3ZDM1YzI1ZGVhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:08:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1454518542\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-334741024 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-334741024\", {\"maxDepth\":0})</script>\n"}}