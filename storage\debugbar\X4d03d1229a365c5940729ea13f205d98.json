{"__meta": {"id": "X4d03d1229a365c5940729ea13f205d98", "datetime": "2025-06-30 15:34:14", "utime": **********.279313, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751297653.89101, "end": **********.279326, "duration": 0.38831591606140137, "duration_str": "388ms", "measures": [{"label": "Booting", "start": 1751297653.89101, "relative_start": 0, "end": **********.236681, "relative_end": **********.236681, "duration": 0.3456709384918213, "duration_str": "346ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.236688, "relative_start": 0.34567785263061523, "end": **********.279328, "relative_end": 2.1457672119140625e-06, "duration": 0.04264020919799805, "duration_str": "42.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43872952, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00283, "accumulated_duration_str": "2.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.267938, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.746}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.272977, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 83.746, "width_percent": 16.254}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  1839 => array:9 [\n    \"name\" => \"ميداليات شكل محتلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"1839\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1825228977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1825228977\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-11420859 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndaaFZTTGtZd25LT05lMUQ1eklVY3c9PSIsInZhbHVlIjoiaUFrODJpK2t2ZU9ubFhYUnExT1dic1Q3ZkQzRmJBeC9VQkJ6cW1FazVhTittTFhSd0U0NXkvR2J6VVZlRysvYm5GQS9RaGJkUzlUWk9yN3JDVVpyR3g0SEdJMmNEdnN6ZXBtSXN0R01LNmZ6RUpuM2ZWN2hUd2RaajZkOWo2UERFSWQ1OUVQRWlNTGJHSk5uZnpKeWZMdnExcU5BVWJYLzlYMHh5NEJYYjBVRHN5SDQzR29HY0Erdm0rcnQwc0NUejArN2V6ZWlhYldhVjZsUVF1VnBYb0VsRVFPZWFYNmRnVG5iaDNmaUk0N2V6aEtLdnRUd2tVOHBlOU5RWTdtL2Y5anFQL21qVVlIYlFlWGFaVDhHeWptdlJUcmp5WkJkLzdKUjMwbm5qVTN1MkVqaDIzc29jY1JEb2t3bm9KRExIeUllWm9iNU10UHFWYklmbEZFdmJ0MlZ4TVo5clN0ME4rcEp5a3I4MlMrUk9xamFPUFN3K1FwaUFBeVcvWmozdllPZUFhQTlLWGVLMzhLZ2VrQkxZbldhY1A1ZHE4WEF5dzdCQlIrOHlML0NFZzZ3Z3VKeVo5S2tlaEhram9NbTN4UEVHMlRsTmxPcU9yakJhL3hKaGRzUUNaZ21WcU5vaGRlekt5S0Ric0xQU1A1aVhiek9LNkVaeStQNjNvUE8iLCJtYWMiOiJkOWNiMmM0MDZkMWE5Zjk5YjkyNzhlYmRhM2FiMWY2MzViMTgwZTg3YTNhNGM3YTUwY2Q1MTc0ODgzZDc4YWIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkF3TksrK0lRQnhBRXhaVS9jUk1NaHc9PSIsInZhbHVlIjoiWmFteDVSUmx3REpRZXc4Z2FHTjZxQThpQzdYd3ZvTUN1RlVva1U1cDdlUjVkaDIvQlhheTM5QTRUc3U4UDhWQTVaODg5T2lQbjZlRnVLN1pnanA2MWI1UmpiS2VoSGhnMVVkS1dzRCtUU2tVbWY2bllCWTBIVlBkTDlrTzIwcUgxVkM1NUR2ZzNsUitzMk1zSm92NStJdzU5WGVVN2F0NjFab211cDhCWWlxU0ZGYW1uaG93bFIwejVQcDRISEhRcy9ObHRueWVQdWVhZlJndTAzMm9FTUhCVk5FcGJmWjJ0NmFsbE9ocTZJUnZnZVBxa3JiZ2thQVJnckFocEFmQ0VJVkNidUU3T3hZbW55endlUU45TmdiWnd6YmhOSFcwSHh2cTBkcW1NUkgxV3R4NUx0ZHBiV0Y5YUY3NzNoMlNWbk45d08zeXdxQVczRmhCQUJudGIvK2pCRXAzKzArUHdYNzQxTjVyTUp6Q0JwYlh5UU0zMFJIcGhLRmlhTU5JcWYrTnNQM2RSN1JETER2Znp0WkdZUHhqSm44VG0weUNwelIxVlk1KzVQUmZwZkk0RktRc0ZLUE11ekFKdlVZbUdtZTNjaS8wb3U1UG9GdEh3Qm1CN3dORisralBsR0Y1QVlHMGRLdDd2cmhSNSthVVI2V2MySXZ4eDg0ZjdCZ1YiLCJtYWMiOiJlNjdjMjBlYzM2MDc5ZTE2ZTgzMDM1MzM3M2U2YTJkNmU2YzkyMmM1MjkyNTFjZjhhZTEwNDExZTdhNDE3Mjg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11420859\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1580689514 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1580689514\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-977656136 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5XN0c2R1NOS3FuLzF0Y2VSUzFGSUE9PSIsInZhbHVlIjoiRExsSUdGTFQvMGJKbnNtUTFUcGVBc0tpREZjelBSYlAxYktQWFB0d2NQbzdKVlQxb29zaktzOERLb0IwKyt4Y0t0QjJwTzNqYzFLL3BRWXorY0UzQkN5WFBZbUpYdEtraFhvTHI3anFmZXl3Z0JJNUpuQ3ZZV0F6SDdQNDRnTGpFcVVCWHZZYVQ3QVByWUVQU0haRzIyZm04ODIwV2tmWnI2cWg5VGs4MVN1UFNrWHpnUlg1Sk01aGZ1Z0xzOTJTM3ZNcW9uaStFRERlWnRVY1VoSG1ucGtHVzhUbTlySnZoSks4OTFnZDBkVW42QWI0ODd6dS8zQkM3Tm16ckpnUHhGVEVGaXVSVzhsS24vRTFxekQ2bTMyRmV5dGl2b3pybVA0ZkFXMVM2ZkVxcFNUc1hNbTFXNnRKTHFmWFV2eDU3UGN0Z1ZIREFaYUQwTjcyQjhBVkt1M2kxcUFFZkVrMzhLbVhEMEV0Ylc1Qlh6b2N0ZUJJNDhCaHJYcXBNWlA2dWlVU2t5NDNGbzRteTRqSWNYZzloNTFLZHlWN095eHc1ZXVlekFQbVh2Q0kyZ0dGR0UwVXcvblE1MHhidUtVVVp3NGtVRkZ2UjhVUUpzV3ZTKythSjgrazA1MklkQVhUa0VJcUREbFBkaGdLdkNyUU9QeVhGMTVobFQ3d1U5cm0iLCJtYWMiOiI3ZmQzY2JiZDhiNTU3OWM0NTdlYmYyYzJjYjNhY2E0NGRmZGNiNzFmM2M3MDY0ZjcyYmU5MjdjMTk2MzE0ZmJiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik12NnIxb2lFeklwcWs4U1VmRFE1bnc9PSIsInZhbHVlIjoiMlo1YjBKbkkyeE1ZU2RCS1J5UFlUM3VFQ1Qrd3ROSk9oNnlyYVpRSXhQMll0cm1VWDVOWFY5T1VoblZiL0hsOGR3Mmg1bjlxVjllTVBEMVdCUlAvaUlNNUpxeGZnR1ZPby9HMGhDWXQ1OTZSdG1WRWN2R1NURklibnpMeVQwT3hIN2l1U2dmL0ZiUTFGNWZORlpSSXRUU25mcy94K09Wd2hlQ1RHLzZJc3djUXROSXJ6R3kvZFpEbUFMTzAzL2Nab2xBN084OGlxVG5FRW5vZ1kxa3ZIeE1QbVR6YTgwZFNESEpGZE5UZER3eWxFdFFsNTRKN2ZWYlpDZDJZdmxJWEhKbkJUcGxNUGdpWEFvWWp5QVJTalZUbWZmSVlqUUFmc3RvNjg4L1hsQXFmdGVSSngwVXY5aG4zeUNLd2N2VEtabTRjVFBUcVBvTXFwc2ZRQTdJaGpyVVRTb0gxV2J1NGZmWVppdGQ0MEFtYVNWa0FCLzc4eXJkazBRLzZLVmNNNlhTWUowbFlQSDRYZGExM29sWjhHd2NkVEhKNE5DYXlDV2Qyd0huamFEeFBMMjNualI2RnZNU1dTOEo5Z3pUUU5GYVJ0UVU5dVFCbmdzZ05EZ2FJSHFaR1pGZ05BR0hiZit6T2tUQU1oY2xPRmlpczlYU0tQSVRhd3JZRzZ4NUgiLCJtYWMiOiIyMDg0ZjUwNjU5MjVkOGZlODNmMTcwMTI1ODM2NDU1Yzg5MzhhYWRmZWIzMWE1ZWYyMGFhODI1Mjg0MWE2NjJhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5XN0c2R1NOS3FuLzF0Y2VSUzFGSUE9PSIsInZhbHVlIjoiRExsSUdGTFQvMGJKbnNtUTFUcGVBc0tpREZjelBSYlAxYktQWFB0d2NQbzdKVlQxb29zaktzOERLb0IwKyt4Y0t0QjJwTzNqYzFLL3BRWXorY0UzQkN5WFBZbUpYdEtraFhvTHI3anFmZXl3Z0JJNUpuQ3ZZV0F6SDdQNDRnTGpFcVVCWHZZYVQ3QVByWUVQU0haRzIyZm04ODIwV2tmWnI2cWg5VGs4MVN1UFNrWHpnUlg1Sk01aGZ1Z0xzOTJTM3ZNcW9uaStFRERlWnRVY1VoSG1ucGtHVzhUbTlySnZoSks4OTFnZDBkVW42QWI0ODd6dS8zQkM3Tm16ckpnUHhGVEVGaXVSVzhsS24vRTFxekQ2bTMyRmV5dGl2b3pybVA0ZkFXMVM2ZkVxcFNUc1hNbTFXNnRKTHFmWFV2eDU3UGN0Z1ZIREFaYUQwTjcyQjhBVkt1M2kxcUFFZkVrMzhLbVhEMEV0Ylc1Qlh6b2N0ZUJJNDhCaHJYcXBNWlA2dWlVU2t5NDNGbzRteTRqSWNYZzloNTFLZHlWN095eHc1ZXVlekFQbVh2Q0kyZ0dGR0UwVXcvblE1MHhidUtVVVp3NGtVRkZ2UjhVUUpzV3ZTKythSjgrazA1MklkQVhUa0VJcUREbFBkaGdLdkNyUU9QeVhGMTVobFQ3d1U5cm0iLCJtYWMiOiI3ZmQzY2JiZDhiNTU3OWM0NTdlYmYyYzJjYjNhY2E0NGRmZGNiNzFmM2M3MDY0ZjcyYmU5MjdjMTk2MzE0ZmJiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik12NnIxb2lFeklwcWs4U1VmRFE1bnc9PSIsInZhbHVlIjoiMlo1YjBKbkkyeE1ZU2RCS1J5UFlUM3VFQ1Qrd3ROSk9oNnlyYVpRSXhQMll0cm1VWDVOWFY5T1VoblZiL0hsOGR3Mmg1bjlxVjllTVBEMVdCUlAvaUlNNUpxeGZnR1ZPby9HMGhDWXQ1OTZSdG1WRWN2R1NURklibnpMeVQwT3hIN2l1U2dmL0ZiUTFGNWZORlpSSXRUU25mcy94K09Wd2hlQ1RHLzZJc3djUXROSXJ6R3kvZFpEbUFMTzAzL2Nab2xBN084OGlxVG5FRW5vZ1kxa3ZIeE1QbVR6YTgwZFNESEpGZE5UZER3eWxFdFFsNTRKN2ZWYlpDZDJZdmxJWEhKbkJUcGxNUGdpWEFvWWp5QVJTalZUbWZmSVlqUUFmc3RvNjg4L1hsQXFmdGVSSngwVXY5aG4zeUNLd2N2VEtabTRjVFBUcVBvTXFwc2ZRQTdJaGpyVVRTb0gxV2J1NGZmWVppdGQ0MEFtYVNWa0FCLzc4eXJkazBRLzZLVmNNNlhTWUowbFlQSDRYZGExM29sWjhHd2NkVEhKNE5DYXlDV2Qyd0huamFEeFBMMjNualI2RnZNU1dTOEo5Z3pUUU5GYVJ0UVU5dVFCbmdzZ05EZ2FJSHFaR1pGZ05BR0hiZit6T2tUQU1oY2xPRmlpczlYU0tQSVRhd3JZRzZ4NUgiLCJtYWMiOiIyMDg0ZjUwNjU5MjVkOGZlODNmMTcwMTI1ODM2NDU1Yzg5MzhhYWRmZWIzMWE1ZWYyMGFhODI1Mjg0MWE2NjJhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-977656136\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1839</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1605;&#1610;&#1583;&#1575;&#1604;&#1610;&#1575;&#1578; &#1588;&#1603;&#1604; &#1605;&#1581;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1839</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}