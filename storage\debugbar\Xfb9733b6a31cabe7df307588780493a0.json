{"__meta": {"id": "Xfb9733b6a31cabe7df307588780493a0", "datetime": "2025-06-08 00:09:22", "utime": **********.441774, "method": "GET", "uri": "/inventory-management/products/8?search=&status_filter=", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749341361.658833, "end": **********.441795, "duration": 0.7829620838165283, "duration_str": "783ms", "measures": [{"label": "Booting", "start": 1749341361.658833, "relative_start": 0, "end": **********.290027, "relative_end": **********.290027, "duration": 0.6311938762664795, "duration_str": "631ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.290045, "relative_start": 0.6312119960784912, "end": **********.441798, "relative_end": 2.86102294921875e-06, "duration": 0.15175294876098633, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47114104, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x company_operations.inventory_management.products_table", "param_count": null, "params": [], "start": **********.431789, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/company_operations/inventory_management/products_table.blade.phpcompany_operations.inventory_management.products_table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcompany_operations%2Finventory_management%2Fproducts_table.blade.php&line=1", "ajax": false, "filename": "products_table.blade.php", "line": "?"}, "render_count": 1, "name_original": "company_operations.inventory_management.products_table"}]}, "route": {"uri": "GET inventory-management/products/{warehouseId}", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@getWarehouseProducts", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.management.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=39\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:39-106</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.018760000000000002, "accumulated_duration_str": "18.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.372845, "duration": 0.0143, "duration_str": "14.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 76.226}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3989298, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 76.226, "width_percent": 3.198}, {"sql": "select * from `warehouses` where `warehouses`.`id` = '8' limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.40423, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:42", "source": "app/Http/Controllers/InventoryManagementController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=42", "ajax": false, "filename": "InventoryManagementController.php", "line": "42"}, "connection": "ty", "start_percent": 79.424, "width_percent": 3.571}, {"sql": "select `product_services`.*, `warehouse_products`.`quantity` as `warehouse_quantity`, `warehouse_products`.`id` as `warehouse_product_id`, `warehouse_product_limits`.`min_quantity` from `product_services` left join `warehouse_products` on `product_services`.`id` = `warehouse_products`.`product_id` and `warehouse_products`.`warehouse_id` = '8' left join `warehouse_product_limits` on `product_services`.`id` = `warehouse_product_limits`.`product_id` and `warehouse_product_limits`.`warehouse_id` = '8' where `product_services`.`created_by` = 15 and `product_services`.`type` = 'product'", "type": "query", "params": [], "bindings": ["8", "8", "15", "product"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.408567, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:62", "source": "app/Http/Controllers/InventoryManagementController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=62", "ajax": false, "filename": "InventoryManagementController.php", "line": "62"}, "connection": "ty", "start_percent": 82.996, "width_percent": 5.704}, {"sql": "select * from `product_service_categories` where `product_service_categories`.`id` in (4)", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 62}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4158058, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:62", "source": "app/Http/Controllers/InventoryManagementController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=62", "ajax": false, "filename": "InventoryManagementController.php", "line": "62"}, "connection": "ty", "start_percent": 88.699, "width_percent": 4.211}, {"sql": "select * from `product_service_units` where `product_service_units`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 62}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4212701, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:62", "source": "app/Http/Controllers/InventoryManagementController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=62", "ajax": false, "filename": "InventoryManagementController.php", "line": "62"}, "connection": "ty", "start_percent": 92.91, "width_percent": 7.09}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}}, "count": 6, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/inventory-management/products/8", "status_code": "<pre class=sf-dump id=sf-dump-664323973 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-664323973\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1510499149 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>status_filter</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1510499149\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2038893309 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2038893309\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1777580131 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749341329306%7C29%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImR1UTNlOXBtazI4YU9rNW1FemhKbUE9PSIsInZhbHVlIjoiY0QrRUpCMmF3QXliYVlFSWpKczZVOGNoUFBOb29oOHU1eCtUQzhYVjFSNlZPVnVpcCtleHdlR2ZNUm5aUGVZWG1Ub2cyY2dOS0paQkRBdmRJbG1iamxwT2NieElQRjFpL2lQOVFwTE9IRWR4V1FCNXRBUXVlUGRkRS9qNXpOUm0zZHpQZTRrZEZOWXQ4VWVGNElFTXpMWVBDT09raVhnSjhlZHlmU2JVVXA0ZURuL2xBNDEzSzJZMEkvRnhUQmlJSEFQTGZJYUFXRkxOZmsrL3ZNZTBPZ1YxdjNVNUtSN1ZHQTYvQkF1YnFqNUpGUXhMMllvb2x0WUw0aFUyeEJrdllEMEJyemtVOGtackk2U3hPWGtHVHhtZytPQ0ZSY2pGbndRTG9EOVhLN0RzcnE4TXA2am5tRXJWL2w4OXV6N2JTZHFIdkFuYVhyMkErWWdJdnFQVElOS3FXS1IxY0RQVUtvK0hHUFdUc2ZsazF2TGVXaTRZQzhXVXIwYk00T1RDWUtsZkorWHczL0VwcFdUeGpac2ZteDVWUEJzcm96dTgzT1BLWjdPdHY4VGJoWU95U2o2T3J0ckJqVlplUG9jUElURTFsMGx2UUM0bTAvT09ONFY3dHRSeDlVYitNNk5CZ2tWU2tiQ2VNRVYvSjJHanNsQmdZV3ZJSXd1RjhPajMiLCJtYWMiOiI3ZGVkZGRiZTJmM2VlZDM0OGE4ZWY3N2YzY2QxNzhjOTM4MWI0NTBkYmU4M2Y2MTU1ZDk4Nzc5ZDVhYjg1ODVkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlVoOXYyZlA2YVFRSEZTa05mY215Vnc9PSIsInZhbHVlIjoiQmJqTEpuemVlc0VqdTRmVUpCSGNFbWxyakVnTzFEc0FGU2tnNmI4K0xnWVVpMjVEeDMySnl2aDViWGVzL1p2Nmg1QjhSSXB6U01wRFJFWHc1RVduNnp3VytUQStEVUh2L2dNNzRyMHlLR2pWUnVKQlN6dklKYzhDOTY2UHd3YXZ6dU9CSDJMV1k1eHRJRUwwWE1kdytYQk1rSDRzQk05ei9Hb3BSS1YwaGJWeEpsT0wwSG95TDR6b1FTZ1RkZFYvMXRncUFBMU50bUMzd0NyS0hDaUpRNjZJZXJaTGlWVzVKLzVFbk1Xc2xubTNLbENjcjFhOG9UQ1VLSGxtbWF5STVpUGI3Q3NSN3BoM25oK2J2aFd6aVIwSWRRMTNrOXp1WW95YndDeXZEWFErZFV2SDVlSzlDeVNFUnhJa01zVjRETVd6RHFRb0xGWlplelQxeTZBMXpGNVJiR0FtTVl6VXBZdDQ0eHR1dENHVTJWS2VTV1hzOEVGMUd0amtSeHJqZHd6L3ZuRHk1N05RdFR2UUhheFpPNzVILzlTdnRteWVaYkxvWlJHR29oaUdTTDl3SjVhKzh2ditRR09ValFZWGtNTDFsR2gyY0ZCcm1PTGx0eDk0dHpMOHZ2dFZSRExVZkdVYm9hYjIyUnBpTnNiZmxheHE2Ti9naVBIamhoakoiLCJtYWMiOiJlZDhmZTg0MDkwZjc5YTBhNWY2NDJlODE1MDA2OTE4ODAzY2Q3NWVhMWJmMTkxZWQzNjUyMDgxYjA2NmVkMTExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1777580131\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1094411867 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094411867\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1941237118 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:09:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImI4Y3owdUthWUhKNXlBRForUVB0REE9PSIsInZhbHVlIjoiNVhhOE1vMlpnNGFMc3JoNVNXdDZkWC9vNTByUVJRMVlYSjZrM3NpdmNKZWdoK1dvNW4xS2M4bnh6TjgwTlpNeDJnYUVtZzh1YitmUjYxcW1hTzlBUlJycjBRcDk2SVgrRmE3VVVldkY5czlXbWU2dHVjRTl0MFZTTzJtNDRSS1p3NG1EdklENW9KOTJ3anlsSTdRRzd5U2srdjhVbFBsTkhGdElPZnRqVDhRcktQcnE4VG01OWsrU002M0lEanNRV3lWcHljazZwMURZcWljdWJEKzBCOUN1TVk5djJUOVZETVpHWVBKejNueXFzdmx3MWkyRnJwWG14NkJLenJPZzNZSUJPYlBFK2YrNUtzcjBEWUZMZjZCT3A5eDMyOG1ua3pZV0VxNk50NmJVYkVZdEliODBkSVVrWXY5RG41L2U0S2Rnd1VPWEhHeXNsSFEyYVpNMngycms5dkl1dlFDVTdHbDRReGxkTkl2VWxHc29LYkFLd0MxNVpsaW5SblBuUmpTejBLSkdmSmZ5NVJVOWZvSWxiSjdpV1FaenEzS0lRL2dGMkhDMXUrdlFSYUJEd0xSMVkzSmp5MTlka05pd1kzUURXUUNtNXcxRFVTZzl1K3FsUXVIZHVGWHAzaklaTVBka3UwcERYVUdmd3lvMGhMZ0FlYzBUMFFBa1pFb3MiLCJtYWMiOiJmMTBmYWYzMGE2YmFhNTQ5YjgzY2M2MTJmZjNhYjE1YWM3YzkzNDcwOWQzMGI5ZTcyYjQ5ZDFjYzdhNWJkMDBjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:09:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InVBNmNFb1N6WWhwSXdyaXhOa2tGdlE9PSIsInZhbHVlIjoid0hDaTlOd2JYVUh2RFpCdVFhQy90dXFIT0JyV3V6RytsOUViVGc2clRvYzg3UnArcWJtRVJMVmVWaExLV3o2UWVxRkF4YnhzU2hGUHRITGZiL3VqMnFwdElKckhiY2JuYW10VGQ1YVhEbEs5REZHaTBnRkFUVWY0SXBodnBhUFArR1NtTnI5VWxPRkI2VnpGTXcxSDQ0MStZTUZDckJWKzc0YnpLcUUzWWc1L0hqU3lnZUMzc0NHTWtiVHJlbDlSOWYrdS9ZbTM3bU1FZ2VNUFpEWFBMZkY1dnRXWCs5b2Q0V3o2NUdRMEpqZE1LKzkyQ3hPVHJNcUp2WUcrRUdRbHBBaDJhVDZmSUd4NXFsS0d3UjhBQ1Fnd1dQcnNwMmdQcFVOdG5sc25EaFRwNmEwcStIZ1ducVJkbndMNkZrR3BLTmNEODVscEZ1OG4yYThPZWtrdmdWSHQwK09vUVRUb0VkV3ZIOWE5T015eE4zUjRzYWx2dkU3Z0NoY2M0aXU2MzBJVzFhRHcxWHYwbTZJWEhpNEZvaGEweXplSFVEZ0FOSTdBa2F1VUdvaGtsZ0ZhcVhkSDArY0p6VFpMZDh6WDhxYnEyVXk5SDk5VGIvd01HS1pBcnZOd0h6THZsZnZZYTh4TFVuQ3kzQU5BVWZOT2xQb0Q1REpBdlZwZVNyWVUiLCJtYWMiOiIyMjUyNzdhYzdjNTliODNiYzBkODFjYTkyMzRkY2IwZjgzNWY2ZWFkMTMyOTdlODJjMmFjODZiMjI3NDZkMWM3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:09:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImI4Y3owdUthWUhKNXlBRForUVB0REE9PSIsInZhbHVlIjoiNVhhOE1vMlpnNGFMc3JoNVNXdDZkWC9vNTByUVJRMVlYSjZrM3NpdmNKZWdoK1dvNW4xS2M4bnh6TjgwTlpNeDJnYUVtZzh1YitmUjYxcW1hTzlBUlJycjBRcDk2SVgrRmE3VVVldkY5czlXbWU2dHVjRTl0MFZTTzJtNDRSS1p3NG1EdklENW9KOTJ3anlsSTdRRzd5U2srdjhVbFBsTkhGdElPZnRqVDhRcktQcnE4VG01OWsrU002M0lEanNRV3lWcHljazZwMURZcWljdWJEKzBCOUN1TVk5djJUOVZETVpHWVBKejNueXFzdmx3MWkyRnJwWG14NkJLenJPZzNZSUJPYlBFK2YrNUtzcjBEWUZMZjZCT3A5eDMyOG1ua3pZV0VxNk50NmJVYkVZdEliODBkSVVrWXY5RG41L2U0S2Rnd1VPWEhHeXNsSFEyYVpNMngycms5dkl1dlFDVTdHbDRReGxkTkl2VWxHc29LYkFLd0MxNVpsaW5SblBuUmpTejBLSkdmSmZ5NVJVOWZvSWxiSjdpV1FaenEzS0lRL2dGMkhDMXUrdlFSYUJEd0xSMVkzSmp5MTlka05pd1kzUURXUUNtNXcxRFVTZzl1K3FsUXVIZHVGWHAzaklaTVBka3UwcERYVUdmd3lvMGhMZ0FlYzBUMFFBa1pFb3MiLCJtYWMiOiJmMTBmYWYzMGE2YmFhNTQ5YjgzY2M2MTJmZjNhYjE1YWM3YzkzNDcwOWQzMGI5ZTcyYjQ5ZDFjYzdhNWJkMDBjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:09:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InVBNmNFb1N6WWhwSXdyaXhOa2tGdlE9PSIsInZhbHVlIjoid0hDaTlOd2JYVUh2RFpCdVFhQy90dXFIT0JyV3V6RytsOUViVGc2clRvYzg3UnArcWJtRVJMVmVWaExLV3o2UWVxRkF4YnhzU2hGUHRITGZiL3VqMnFwdElKckhiY2JuYW10VGQ1YVhEbEs5REZHaTBnRkFUVWY0SXBodnBhUFArR1NtTnI5VWxPRkI2VnpGTXcxSDQ0MStZTUZDckJWKzc0YnpLcUUzWWc1L0hqU3lnZUMzc0NHTWtiVHJlbDlSOWYrdS9ZbTM3bU1FZ2VNUFpEWFBMZkY1dnRXWCs5b2Q0V3o2NUdRMEpqZE1LKzkyQ3hPVHJNcUp2WUcrRUdRbHBBaDJhVDZmSUd4NXFsS0d3UjhBQ1Fnd1dQcnNwMmdQcFVOdG5sc25EaFRwNmEwcStIZ1ducVJkbndMNkZrR3BLTmNEODVscEZ1OG4yYThPZWtrdmdWSHQwK09vUVRUb0VkV3ZIOWE5T015eE4zUjRzYWx2dkU3Z0NoY2M0aXU2MzBJVzFhRHcxWHYwbTZJWEhpNEZvaGEweXplSFVEZ0FOSTdBa2F1VUdvaGtsZ0ZhcVhkSDArY0p6VFpMZDh6WDhxYnEyVXk5SDk5VGIvd01HS1pBcnZOd0h6THZsZnZZYTh4TFVuQ3kzQU5BVWZOT2xQb0Q1REpBdlZwZVNyWVUiLCJtYWMiOiIyMjUyNzdhYzdjNTliODNiYzBkODFjYTkyMzRkY2IwZjgzNWY2ZWFkMTMyOTdlODJjMmFjODZiMjI3NDZkMWM3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:09:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1941237118\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-713283328 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-713283328\", {\"maxDepth\":0})</script>\n"}}