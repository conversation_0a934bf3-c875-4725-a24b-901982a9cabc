{"__meta": {"id": "Xf5d2f42bdaa7a82d48a00e4165e07c25", "datetime": "2025-06-08 00:12:00", "utime": **********.992478, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749341519.910289, "end": **********.992515, "duration": 1.082226037979126, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": 1749341519.910289, "relative_start": 0, "end": **********.868266, "relative_end": **********.868266, "duration": 0.9579770565032959, "duration_str": "958ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.868289, "relative_start": 0.9579999446868896, "end": **********.99252, "relative_end": 5.0067901611328125e-06, "duration": 0.12423110008239746, "duration_str": "124ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45052544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.006319999999999999, "accumulated_duration_str": "6.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.93207, "duration": 0.00437, "duration_str": "4.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 69.146}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9580278, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 69.146, "width_percent": 14.557}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.971432, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.703, "width_percent": 16.297}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1251571085 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1251571085\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-961016887 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-961016887\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1129482589 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1129482589\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1552302731 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749341500137%7C30%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZkdXFCWlZDOUNyNGRjSjZ0Qnk5Unc9PSIsInZhbHVlIjoiaGJaTjNkZWUrUWhFSzZWcW9QQUcwN3NxZmF2RzcvNjFmSDY2V3RNcTcxNTZ4V3IrcmI4M29GL2xlT0dWazIydm82bUpMMUVmSUlSN3FGbXZJZmtTenlUMzIvRWl6dzA0eklGd0ZtTjBtR2ZXcEZ1V3JWaGlUMXZ5bDljbzlUNGU5Z2p4dEpGZ3pWeEZrQ2lwY1ZMakFHcFBNeUpIbWR5b0ZTL3JMR3FnWmdCM3Z6ZmhVcU1WMXdEcVJOVHNrRVgzWDRQS2pETDlESWFTbE9zamhDTUJQa0dYYkI1NUpuU1cwbGZxam9VRjJFTE02Ly9QRFA2dkhRTGZqTWFMSjdZbndsRlptY2RZSHIwY3FlcGNjY2tHbmZKUDQ0cmJYVWtZaGp1VFUyV3JRa3hxMXFOYnNSbElGTk9oU2NMcmk5VVBjSzlsV1JDZHdDMWgxYVhVaEZsRXVQSVJnblpoRkFIM3JxcWU5VldIVCtJbEhEYmVvczdYTnR4WFdlRUN2UGM5bUpydUJTQVkzZTFUSTRtRDd5L2VScVRmeVdIRDFRRFFkdHRRSTIxaEZ4czYra2JSS244Szl3aE9ONVJnREhJd2UvRUZkSE5tZXJaTmlTNitWVStqS2lrQVhqZ3pseE9RZCtKS2gzblc3ZmFEcG11cnFGdHZQMmdPU0oxelZkRDEiLCJtYWMiOiJhM2VmMDQ5MGEzMGM4Nzc0YmU2MzAyMGRlNmQ0Y2NjOTg0MmExZGU1NTM3NGY2MzA4ZTAxYjZjNWIzZDRhYWRkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InFUemVLc3ZScTZsd3Y0WDNZOEV5N3c9PSIsInZhbHVlIjoiQ1JoOUZhaFVHUG1KTlZ2YS9qbW1HejZxYmk3S3g2TGhUZ2ZFZjVIejROV1lELzBFUG5QK3ZxdGRwZDF1NW5Lb0RkWHlLUWFaeFdnZ2xpYkVmZVFtc0pwTVV3S0NsSURQMTJ1blA1U1ZjVStwMFBINXBqd0ZmbXhZbVRNUWxTMXBqOFkxeXd4NXdEb0ZrR3llQ2VoYm5DUG5PK1pxWTNxaVU5VWNrZjcrQXFOUW9aUkhrTGc4K0tYMk42dUFzcXYyOE9yZFB2ZVZxMURkdTQ2UXhFS0ZCNlN4eDRLUkZSalUzaWFRZC9CQVpvOGpRcVZkTU5sK1lJaGtraVI0WVVSLzdkQ21lL0FTYWZxM2N6ODk3TlFSUHZGQ1VPZ2tTVVBGSDUrOXFYTXVvc3R0K0VsS3UySzhRK3JHeVBkaTVsZldKdXBYQ1NGK2htSTZLY01JMHRFbnFMOVdRRjlBVWJUcmFaMVpPRENnRkg5dHBHNTBzQkIvZVJMYWU5c3VRR2NicUNFN09STTQxVitELzBvZkFZdjBOL3E5VitZR1lBWS9pZzVyOG1wWkRMMW9MUDhHSEx4eVNCUFRpSzJFZjB5KzhlTTR2ei93ZmMyQ1FTR2ZsbVI0YUhOcE1FUGVmTGw5NGFIT3BJTVhUVk9GU1FxVzk4bVZJRCtSa1M1aHAvOVIiLCJtYWMiOiI1N2IzNTAzZTE3YzE5YmM5ZjZmMDhiOWFlYjViNjYxY2IzZDM1ZjIyMzYxNzRmMzk5MzliOTA2MzJiZjYwZTA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552302731\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-177715274 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-177715274\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1808980518 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:12:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlyMnBaRUlGRStleS9uZ0ZpVFNsV2c9PSIsInZhbHVlIjoiTmh6dCtmekk3OGVBL0EzbHVEUWo4a2dkaVBrdUhVMzJYSzdJMk10TE1XRFdKT29iVzNXMDF6QmVGMTlSM3BqMDEzVFFlRTgvSkR2NHBzVDRQZitRU0lhdUZzTTBlOEpWTWt1akppUFhHOXoxT3Jkcm04QmpFT3lTdnBmSEdiTXVjdjZYYk1NT09uMXA0ZUl6UGxuNlAzeW1sNkRKa0oyaFdNYmkxU212cWtSR0dxSFVTbzkxbEJVWnBqWkdpZXdheVlTeGhzT0prSUdnSjFjWnpNR2JMU0hBL25nR2NzaXlobllOVFpHNlFJTDVIMGJ1eVNvdTJJYzJ1dSt6Z3ZIc0g0WDU3dnVOT3ZaUjdYK0ZRR1Y1MCsxU29LKzJmRVpaSVJYUWJ4Y2hpV2trcFovN0NTS2VYdTl2Wk16SVVTQnhXZkxmbVRxOFBoMS8yS0YrNWdPSGNjczZUNTh5ekN3VndIaFNVSHZxcUxITWpUVG1CZEh0WVhGN1ZvSjJHa1U2dGFaWnEwV3JzQ0pYdVlQWlB5cUthbGJab0E1dHY4OGEzQmFaOHMyU0FBaGlTQzFGanQ2aVZYcndES2gyVGdaM01NNmw1OHI0YURvVWtEL3FoajdIcGRJQjAxOXZ2NWNXQWpTS3VyRlNCTFVSWHdJMEtBTDhKNk5BcFRsbWVrZnIiLCJtYWMiOiI1ZWYwMGZiYTI1NDYzMzBiM2EwMzNiMTk4MzEwOWNmOTViMWFmMTAwNmUxYjA4ODZjMDVkMTllMDE3ZTI0ZDkzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:12:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InlpYlRkajNjWXU2b0plNWVxM0xYTlE9PSIsInZhbHVlIjoiMHlnbW42MG5DbUF3NGs5MVExdzJHL1pEaFhHZWVRU3BIVklkUlNEdDdSVW5jbDhra25zMW5YdnNQbkFGdDUwMExFU1hTc2Q5eFZoV2JmN2ZVT0RHc09KL29PV2JvQ283MUIyREgrdmp6TVF4b3hicFNDdGw4TU1hTkJIVmUzNk5LZklNSnY5enRBbW0vNTBNZU8vZnp3QlVNMkVtcHZDakVFZWUxbkwrR08yNFltbDMwanY5NmFueTRNbmJoSFNJSk9QdTFNRWFVNGhvUk4xb3NVMEE5dXY1cCtOT1E4dW1JSHZ3VUpFMGx2L1RxeE55YlUvZ2xSSU93dC9lK3NNUUlPOWtocFI5MCtkZmVtTGhDUkNXUTFURWdtUm16VXUvOThIdWRmRnlISVJWUjNiNHVOUGZYbmZVVXBmY2VKYnpaeUtieDNsVkVnVWcyN1VQR205SFJHM1JqaGtuLzhYcm93aXo4NVJCd2VPRVdiMUpBWEtSSFNmZ0Q5Q3ZTb1B4MUp0UWRGc05uRjlGSlYzQXRNOXN0clNSSnVvMjJuOTZXRGlHR1A3Z2pjRU11RUYrNmR5Tk1VbGhWZVErUmc3ZE9pdEJNTTVwUWtJNERnWGhEcGZlRXEvSFByL2xWbXhOVmdsbmRPcHVxaWFqWW1BNmZKMTJxL1JBVnRPV0dtdWMiLCJtYWMiOiJiOWJmMzE1Y2E1NjNjMGFlNTg2MGRlZDYxOTExNTY3YTJhNWU4NTViYzk4YTNkOWQ3MTZkNmU3YjU3ZTNiY2U3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:12:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlyMnBaRUlGRStleS9uZ0ZpVFNsV2c9PSIsInZhbHVlIjoiTmh6dCtmekk3OGVBL0EzbHVEUWo4a2dkaVBrdUhVMzJYSzdJMk10TE1XRFdKT29iVzNXMDF6QmVGMTlSM3BqMDEzVFFlRTgvSkR2NHBzVDRQZitRU0lhdUZzTTBlOEpWTWt1akppUFhHOXoxT3Jkcm04QmpFT3lTdnBmSEdiTXVjdjZYYk1NT09uMXA0ZUl6UGxuNlAzeW1sNkRKa0oyaFdNYmkxU212cWtSR0dxSFVTbzkxbEJVWnBqWkdpZXdheVlTeGhzT0prSUdnSjFjWnpNR2JMU0hBL25nR2NzaXlobllOVFpHNlFJTDVIMGJ1eVNvdTJJYzJ1dSt6Z3ZIc0g0WDU3dnVOT3ZaUjdYK0ZRR1Y1MCsxU29LKzJmRVpaSVJYUWJ4Y2hpV2trcFovN0NTS2VYdTl2Wk16SVVTQnhXZkxmbVRxOFBoMS8yS0YrNWdPSGNjczZUNTh5ekN3VndIaFNVSHZxcUxITWpUVG1CZEh0WVhGN1ZvSjJHa1U2dGFaWnEwV3JzQ0pYdVlQWlB5cUthbGJab0E1dHY4OGEzQmFaOHMyU0FBaGlTQzFGanQ2aVZYcndES2gyVGdaM01NNmw1OHI0YURvVWtEL3FoajdIcGRJQjAxOXZ2NWNXQWpTS3VyRlNCTFVSWHdJMEtBTDhKNk5BcFRsbWVrZnIiLCJtYWMiOiI1ZWYwMGZiYTI1NDYzMzBiM2EwMzNiMTk4MzEwOWNmOTViMWFmMTAwNmUxYjA4ODZjMDVkMTllMDE3ZTI0ZDkzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:12:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InlpYlRkajNjWXU2b0plNWVxM0xYTlE9PSIsInZhbHVlIjoiMHlnbW42MG5DbUF3NGs5MVExdzJHL1pEaFhHZWVRU3BIVklkUlNEdDdSVW5jbDhra25zMW5YdnNQbkFGdDUwMExFU1hTc2Q5eFZoV2JmN2ZVT0RHc09KL29PV2JvQ283MUIyREgrdmp6TVF4b3hicFNDdGw4TU1hTkJIVmUzNk5LZklNSnY5enRBbW0vNTBNZU8vZnp3QlVNMkVtcHZDakVFZWUxbkwrR08yNFltbDMwanY5NmFueTRNbmJoSFNJSk9QdTFNRWFVNGhvUk4xb3NVMEE5dXY1cCtOT1E4dW1JSHZ3VUpFMGx2L1RxeE55YlUvZ2xSSU93dC9lK3NNUUlPOWtocFI5MCtkZmVtTGhDUkNXUTFURWdtUm16VXUvOThIdWRmRnlISVJWUjNiNHVOUGZYbmZVVXBmY2VKYnpaeUtieDNsVkVnVWcyN1VQR205SFJHM1JqaGtuLzhYcm93aXo4NVJCd2VPRVdiMUpBWEtSSFNmZ0Q5Q3ZTb1B4MUp0UWRGc05uRjlGSlYzQXRNOXN0clNSSnVvMjJuOTZXRGlHR1A3Z2pjRU11RUYrNmR5Tk1VbGhWZVErUmc3ZE9pdEJNTTVwUWtJNERnWGhEcGZlRXEvSFByL2xWbXhOVmdsbmRPcHVxaWFqWW1BNmZKMTJxL1JBVnRPV0dtdWMiLCJtYWMiOiJiOWJmMzE1Y2E1NjNjMGFlNTg2MGRlZDYxOTExNTY3YTJhNWU4NTViYzk4YTNkOWQ3MTZkNmU3YjU3ZTNiY2U3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:12:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1808980518\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1961875913 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1961875913\", {\"maxDepth\":0})</script>\n"}}