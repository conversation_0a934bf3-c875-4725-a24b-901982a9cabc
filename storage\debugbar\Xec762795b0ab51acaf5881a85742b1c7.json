{"__meta": {"id": "Xec762795b0ab51acaf5881a85742b1c7", "datetime": "2025-06-08 15:34:13", "utime": **********.093712, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396852.297263, "end": **********.093732, "duration": 0.7964692115783691, "duration_str": "796ms", "measures": [{"label": "Booting", "start": 1749396852.297263, "relative_start": 0, "end": **********.013652, "relative_end": **********.013652, "duration": 0.7163891792297363, "duration_str": "716ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.013664, "relative_start": 0.7164011001586914, "end": **********.093734, "relative_end": 1.9073486328125e-06, "duration": 0.08007001876831055, "duration_str": "80.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45166072, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.005999999999999999, "accumulated_duration_str": "6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.055483, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.07216, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64, "width_percent": 17.833}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.08166, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 81.833, "width_percent": 18.167}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1136179968 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1136179968\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1396254640 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1396254640\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-910826823 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-910826823\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396575006%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFFVmwreEU4QS9jR0EvdnZ4WEx1TEE9PSIsInZhbHVlIjoic2tZbG9kNEJFd0k2K21QM1lzRnhqMkFsQmdmU2hlZnpnQmt3VlhVSjB3ckNMVCtJUGRSZDl2WkdsSmh3eUxmTFlzY2YzS3cwaFQ5ZEIyVzFFNHRTeGN4bWhsSkdPRjdPT3RIVWZHVVZUT2NWK3ZXYmlyZWYzMXl4YjdxK1ZFTHBnNXlhL0NXb1BnNloweGEwZ3J3d3JNNkdtUVlCcnVNT1RxVGlzVDMrK0EvK252TXhVdmI3R3FrbmFKeWtZMlBoSm1mbWhIZXowK2ZiQld4dkVFRjZDTThTcllXdVBaSUwxT09yVm5LQ2lTNG8ydDFFL3pZNGc1SDlBcnJlVDhRRW90NnArUS9nL1NjN2ZTcHZuMnp1YUhURFVTTWdLOERiYlF1cVFRNythem1pYTFSS1ZiOWZFYmsyZTA2c0pHL3orNC9aODNXQkVtc20xcEowSkF4dTJ2aWRPc3FLY1gyWWxqa2F3bUIyNGtlOUpWQXdYdTVOZHRVNjUvMW0rS1NsbmdRMWk2SDJzZnBzb2JBVlQ1c2IycEpUZllLNzZEYXBPbnlIMGxmZ3Y3elBYakFvQ3d5Y2xBajk5L0tqOEx6L1V5aVJrTGxkcm02b2w3M3doMmppZXlROXkrdDZiZEE2N1hUbVR0T2NhNndpSFRDYTYvdGM2RzhpSGh4RTlRTmgiLCJtYWMiOiI2NzliNmE5YzNiYmJjNGFmMjc3Nzc4Y2MyNDc5NjM3OWVhOGY4ODZlZTRiNzczODI0MDdmMTlkZDhlM2FkMjM3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im4ya1RFc09TT0VwYVozV1BFWTMybXc9PSIsInZhbHVlIjoiRktpdjcxMzkrdFVVaUNhQ2VTdnhxU1VWemFaSTl5UWtLNXNvYTMxYW12UUVMcVBOZUh3YzFsVUNWZ01lZDc5Z1EvT1EvdTdVdDNSelozSHh5MUNDS2gvTlJzVnVkU09Xbk42bHBzRk4weUh6aWZDN2tSNlllMU83dFltSUc5MGFvMDBnaE1oN0FFSDZnTXhwdlN0OURNZldiSnkzRTVYTXpMRUJtYW9pRm1qdXJjdXBJYndiVzEwSHJJcWlVbEdRbUVaeTJ1Wk1NaUdFSk5VeEdJaG9IaEdsdVJ5QU5HWHhlZUxiRkRxeFJ2NkM3TXRnUFc4WXM3cXJZMEduMHhiOTlhWm5OR1ZDOGdYZ0Q3YnhaOUJJcU4vZklja1NhZFJuc3VjVkczS2NvTHA1MzZ1SG4zcjRpdlkwWm9YUHZnY1AvMDZGUTRHUGd6cnpSVEkwTzRzV1BGUDMyMzZUT3VBWm1vYlNrNG1uaDhoNSs2UkxwNGVjcDg1TUQycjYyUnNXTTRpYnFPVnRTdFBmMHhCQTAxaHdtcm15YjVXMG9xNGZJc0llYUdDdXRzL0x0cy9HVTJDOXJkaGQrUEl6a04wZFNYdEVwd2tFV2QrekVTREZkMWJVUW12WXAwN3NzdkJwOFVFN2pXU3VUalJyQkJxMGdBbTEyVklvU0pUVHpNWTciLCJtYWMiOiIxYmIyNGM5YjQwOWU4ZmYxOWZjZjMzZjllNWY4OTgzMDU2MjAyOTY5MDBkMzA5MDA0NTViZTg0YTkxYzBmOGM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2099265643 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2099265643\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1214288155 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:34:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhoMDBzcjF6OUVWbUcvTVlHUUF3VXc9PSIsInZhbHVlIjoiWTRqbGlWakEwZnJkc0QwV2ZOYU1ybElWeEpWOVlOc1ZtNm4wdnJYRjEzd2tULzB2NnhQUlprQTM3WXQ3VVNvT3NVYVIwbElzU3FEWUV0TUlvSCtMVVlDNmtGYzRqcVBFZ0lnZ0VTT21ISjJsNEprS0pRb01xZ29MM0IzbUVNUWx4YTdQT3hzcUVDQ2JDbnk4VWVoc0lOMjdyc0NvTXUxL0VPTnFuUGxTTDBrTUVRUjlxdXBMM05wWXhTeDQyaFBPRm5xTHFESXB0TlVWaEpkUkdkTXFrVHlpS20rbWRjV1BQNUNUczF5T09RQTh1MXc0Uy9vdzNsMkNUVGhjU2M2SExkSi80SDRoS3BmRlJFZ1ZtRmt3YklUSUJZYzZBazFKNmRRR2NJaDdPMjZUT3BHSFRZS1VqbkhQRlRwZ3pFa0ZDZmE1V0ZwWE03S0FHOElRbGpGa3U5QmdnSThISXRQZER4RXJ5YkxrOW9YZzI1TW9BVHA1MThFRmk5aGFkMkVlR3hqTjgyRmdVclRXRFk4NlVVL0ZCWHFYY1YwV3Qxb2hhbmdRWlJqbnNjeFZRUW1nTkhDWHVUa0VYZUk2T1ZLdjRjK0RQN0dnNmRkb1pKeUZrMHdFbEFCUkRIb1o1eTlUL0x0VEpnUU15RnU5V1k3Sk5SUmg4YXlvSnpKWUdiVUwiLCJtYWMiOiIxNmMzNTYyNTI4YmU4YWZiOGJiYWMxNDBhODcyZWY4NTc2ZmQ5MWFkOGRkZDU5ODg1MDI0MTcxN2ZjYWYwMzdmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:34:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkpTcEFIRHBlZW5ManZGU1VkUFR6Nmc9PSIsInZhbHVlIjoiV0lrd1RMY2JKYStSS01wdU8wR1dBSjg4UWRTakFzanppdldpb2FtbytGNE40NkZhRnlWSXlKSGJydWVuaCt6WUoyNG5uMXVmZWkzNXk4cW1temRCdTFvQXB4TDJoL1pYcGtJNUkyN0pKekhNQXRKTXZaU1JnY2tqT09qd29YdElaMlk0aWo2aWdZRkhrbDZzd0NwcUh3Z3dTWFF2M1A1KzhBSjFYcFJRaEpoZi9RVmlTbkoyQXRuRjRUT1FGVFJMYmxJaWc3WkdSTjdLYjlIWjRIVytkT2RxbkdISk9YcStLYzMxdzlyV1FqVWtyQm1ieWtrQzFpWUxBT0MwcTBuaE1lVkdyYW9SdDJXK1dLQkRZakNrOFAvcE1sc2dxZFY0VVgzWE85UHkyb0FQbUZ5OThxWGNJVi83VmlXbXYzT0RDOGs4S0lhL1FHYzhpRVhUcGtuWEN5eEJjY0xGS3NxTmNxcVNvRHlCM09IbFJhTXdwbG4vZDhLb0ZQQkhHOFlwNVJTYWRQRjVHNm5oZWJHM21RZkV0eHVEMnp3U24wZkJmVm5HWEFNbkxpdFZlLzV4RFArQWFlZ0NDajllWklkdGFmaTVFYW5Gcnh6ZWlnRnAyQ2lsdzREeUxJVEJJbXhRTjQrYU1qUUd5MXc0UFJab1p6cVJMMURUNHFjK2RQcWYiLCJtYWMiOiI0ODYwMjVjNDQ5YmE4MTEyYWI1NmY4ZjNlNDM4NTg2OWJiMDQxYjNkZTA3YTgzM2ZkM2FhNWMxMjQ2ZmE0M2QwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:34:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhoMDBzcjF6OUVWbUcvTVlHUUF3VXc9PSIsInZhbHVlIjoiWTRqbGlWakEwZnJkc0QwV2ZOYU1ybElWeEpWOVlOc1ZtNm4wdnJYRjEzd2tULzB2NnhQUlprQTM3WXQ3VVNvT3NVYVIwbElzU3FEWUV0TUlvSCtMVVlDNmtGYzRqcVBFZ0lnZ0VTT21ISjJsNEprS0pRb01xZ29MM0IzbUVNUWx4YTdQT3hzcUVDQ2JDbnk4VWVoc0lOMjdyc0NvTXUxL0VPTnFuUGxTTDBrTUVRUjlxdXBMM05wWXhTeDQyaFBPRm5xTHFESXB0TlVWaEpkUkdkTXFrVHlpS20rbWRjV1BQNUNUczF5T09RQTh1MXc0Uy9vdzNsMkNUVGhjU2M2SExkSi80SDRoS3BmRlJFZ1ZtRmt3YklUSUJZYzZBazFKNmRRR2NJaDdPMjZUT3BHSFRZS1VqbkhQRlRwZ3pFa0ZDZmE1V0ZwWE03S0FHOElRbGpGa3U5QmdnSThISXRQZER4RXJ5YkxrOW9YZzI1TW9BVHA1MThFRmk5aGFkMkVlR3hqTjgyRmdVclRXRFk4NlVVL0ZCWHFYY1YwV3Qxb2hhbmdRWlJqbnNjeFZRUW1nTkhDWHVUa0VYZUk2T1ZLdjRjK0RQN0dnNmRkb1pKeUZrMHdFbEFCUkRIb1o1eTlUL0x0VEpnUU15RnU5V1k3Sk5SUmg4YXlvSnpKWUdiVUwiLCJtYWMiOiIxNmMzNTYyNTI4YmU4YWZiOGJiYWMxNDBhODcyZWY4NTc2ZmQ5MWFkOGRkZDU5ODg1MDI0MTcxN2ZjYWYwMzdmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:34:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkpTcEFIRHBlZW5ManZGU1VkUFR6Nmc9PSIsInZhbHVlIjoiV0lrd1RMY2JKYStSS01wdU8wR1dBSjg4UWRTakFzanppdldpb2FtbytGNE40NkZhRnlWSXlKSGJydWVuaCt6WUoyNG5uMXVmZWkzNXk4cW1temRCdTFvQXB4TDJoL1pYcGtJNUkyN0pKekhNQXRKTXZaU1JnY2tqT09qd29YdElaMlk0aWo2aWdZRkhrbDZzd0NwcUh3Z3dTWFF2M1A1KzhBSjFYcFJRaEpoZi9RVmlTbkoyQXRuRjRUT1FGVFJMYmxJaWc3WkdSTjdLYjlIWjRIVytkT2RxbkdISk9YcStLYzMxdzlyV1FqVWtyQm1ieWtrQzFpWUxBT0MwcTBuaE1lVkdyYW9SdDJXK1dLQkRZakNrOFAvcE1sc2dxZFY0VVgzWE85UHkyb0FQbUZ5OThxWGNJVi83VmlXbXYzT0RDOGs4S0lhL1FHYzhpRVhUcGtuWEN5eEJjY0xGS3NxTmNxcVNvRHlCM09IbFJhTXdwbG4vZDhLb0ZQQkhHOFlwNVJTYWRQRjVHNm5oZWJHM21RZkV0eHVEMnp3U24wZkJmVm5HWEFNbkxpdFZlLzV4RFArQWFlZ0NDajllWklkdGFmaTVFYW5Gcnh6ZWlnRnAyQ2lsdzREeUxJVEJJbXhRTjQrYU1qUUd5MXc0UFJab1p6cVJMMURUNHFjK2RQcWYiLCJtYWMiOiI0ODYwMjVjNDQ5YmE4MTEyYWI1NmY4ZjNlNDM4NTg2OWJiMDQxYjNkZTA3YTgzM2ZkM2FhNWMxMjQ2ZmE0M2QwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:34:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1214288155\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1828068431 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1828068431\", {\"maxDepth\":0})</script>\n"}}