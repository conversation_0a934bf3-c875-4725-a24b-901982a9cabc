{"__meta": {"id": "Xfe61c7d717a46c726e55877fd83971f7", "datetime": "2025-06-08 15:44:10", "utime": **********.452832, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397449.776259, "end": **********.452856, "duration": 0.6765971183776855, "duration_str": "677ms", "measures": [{"label": "Booting", "start": 1749397449.776259, "relative_start": 0, "end": **********.369163, "relative_end": **********.369163, "duration": 0.5929040908813477, "duration_str": "593ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.369177, "relative_start": 0.5929181575775146, "end": **********.452859, "relative_end": 2.86102294921875e-06, "duration": 0.08368182182312012, "duration_str": "83.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45373936, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00745, "accumulated_duration_str": "7.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.419542, "duration": 0.00641, "duration_str": "6.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.04}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.440232, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.04, "width_percent": 13.96}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-115447049 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-115447049\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1569001791 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1569001791\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1098937898 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1098937898\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1286362444 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396510751%7C76%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlrSkVCc2NkS0YwTzNIaHovZjBtbXc9PSIsInZhbHVlIjoiWVNFVlh3Vzc5Wmxtck1XVFdCY1RFaitFMVpOMWV2MmRIVDBJbXFWQ3NKcGcyd0tkSnE3UTVWZFVnWEdZdEU4cVY0UjMrNWlkbVlmZWMzQytUTnBCbHd0K1g4djVtSGRPVUdKK09rd2c3M0hJeFZNaFBUV3ZxUnZjSzRJYUttN0JxZnU1Q0tzL3BSTEdaWUsyNFJubFJNYU9ObTRJTzZoZ2VUbmphZEJYa1ZXRUkzMXo2aEcyYUJkQ0QyTmNGSHBKOXpIMThlL0xIaXhIWGFtK01PditlM3RaNm1LVDh6OEp4elpoQUJNRjB3WWM0Vi9FbTlPcGVnMFk5cWN6bGNTdy8rMGRieWs1ckVLYVpEWS9zYUIzcFdVR25uUHVjbFo0SGVhV0syMUhpb3JTVjdsMmFBR3luVEo1R2d3aS9YUnVHU1JNWi9SN2tTOERiaXdUUDFRdDgya095U1BzSXF0SEpSS2ZSR0VjZk9YSk9BVXJ3T1hjWUxGNENmRE0zN25kUHJXTDQ0R2R4NFJnZnZlR1pWSjJ0UHJPWkgrSk45MGcrNjZxMFNqaXhTaFBHR2JjQkZrZkxTeVhkUWxnT2RkdkltL2FBZ2Radm1hT01sOEtEc1JIZVdkQUNxR0xQQlpyME1aeE5DVWpNcFZnenowUUR3VmpKZkNWb2kwNFVVRUkiLCJtYWMiOiI1YmRiMjIxN2Q1MDUyYzM3NWU1ODg5NDhmYTg0Y2UxZTU0YWNmYTIwNGQ3NWQ5M2MxNDc2NzEwZTBlOWIwZjVkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkxKQk1EMUI5ZjUzd0xxMElZQjZtdmc9PSIsInZhbHVlIjoidkNZYjlPdnFvZnExUXRheVY5ZWI4dE9uWHpPRGt2eWloWGxqL2JnT0xDQkRtWUk0TjhwZUtEWFdSN2lQVHV6WEZ5bWRKb3U5ZzFNOS94N1lQdXFtYjRvK3E4QjhBMmFOWFZ6M2hvc3VEVk9DUmVwaytjcEF5cEFmVFFWU0FsbzFGNTZWbm1xSm4zLysxam4xS1JPVWFOTm9NTXpZSUVaUUlUT2FIYW9reGVvQW9rTTQvRHdjcG81WU0rY2tOQSsvQUh3a2labDgwU09naytCM3JWS2tjNTVNOVpCWHJYZ1lYL3BxYXQxOEhiL2h5Q0J1eGhlT1VmYktnZnRPRUlBZ1dnWFdDVkxrTlhiY3YyQ25uMFE1dmhFRDRMdE9PYStiV2lURGlsbWlPS3M3ZnordDdzdzY5R2F2NS9UMkd0QVJ2T3VnYWZmSVc0NU1nVENCS0ROMTlkc0p0Wm5sVnczWC9GSU9IcmJENTMrc2xzZjM2U284Qmh0a0hZY3FRenZ0SUFIM3g2YnRCNC95cjdvUmpjL3VodEtIaS85NWZqU0Mra0d1Z0NFQXZmNGxRVXBJejYvNGlZUHRWYmhJTkZJWlNHVlFIaE9yVWJzZEVXZWw5SjdYbEtLSDhieWRlNGxxN0gvdlg5QXVURW1QZlQrbEZxcGRORlRPUDhtMHlQRjgiLCJtYWMiOiIwNDNmYmZkNWRkMzYxNDViYjVmZTAyNmY0MjU0MjEyMzRmODdhNTE5ZTQ1NDViM2M2M2JlM2U3OTdmY2YzZmU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286362444\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-895556000 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-895556000\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1516240638 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:44:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii95bVRSMXZXdVVGTmdQUVcxaGU2ZFE9PSIsInZhbHVlIjoiVW5BK1pBeDNsWE9QelhVSjhlWHd6NVNQL3BldVFtaVVIRm1EbkF5U0RaMlBzSkttdjlaYXNwenVhb2ZDVGZ5dThvM1pLM0tiM2V4WUlhTjRDYkxhU1JjeVRleU9Ec3Y1UUVWWGd4VDkyMFBpNW5QQjRqRnk5OWF2enhlYWVZbllqWUJtMTMvT1d6S1hVaGNRSWFsUUJ6U1lXbXhZMHFlTktuNy9MRWVpMElWY0dtYmhGSXJHK1NBaVhoWVc5Ymd5REFzeFVWR1hVNVZ6NmJyZmxOeXhFQlNPSnBpdTlTN2xlNkl0WjY0K3cxTTJXRTc1eGlwRjhkcDMvVmNvN2VWTW50b1k0bUd3T0E1enB4dktVWFZNRlhiV1R1RGpLY1lOcHVMZFlvMDNCcndmWmxkNy9FUzBwQmdPZFFNaDdZNnJzViszUmE0NWRXV09kSU9hYTVONzFZV2VTcGtXdTJFcE5DTWZCT1ZBR0NtQkhrNlliczdoSUZlYWZGRTdUd3BzZTVkYlc0dE9LbExleUVzWHh2emFMSUxMcThsZmxzMGxiemdjODdBUUU2K3lKRE9TVXg4bzA4bGs5QjN2WXFPOHlJZDhBNjM3bXFiK0ZWNlpNQ0Y2TnA0bXUyQ2JuSDE1V3hOUWgwcGZsc3ZVYWR3TVFiTFl4ZDdkSFFLU0ZtU3MiLCJtYWMiOiJmZTFjMmJkYzUyYTQ5YTg3OTFkOGI5YzllN2FiNTJmMmMxYzM2OTExZDNiMDFmZTA3NzBlZDBlZmZkNDU5NzA5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:44:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZsY1RMTlhNN0xaMnlFRHY5bE8vSHc9PSIsInZhbHVlIjoiVUxrUUxiU2FoaHpQNzVxdlVhcktjUGo2SmhrdVdTOXpKeE0rUHN1dHMyNDhvYzVGdkhSOENWSWRtYVJqNE4vU2krRHN3MTZZMnRFaG9sWkZkSEhYUmt1VGlhTWVvd0Q2aWhyMDVXU2JlNFp6bTY5S3NKUk0wVFN6ZDUrRjdnaWg5VnAzL1dsRlBkVmphNGxsOEpFTzVMSThHeVZEMklXREFweC9pTllFZnE2MkVMa2g1bVA0UUViTHFVNXJpbVVrZWhaeWY2SVdNWERpWVl6TmdxbzZESWErRVFGa2VoUjk5VGNGMUE0RENQK0ZYdGVsRThHdyt3QWp1dncyUytNVEhoTzdvQkp6MXFKdzlMOEh1STMySFFHOG1tS2pJRnlXd01IQmd0dmVGQlFiVlFIU3d5eDZVdDVvR0NxREFjZ3lYMnMxM08ycUJlU2dJcXU4YjF4NEh6Q1BLcjcvUVlKbXJvanpKYUhVZFd0QmRFczNWYkkxWG1LaEpyK29WNkVCbmRRSHhlYXErYlpyM1VPSHo2d0U5OW45M01BcHJCVWJaK1h6UzROZUU4Zng2WHZnUVRncXVpR25xenVvQkJMYTNMdzJuK1BhdW1KVEhVQjBaeEZHaUtRVXpoM1p3NVltNkpDU25ySmN2dGRsaXFBZktwNGJRQ2JZVHcrS2NGTUYiLCJtYWMiOiI2NzllMjgwM2QwMDhmZTNmZDkyZGMzNWI3OTUwN2E5ODVmYjIwODA3MjUxYjM1MjNjZWUxMzg3ZWZmMzQ3MTE5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:44:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii95bVRSMXZXdVVGTmdQUVcxaGU2ZFE9PSIsInZhbHVlIjoiVW5BK1pBeDNsWE9QelhVSjhlWHd6NVNQL3BldVFtaVVIRm1EbkF5U0RaMlBzSkttdjlaYXNwenVhb2ZDVGZ5dThvM1pLM0tiM2V4WUlhTjRDYkxhU1JjeVRleU9Ec3Y1UUVWWGd4VDkyMFBpNW5QQjRqRnk5OWF2enhlYWVZbllqWUJtMTMvT1d6S1hVaGNRSWFsUUJ6U1lXbXhZMHFlTktuNy9MRWVpMElWY0dtYmhGSXJHK1NBaVhoWVc5Ymd5REFzeFVWR1hVNVZ6NmJyZmxOeXhFQlNPSnBpdTlTN2xlNkl0WjY0K3cxTTJXRTc1eGlwRjhkcDMvVmNvN2VWTW50b1k0bUd3T0E1enB4dktVWFZNRlhiV1R1RGpLY1lOcHVMZFlvMDNCcndmWmxkNy9FUzBwQmdPZFFNaDdZNnJzViszUmE0NWRXV09kSU9hYTVONzFZV2VTcGtXdTJFcE5DTWZCT1ZBR0NtQkhrNlliczdoSUZlYWZGRTdUd3BzZTVkYlc0dE9LbExleUVzWHh2emFMSUxMcThsZmxzMGxiemdjODdBUUU2K3lKRE9TVXg4bzA4bGs5QjN2WXFPOHlJZDhBNjM3bXFiK0ZWNlpNQ0Y2TnA0bXUyQ2JuSDE1V3hOUWgwcGZsc3ZVYWR3TVFiTFl4ZDdkSFFLU0ZtU3MiLCJtYWMiOiJmZTFjMmJkYzUyYTQ5YTg3OTFkOGI5YzllN2FiNTJmMmMxYzM2OTExZDNiMDFmZTA3NzBlZDBlZmZkNDU5NzA5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:44:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZsY1RMTlhNN0xaMnlFRHY5bE8vSHc9PSIsInZhbHVlIjoiVUxrUUxiU2FoaHpQNzVxdlVhcktjUGo2SmhrdVdTOXpKeE0rUHN1dHMyNDhvYzVGdkhSOENWSWRtYVJqNE4vU2krRHN3MTZZMnRFaG9sWkZkSEhYUmt1VGlhTWVvd0Q2aWhyMDVXU2JlNFp6bTY5S3NKUk0wVFN6ZDUrRjdnaWg5VnAzL1dsRlBkVmphNGxsOEpFTzVMSThHeVZEMklXREFweC9pTllFZnE2MkVMa2g1bVA0UUViTHFVNXJpbVVrZWhaeWY2SVdNWERpWVl6TmdxbzZESWErRVFGa2VoUjk5VGNGMUE0RENQK0ZYdGVsRThHdyt3QWp1dncyUytNVEhoTzdvQkp6MXFKdzlMOEh1STMySFFHOG1tS2pJRnlXd01IQmd0dmVGQlFiVlFIU3d5eDZVdDVvR0NxREFjZ3lYMnMxM08ycUJlU2dJcXU4YjF4NEh6Q1BLcjcvUVlKbXJvanpKYUhVZFd0QmRFczNWYkkxWG1LaEpyK29WNkVCbmRRSHhlYXErYlpyM1VPSHo2d0U5OW45M01BcHJCVWJaK1h6UzROZUU4Zng2WHZnUVRncXVpR25xenVvQkJMYTNMdzJuK1BhdW1KVEhVQjBaeEZHaUtRVXpoM1p3NVltNkpDU25ySmN2dGRsaXFBZktwNGJRQ2JZVHcrS2NGTUYiLCJtYWMiOiI2NzllMjgwM2QwMDhmZTNmZDkyZGMzNWI3OTUwN2E5ODVmYjIwODA3MjUxYjM1MjNjZWUxMzg3ZWZmMzQ3MTE5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:44:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1516240638\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1728180011 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1728180011\", {\"maxDepth\":0})</script>\n"}}