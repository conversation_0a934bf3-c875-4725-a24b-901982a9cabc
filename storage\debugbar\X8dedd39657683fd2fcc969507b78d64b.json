{"__meta": {"id": "X8dedd39657683fd2fcc969507b78d64b", "datetime": "2025-06-30 15:34:05", "utime": **********.898861, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.484193, "end": **********.898874, "duration": 0.41468095779418945, "duration_str": "415ms", "measures": [{"label": "Booting", "start": **********.484193, "relative_start": 0, "end": **********.808747, "relative_end": **********.808747, "duration": 0.3245539665222168, "duration_str": "325ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.808756, "relative_start": 0.32456302642822266, "end": **********.898876, "relative_end": 1.9073486328125e-06, "duration": 0.09011983871459961, "duration_str": "90.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48095536, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02389, "accumulated_duration_str": "23.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8396091, "duration": 0.018940000000000002, "duration_str": "18.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.28}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.867573, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 79.28, "width_percent": 1.884}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.880924, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 81.164, "width_percent": 1.967}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.88257, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.131, "width_percent": 1.507}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.886675, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 84.638, "width_percent": 9.837}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.891087, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 94.475, "width_percent": 5.525}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1503403833 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1503403833\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.885782, "xdebug_link": null}]}, "session": {"_token": "VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1075779901 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1075779901\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1441777510 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1441777510\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1995405286 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1995405286\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-130156479 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">_clck=i8cowp%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=hcmbe9%7C1751295439141%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxBM0h6TXd5RGthL0I2ZVhQcytBYkE9PSIsInZhbHVlIjoiQzdST0RVSy85Y0U3dXhFdGtRSnhCejRpalhiaVpMZkJ2aENvUEdXZU4yaFFkZlVLekU0ZXpxci9XY2Njb1NxQk85S0g4UFo1SFBHVGp6RjRJUkIyNlF5djhkditTd0RGOEpCd2hoelJzbVF2R1BNeTdRa0NZQkthc0lHa29EamRpYklZTXdvQlFlTWZzM091bUtpckZJMHV3QWFXazFpSk9lWWRObk1HeEg1N2djMC9VYmM2aDhjR09FQnZCU2J6cVdNV0dJKzM1R3VNV0dFSkVKdSt4MUJaV1BIcDBKaExKRG1UQjBocFNuY0ZTWmU3enVpYnBRaXcwWXQvL3pYV2g0WExiZ1EvdkdlN0ErT3BVNGlxUmxESVJ6Q0FZb3YzN08wR3pRbVg2MXBEOXcyM0RPM21nR1JKS1cxY29SdFg1VGFPdFFrNFlzZk9rYVFPNVV4cms5UmVyRWJCcFpkbHpHSlV2SXFrUGxCa216cjM4ZlpIMmlVQU56aDZMaEcxZm5PZld5djdpbk42dnUwcldhMXZHenN0aTROYVNEZnVmSTczV3lINE4vS3JsVzFFMzljcStOeFVnQmNUZm14bnhhT0pxa2Y5MnZPbVBEM2NqNkNRNTY2Z3NYODdMeXNOTGNYd1JLRVBHaXZIRTF5LytnaXpqcHgxdXJKdzFVREQiLCJtYWMiOiIwNDg5YzUzY2NkMGU4ODZkOGEzNmE4MTY1NmMyYjk5M2FiNjE2MWYxZGE4ODViZjZmMDEyNmE5M2M1NGQ5MGM1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InpSeTdVWjVmODJPNXRSdGxVMWUrZWc9PSIsInZhbHVlIjoiRVZVZ0xPb2NJakxDQllQSUZpVHR4S29GZWw2Ty9BVHNSaU00c2VvTmJxZExBY0JHK3ZGZFBZUEc4SWlZRzVCQzRvTEZsS0NhMVRTTTB3UW9oT0luWFBQYWxxSG13RTNkRWdqdkkwUGRoOUJEY0pqKzAvYzd4SnEzUmdLeDNSb1dUMjI2M0xtaG4wOEtRd3Y0enZBd2NYMnp5UWNZR3UxajVZejFXMER0QVkrR0VNOVkzbWxEc3lyN1ZVelBKQUJVbCt1UnIrRWJYM0tEN3JDMEdDd1pqYkFaN0pGd0lBdHhtWjVEMG0yOEhERnBXZHdYaTVLUEYxdFI4cDNQaDFTV0ZORndBWjY2eGZMWFRDWGtWNWJvdXdxUE52ZFo2ZUExN2lURWFlcVdKNWxnUS9zaHlNYjlaYU9NeHd1SmFMMEVlZHhZOHVSOEpkY0ZXUDhWMmFEZko5cVJoNThUOEFjcWpJSkdpYmFkeElIMldWU0Y0ak5LTkFiL2w4TEZyVi9keHhJNEhUVEJsQmNiRzByNUd0Q0RxV09ObkQ2RjBDMEtJUzVEc3Q3WW1Dc2NUcXQxWTNPVnlKcWJXV2MrTGxDeVlic0xJWW1XWVpCTURFWWZ4NDk2MENuajJra3FnRWVFL1RMUWN4WUM2YWlZNUd0M09zL0Foa3ZUdDJHakVFTmMiLCJtYWMiOiI0YWQwZGY2MDYyYjg3MGY3NmY0ZmRiMzJlMDIxZWRiMDlmODdjOWI1MTYyYjQ0MjA1MmQyNjE5NTkyNjU0NThiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-130156479\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1383944001 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxJpMPBQz0uKMlPoWn8dhWwRvYpWPoQ7l8KPPP14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383944001\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1822512294 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 15:34:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlUrWDhINzNPb01tNTNsVUF4Y3JsQmc9PSIsInZhbHVlIjoid1pqYXFJOXhvZ0FIWE4xM0ltOVJvQ1NSSVBUNmRpTkZvMHNkaCtxeVAwbk0wa2lPOEdoaUIyc0tOeWZkZDRSdURubStCS00yWUFUTkJUbzRFYjh0UFdkL0dXTngvVmJCYkJXelo5V1lERjNhTW8rZkpCaStoQ0RDTnFUYjFFSlFxY3NTZmRiR0dvV2RWTDFrWEVCY1A3Zm40d1pkdDhLRUNqcmJLRjRINjRZemxqaEhYWUlpa1FXZGM4dDcwY3FKaGJGMWFNdm1ZZGRnOEw0cktyMEdvazRKczc2YUdoblAwbjhMRVR1UFR4QUlwZ2dKVHROcklhRWUyc3FXd1JPWnFYNDlEdlptRWlDaWtidTNHSXhWUitWRDFSZ04zbFYrZGpnTEdPWnFBdU5pUVQvSVo4ODFvMm96RkFicWFKemxxVWxxZEpGamhWdE9ObU9xSjdNT29sWVZYZGRtR3Z0Rm9HYnRwOEFVNzVTUlFUMkN5TDJpN1JFcVlnMnRsV1p0MVRJVnJNQXAvbkp3WlZpQUp5NFJjekRkZGZ4b3lCMlNCMjV1a0xBN2NBUm1ZQTZtYUpvS3FtT2RUQStkQkRoWUt5TVpOQVkrQWRYY2tEd1M2SDFCdlhlVktnSTJkUmJKYmhaOWk0U3lHTkVaRmt1NlJSV3VzUFVpVzI4Y1RQdFMiLCJtYWMiOiI1Y2ZlZjhiNTYxMWVkMjVlNzE1NTE0MzhiYzQzODI5MGU1NzEzYjA2M2YyMGYzMzEzN2VhMWRiYmNiZmM1YWY0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImFhSzZjT1lFaTRtcms0MytGbXQzU1E9PSIsInZhbHVlIjoiZmlocm96WHJCWmpveFVNSmsxZXdkMFYyeUVwWFprZ0UzSVowbk94RExhTEduYzRJdlFzS3V6djQ2L204VTBlSE95UXF0ZlVMYzNZdmFkL3VCQU1FRGdOMWpZQVIxa1lBaks3Q3J1ZE82NHRPVW1GdU04dm5ZWGVuVVkxbVFVY21EYll6QkZCallKNXp6T0gxb0NTYllKZ1k3U0szRitkQlgwem5CdWdaYkc0MDh3SnZtVlpVSUErclBvK3F1aFlPbDRraElMc1BjSXAwek1UaTRNRlZuakZYbFdhUVY3NUo0MHk0ck5UTGp4UFk3U1kvUEU3dmd0TER3M242cWJyTGRhK3RLakpocmcwWlVETDlXMk9XSUxiOVppemVFS3FJMExqcXpWaE5lSjY5QnN2TDJUemNNTVJOM0V0aUF1ekxFekVPNTZkVjBqN0RXRVliaEJLQVJ6YVVsbTRlb0FMNnN4SjlQKzVKVTRPOGFmZXNQQkxzcGQyNDEzRmxYZ2puSmVtMmN0cmpMbms4VHg1cXJxMDhISnIxWkJaRThnTnFMRzlmMXB6eSs0SXhWeXJKTWdqV3NQK0V0TjNlZS91UmlPdEN0cEw3V0VoSHpTanFVY2dkdVpTWVdELzhiUmcvMlhSMUJNcEEyQ0hpUmpkREl0RWVoS2RJcFd3TEFVd20iLCJtYWMiOiI1ODIzODFmM2U3M2Y2OGZjZDU4MzkyOTljMTAzNGJhNmQ1ZjEzZDkzYmFhNzJkMmViZGM5ODg2ZDU0MGQ1N2NkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 17:34:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlUrWDhINzNPb01tNTNsVUF4Y3JsQmc9PSIsInZhbHVlIjoid1pqYXFJOXhvZ0FIWE4xM0ltOVJvQ1NSSVBUNmRpTkZvMHNkaCtxeVAwbk0wa2lPOEdoaUIyc0tOeWZkZDRSdURubStCS00yWUFUTkJUbzRFYjh0UFdkL0dXTngvVmJCYkJXelo5V1lERjNhTW8rZkpCaStoQ0RDTnFUYjFFSlFxY3NTZmRiR0dvV2RWTDFrWEVCY1A3Zm40d1pkdDhLRUNqcmJLRjRINjRZemxqaEhYWUlpa1FXZGM4dDcwY3FKaGJGMWFNdm1ZZGRnOEw0cktyMEdvazRKczc2YUdoblAwbjhMRVR1UFR4QUlwZ2dKVHROcklhRWUyc3FXd1JPWnFYNDlEdlptRWlDaWtidTNHSXhWUitWRDFSZ04zbFYrZGpnTEdPWnFBdU5pUVQvSVo4ODFvMm96RkFicWFKemxxVWxxZEpGamhWdE9ObU9xSjdNT29sWVZYZGRtR3Z0Rm9HYnRwOEFVNzVTUlFUMkN5TDJpN1JFcVlnMnRsV1p0MVRJVnJNQXAvbkp3WlZpQUp5NFJjekRkZGZ4b3lCMlNCMjV1a0xBN2NBUm1ZQTZtYUpvS3FtT2RUQStkQkRoWUt5TVpOQVkrQWRYY2tEd1M2SDFCdlhlVktnSTJkUmJKYmhaOWk0U3lHTkVaRmt1NlJSV3VzUFVpVzI4Y1RQdFMiLCJtYWMiOiI1Y2ZlZjhiNTYxMWVkMjVlNzE1NTE0MzhiYzQzODI5MGU1NzEzYjA2M2YyMGYzMzEzN2VhMWRiYmNiZmM1YWY0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImFhSzZjT1lFaTRtcms0MytGbXQzU1E9PSIsInZhbHVlIjoiZmlocm96WHJCWmpveFVNSmsxZXdkMFYyeUVwWFprZ0UzSVowbk94RExhTEduYzRJdlFzS3V6djQ2L204VTBlSE95UXF0ZlVMYzNZdmFkL3VCQU1FRGdOMWpZQVIxa1lBaks3Q3J1ZE82NHRPVW1GdU04dm5ZWGVuVVkxbVFVY21EYll6QkZCallKNXp6T0gxb0NTYllKZ1k3U0szRitkQlgwem5CdWdaYkc0MDh3SnZtVlpVSUErclBvK3F1aFlPbDRraElMc1BjSXAwek1UaTRNRlZuakZYbFdhUVY3NUo0MHk0ck5UTGp4UFk3U1kvUEU3dmd0TER3M242cWJyTGRhK3RLakpocmcwWlVETDlXMk9XSUxiOVppemVFS3FJMExqcXpWaE5lSjY5QnN2TDJUemNNTVJOM0V0aUF1ekxFekVPNTZkVjBqN0RXRVliaEJLQVJ6YVVsbTRlb0FMNnN4SjlQKzVKVTRPOGFmZXNQQkxzcGQyNDEzRmxYZ2puSmVtMmN0cmpMbms4VHg1cXJxMDhISnIxWkJaRThnTnFMRzlmMXB6eSs0SXhWeXJKTWdqV3NQK0V0TjNlZS91UmlPdEN0cEw3V0VoSHpTanFVY2dkdVpTWVdELzhiUmcvMlhSMUJNcEEyQ0hpUmpkREl0RWVoS2RJcFd3TEFVd20iLCJtYWMiOiI1ODIzODFmM2U3M2Y2OGZjZDU4MzkyOTljMTAzNGJhNmQ1ZjEzZDkzYmFhNzJkMmViZGM5ODg2ZDU0MGQ1N2NkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 17:34:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1822512294\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1623789539 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXY4oMFrYABsJybEuA7gOwKsRkPcLnRWyk7lWdIH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623789539\", {\"maxDepth\":0})</script>\n"}}