{"__meta": {"id": "X0157f3027151a9cf447f2355b2c571bc", "datetime": "2025-06-30 14:56:53", "utime": **********.52429, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.050209, "end": **********.524305, "duration": 0.47409605979919434, "duration_str": "474ms", "measures": [{"label": "Booting", "start": **********.050209, "relative_start": 0, "end": **********.466678, "relative_end": **********.466678, "duration": 0.41646885871887207, "duration_str": "416ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.466694, "relative_start": 0.416485071182251, "end": **********.524307, "relative_end": 1.9073486328125e-06, "duration": 0.05761289596557617, "duration_str": "57.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043120, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00343, "accumulated_duration_str": "3.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.499043, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.557}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5101788, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.557, "width_percent": 17.784}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.516855, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.341, "width_percent": 18.659}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1361470988 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=2uy8by%7C1751295408578%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZ0QUU0ZVkwc25GN3BwOFR1dDhZcVE9PSIsInZhbHVlIjoiRDRhTk1lQW1RSTJPQUNON1p4YS9pNmp4U3lJUi8wMUthV1dTeVBiZUN0cVYvYy8xK1RKOWQrdWpnVFpHZ0ZmRk51WXdpTnYwU3R4Q205bENySHpmQnJ2T0REaXlPamVvdnYrM2h5SEk0Uk5rRHVLZkd0b1B5M1RnQlhyR21Tazd0VEdTYkpXZytZU2hvK0JhSkRiTERrNWlFN3czcE02bTJDUnB3VlBXWUE4Qm94L2c2UXVSWXhCanZjRmVTOUNVbHJiWGhHaFkwU3V0ZDlLeVAraXRoU005UU04Vm1PRXlvbk9jTmNoSkt2SVU0WmFEZTNpYk9kanhIQWxrZnFzT1RZTnlYZWhPVU9wM0hFdEJaSVc3aEsxUGJ0dStQSGtuRFd1OWRYWjZHWG5nTEpHZHoxODl3Wm92ZXovcnVDTTVOSXUwdjZEZEpwSXAzZFllUHFPTjJHMzBOSERsMXM0VGZyNVRlYmE1MzB4aytiVEI4KzBuZ0xuMHhTSm8waDFRdWF1am1UcmNqNGNGRTAxUytURC9WcTJvemltUVB3U2FGOXp6cmY4c3pRWU5DaHhVTTBVZkhBTm1PY1JFU3JzQ1Rldkk3cTB4SmVLdy9hTFo2SzlUcU51Yzk4ZGVRNXVjdGxrWUNjQi9sd2NKaHhJZFRCejFBUS9zQTZCaHNvY04iLCJtYWMiOiJhZDQ0OGEyNzJhNzM2ZjVhMDQ3ZGQ0Y2FkNDJlNzMwNWJiZTExMmM3ZWUwMGM2MjE3YjczZmViZGRmZGI1NmE1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IitER1VXdHd1dEhablBLdVRoMnl5N1E9PSIsInZhbHVlIjoiV1pYUU1ncEJSNGJncGtkK0NzbHRPYXRramdtT0UwTU9XQWxka0IzMnRhT2o2OWxhaGEzNHJLUUNpMWkwMEE3MnFUV1l4bGxYeFBVWC9lWEgrdVRkS3V5T2s5bDR5SXFjWGtOR2R1QUR4KzBxdDhtQW5qYnp6VXRhNlNXNEx0RlZ3dmJpYjVJU2EyY29ja3VJRlhqTG1mTzNldlQvSDV3dy9WeCtmVUNJOC9IamlvOFp4UTQvaWJOVlpBYTdLTXN2UTczbWwvUjJUalRxY2V2Z2RhRFRETlVoUElGMWdkM3IxK2xXek1nRDdtMVVld2VPQWFxRURDb1VNV283aDdmYkxrRjV4REpDZUR1YVE3ZUc3NkNjekZnM3dkY0VwN0Q0N2RjOVFDdVFZSVQ1Mk9iUnJBVnBmNE1xWWV1ZUtITUd4SHVlNEl0T0dQM1QxamZTY21VUkVUY3J6TzlSaEdsWkowOGY4SDYydUdLYmE0eThxcEgzOXNVV21wYlYwdVh1bFZyMHJvUjNnSFdNODBsa0p0alplbUxUUlhSUmh6ODdqSlAyY2ZvZmppUjZXK3NRNTZRNTFOTlN2SnowaVdEd1J4dlpZR0RFY2xOWlZ6VnNPU3puRDg5VUZ4RWczY2VERCtnRnpjUjVLZU91Qnp2Zk04QnFwcmZmN2RGTGZvc1oiLCJtYWMiOiJhYjk4OGYxZmIwMGVkODIwMTJjM2M2YjI3NTM1MmJmNjc3ZGI0YzkzMGRmOThiYWI1ODIzMTk3ZmUzM2Q0ZGMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1361470988\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1976606706 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h9WVO03qniSiJmg5fuu4tum1lr0j0c1k18JkoyAo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976606706\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1344536352 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 14:56:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlczVUNaNHlGM2JWaHhENmV5MTJJVXc9PSIsInZhbHVlIjoiZWRXREtJK1REK3UxeXNlOHZIS1ZXNG1ZWEIxd0JVMUNNYlRyYUtoSWNob1BQVnNJQlRjdFpjN1QwVWNEaGU3SjBReEI4OGFENk5taGtKZVdOTldzcUdpbWRCZFkrT1lGOUhSdjgySDBzTHFBcXFlaEZucnByMFo3NklqdnBNNUlDSnE1ajUxZTZoZ0pZYzJ0SjBYVnUvOWRqMlgrUXp3UjFnZ2lQVzBDM0dTZjBJMkphdGdkcDN1b3VlNVU1YzVGeWpPSks4SUJwZGJHUFp4eTZyWS9YclRnWC80K0hmM2tOa3AwRjhFNHlhSHpsbUlWYmdDcUpKdjJuOWk5R1U0N0hkaHRXUm91OWhBdHNrKzV5UG9GYS9GU1EzNVUzaUFMT29tVmxxMnRuWm1vSzl1TXkvUENGZHJkeWVtZXJYNlk3NjNPeGluMVpzNFJGdTNQZ3QwZWg4UzM2WTY0Y0VTdWJabzgxRGpEQ2JlcjA1clVYczNlVlY2YlA4NGVGQ3NOUmZvRUNIczl6VC9UbEVhVFFmRlNUMmZvdWwwOXNoMTN6S2VWcHRKMnpubXovSnRPYkUvcXcwK21jZEZQTjFyWjIvbWdFOVNkK0lKcE9QTnNpVWJSZnRidUN2bVE5clFpcm9oc2tUbEJTRTJtd25XT3F4ckl0REJEWVJsbnF0ZHkiLCJtYWMiOiIzNmYzY2Y3MmJlODZkMTU4MWE4OTlmN2JiMDM1NmZiYmQxMDk5MmIwNGU3ZGI0YmM1MTU2ZDM0OTEzYjczNjA2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:56:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IitRSElNdnA2ZUp1WFg2OEZqU29JeFE9PSIsInZhbHVlIjoieTJReW01dDNEK0xrQnMvdFZnbmRpZysycDBYanJZMjRxdVNxVWpwUXdxSUhzL0drK3ZKS3QxY3h5dWZlc29LS1NEd045ZkMzRW9LZHlWRklZQzdHTGhTOEpucDI1MjlVUERSMTdaVm1IU2ZSbTkzL2RuM0NjNEtmZklxTVZHbkR4eUtDbEIwdktyZHE3YThjUHBRSlJSeFB3TU5oMHhkSVpZd1RMUjJFT21UcWE1eHZ2cGluZ3o5R1dEUnpxY1pleUNRR2RVcHVvWjBpc2ZMNlUxd3ZHVTdsZXEwN3VXZHV0R2M0TklPMDMwSzAramZQTjMzYkhreXV4N1YxOXFqT0h1UlVRRnNHbkhJTEtoRlJJU2drRGZZaVh3WkdxREgwV1BQY3JScjF6VVJoU2VYSEFabitVRmVaemR3d2NDN2RWZjVsdFpkeFViR3RIQzE2VENQT3NGSVpwbGdvMGxlbjBsOTNINVlBL21hTllaMVJHOWJxU1NsNWJaR3ZPZktWOWNrd0VBWlcyOWo3dy9xQnQ4OE1TUGJnQVd3ejMzQUtNVzZveTYrcitabHJEVTBOS3NzSFVKKzRuVDAxR1dHMmtyVWJhTzdmNVo3NEttNlNKVnc2N2NwS1lRblFWK0dPYXZtU3ZmekF4Yk1kWnhYa0F5elVvSmJMcysxdDczbkEiLCJtYWMiOiI2MGNmNWVkYzUwZjQwMjYwZWFmMWJmODM4NWI3MzFjMjYwNTJiMzUwMzAxZTFjYjQ0MGYxYzIzMTI1MTk4MzdiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 16:56:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlczVUNaNHlGM2JWaHhENmV5MTJJVXc9PSIsInZhbHVlIjoiZWRXREtJK1REK3UxeXNlOHZIS1ZXNG1ZWEIxd0JVMUNNYlRyYUtoSWNob1BQVnNJQlRjdFpjN1QwVWNEaGU3SjBReEI4OGFENk5taGtKZVdOTldzcUdpbWRCZFkrT1lGOUhSdjgySDBzTHFBcXFlaEZucnByMFo3NklqdnBNNUlDSnE1ajUxZTZoZ0pZYzJ0SjBYVnUvOWRqMlgrUXp3UjFnZ2lQVzBDM0dTZjBJMkphdGdkcDN1b3VlNVU1YzVGeWpPSks4SUJwZGJHUFp4eTZyWS9YclRnWC80K0hmM2tOa3AwRjhFNHlhSHpsbUlWYmdDcUpKdjJuOWk5R1U0N0hkaHRXUm91OWhBdHNrKzV5UG9GYS9GU1EzNVUzaUFMT29tVmxxMnRuWm1vSzl1TXkvUENGZHJkeWVtZXJYNlk3NjNPeGluMVpzNFJGdTNQZ3QwZWg4UzM2WTY0Y0VTdWJabzgxRGpEQ2JlcjA1clVYczNlVlY2YlA4NGVGQ3NOUmZvRUNIczl6VC9UbEVhVFFmRlNUMmZvdWwwOXNoMTN6S2VWcHRKMnpubXovSnRPYkUvcXcwK21jZEZQTjFyWjIvbWdFOVNkK0lKcE9QTnNpVWJSZnRidUN2bVE5clFpcm9oc2tUbEJTRTJtd25XT3F4ckl0REJEWVJsbnF0ZHkiLCJtYWMiOiIzNmYzY2Y3MmJlODZkMTU4MWE4OTlmN2JiMDM1NmZiYmQxMDk5MmIwNGU3ZGI0YmM1MTU2ZDM0OTEzYjczNjA2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:56:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IitRSElNdnA2ZUp1WFg2OEZqU29JeFE9PSIsInZhbHVlIjoieTJReW01dDNEK0xrQnMvdFZnbmRpZysycDBYanJZMjRxdVNxVWpwUXdxSUhzL0drK3ZKS3QxY3h5dWZlc29LS1NEd045ZkMzRW9LZHlWRklZQzdHTGhTOEpucDI1MjlVUERSMTdaVm1IU2ZSbTkzL2RuM0NjNEtmZklxTVZHbkR4eUtDbEIwdktyZHE3YThjUHBRSlJSeFB3TU5oMHhkSVpZd1RMUjJFT21UcWE1eHZ2cGluZ3o5R1dEUnpxY1pleUNRR2RVcHVvWjBpc2ZMNlUxd3ZHVTdsZXEwN3VXZHV0R2M0TklPMDMwSzAramZQTjMzYkhreXV4N1YxOXFqT0h1UlVRRnNHbkhJTEtoRlJJU2drRGZZaVh3WkdxREgwV1BQY3JScjF6VVJoU2VYSEFabitVRmVaemR3d2NDN2RWZjVsdFpkeFViR3RIQzE2VENQT3NGSVpwbGdvMGxlbjBsOTNINVlBL21hTllaMVJHOWJxU1NsNWJaR3ZPZktWOWNrd0VBWlcyOWo3dy9xQnQ4OE1TUGJnQVd3ejMzQUtNVzZveTYrcitabHJEVTBOS3NzSFVKKzRuVDAxR1dHMmtyVWJhTzdmNVo3NEttNlNKVnc2N2NwS1lRblFWK0dPYXZtU3ZmekF4Yk1kWnhYa0F5elVvSmJMcysxdDczbkEiLCJtYWMiOiI2MGNmNWVkYzUwZjQwMjYwZWFmMWJmODM4NWI3MzFjMjYwNTJiMzUwMzAxZTFjYjQ0MGYxYzIzMTI1MTk4MzdiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 16:56:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1344536352\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PiQmmBiJghmJQX6dSHtdxHah3ErUGjCP2CvUSTvF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}