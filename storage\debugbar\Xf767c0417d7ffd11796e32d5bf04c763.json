{"__meta": {"id": "Xf767c0417d7ffd11796e32d5bf04c763", "datetime": "2025-06-07 22:50:13", "utime": **********.975647, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.182275, "end": **********.975675, "duration": 0.7934000492095947, "duration_str": "793ms", "measures": [{"label": "Booting", "start": **********.182275, "relative_start": 0, "end": **********.867572, "relative_end": **********.867572, "duration": 0.6852970123291016, "duration_str": "685ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.867591, "relative_start": 0.6853158473968506, "end": **********.975678, "relative_end": 2.86102294921875e-06, "duration": 0.10808706283569336, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45578752, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02008, "accumulated_duration_str": "20.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.916337, "duration": 0.01797, "duration_str": "17.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.492}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.948988, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.492, "width_percent": 3.934}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.959342, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.426, "width_percent": 6.574}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-33258560 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-33258560\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1001012994 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1001012994\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-15126133 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15126133\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1431819402 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749336607961%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlAwRG0rNW9tRm1TTkwrMkx5NXpNMUE9PSIsInZhbHVlIjoiY2tLeVdVckdHM2Q3V0tZUXh6Mk5CaGNVSWZNMDFDNXNHNzczRTJPQmwwWGlaZ1h1bGJJbGdJMDlnZTh0dlc2cUdYTFh0M21pYldRMEFaa2tjbTQ4L2llNGlNN2ZGRU83QjBkMlkxSHpjVmQwZURpQUpnaElidGxFYUJJbWFPdjVFNlBmYncvWUI4NHdCNmhubjRhMFJvQ2ZUZ3NsS0FIUldUb0Rid0RyVU5VaGJBaCtiVDNkalY0ZTJ5bm1wUDI5Tjc1VWh0Ty81VXR0Z2o5bXdPUDd2bWVMeDZZMkU5NWNVcEt1cmlyNXoyYUV2US9wdFNralRRQ3JZZzNxOUhYblBKaktqclFLc0luNXM0aHluam5Xb3BzM094aG9BU1JMbE9uQ0gvK3I4SVducW5VWklsWThrbWFiTmZaYTF6clRvZVZSWFVES2lHQjFBZmRhNHlNNjJFK0pqWGFMbmZPYVgwYTB6K0s4Z2F5a1RBdXk1eHJFc212Mll2MW43Z3ZCbnNJMkhyQ0VqWVVGeXdTUUdUb0t3Z2hxLys1Mmk4aWJaaVNYb0xBS2l2NFJxcGl6a0tjRENsR0xzVkNKT3k5cGVQdmo4NUJlZS9BMk1aYzlpbys4Z0IzMC81VW5oSHI0WUFzUm40Ri9NckZTa05FeWhBcStxOEU2ejNWeHorSVMiLCJtYWMiOiIwYjMzOTZmMTY2MTg2OWVhMzViN2YzYWU3NmZlYWU5NDUyNjkzNjIwZGU0YmE1YzJjYjBlN2QyM2VkMzNlMDU5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlRuMVBjWDdxSnozVS9INERGVXVxUVE9PSIsInZhbHVlIjoiYUtXZVM0UndIUE1TQUs4ZkU0SDZlU3NBSGJzbzJJRXdFREpTL2tOZDZxNGhCNlNQb3ZSZXpvaTB6enhBT2J4OCt2UlYyR3BQTVNUZTRMMEZQMFp1ZU95TzdNWWRveWVPNmp5aGhCWkpUZ3A0ZTcwUjRsVFFoSkViODVCMHZGaWh2OG0yNStJdWx5cmZYdEhFeWZHL1RrdVphS0Jwc2ExZ2lObU5ZcnVVbDI5RCtDZk1oU0dWTEJCRzFUY3k4dXhldmxGbW12MUcxMC85UkQwUml0QjlCQmJTQzExZDQwN1hjalVHcEU0N0Rja3pxTHdTalhmWkFuUVUvRG92RjNYNW1ZRkY5UEVVMXE4eVA4bVNnU1RzMEFURkxBMDNvSEM1dHhhR2tpUk9pNTJtOG9YSk40c0VPMXJCY1JGM1I2NStXN0ZKcWZlNVV5WkdwdGRTcGp4S3lDT1VsNnpxalp0aks5K252NlpibFhuNldydG93c2E5TFppT0dHK21nNWQ1RjB1K0hValJRMmI3bDJZN1NsRG90czRzT010TTFTaVQ4NVJZcnBZWFFmc3hRakd2Vk1NeDA5N2NlSXh2K0hrTStCZjBpS0VYMnlOSXdvcUNrUlFPL2lxbjNzckJnUHlHSHcxemVxR3R4UmRLTCthdmFrTndKc1RMN0xpZ25HSDEiLCJtYWMiOiI1YzcyNDNjZjYwN2FjYjc2NzFlNzJiMmE1Yjc4YTkzY2QxYWRlYWE5OTk3ZTU0ZmViOTg3ZDg0MDM1ZTU3Zjg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1431819402\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1626517090 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1626517090\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1881151768 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:50:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii95UHZPMEliaVMrYktKRHM2U3RqYVE9PSIsInZhbHVlIjoiWXJ2N1RZeUxZTzk2eXdzMDdHVXJoWTQrSVAvampaQzVPYy93c2xjaHVxdEtnSXlwaDVOVi9sclBlSXQzeWc5NXhvMjh1cS8rL2d2S0VOZDdXVlA2Y2hkcWc3Vm5Yc0VlUFBVT0ZMMndyZW5WVi85S0ZtWElFTDROT0pDSjg3SXBsREpTa3NDdTFLcml4OUZsTTZDLy9adk0vVENmQXpySkJxc3lEcTgxVVdWZjYrZ1VzRHNMZEpqeGFiSkpJYWx0b0FOamZlTXhLMWlYNHNFUzBVbUxQUmlmU0p6d21Da2YwWG83QkVPUVJpYStycGgwVkhXN0Q5aUp1WXBtT1RUMzM4SEJ0QlVsaXBlL0hJYU1LMnJ2bFB5d0hmZTVuNFVGNnBlZGdXVUhlbkNOZS8zNkFTSExDZCtlTzJDMUQ2ckxraithNGdoRnl2V3d4Ykw5b2tnWDFTMUt6b0VGeVoxdE52bUhZUEdEVGhtb3JKbUZmWWRVV2tjejV0V08xQ1I5d3VrZXhIT1loaEt6ZHAwV1EraVpkcE9zcklpaDBoRW1PSTlIeFZvSHJOQ2lqZUF5SEl3L2tZWjVFQkJDQ09hVEtyRTFwRUs3VjNKN29CZnJRQXV2OVFPM1g5bUJnOFVTdnNSVGtkM3pjSDZlRm1YZGNEZTdVK1ZoSndIY2tENFYiLCJtYWMiOiI0NTI0ZWNlNmE4ZDYzZGFjMzhhMzFhMGEzMjJmM2FlZWQ1NDE1YTVlNTc1NWM2OTc3NzQ5ZmI5MDZjOTE5NjM1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:50:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZEV2V5WEVXd256TVN4QS9CVUZTRFE9PSIsInZhbHVlIjoiRFNaRkhDanZVYmtNRm02SVpGRXRZRVYxWElJS3VoM3laaHEyRi9oeHNvOXNla2FyRjFkaUpwREdyYVVNSzlGQkF6SmUvdXRIWmQwamdMNEllVWU5NEtQd0hGc3lkb0N1T3dTUGpNVkdqK09jaUR0NE9PT1Z4TWNWZExZNGdqQkx2ZWp5d2g2eGNTRlozQ2tXWEpxQ0ZDNlBJaHIxdllmTlJPUklOU2hKdUhNWExVMlNoczR2WmxUYUNVa1RTblBML3MvY09yUGExcmJ4dU9abmxiaGFhUGdNelB6S3FQQmNMSE1ubTkvdHhndjV2Z0IyVlQzOU03emUzNWh1anVlZ3ZhZ0FPYzZMR1VQL1lmc0tydjZxSnE4ZlJFSG40T1B1U3JkVnArWmRXWXExbGVwMGRFbUp6QzVaSUVhMnBrWmR4MzJQMTYvaHZNM0VMNmFhQnk4MDFkd1NKbFMvV2hVdDFMdDlRYU1zM1UyUXh2eU54MmNMQW04ZktCbHhDMWJRcnFnYnVqMUdycW1oME9iR2h1cDJWRFl2K2VkdHhkSnBEdjFlaHRyZ0V6eU1QdHVybWN6SE81aHAzdXJrL2syQ3RrOFdKc0h0NktEOVdQQnFsOHBaZVJLVHlENTVuRWF3YlhNOE91TlBCUFJUNU1Kc0RXOWU4M2lPZEtuWVovOVYiLCJtYWMiOiJhMzM3ZGVmOWJjM2JlNmNjYzkyMjU4MDNjNDgyNzU5M2E5ZGMxM2EzODNkODFmNjkyNDE2ZWE4MDJkZjUwODdjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:50:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii95UHZPMEliaVMrYktKRHM2U3RqYVE9PSIsInZhbHVlIjoiWXJ2N1RZeUxZTzk2eXdzMDdHVXJoWTQrSVAvampaQzVPYy93c2xjaHVxdEtnSXlwaDVOVi9sclBlSXQzeWc5NXhvMjh1cS8rL2d2S0VOZDdXVlA2Y2hkcWc3Vm5Yc0VlUFBVT0ZMMndyZW5WVi85S0ZtWElFTDROT0pDSjg3SXBsREpTa3NDdTFLcml4OUZsTTZDLy9adk0vVENmQXpySkJxc3lEcTgxVVdWZjYrZ1VzRHNMZEpqeGFiSkpJYWx0b0FOamZlTXhLMWlYNHNFUzBVbUxQUmlmU0p6d21Da2YwWG83QkVPUVJpYStycGgwVkhXN0Q5aUp1WXBtT1RUMzM4SEJ0QlVsaXBlL0hJYU1LMnJ2bFB5d0hmZTVuNFVGNnBlZGdXVUhlbkNOZS8zNkFTSExDZCtlTzJDMUQ2ckxraithNGdoRnl2V3d4Ykw5b2tnWDFTMUt6b0VGeVoxdE52bUhZUEdEVGhtb3JKbUZmWWRVV2tjejV0V08xQ1I5d3VrZXhIT1loaEt6ZHAwV1EraVpkcE9zcklpaDBoRW1PSTlIeFZvSHJOQ2lqZUF5SEl3L2tZWjVFQkJDQ09hVEtyRTFwRUs3VjNKN29CZnJRQXV2OVFPM1g5bUJnOFVTdnNSVGtkM3pjSDZlRm1YZGNEZTdVK1ZoSndIY2tENFYiLCJtYWMiOiI0NTI0ZWNlNmE4ZDYzZGFjMzhhMzFhMGEzMjJmM2FlZWQ1NDE1YTVlNTc1NWM2OTc3NzQ5ZmI5MDZjOTE5NjM1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:50:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZEV2V5WEVXd256TVN4QS9CVUZTRFE9PSIsInZhbHVlIjoiRFNaRkhDanZVYmtNRm02SVpGRXRZRVYxWElJS3VoM3laaHEyRi9oeHNvOXNla2FyRjFkaUpwREdyYVVNSzlGQkF6SmUvdXRIWmQwamdMNEllVWU5NEtQd0hGc3lkb0N1T3dTUGpNVkdqK09jaUR0NE9PT1Z4TWNWZExZNGdqQkx2ZWp5d2g2eGNTRlozQ2tXWEpxQ0ZDNlBJaHIxdllmTlJPUklOU2hKdUhNWExVMlNoczR2WmxUYUNVa1RTblBML3MvY09yUGExcmJ4dU9abmxiaGFhUGdNelB6S3FQQmNMSE1ubTkvdHhndjV2Z0IyVlQzOU03emUzNWh1anVlZ3ZhZ0FPYzZMR1VQL1lmc0tydjZxSnE4ZlJFSG40T1B1U3JkVnArWmRXWXExbGVwMGRFbUp6QzVaSUVhMnBrWmR4MzJQMTYvaHZNM0VMNmFhQnk4MDFkd1NKbFMvV2hVdDFMdDlRYU1zM1UyUXh2eU54MmNMQW04ZktCbHhDMWJRcnFnYnVqMUdycW1oME9iR2h1cDJWRFl2K2VkdHhkSnBEdjFlaHRyZ0V6eU1QdHVybWN6SE81aHAzdXJrL2syQ3RrOFdKc0h0NktEOVdQQnFsOHBaZVJLVHlENTVuRWF3YlhNOE91TlBCUFJUNU1Kc0RXOWU4M2lPZEtuWVovOVYiLCJtYWMiOiJhMzM3ZGVmOWJjM2JlNmNjYzkyMjU4MDNjNDgyNzU5M2E5ZGMxM2EzODNkODFmNjkyNDE2ZWE4MDJkZjUwODdjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:50:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1881151768\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-134605658 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-134605658\", {\"maxDepth\":0})</script>\n"}}