{"__meta": {"id": "Xa97d38633083f7f1101bb044b4bd4643", "datetime": "2025-06-08 00:05:37", "utime": **********.441836, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749341136.905379, "end": **********.441856, "duration": 0.5364768505096436, "duration_str": "536ms", "measures": [{"label": "Booting", "start": 1749341136.905379, "relative_start": 0, "end": **********.3847, "relative_end": **********.3847, "duration": 0.4793210029602051, "duration_str": "479ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.384711, "relative_start": 0.47933197021484375, "end": **********.441858, "relative_end": 2.1457672119140625e-06, "duration": 0.05714702606201172, "duration_str": "57.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43398680, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01192, "accumulated_duration_str": "11.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.420383, "duration": 0.01192, "duration_str": "11.92ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LcLdUuv9ncxCQAwxbWudm7IigIdCqOvxHs1ba2vd", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2070824982 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2070824982\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1119235601 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1119235601\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1552094700 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552094700\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-610046378 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-610046378\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:05:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVhMVBxaVNoV0tYeXlQYWVDV0dkTHc9PSIsInZhbHVlIjoiTldrL2tIMitqd21JMkk4UEVaVFJIVnlKMFhIUVVXcGkxM2hLZ1ZKZ1BDWm5TNEFlS2l5dmdSR3A4ZlFBRG44SXB6KzlUVXRTaTk5ZnhGNVIyYW96amhsczlCeG5XK2VOYm9Yam4xV0c2d1M1RUNLaWp0VVlUUXYrdG1GQkZGd0tvSThCNmp0bWJGNjVJR1JxTEZ6K0VtNDhycFZ3SlBNRklGRkZYUk5BTGZ3WFFCYy9IRXl0WFp3Qm0zRkMyUTdLYVNaWURGMFNSWTU4NXVrdy9NdzB4MUVHTFRLWlVtaHJSSXNqejJuNnIyanp3VmZvMHF4QzlLWEFrYXdlRlpHQ3lIQWJ1MzZibG9hcnA3dnI4dFYyMzVOZ2V6cnBzZk8xTnFoc3lpa3hpWlYwcFREZ0ppZXNrK0tVK0JwRXZDMVo4MDVqR1FSYWZKd3RPbExYcWlpRHZwc3k1d3gxdzB0Q0VHMGkwTmwxN0MrSk5HM2dJR3J4OTRTeHpDRFJ0YVFaVnhiZ0Y0dTA1M0VkeTBpcFJ5bmhqNlVsTWFsYWdNaGkzcEpRRE9MMUh5eFhIemZWV3k3ZThxMFY2bitsUE5uZzd1RkVFK0dySkQrMVczSy9pdzR4QjVzZ0U3Q05ONGh0QTFJSkZoc1k4QzhaT3RCdmNoOUR6MmNYYnNHajVNRnQiLCJtYWMiOiJjMjYzMTBmOWVhZWZkMzk2MzZlNGNlNGQxYzM1MWNiYmQ3MmVmMzQ3YmM4MGYwOGVmODllMjE4NmM5YzIyOWRjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:05:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdxbHFuN2VBVTlXYWgzcGx3OTBQSHc9PSIsInZhbHVlIjoiSmxLbWZ1bUQ1SEZHaHRLeGRnN2lSUlF3ZStiSUE1V1RDZFdab3hqdFNEVjdEY3drSnBDSCtBMjhKOHNvVyt3S0dqaG11TFNsSGxKUEkrQnRxM2ZRWEdFQkdxb2huNS9tdGhKSm5zVUVsRlhha1JPdDVod3hvdEsyNzlYZk9IRmNzdHpmd2JGdjdUbXV1NlZSaFJLSDRCQ2YvUVBUZTZyV1VoQWdHN1MxRnZjUjllbFlyRnZXSmpZZFY4ZHRyUmRXQlYvenRzaTkrOWlmMHNFRG42RXA2QlB3dkRJenQ5MjBJdmxjRDhmLy9zUStBZ0orY2h3YVdUOVZRVEx5TCsrcWpheUVMSmhZYUZlRHUyYjNRS1VtREpxWXY5ekEzYXV1UlRsMGVrWEF2Z1p5Snl5cDd4eGJMckk3ekVrTlNqWnM2WVRHMGJrQlU2Rlo0NFBSYXRHNFpsWFN2ajdvQ3MwbkIwUFFtL3AvMDNVaUNqc3hsbkNIMDgrOSs1ZlF1dnluQllzNmorVlZmbVhiOThDNTZIVlQwQ2Z5UjJTS0pjUXhFdE5LbmRycXlmM251NGNxTW5RbklIakpqOTQ2NmxjbUkrOTdkMW1lQ1F2RjdDd3N2ODZGQXhZLzNLaG1YeTl6RWNVTGJRa081TStYaU4wOGV5RUtCOElsKzFqdHVRZkYiLCJtYWMiOiIzYzI2NzE2ODI1NzNiMzRjMzhkNzg1MjAxZTZlMmY1ZTdlNjUwOGZmNjJjYTdlZDJjYTM4NmM2ODI2M2Q0ODUyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:05:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVhMVBxaVNoV0tYeXlQYWVDV0dkTHc9PSIsInZhbHVlIjoiTldrL2tIMitqd21JMkk4UEVaVFJIVnlKMFhIUVVXcGkxM2hLZ1ZKZ1BDWm5TNEFlS2l5dmdSR3A4ZlFBRG44SXB6KzlUVXRTaTk5ZnhGNVIyYW96amhsczlCeG5XK2VOYm9Yam4xV0c2d1M1RUNLaWp0VVlUUXYrdG1GQkZGd0tvSThCNmp0bWJGNjVJR1JxTEZ6K0VtNDhycFZ3SlBNRklGRkZYUk5BTGZ3WFFCYy9IRXl0WFp3Qm0zRkMyUTdLYVNaWURGMFNSWTU4NXVrdy9NdzB4MUVHTFRLWlVtaHJSSXNqejJuNnIyanp3VmZvMHF4QzlLWEFrYXdlRlpHQ3lIQWJ1MzZibG9hcnA3dnI4dFYyMzVOZ2V6cnBzZk8xTnFoc3lpa3hpWlYwcFREZ0ppZXNrK0tVK0JwRXZDMVo4MDVqR1FSYWZKd3RPbExYcWlpRHZwc3k1d3gxdzB0Q0VHMGkwTmwxN0MrSk5HM2dJR3J4OTRTeHpDRFJ0YVFaVnhiZ0Y0dTA1M0VkeTBpcFJ5bmhqNlVsTWFsYWdNaGkzcEpRRE9MMUh5eFhIemZWV3k3ZThxMFY2bitsUE5uZzd1RkVFK0dySkQrMVczSy9pdzR4QjVzZ0U3Q05ONGh0QTFJSkZoc1k4QzhaT3RCdmNoOUR6MmNYYnNHajVNRnQiLCJtYWMiOiJjMjYzMTBmOWVhZWZkMzk2MzZlNGNlNGQxYzM1MWNiYmQ3MmVmMzQ3YmM4MGYwOGVmODllMjE4NmM5YzIyOWRjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:05:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdxbHFuN2VBVTlXYWgzcGx3OTBQSHc9PSIsInZhbHVlIjoiSmxLbWZ1bUQ1SEZHaHRLeGRnN2lSUlF3ZStiSUE1V1RDZFdab3hqdFNEVjdEY3drSnBDSCtBMjhKOHNvVyt3S0dqaG11TFNsSGxKUEkrQnRxM2ZRWEdFQkdxb2huNS9tdGhKSm5zVUVsRlhha1JPdDVod3hvdEsyNzlYZk9IRmNzdHpmd2JGdjdUbXV1NlZSaFJLSDRCQ2YvUVBUZTZyV1VoQWdHN1MxRnZjUjllbFlyRnZXSmpZZFY4ZHRyUmRXQlYvenRzaTkrOWlmMHNFRG42RXA2QlB3dkRJenQ5MjBJdmxjRDhmLy9zUStBZ0orY2h3YVdUOVZRVEx5TCsrcWpheUVMSmhZYUZlRHUyYjNRS1VtREpxWXY5ekEzYXV1UlRsMGVrWEF2Z1p5Snl5cDd4eGJMckk3ekVrTlNqWnM2WVRHMGJrQlU2Rlo0NFBSYXRHNFpsWFN2ajdvQ3MwbkIwUFFtL3AvMDNVaUNqc3hsbkNIMDgrOSs1ZlF1dnluQllzNmorVlZmbVhiOThDNTZIVlQwQ2Z5UjJTS0pjUXhFdE5LbmRycXlmM251NGNxTW5RbklIakpqOTQ2NmxjbUkrOTdkMW1lQ1F2RjdDd3N2ODZGQXhZLzNLaG1YeTl6RWNVTGJRa081TStYaU4wOGV5RUtCOElsKzFqdHVRZkYiLCJtYWMiOiIzYzI2NzE2ODI1NzNiMzRjMzhkNzg1MjAxZTZlMmY1ZTdlNjUwOGZmNjJjYTdlZDJjYTM4NmM2ODI2M2Q0ODUyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:05:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LcLdUuv9ncxCQAwxbWudm7IigIdCqOvxHs1ba2vd</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}