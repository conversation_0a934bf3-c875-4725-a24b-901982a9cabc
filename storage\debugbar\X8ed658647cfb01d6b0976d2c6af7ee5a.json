{"__meta": {"id": "X8ed658647cfb01d6b0976d2c6af7ee5a", "datetime": "2025-06-08 16:25:14", "utime": **********.1367, "method": "POST", "uri": "/pos-delevery-pay", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.479436, "end": **********.136731, "duration": 0.6572949886322021, "duration_str": "657ms", "measures": [{"label": "Booting", "start": **********.479436, "relative_start": 0, "end": **********.946361, "relative_end": **********.946361, "duration": 0.46692514419555664, "duration_str": "467ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.946373, "relative_start": 0.4669370651245117, "end": **********.136735, "relative_end": 4.0531158447265625e-06, "duration": 0.19036197662353516, "duration_str": "190ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52332648, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-delevery-pay", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@finacialdeleveryBill", "namespace": null, "prefix": "", "where": [], "as": "pos.delevery.bill", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=348\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:348-415</a>"}, "queries": {"nb_statements": 16, "nb_failed_statements": 0, "accumulated_duration": 0.040029999999999996, "accumulated_duration_str": "40.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.988905, "duration": 0.01774, "duration_str": "17.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 44.317}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.019302, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 44.317, "width_percent": 1.524}, {"sql": "select count(*) as aggregate from `pos` where `id` = '44'", "type": "query", "params": [], "bindings": ["44"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.033813, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 45.841, "width_percent": 1.799}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 17 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.063092, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 47.639, "width_percent": 3.947}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.06773, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 51.586, "width_percent": 2.523}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 167}, {"index": 17, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0766902, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:167", "source": "app/Services/FinancialRecordService.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=167", "ajax": false, "filename": "FinancialRecordService.php", "line": "167"}, "connection": "ty", "start_percent": 54.109, "width_percent": 2.873}, {"sql": "select * from `financial_records` where `shift_id` = 2 and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 173}, {"index": 17, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.081465, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:173", "source": "app/Services/FinancialRecordService.php:173", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=173", "ajax": false, "filename": "FinancialRecordService.php", "line": "173"}, "connection": "ty", "start_percent": 56.982, "width_percent": 2.248}, {"sql": "select * from `financial_records` where (`id` = 2) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 184}, {"index": 22, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 59}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.085346, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:184", "source": "app/Services/FinancialRecordService.php:184", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=184", "ajax": false, "filename": "FinancialRecordService.php", "line": "184"}, "connection": "ty", "start_percent": 59.231, "width_percent": 1.774}, {"sql": "update `financial_records` set `delivery_cash` = 34, `total_cash` = 1310, `deficit` = 34, `financial_records`.`updated_at` = '2025-06-08 16:25:14' where `id` = 2", "type": "query", "params": [], "bindings": ["34", "1310", "34", "2025-06-08 16:25:14", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 184}, {"index": 21, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 59}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0888588, "duration": 0.0028399999999999996, "duration_str": "2.84ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:184", "source": "app/Services/FinancialRecordService.php:184", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=184", "ajax": false, "filename": "FinancialRecordService.php", "line": "184"}, "connection": "ty", "start_percent": 61.004, "width_percent": 7.095}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 230}, {"index": 17, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 65}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0944839, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:230", "source": "app/Services/FinancialRecordService.php:230", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=230", "ajax": false, "filename": "FinancialRecordService.php", "line": "230"}, "connection": "ty", "start_percent": 68.099, "width_percent": 2.323}, {"sql": "select * from `delivery_financial_records` where `shift_id` = 2 and `created_by` = 17 and `delivery_financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 237}, {"index": 17, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 65}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0988889, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:237", "source": "app/Services/FinancialRecordService.php:237", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=237", "ajax": false, "filename": "FinancialRecordService.php", "line": "237"}, "connection": "ty", "start_percent": 70.422, "width_percent": 3.098}, {"sql": "select * from `delivery_financial_records` where (`id` = 1) and `delivery_financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 249}, {"index": 22, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 65}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.103582, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:249", "source": "app/Services/FinancialRecordService.php:249", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=249", "ajax": false, "filename": "FinancialRecordService.php", "line": "249"}, "connection": "ty", "start_percent": 73.52, "width_percent": 2.298}, {"sql": "update `delivery_financial_records` set `delivery_cash` = 34, `delivery_financial_records`.`updated_at` = '2025-06-08 16:25:14' where `id` = 1", "type": "query", "params": [], "bindings": ["34", "2025-06-08 16:25:14", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 249}, {"index": 21, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 65}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.107333, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:249", "source": "app/Services/FinancialRecordService.php:249", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=249", "ajax": false, "filename": "FinancialRecordService.php", "line": "249"}, "connection": "ty", "start_percent": 75.818, "width_percent": 5.921}, {"sql": "update `pos` set `is_payment_set` = 1, `pos`.`updated_at` = '2025-06-08 16:25:14' where `id` = '44'", "type": "query", "params": [], "bindings": ["1", "2025-06-08 16:25:14", "44"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 73}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1130729, "duration": 0.00283, "duration_str": "2.83ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:73", "source": "app/Services/FinancialRecordService.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=73", "ajax": false, "filename": "FinancialRecordService.php", "line": "73"}, "connection": "ty", "start_percent": 81.739, "width_percent": 7.07}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialTransactionService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialTransactionService.php", "line": 21}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 395}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.118975, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "FinancialTransactionService.php:21", "source": "app/Services/FinancialTransactionService.php:21", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialTransactionService.php&line=21", "ajax": false, "filename": "FinancialTransactionService.php", "line": "21"}, "connection": "ty", "start_percent": 88.808, "width_percent": 2.648}, {"sql": "insert into `financial_transactions` (`shift_id`, `transaction_type`, `cash_amount`, `created_by`, `payment_method`, `updated_at`, `created_at`) values (2, 'sale', '12.00', 17, 'cash', '2025-06-08 16:25:14', '2025-06-08 16:25:14')", "type": "query", "params": [], "bindings": ["2", "sale", "12.00", "17", "cash", "2025-06-08 16:25:14", "2025-06-08 16:25:14"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/FinancialTransactionService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialTransactionService.php", "line": 27}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 395}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.123638, "duration": 0.00342, "duration_str": "3.42ms", "memory": 0, "memory_str": null, "filename": "FinancialTransactionService.php:27", "source": "app/Services/FinancialTransactionService.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialTransactionService.php&line=27", "ajax": false, "filename": "FinancialTransactionService.php", "line": "27"}, "connection": "ty", "start_percent": 91.456, "width_percent": 8.544}]}, "models": {"data": {"App\\Models\\Shift": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "App\\Models\\FinancialRecord": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FFinancialRecord.php&line=1", "ajax": false, "filename": "FinancialRecord.php", "line": "?"}}, "App\\Models\\DeliveryFinancialRecord": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FDeliveryFinancialRecord.php&line=1", "ajax": false, "filename": "DeliveryFinancialRecord.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage delevery, result => true, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1444391362 data-indent-pad=\"  \"><span class=sf-dump-note>manage delevery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage delevery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444391362\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.074982, "xdebug_link": null}]}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "success": "Delivery has been paid successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-delevery-pay", "status_code": "<pre class=sf-dump id=sf-dump-2099713802 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2099713802\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-181784724 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-181784724\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1441329241 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>payment_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cash</span>\"\n  \"<span class=sf-dump-key>pos_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">44</span>\"\n  \"<span class=sf-dump-key>select_payment</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cash</span>\"\n  \"<span class=sf-dump-key>total_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1441329241\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-439600820 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">113</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=1y4k55%7C1749399896435%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhBT3BwWVRuV3ZWMUduRW4yZzRHdGc9PSIsInZhbHVlIjoiek1SeXFRZ3BzMTNxcks2TlcxUkpueVBnd1l4TEl3VmMxQ1BUUGJXemdxbzA3VXNObWFSeWlLTTRXcytvdlJERkFic3pxeWtFQlFwZ1daWFBVMC9xeXJoZmhxRnkwV2Q1bmVMenV2dy93OU9nQndpeDU5b3lFMmU2VHhDTWZjUXdDZGc5K3AyajRYeTNnYnZST3NSUFJkVzIrVEdSbDRxcGk1bGhUcmxoaUdHRkg5eHZFSkZXWGZnRkNyMlVHM2VRUW0wVE1mWVNmS3RQMnlpWC9rMnp0a3B3Rm1wVEtURGVZNnFaaXVwSVBxZlJIRkdybDh0RWpXcVhLR2pOTlpsZXZMeS9Ld0Z0ZDBYbGxRaFUrcW1hcFErQ1Q3c1d1WUJkbldvdlMwVFZRQ09nanFFbXdEMzk2SHdSZVRxMkZFOWhzYUtqMHliRmM2NDVDM3dQQ1ZLQjZzVWRLaWhLYVA3SWd6a2Vwd2N3dnRHTVR2WW93M1k1NVBHUDhBaTV5eGs5cFA5NXRvZGZlNEpzOGhwZUtNd1N6UlVRdW9yNk9vQXM1T1h6ejg3SWZrVHRBMThwa1BxQVI2anRWcGFYaGdTY21iRkd1WThjQlVsbURjQjYyRlEyVWlMNlBpMHZnVjdwU1FhTndKOUIrUjlYcHJ0YncxNnhrUHZjbWNTWHZzUkEiLCJtYWMiOiIwNTNmZmMyYTg4OWNhYTZkZTM2MWQxMjY0MTUzNDNlNGMyNTZkMTIxYjVmMWU5ZTg4ZDViODdlNTk3ZTExMDc0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik5uSmVFRWhqS3Nsa0tWOUt5Mm8xU3c9PSIsInZhbHVlIjoiajBUeEZvOFMwQWNMc014SURtK3YxcGMrTDJFSTFOZXFMV0oyZS9PeEdIQVZSOUtJZWMrZXJRcWRQSjBoOW9rVDM5TmhaZDd3RTJTVVFDTVVOK0I0aVVmRXhsanBXS0VySGdRd0VrVlg4SVRNTW00S3B6a0NrR3Zlc04rMmFSK2JTcGxIbkJMVHE1a2NVOVZoSGlmNk4vV210YU04MWxHMUg4djhxVnpodFE4emdKWDUyUWZIUVM3ZlJzSkdVRE5pVGt1OEZTYndkZG8rMlFndlpZOTF1ejArdHZpdDlJY09QaVNFKzdRWnJlQ29vOWJLYUtpWlBBRksxejRvOUZLWmpHaWJ2dnJzWmlSSlFyQUZaWENQTWk0b1R6ZnlubDA0RXRqczUxQ2NmbmZKZHJKbkF4bU5rbzhnd2ErdXdkT2w0aUtJc3NLOVFvTGZFelBMb2Qra1I4OHBEYnliMGRYVzlFS3ZTcWc1OGZtYm9YSFZJNUNYQ29hUm0rNm44QlkyeXFWMEdzancxOUorNm9ReFVhZTd0VFpsT1M0SU9hQWVHZkYrSXJCaEFpWS9TRHFOMEhXOHR4L2F0bFZWQWVGOGxtcGs0SU5BL3ZlZ3pXbjJjNWdIaktrN3QyY2xMZ2VyazB0dzlSL0hNajVtQzlMUkd2dmNQRjNubllLMHc4NHciLCJtYWMiOiJlOGYzODdjY2EzODIxMGNiZmFmZTRlNWE4ZmJjZGQ1ZjM5OWVjOWRmZjE1ZWQ4Mjk2MDZiMDI2YmI5ZGI4YmEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-439600820\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1474924754 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1474924754\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1926510742 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:25:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5YZ3M0eVRGaHVyOWdGZ3VSNG9HYlE9PSIsInZhbHVlIjoiSkprOEtBWldUYVZzN0NwMFhFUFM1NFEvWXNUZDY3NDdEV3A3V3VVK3R5cS9lQktkclBQMTRZK0txSVlXMG5LWmJDaWRVNUlvNVIwdEt4RllaZDNrZzZkTW5kdm5uZkpZSUxKdTJtaUxqazlDb3BxdDYzVXZHOS9JWlZyTDZDSm84aGFxNzQvaEE3c2xDTkgxb1JzRm9wZE1JSE1yVVlDdnZmUkpxWmdySTBIUDVQREhMKy9FU1ZUZnlWRTZPanFFbkxDdEJWYTN0aWx3TEtFeXRtTW03Ukk2eHVzZTU3VHpNQzFienMwRWhZaDkwaFV6a3Y5bmhyQkd1bjN0UEEzejNUNmg1QTdGQ1c0WitSbVJ0cmljSUE4Wmg3cFVsSTdjNTV2NENZUWVaRnZCVGNZcFV6UTVkSG5IREJYOW5kQXZ5NER3Wm9IU01RQjkxbEFyNlpkeE1iT0dEeWJaRUR1clk2Mk83YVdkUUZENHJJSzVFdnpiQ1lhV3cyeGgycFB5Q1N5K1VRbGE4Z2tZczVPZmhjVklFM3JVeS9OMUxZUlNjZ0xUb242cXVVRHVZeWs3MVhzb0NmMk1TWThSbmN3d3g3NUZNYUVCeXZpWHI4RnNreHRtaVhwdi9ieGF2N2Q0OFRLR2lxYkZXek9ORGRxK1lZZm93T2pRblBzbGR3dXgiLCJtYWMiOiJiOWI4ZDhlOWYzMDM5NGI4MzRlZWMwMTg5ZTY1ZmYzODZlNGJmNTBlNDBiZjM4NTE2ODg5YTcwNGNiN2ZkYWQxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:25:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlZraWdJdm56WUh5clRWR0p6cEFiM0E9PSIsInZhbHVlIjoiR1VKY29uNW9XbHkraVV2WDYvM3RsVldDODhRRnJQSFZRaitjYlE5YzQ5clVrQ3JpcW9uNlA2R2pxZzZId2NLajFXS0JyQVlCWW92c2RzSmJyTGxvd1A5clk3OFNsS1pvOFliZTlyd1pqZHZMd2xiK3lydDAzdnZXR1ZhOUtSZGc2MkZFKzNUWGtxaDhFS25FS29KL1NLaXF0NTY4bURRTXhFS0ZYY1cwUldYbTh5NXB2VGVTbHJNWlczc3Q4bmRUNTZOT1E2MHhHb1N4elgzSVNuUmFLRXV5M3VNc3JnRzNRWVFzMDhpSTUzeEoxb1c0emY5ZEU3NnZmQmZGS2xwckhzSEFEVGRXZjJseitsSDBzSkRPbzZoSkN5VmdxZ2lzTDhlbVA1YWdqZmkxek5wc2k3QU1WeUwzQ2xvTkFEYTk5NGs0T0FaQVhmMFN1bDNRZHUrdXlvcXh5QzJscTlVd1JsU2NQRTF1eUM3cmU0VFNObXB0c2d5V0ExQ0ZlZVpFa2lEZ2hLUk4zZGpXeHBJOGdacm03Z3E5OHJTNUxJeFYzZUpKdDVMYkJtMStKb2p6SXVMS2gwamI2Q1ZtMnNPZHM5bm1ER0Rya0liMVNUTnR1M3VCL3R6MUR6MFQrbmJPYjk5TUg5ZHVjNkdVNXlzcTFPSkVQbUtXYXQ3c09WTXIiLCJtYWMiOiJlOTI4NzA0N2YzNDU4ZDMyOTJlYjBmMzg5ZTg4YTAwY2FlNDgwMmE2YmJhOTA1ZTM5ZTM4YTVlZTczNGU1OWUyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:25:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5YZ3M0eVRGaHVyOWdGZ3VSNG9HYlE9PSIsInZhbHVlIjoiSkprOEtBWldUYVZzN0NwMFhFUFM1NFEvWXNUZDY3NDdEV3A3V3VVK3R5cS9lQktkclBQMTRZK0txSVlXMG5LWmJDaWRVNUlvNVIwdEt4RllaZDNrZzZkTW5kdm5uZkpZSUxKdTJtaUxqazlDb3BxdDYzVXZHOS9JWlZyTDZDSm84aGFxNzQvaEE3c2xDTkgxb1JzRm9wZE1JSE1yVVlDdnZmUkpxWmdySTBIUDVQREhMKy9FU1ZUZnlWRTZPanFFbkxDdEJWYTN0aWx3TEtFeXRtTW03Ukk2eHVzZTU3VHpNQzFienMwRWhZaDkwaFV6a3Y5bmhyQkd1bjN0UEEzejNUNmg1QTdGQ1c0WitSbVJ0cmljSUE4Wmg3cFVsSTdjNTV2NENZUWVaRnZCVGNZcFV6UTVkSG5IREJYOW5kQXZ5NER3Wm9IU01RQjkxbEFyNlpkeE1iT0dEeWJaRUR1clk2Mk83YVdkUUZENHJJSzVFdnpiQ1lhV3cyeGgycFB5Q1N5K1VRbGE4Z2tZczVPZmhjVklFM3JVeS9OMUxZUlNjZ0xUb242cXVVRHVZeWs3MVhzb0NmMk1TWThSbmN3d3g3NUZNYUVCeXZpWHI4RnNreHRtaVhwdi9ieGF2N2Q0OFRLR2lxYkZXek9ORGRxK1lZZm93T2pRblBzbGR3dXgiLCJtYWMiOiJiOWI4ZDhlOWYzMDM5NGI4MzRlZWMwMTg5ZTY1ZmYzODZlNGJmNTBlNDBiZjM4NTE2ODg5YTcwNGNiN2ZkYWQxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:25:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlZraWdJdm56WUh5clRWR0p6cEFiM0E9PSIsInZhbHVlIjoiR1VKY29uNW9XbHkraVV2WDYvM3RsVldDODhRRnJQSFZRaitjYlE5YzQ5clVrQ3JpcW9uNlA2R2pxZzZId2NLajFXS0JyQVlCWW92c2RzSmJyTGxvd1A5clk3OFNsS1pvOFliZTlyd1pqZHZMd2xiK3lydDAzdnZXR1ZhOUtSZGc2MkZFKzNUWGtxaDhFS25FS29KL1NLaXF0NTY4bURRTXhFS0ZYY1cwUldYbTh5NXB2VGVTbHJNWlczc3Q4bmRUNTZOT1E2MHhHb1N4elgzSVNuUmFLRXV5M3VNc3JnRzNRWVFzMDhpSTUzeEoxb1c0emY5ZEU3NnZmQmZGS2xwckhzSEFEVGRXZjJseitsSDBzSkRPbzZoSkN5VmdxZ2lzTDhlbVA1YWdqZmkxek5wc2k3QU1WeUwzQ2xvTkFEYTk5NGs0T0FaQVhmMFN1bDNRZHUrdXlvcXh5QzJscTlVd1JsU2NQRTF1eUM3cmU0VFNObXB0c2d5V0ExQ0ZlZVpFa2lEZ2hLUk4zZGpXeHBJOGdacm03Z3E5OHJTNUxJeFYzZUpKdDVMYkJtMStKb2p6SXVMS2gwamI2Q1ZtMnNPZHM5bm1ER0Rya0liMVNUTnR1M3VCL3R6MUR6MFQrbmJPYjk5TUg5ZHVjNkdVNXlzcTFPSkVQbUtXYXQ3c09WTXIiLCJtYWMiOiJlOTI4NzA0N2YzNDU4ZDMyOTJlYjBmMzg5ZTg4YTAwY2FlNDgwMmE2YmJhOTA1ZTM5ZTM4YTVlZTczNGU1OWUyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:25:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926510742\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-22790224 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Delivery has been paid successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22790224\", {\"maxDepth\":0})</script>\n"}}