{"__meta": {"id": "Xdde3312eaaf968c03c004bda49b59c56", "datetime": "2025-06-08 15:42:37", "utime": **********.54843, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397356.803156, "end": **********.54845, "duration": 0.7452940940856934, "duration_str": "745ms", "measures": [{"label": "Booting", "start": 1749397356.803156, "relative_start": 0, "end": **********.461755, "relative_end": **********.461755, "duration": 0.6585991382598877, "duration_str": "659ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.461768, "relative_start": 0.6586120128631592, "end": **********.548453, "relative_end": 3.0994415283203125e-06, "duration": 0.0866851806640625, "duration_str": "86.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45499168, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.014289999999999999, "accumulated_duration_str": "14.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.505563, "duration": 0.01249, "duration_str": "12.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.404}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.531954, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.404, "width_percent": 7.208}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.537968, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 94.612, "width_percent": 5.388}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1219294503 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397219377%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJlbHh5YmZXbHU5am5mVUdzR1RyNUE9PSIsInZhbHVlIjoiNjBnK09TNERYZ1hqVFBpNytMbG01WVFBSnNJQXZBSFpCVHBkQmhSeFArNThZS0JXVXd1eFM5V2JuOGFZcW5IWDEreFc2dCtsT25wdExFdi9PRTdzY3RjU2VIQmlYZFc0RGp2TTVDTFBDZGs3NHlESVB1eG1aOVlZS0hoR083UDFKY0JaOUs2YmVzQ0N6UloyNGVpQWZQQ08vR0hkUG1BSkkrZlNlclZNK1FBRW0ydTU3YkZyL3FIMXR1OEg0Y1o2Mjc3cFh3WmdCQTBZQVNDZW1KdFdJZHdPS2Uva2FBa2JPanNaRWdFeW5nKzNZK1UzeWFXZi9BaVM0VTR6N0VmNVFVcXZqVHFqQTNvTXhKT0k3NmEya3g0QkxBMzVubE1uSzFDVXJDUFBXa3lzbU1aZU9IVjczcXZRbVI3ZVZNc1dUcWViSGJLbjlNeFVPMTI0OGNTckJEV09tdDJETW04RG5ScnJaeG44K3Mvc2RGNXJVczQ4QXNmUjlnUGM1VnZ2b3VqK3RFQmNHeCtXdnc1RkppQkRBT1A1bjd0TTRJQ093TUFKQ1cwWDFMaDlkSEdkMHpyMTgrakxFN0tOcDdpSHducm1FaW1sYWZRYW1xS3BhSTRlZXdqTHB6eXkxSUFtd3cxSVR0UUdsTktyVVZ3a3BCU29yMzdLMGU5a1Z4SEMiLCJtYWMiOiIzYmU4NTcyOWZhMjZiYjFmNGI3N2YxYzNiMjExYmE4MTI0ODMxYTIzYTc1M2QwNmM1OWY0ZmQxYTBjMmQzYmJkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImI1ejlZdTA5R2U0VWxJZ2ZtQWUyWHc9PSIsInZhbHVlIjoiVUVKM3Q3c1AvMnpBUHVxaTFmMDRteUJKdmh2UFdnWU91cFB6eUlNbGdzMUpPRHoxQWZ4dU9IbW1qVms4WUxLdG9kYzhHSDUvNWR6bXZzekdCQ3FjZVJaWTUyUmZkSmtVQjFBZkMxRkhjU1Nwa1p5WjZjM0N1bkpJQk84ZXRBZU83cnhxc0hQbDZXcE56cmZxbFplbkhaM3dHcGQzd01oV21KK29jRXlHUE44N3lWVW9aUm5JaG11R2ExYm4vME1PNkhsSG9VaHBIdVAzeXFMVWtqTWtWb2gvaTlZZUdwNkVTdS9rSEo5UmNTUWs5MjhsOHlmclJ6cjlkc3JCYnRRNS9OMlRwcFg1bE56RTFicTZ5b0tka2FTcGRqUnZCSDB0S01jQ0JQYmI2ZlN5U1VZUXdYYnYvWFY0RUdmR2pzUU5qeHljN0FKRXEyOSt3dDVrc3RRbEFJSmFPcVE4Ly9LeTdndmhuYkdvZ1JOWEhCblY5OHlvN3p0QU1vd3YxbE9wSDRnR3JsTHAzdkNoTFhLcmxsdlZqWEZiQW1oTmgvOExlRit2dG1UTGVWeHQ3RHZSbVQzclRWbmZHVUVuOVJ0TkN4WEpvczYyQjVoS29XY2tyNXVMOFdKVjNBdStpQ2YxN01aR2dWcFZlNUhNMG9HdE5CZng2MUhjUThHREpVOVUiLCJtYWMiOiI0ZTEyNDMxNzFjMTA5ODU3MmNhZDVlNDcyMjVmOTc0OTVlM2U0M2JjODE2MWJhZjM3NjQ2OTUwNDhhM2Y4NmJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1219294503\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1843619855 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1843619855\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2024162717 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:42:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhQcXVNYlZyUmk2STJ6U1oweHlUQ3c9PSIsInZhbHVlIjoia3JySWVKU1VlQ1dTb2M5MHVONUlZUFhkRFRpdUJIZ1I2V241blNVcEUyZjNyT3JIZ2hYYldTRHIrSzMxWlZjWlY2T2o5VHQ0Y3gxNzAvd3pIUFBQQVhNSUNGdXRXVkUwQW05VGJ6M2MwNjNHV053V1hDODU3UmxFRmdlYlRqOFJMZWhZVDZjYW81T1BjdTdUUkg3YXY5d2ZKdU9jTTBWZUpQN2dLY1lwSEwzbEY2MWR0K3BiUFRzK3BPWDQ4MFVkcTJzSFcrYjRwVjBPN21SU01qU0lKWUQ0dW1BUHVOdWRQMitLSTVTb3NpbHJkdUVIb2dkTmNUMFNvcklNaENIM1FBcGJ3YlBJSm9ydHV1aWhnOGpsZWpYdnEyaitEUW9tYnBWNURzMGlIb0JVV2M4L2FuMG9EREN1UXJXL0tPWVl6amVYdnM5TFllTHFZL0g0NklNbkhEQVkxMFJGenFxeG12U2lzMllBajNoZCsyMU1vekhVQk5scGg4VmUrMW92SnNpWVhvZnJaQWJ1bnFqbllyRkVpRGZYdkFtL2l4YnJVVndocWJ0Z3RGN1lPY3JlZ2ZXS1JSNHN4bmZjOEIvdFd6dWdINVA4U1BjamdpRU9tU2ZvdGJIRjRNaHAwN2Q2anBxYmxmUkljaE5Ud1FjZFBsWUZSZmhBUFJ3RzE3R3UiLCJtYWMiOiI4ZjBhMWZhMTEwOGEyNjY1NjFiZDRjZGJjZjQyNDIxZmNmNzBjYWQ3ZjdjMWNkYzllZjU1NDk0YzcwOGY0M2I5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImpTdjNvS2dkQngxdkE3cG1ZSVZCZXc9PSIsInZhbHVlIjoidmp4ay9XZWIxSDhxZXBZZGI2NnFpS2ljS0FraVBjZlpFbG9aRnJsYmR6R2JKV1M5OUEvaTFoYzNkZnQyaFVYcS9MNVhKRVVNb0hnTktBUyt5K08vWWhBNXZ2TmU4ZkM3anRzQXZPT1BnMk1LWkR0b0NaZnFaaitQY0Fsb0ZzRndxTDJiZFRTQjFIVzV1Q2NtZTQxREVrTHY4SkdCUFpteUJoMFJUVGhyenRtejNqd0JYU1VYMHgzdVJ1SFloV1N0VGRNZFZQdGFuaWFQZVlyZzdCd0pDeVpwemtQYWtHdkZlV0Mra0FrSjU2Z2IrdldtYXpGMkppemVWTzl2UDIyMWs5bUZjMlUyWFUwNFlNaEJvUFgyU3JmenBzcVR6eCtHM1ROazJXa21BUFdwaVllb3prSkQ0c3JKMXVzdzVnRDhRNkxKZmRoQ2RJL21RdDJTTzliVG5qUFU3UFI4TjMySG1FaUhIckNac1NJMXpNYUQ5VEtSeTQxNFR4bUFjQzE1RWZtWlQ5OUo2K0w3ZTFkTFoza3VqUzRoc3plWjcxalh5eSt4SmxGYUVvVnV4TWhodi9pM21mdGVXRGJ5ZlpMaHRIWDNSMXJ2cWNhNDV3UzlmbjExZWtaTWtOU2lnYmhPdnhKKytsVXEzdzlZdlFBbC82QTAxZlZyV3JhSEF0NEQiLCJtYWMiOiJiMDE4NzQ0ODM3OTg5MTFlMjM1NDQwMmRlYzZlZmUyOGM1YmU4YTk2NGY5ODc1MjgxZWU0ODQ4ZjFlM2NlZDg0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhQcXVNYlZyUmk2STJ6U1oweHlUQ3c9PSIsInZhbHVlIjoia3JySWVKU1VlQ1dTb2M5MHVONUlZUFhkRFRpdUJIZ1I2V241blNVcEUyZjNyT3JIZ2hYYldTRHIrSzMxWlZjWlY2T2o5VHQ0Y3gxNzAvd3pIUFBQQVhNSUNGdXRXVkUwQW05VGJ6M2MwNjNHV053V1hDODU3UmxFRmdlYlRqOFJMZWhZVDZjYW81T1BjdTdUUkg3YXY5d2ZKdU9jTTBWZUpQN2dLY1lwSEwzbEY2MWR0K3BiUFRzK3BPWDQ4MFVkcTJzSFcrYjRwVjBPN21SU01qU0lKWUQ0dW1BUHVOdWRQMitLSTVTb3NpbHJkdUVIb2dkTmNUMFNvcklNaENIM1FBcGJ3YlBJSm9ydHV1aWhnOGpsZWpYdnEyaitEUW9tYnBWNURzMGlIb0JVV2M4L2FuMG9EREN1UXJXL0tPWVl6amVYdnM5TFllTHFZL0g0NklNbkhEQVkxMFJGenFxeG12U2lzMllBajNoZCsyMU1vekhVQk5scGg4VmUrMW92SnNpWVhvZnJaQWJ1bnFqbllyRkVpRGZYdkFtL2l4YnJVVndocWJ0Z3RGN1lPY3JlZ2ZXS1JSNHN4bmZjOEIvdFd6dWdINVA4U1BjamdpRU9tU2ZvdGJIRjRNaHAwN2Q2anBxYmxmUkljaE5Ud1FjZFBsWUZSZmhBUFJ3RzE3R3UiLCJtYWMiOiI4ZjBhMWZhMTEwOGEyNjY1NjFiZDRjZGJjZjQyNDIxZmNmNzBjYWQ3ZjdjMWNkYzllZjU1NDk0YzcwOGY0M2I5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImpTdjNvS2dkQngxdkE3cG1ZSVZCZXc9PSIsInZhbHVlIjoidmp4ay9XZWIxSDhxZXBZZGI2NnFpS2ljS0FraVBjZlpFbG9aRnJsYmR6R2JKV1M5OUEvaTFoYzNkZnQyaFVYcS9MNVhKRVVNb0hnTktBUyt5K08vWWhBNXZ2TmU4ZkM3anRzQXZPT1BnMk1LWkR0b0NaZnFaaitQY0Fsb0ZzRndxTDJiZFRTQjFIVzV1Q2NtZTQxREVrTHY4SkdCUFpteUJoMFJUVGhyenRtejNqd0JYU1VYMHgzdVJ1SFloV1N0VGRNZFZQdGFuaWFQZVlyZzdCd0pDeVpwemtQYWtHdkZlV0Mra0FrSjU2Z2IrdldtYXpGMkppemVWTzl2UDIyMWs5bUZjMlUyWFUwNFlNaEJvUFgyU3JmenBzcVR6eCtHM1ROazJXa21BUFdwaVllb3prSkQ0c3JKMXVzdzVnRDhRNkxKZmRoQ2RJL21RdDJTTzliVG5qUFU3UFI4TjMySG1FaUhIckNac1NJMXpNYUQ5VEtSeTQxNFR4bUFjQzE1RWZtWlQ5OUo2K0w3ZTFkTFoza3VqUzRoc3plWjcxalh5eSt4SmxGYUVvVnV4TWhodi9pM21mdGVXRGJ5ZlpMaHRIWDNSMXJ2cWNhNDV3UzlmbjExZWtaTWtOU2lnYmhPdnhKKytsVXEzdzlZdlFBbC82QTAxZlZyV3JhSEF0NEQiLCJtYWMiOiJiMDE4NzQ0ODM3OTg5MTFlMjM1NDQwMmRlYzZlZmUyOGM1YmU4YTk2NGY5ODc1MjgxZWU0ODQ4ZjFlM2NlZDg0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2024162717\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-12******** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12********\", {\"maxDepth\":0})</script>\n"}}