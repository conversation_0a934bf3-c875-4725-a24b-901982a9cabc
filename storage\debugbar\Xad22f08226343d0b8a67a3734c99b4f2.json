{"__meta": {"id": "Xad22f08226343d0b8a67a3734c99b4f2", "datetime": "2025-06-08 16:17:59", "utime": 1749399479.020384, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.45162, "end": 1749399479.020407, "duration": 0.5687868595123291, "duration_str": "569ms", "measures": [{"label": "Booting", "start": **********.45162, "relative_start": 0, "end": **********.948806, "relative_end": **********.948806, "duration": 0.49718594551086426, "duration_str": "497ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.948819, "relative_start": 0.49719882011413574, "end": 1749399479.020409, "relative_end": 2.1457672119140625e-06, "duration": 0.07159018516540527, "duration_str": "71.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152152, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00488, "accumulated_duration_str": "4.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.985348, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 75.82}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1749399479.001473, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 75.82, "width_percent": 11.885}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749399479.010227, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.705, "width_percent": 12.295}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1328641595 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1328641595\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1534694127 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1534694127\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-991761048 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991761048\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-633566609 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=1y4k55%7C1749399475493%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikl3cllyelFvSEZOSWV2TGU2YjFuWXc9PSIsInZhbHVlIjoiTjF2WGZmWVRaT1NYcXRkT3lSTVJJamFlTFdZNENCOXprbE5JUzJtcDRkc0lsWi92Z3RuanpNcXBsSjZFS29BQitMMGNOVXBuQ3dtNU5CS1lZUXhzdGhxOTg0c2JadmtZK1Vza0I0cElPNmRWTzdZL3NCOEVyeUdacGo3WmxmTUVleThIb1NtYlA4OGtKMG42bnN3VGxLTVZha3ZOK2hua01OQ3pmU0pkS1JLSDZZTHZsL0g3TjVRNkVLNjBDZW5wRW9jSXIxZUxMcUFGaExrK3hBNmExUnVxZGJra0I5RGdEdStMWmpjWkJoamh2ZnBtdzlmOTI4ZS9zdjJNdzk3OHlodXN1NVI2ZHpZa3pzNFpXQ043dmNKcEVoRHVCbTlhQndlek9yc2d3RU1rYzJBdlMrTWJISEgrdkVpWkdaZGQxMEpZMG9aQW5WYWtRUldLUzZkNDBNeWd4ZmsvRGhVVk1wZ0gwRzg2WUsrRTlPMGlvTXQwQ3BGQ1V0TS9XYkN6VGY3YXV1ZGJQUk1aZlpnZTc0c3l3dnFHRXpBWStZVE1keWhsaTBpWVRTMUJOTzNoUmYvNzYrN3QwZzY4QWxNcjUrV1dqWHJETTNuMGxuTXVhaXRzcU5zR3RRTVZzeXhzTG9lT1QveUtkUXBhejd3ZUhtaUlIWGQ4NzZ2ZUxpbUMiLCJtYWMiOiIzMzUwZGY1OTQ2ODdhODdkYzNmODY0MjdmMjJiYWI2MjYwY2Y5OTBhOWQ3MGFhYzNmOWY0ZTc1Yzc4OTMxNGE3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkdqS3Bqd2YzaVNvSjRTOGl6aDhsb1E9PSIsInZhbHVlIjoiR0NsOE43ajBNTmZudThLQ2lBTUZDUkw1dUx0QTdRSkQ1UWJoTE1MNC9DNFljdVI4dWhxdHJVdUtBUVRHblZVTnZKSTFkN1BOdU02NXh4OWc5VnJFMVpLenhuTlBqMGg0VWsyRFlCQ3ZEeTl2RlZhS2Fkdm5RTC9pbVRFRjZGRHYwZTF5TnR5VkRmaUNaRFYwUWJ5SWxPUkNsZTgvN2l0RHN6SkxMRUsvVm4ybGFkRjF5dG5tNXlYVWdvcCt3SDB0eTNNWkZoeDRoWk5tN2c5bXpzcFdUbkhYUEpmdEk1TWdWTVp5VmNUSUliN3JiY2wzYWFOTlNySXdML2VZWGw2aDR2NUd4S1R0WVpHZkJRcHJKNkNncUhCR2tSVFJBNlREUUJ1RzcrNXFwNVV1dFNiZUVPZlRpU3VNVXAwUlNqUHpnMWNGTzdBUlpGeGRqOWtwQ1laUWNlaTZXMms0cUZzYS9kVjZnaFkrSmhVMUZ3ZUZ2bG9aS011Nmc1NmErejhOSWxscVNpS0Q0QU4yTSt3K1M5bklYSGgyYTlyK3pSTXNmVXNwVWZiUmR4Q3QzWm5Dc1A3Sk1mbFVwKzlMd2M1b0dlZzlLTUZCM2hkMHBNWUtpaWxrdXNQM3ZsOE1udjFXWVpSQVk4ZTJYNW9ySFlEeFdYRkR0aDhlTjBIRWViTEMiLCJtYWMiOiI2NjM0NDM4YjhiNDBlNDM0ZjU2YjQ4ZjY3OTMzNWI3NjAzZDA4ZjFhNjNmMjBhZGFjMDEyNzJiNDMyZDc2MzUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-633566609\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-525161526 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-525161526\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:17:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFJamF2MGZYeUg2dFUwb24vWERCdlE9PSIsInZhbHVlIjoibDlxbk8rZVc2MHBiekttVEpzNzRXTmd1RjhLWDBvQnlseGlrT2pwemxsQmtDVjlpdWpvUGhDRTVSSzJDTTNXRmVwRTdJYUpyYlBpY3JCOHlXQ2Qyd2N2eVpmdGZlZVpSVnlVWVkwc2dDakxDVFVwNzMwaHp5NTd5aGxnWlY1MzRSWlFadzdONyt0Qk9KVGF1bG1OVkVDR0xNbVNYYTYzSVZWRjhiTkRUU0lTTDBEd2ZQYmsyMTR5bVF1Y0lPRzRSdUQ3ZVppUUl6Z25mR21OTCtDWlVjTC8xNE52L3BubDJ0VDV4ZnlpSmNCR3l6L1IyTDhmVW9YVStvN09sTHZlMVJwdXo3NXhJM2hxNGlsYTVyTWFNSzdLWU9EMVJza0RKaXFhb016dEhQQUZhTVAvbitEb00yN2hoa09BQ1VGazFYa1VYUVpwV09RSmxRdkRtNW1DVk04d1dwNFV3L2JoV3RhY3NLZjc5NndmNDNGYlpzb1NUM1IyOWJpN1RjTHBmKzI2WTUxSzdNMEVYK1VKRlNoa29qZFBRNWJVN3dZcnE4NEVrQlVxSjdRR01WMnFxRmVHZG5tdWw2YW1hcDU5L0I1OFZDV0M0YkF3WDFEVzFpZGc5dFJVR2V6aFZzcnR4SkV5bzFiczFvRlVOa0taTE5GWE1nWjNUYWdSaGpLNHQiLCJtYWMiOiI2YjMwM2YxOTA0ZWE5MGM2YWRmY2ZhNjdiYTgzMTlhNDgxZDI2MGQ2NDZlMTRlNzJhYTEzYmZkMThjNDVjMDBlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:17:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik54MTQrR3RwV0NrVGZMTGYzUEpoK0E9PSIsInZhbHVlIjoiejYzYlA4RVQ4VUo4RHVtaWlhUVdaT2xwNjJDQVFnd1A3MWpHY2lrM3U2TlJKcnQ2U1BsV1lLOE1WZ0EzcXVEY094Yzc1MFlaZnp6L1ZiVnNGZXVVSnZiUG9EeDZkd25ONVRjcmxUWU45N2RMazhraVZBMDZSd2VqR1dvTkJrVG9ka214K0pTbm4vTjRGcGNGSS8xTlFSMmd6QlAvMFV3RzgwblNxb0xPWjAzTEtiU21MZmtnaklBbjJlRjg0NS94SjVCZkM0WGRNM1A3a2F5LzZCcDRqY2lOOWlKKzNQVTJzK2RKZUw5UjFoM21VQWM5ZWM4OUg2aGVzTFY0VG8xRk8weGFiclV5enNHZVVJV1NPd3dwK2orQ204SnFxcUJGOHppK3pTQlZUUWsyajg4TlprNFA5RzRwYmRRZkVJSCsvRDQ1eWl5akZqOTN0ckQ0S3graFZCSDk0VHJVNnJwZmxSYnVrN3p5dHJsMnhtcjlzVXpCRHRMVmRPeE5ZUlkvdE9mUjBOSERaSU5WU0hoUnF5NWxpSnZveU4vcXJBb3NOT2lhV3lhaXJuaGU2VlB4bzBkbEg3ZFNXTGgzWDlpSFhES0I4ZEVFQStCeTFYbzhrUG8vejZka1Jia1VLZFZlcWJ0RGRHam9CZk8xbnltTkVIa3I2Z3UxQnZWQ1dGVHMiLCJtYWMiOiJjZmI5OTg2NDE1ZDNiNzllZWRhM2MyMjllNWRhYWE1NzdlYWM2YWNiNzRlZTM1N2FkNWI2NmNhZjdkYTAxMTc0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:17:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFJamF2MGZYeUg2dFUwb24vWERCdlE9PSIsInZhbHVlIjoibDlxbk8rZVc2MHBiekttVEpzNzRXTmd1RjhLWDBvQnlseGlrT2pwemxsQmtDVjlpdWpvUGhDRTVSSzJDTTNXRmVwRTdJYUpyYlBpY3JCOHlXQ2Qyd2N2eVpmdGZlZVpSVnlVWVkwc2dDakxDVFVwNzMwaHp5NTd5aGxnWlY1MzRSWlFadzdONyt0Qk9KVGF1bG1OVkVDR0xNbVNYYTYzSVZWRjhiTkRUU0lTTDBEd2ZQYmsyMTR5bVF1Y0lPRzRSdUQ3ZVppUUl6Z25mR21OTCtDWlVjTC8xNE52L3BubDJ0VDV4ZnlpSmNCR3l6L1IyTDhmVW9YVStvN09sTHZlMVJwdXo3NXhJM2hxNGlsYTVyTWFNSzdLWU9EMVJza0RKaXFhb016dEhQQUZhTVAvbitEb00yN2hoa09BQ1VGazFYa1VYUVpwV09RSmxRdkRtNW1DVk04d1dwNFV3L2JoV3RhY3NLZjc5NndmNDNGYlpzb1NUM1IyOWJpN1RjTHBmKzI2WTUxSzdNMEVYK1VKRlNoa29qZFBRNWJVN3dZcnE4NEVrQlVxSjdRR01WMnFxRmVHZG5tdWw2YW1hcDU5L0I1OFZDV0M0YkF3WDFEVzFpZGc5dFJVR2V6aFZzcnR4SkV5bzFiczFvRlVOa0taTE5GWE1nWjNUYWdSaGpLNHQiLCJtYWMiOiI2YjMwM2YxOTA0ZWE5MGM2YWRmY2ZhNjdiYTgzMTlhNDgxZDI2MGQ2NDZlMTRlNzJhYTEzYmZkMThjNDVjMDBlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:17:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik54MTQrR3RwV0NrVGZMTGYzUEpoK0E9PSIsInZhbHVlIjoiejYzYlA4RVQ4VUo4RHVtaWlhUVdaT2xwNjJDQVFnd1A3MWpHY2lrM3U2TlJKcnQ2U1BsV1lLOE1WZ0EzcXVEY094Yzc1MFlaZnp6L1ZiVnNGZXVVSnZiUG9EeDZkd25ONVRjcmxUWU45N2RMazhraVZBMDZSd2VqR1dvTkJrVG9ka214K0pTbm4vTjRGcGNGSS8xTlFSMmd6QlAvMFV3RzgwblNxb0xPWjAzTEtiU21MZmtnaklBbjJlRjg0NS94SjVCZkM0WGRNM1A3a2F5LzZCcDRqY2lOOWlKKzNQVTJzK2RKZUw5UjFoM21VQWM5ZWM4OUg2aGVzTFY0VG8xRk8weGFiclV5enNHZVVJV1NPd3dwK2orQ204SnFxcUJGOHppK3pTQlZUUWsyajg4TlprNFA5RzRwYmRRZkVJSCsvRDQ1eWl5akZqOTN0ckQ0S3graFZCSDk0VHJVNnJwZmxSYnVrN3p5dHJsMnhtcjlzVXpCRHRMVmRPeE5ZUlkvdE9mUjBOSERaSU5WU0hoUnF5NWxpSnZveU4vcXJBb3NOT2lhV3lhaXJuaGU2VlB4bzBkbEg3ZFNXTGgzWDlpSFhES0I4ZEVFQStCeTFYbzhrUG8vejZka1Jia1VLZFZlcWJ0RGRHam9CZk8xbnltTkVIa3I2Z3UxQnZWQ1dGVHMiLCJtYWMiOiJjZmI5OTg2NDE1ZDNiNzllZWRhM2MyMjllNWRhYWE1NzdlYWM2YWNiNzRlZTM1N2FkNWI2NmNhZjdkYTAxMTc0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:17:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1727263325 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1727263325\", {\"maxDepth\":0})</script>\n"}}