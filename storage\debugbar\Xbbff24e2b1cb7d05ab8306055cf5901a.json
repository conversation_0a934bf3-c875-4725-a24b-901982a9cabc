{"__meta": {"id": "Xbbff24e2b1cb7d05ab8306055cf5901a", "datetime": "2025-06-07 22:18:07", "utime": **********.897185, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334686.330884, "end": **********.897228, "duration": 1.5663440227508545, "duration_str": "1.57s", "measures": [{"label": "Booting", "start": 1749334686.330884, "relative_start": 0, "end": **********.681129, "relative_end": **********.681129, "duration": 1.3502449989318848, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.681152, "relative_start": 1.3502681255340576, "end": **********.897233, "relative_end": 5.0067901611328125e-06, "duration": 0.216080904006958, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45098328, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.011300000000000001, "accumulated_duration_str": "11.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7841501, "duration": 0.00591, "duration_str": "5.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 52.301}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.818701, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 52.301, "width_percent": 11.947}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.85132, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 64.248, "width_percent": 18.053}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.871975, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.301, "width_percent": 17.699}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1853211797 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1853211797\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-359178487 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-359178487\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1282977412 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1282977412\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-393646727 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334667398%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im8rL2ppQXlSQ1hKK2ZoUzFUUGNuR0E9PSIsInZhbHVlIjoiYXlLazNDVTc0NUt3VHdmR2dnQXdmWjJYbEh3ZEZxd3QvNGRUd0UzWndheWcwRHQ0UUNjS25Sb2RRamttVWNmTXdrb0svdDVyODN4dDBaK1l1K0RYT1F1NGx1eTh4Vk5xQjhWUE8yS1dVcjVGekZxSHQ3TUpobEMyWjJDS3d4UUVKcUhMYndjVENHankyNjlqRWJOZkVFc3V4eWRGZXhKc2ZtOTFxbElPQjhONy9hQWx2VHB0TERvYXpqVWp3OGQ2Vmx1eWthL1dNUE9TWG1EdE9MWTV5ak55dDRublh0OVc4WC9sdEg0ZFV1R3dGL04yWDVYeHpRb0lnNXlwT21meUdJcXNHT1diNHhVeXoxdUZrYXFuUTlSVG1MZFhrQ3ZFTlBYWGZSNWFFZndhclVCOHd5djZkckNIQ1c3OUlwZUI3NmxRNzlRd0VMNGQxa0VPSkJUVWhCanBDSzRBTUprUi8veWlnTnNUWmFMbmE0ODdrL0pFTGRZb3l4SzlLY09LOHZseEtpcHoxRFFvSElaRXNCQ2RFWGxSbklRTDVidGQzaDYyamR3aFpjZ0Q5dWVCVFhaSCtOSkJITlJ0UGYxUDhmR0RBUEtndWlaY1VndXQ0MnBsWEw3OVBQcmxOdm5GdzVVRUV6TS9WSXhsR3dWQ2c4U3FscGlyQU9YYlp0SmIiLCJtYWMiOiIzMThmZmRiMTZhZTNmODA1NTQ3OTg1OTJlNjdhMDgwMTgwMmJkNTJiZGQzZjk1YzJlMjEwMmYyMjE4MGM2NDY3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBKclpxNllYeU9rK2QxeksvUnMvdmc9PSIsInZhbHVlIjoidWpUaDF1U2V2WEpIeFNUZUYydkU4R1lNM2QxdUxVQ1NzMFN4Ry9wVlBpQmNyUzdvUW9yTEFxWkxrWGZWRzV1aGlSSCsvT1lSRWlLNEY2SjZUWm1WbW5VdXFNOG5CZll0dlBXUTVaWGthdzNpN05ySmpCVXVLenp2OXVLelcyZFl4SUgvYllZcTA3RHpoWU9iRExEUHFrczZGYU1BWklzYmoxeXFTL2k1VnVvdFNVRDRDYytraHpycFRHSmJYbDhZSFluV2svNHRDS2ZDc1o2L1N2Rk9uMDFJMlFkUWQ1ejl0dGpCVUZRcVhBazVQUTYwNVhiWTF3dnpsZk14QURUN0pRWkVrMkJhZyswbi9RWmlobGphazFXUm9XUWZZOGZaOG9OT1crMm9QRnYxQ3daUDVlSHkxSnpWK25oSmZtVklVVThRWnNDbGVGdU9MeWFXMlExMzREZklMYmQ3Rm00WGZFdUpYR2RhMjNMWXhHRlZZVjMwREVoNzh6TTBJbjQ0SnVLRXljbFVoa1dENDBCU01YTFZVT1dIK0tSK0pMWittZjhSLzdJdSt6NTBHWU1PSU00MzlvQmJGd0RKazZOU2ptbEJVSWlFQ3ZYQnVGeHo3R0lvS2lKVkxKZXZ0QjhBVFkwTjUyNm5hVHNRYnoyZkoxbmZmbzhKQS83cVpXQ2ciLCJtYWMiOiI4OTJmM2EwN2MwOGYwZjljZWVmNGEzOTQzZjc1ZWMzYjlmZmRkNmJhZWY0OWJkNGE1NTdiYzlhNzUxMjA1OTc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-393646727\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2103909150 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PmFRgUqysTWH0qB2ROlhITKoLIETZGW2KykVXtOx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2103909150\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1562723528 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:18:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1OMHFSbXhGdjBiZnRiajZMNVFpd3c9PSIsInZhbHVlIjoiTEI3SjgvVUhxUkE2aEgwUEFCSXlleHNZZ2ZTMWNMeHVmVXVHOWVtU1IwdkhrQ3hFTWpiOHNZVmViZklTbnFHbGFhSHNscnhZZU5DZkk4SmpFZXErMDJKMVNVeE5EVUtVNjY4UjViM2tFUVRqL1RrYVFLK0FUTk1Ha3U2TXhnWkRhb0ZONnpJWU9rSzZiNWlGVWdRL2FTU0h4Y2EycUkwb3ZXRVVNMjg5RGhjK0JiSmVIK3VXNmdaZzcxVCtrWjE4ZUxMNEorR09Fa0N3MVJvRHVILzdaM2o0LzBIQm1mT1locVowd1p6VUFmT1BaVUYrQ0RHcU1GZTd1Z2wwRVAweFJqbmQ5NlRuSkFCbTZOdmJYYjAzS2ROeE85eEhtb3h0MG9td1lFMzNsK3FyTE5rNWJ2d0VUQVZ5N1J1cUtPVEw0Z0J4eFFkR3o0Tm1sbzNRMkU5a0FmbWs5TEY5Rno4RGN1TWZYS1lKQ0JQVk9yeGNUZFJZMDEwTHViM0hHQzJqWDVWS2hpSFNWTXYvdWlVWEhidGtXWFREQmQ0cXpmVFhXaHQzai9HSldkOGZXdENMQ0tRN3dJTUo3eEpkVEZoRHByWHBWSktuKzArTE82MDNsVFZ3RnI1T1MvWDR6VnRmOXdxNi9nTTFYcjBsQ0lDTEJLRW5KUytYTUZ6SXBHMngiLCJtYWMiOiI5YmUyNzIxYmMyMmZhYWIwNjY0MmNlNDEwYzA3OTUzZWQ1MzhlM2NiZDcwMTAxODY0ODc1YjI5MGY3NGZjNDBmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Inhyb2JTNXdaOXFxb21PRHltWHFPZXc9PSIsInZhbHVlIjoibHl2RXdySWwzbHZUQjF4aElBR09EUnRGaFA3eENuK1ZBSGw1cDFLK2s1SXBxVXVXWjJBTmlDZjZNczlFdStMVW9jdlNuVHhscVBqZWVjTDVVOGxmcTFTaktIN2R1dGJaNW01Z3FFY2RLMzhtTDBhbHhDSk5Hc2tyWmtGSlpUTU1ic1d5MjVHNnVjMHFiS1QxTFJXZFZEVFZnUjhhUjk2ZHphMGhiQk1obGR6S2NnTWk3T0NPQTlUV0NzZk9CbDFNVTNpTmF0d1JOM1BBV1l3c0lQN2dlUlBvaXROSys0RjhNN2pxYkY5dldhcUNWL29XcHlGVTBjS0tmZCtCcGhyelpWS1JJdStZbnY5bUR1aWpxVjMrMDB1Sjg0TG5IOEFvdm5WR1VnR01sM1ltSmVRL1VzL1dJSys0Q3dJYThNRlZ2NE5mQXUreitqcTdtdS84allvanZqY1JxU29CSEE4SnNuWVZPSWpiTklzQVU3N1E1UlM4QjJWUHpFUjcwZlBGb0duMTJYUWhzOTBiSGtrb3UzUURzNnV2cHZiWFU3S3JhelpidG9hcnJINzcrQ0JVODdvMlFVNmhPbGEwdGZSRkphRDh2bG8vTnUvSzNCMU5jakVtbnluSUxLM0tPSDE4elBINDRaYjNGRncrL3kxZ3l0VjU2T1p4SWVmRjJpcUgiLCJtYWMiOiI4ZTRkYTVjZmJjNDlmMWQ5ZWFmYWZiMWFmY2JiNTVjMGFlMzRkNmE2MjkzNzAzMWVkMGU5MjUwZDA3ZTgzZjgwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1OMHFSbXhGdjBiZnRiajZMNVFpd3c9PSIsInZhbHVlIjoiTEI3SjgvVUhxUkE2aEgwUEFCSXlleHNZZ2ZTMWNMeHVmVXVHOWVtU1IwdkhrQ3hFTWpiOHNZVmViZklTbnFHbGFhSHNscnhZZU5DZkk4SmpFZXErMDJKMVNVeE5EVUtVNjY4UjViM2tFUVRqL1RrYVFLK0FUTk1Ha3U2TXhnWkRhb0ZONnpJWU9rSzZiNWlGVWdRL2FTU0h4Y2EycUkwb3ZXRVVNMjg5RGhjK0JiSmVIK3VXNmdaZzcxVCtrWjE4ZUxMNEorR09Fa0N3MVJvRHVILzdaM2o0LzBIQm1mT1locVowd1p6VUFmT1BaVUYrQ0RHcU1GZTd1Z2wwRVAweFJqbmQ5NlRuSkFCbTZOdmJYYjAzS2ROeE85eEhtb3h0MG9td1lFMzNsK3FyTE5rNWJ2d0VUQVZ5N1J1cUtPVEw0Z0J4eFFkR3o0Tm1sbzNRMkU5a0FmbWs5TEY5Rno4RGN1TWZYS1lKQ0JQVk9yeGNUZFJZMDEwTHViM0hHQzJqWDVWS2hpSFNWTXYvdWlVWEhidGtXWFREQmQ0cXpmVFhXaHQzai9HSldkOGZXdENMQ0tRN3dJTUo3eEpkVEZoRHByWHBWSktuKzArTE82MDNsVFZ3RnI1T1MvWDR6VnRmOXdxNi9nTTFYcjBsQ0lDTEJLRW5KUytYTUZ6SXBHMngiLCJtYWMiOiI5YmUyNzIxYmMyMmZhYWIwNjY0MmNlNDEwYzA3OTUzZWQ1MzhlM2NiZDcwMTAxODY0ODc1YjI5MGY3NGZjNDBmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Inhyb2JTNXdaOXFxb21PRHltWHFPZXc9PSIsInZhbHVlIjoibHl2RXdySWwzbHZUQjF4aElBR09EUnRGaFA3eENuK1ZBSGw1cDFLK2s1SXBxVXVXWjJBTmlDZjZNczlFdStMVW9jdlNuVHhscVBqZWVjTDVVOGxmcTFTaktIN2R1dGJaNW01Z3FFY2RLMzhtTDBhbHhDSk5Hc2tyWmtGSlpUTU1ic1d5MjVHNnVjMHFiS1QxTFJXZFZEVFZnUjhhUjk2ZHphMGhiQk1obGR6S2NnTWk3T0NPQTlUV0NzZk9CbDFNVTNpTmF0d1JOM1BBV1l3c0lQN2dlUlBvaXROSys0RjhNN2pxYkY5dldhcUNWL29XcHlGVTBjS0tmZCtCcGhyelpWS1JJdStZbnY5bUR1aWpxVjMrMDB1Sjg0TG5IOEFvdm5WR1VnR01sM1ltSmVRL1VzL1dJSys0Q3dJYThNRlZ2NE5mQXUreitqcTdtdS84allvanZqY1JxU29CSEE4SnNuWVZPSWpiTklzQVU3N1E1UlM4QjJWUHpFUjcwZlBGb0duMTJYUWhzOTBiSGtrb3UzUURzNnV2cHZiWFU3S3JhelpidG9hcnJINzcrQ0JVODdvMlFVNmhPbGEwdGZSRkphRDh2bG8vTnUvSzNCMU5jakVtbnluSUxLM0tPSDE4elBINDRaYjNGRncrL3kxZ3l0VjU2T1p4SWVmRjJpcUgiLCJtYWMiOiI4ZTRkYTVjZmJjNDlmMWQ5ZWFmYWZiMWFmY2JiNTVjMGFlMzRkNmE2MjkzNzAzMWVkMGU5MjUwZDA3ZTgzZjgwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1562723528\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-701116155 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701116155\", {\"maxDepth\":0})</script>\n"}}