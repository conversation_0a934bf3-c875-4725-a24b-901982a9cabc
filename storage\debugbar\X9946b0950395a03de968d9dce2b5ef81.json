{"__meta": {"id": "X9946b0950395a03de968d9dce2b5ef81", "datetime": "2025-06-07 22:38:55", "utime": **********.336006, "method": "GET", "uri": "/product-categories", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749335934.511949, "end": **********.336042, "duration": 0.8240928649902344, "duration_str": "824ms", "measures": [{"label": "Booting", "start": 1749335934.511949, "relative_start": 0, "end": **********.162241, "relative_end": **********.162241, "duration": 0.650291919708252, "duration_str": "650ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.162258, "relative_start": 0.6503088474273682, "end": **********.336045, "relative_end": 3.0994415283203125e-06, "duration": 0.17378711700439453, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48103072, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.027430000000000003, "accumulated_duration_str": "27.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.232629, "duration": 0.0228, "duration_str": "22.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.121}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.270935, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.121, "width_percent": 2.953}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.301017, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 86.074, "width_percent": 3.245}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.3055801, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 89.318, "width_percent": 2.953}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.315047, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 92.271, "width_percent": 5.031}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.321989, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 97.302, "width_percent": 2.698}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-31937472 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31937472\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.313197, "xdebug_link": null}]}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-640783635 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-640783635\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1861538493 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1861538493\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1899818798 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1899818798\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1809522718 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost:8000/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749335881083%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZ5bW81b2pGMHFGL0tkQTNFMUhoUUE9PSIsInZhbHVlIjoiSzg1ZFZmK29qMURXVktOWmY0ckRzN1JkbG1rbEJ1SXdVN1B6NlpzVVBEWGx1YW5mYzEyOGh1Nm16TWNaL2p0blpYU0lsTEF6OGNWZGVPZlNrdXJqSlQ1OHpubThnck5WMHZFejU4SENMeUxTZkpLUW0zRHBlTlhCZ2t4Y3JidFVNcE83ZnpqcFcrV0RqNVh0ZldCUUxCcXBkUU9ma2EzVHU5dXZ4WStRMjVIMm5IZ3A3TXJYU2ZzTHRsQkFkRlR3amtodlhySDE0NGxFeDZCejFZUU1ZWFR6cHdiQngwekwrVU1qQlk1Unl3ZmY1REI2V3BxbVluZGJESEpTTUVaZm9JTkV0bU1YbGYyaStGTU1pTEd3QVZLS1Q3UDg3aEhCSTUwWTdnckdLcWt4SkF6bHF4cUV2bGUxRkNnT3lhWkhJT0lXMWZWSjBSUTBzWGw0amVWNDBZQVp2cXR2RllMVWU2aTBGWmJIcVZ6NjlkYnFpUHJKd2Npd2NJQmliWGQrQ09WR1NnWnhOVk1MVTRIaXM0QmlRVis5Z3BEcVZYdE5PNHhpbVpoRGpscjA5U2Z3bTRzQkFVUFNxWkowcnhFNWFLRmZlb3JSUXFHcENPQUltRUNycy9raDd4elJkUllyemdJM3ZWUnR1dFo1dHRFSzFFY2pVaUt4YmJ5cjJBck0iLCJtYWMiOiIwZTBlMDRkNjM4NjhmMGYyYWUxMTMyMDlmN2RhOGRiODk0MzE5NmI1ZjQwNWFjMTBkOTNkNjZjMmI5Yjg5OTM4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImpRVC9RNWNPQ0V2RUN1M3FpMEdTTmc9PSIsInZhbHVlIjoiQTRmOGROQk9lWXBVMWtPKzFITFJHbDZtcVF6NTdqSHdrenlCTWQvTmRacEhYcksrMDUxQnpVYlZESXUwOU12cW5ydDl0aTFPQVBmUEJGY1VPaFJMc05mZFRrTzloYlhEelczOUxOczhsbTUvb3JFS0dHSWZDZkxHdGNGRHJQd09MaVdtR0pVLzFRK1dKbjE5V0U5ZkNJVXUwZjlObDdtUm5zUWc1SGMrUUhUTUhDUlYzSHZmblhCN2RXbzFkeVYzMFpuODRWc242b3Izb2xkK2x5UitXVGdGMldsMlZjV3hXU1oxNXFqbm1wNXpSMURjUk1FeXB4dnpOUGFJeTlaTGdiNTYzaGM0UHVDUCs4dzBJb1YzRmNybzZZK0hZS04ydUYvZERhcjdxRThCZzkxWHhHUWZtNG9IRUYvNFBNa2RzNngwUytnS3VEdjRJNHJrdDZkK3BWdVVkWld4Rlhwb01IUmtkc29vTTFTZUJFc3RQTDFGcU90bFp6b1pTbnRHbTY0SDBvWStPVEF3aDMxMWZiU2dHNHlZQ0VsK3JkYTlCOHRvY04vazluOTAyZTFWdEFTMlQ5dVR0Rmx1RmVRZzJSY1U2Ym9QWnJDN0pIcm0xQ0VKdXBrR2o1NERkQ1RWSHgwMFNFTUt0V05zNkY0UnBGNFg3dVd3K3AwNTYvc0EiLCJtYWMiOiJjZDk0NDQ2NjA1YTY4ZWEzZDMxNzc4MjQ0ODM2OWRhNmM0MTQ5OTRjNjRmMDBjZjllM2M5ZmUzNjMwNGQ3YmE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1809522718\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-797446961 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-797446961\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2106916004 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:38:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9KeXVpV3dPalc2ZURCci96WWllY2c9PSIsInZhbHVlIjoicVVwSDJkRnVWdDJvS29QSmFOL2hHcVN0Rjk5elNLV3licWRoUDh5RWw0cG5PRG1adVZ1YnI5SGNpYnRkV0M5WGpscVlwT2J4ZXpBalB2ckphOU5QampvMmRnZ2VYWUw0c2ZqVUxDc2liazBMc2kwL1J4clNFOFpNS2FycTdSTUpvZW9LVElDSERwRzMyd0tnclNGV0M3Z0lzMjRReWdOUzlJUU1ncGxDY2Z0aUdhbng5dkFBMG5wSmRDWEhHQmZQZU1zcWg4Tks0NFQ5WDF3SmY5dzR6UjJqNGRYa2pwMEZEOHpCYTdrbWVjN0czN0NRK21OQUF5YU1GanVRY25KWkc3Zis4L3dmQ0NSU1c3aFZsV2p4amRHSlhiMWIybWlJb3IxbkJ2TjNQQ01sRTlTaEJwNFoxaFJWSUF3N3hncVZyYjVEdEd6Rm40RVFEL3hvbm45ZmlmZVFxVjFqVHg5MkpkQUJNTThBV2pITlpvOHdTSDM5RnFIQm5nZ09Bc3NGWnpVcnhOUk9rZGVXL3NiWTVBUEp2b2QzWVNFdlY0Tm5tUHRpWVBxWXAzMDRZR0M5QnhlRG5IdHpoSmFPYmZQRHoyRm5td3RiRE9ZMUo5dkozTFo4WmJHcUlJcU1oMXlqeEZMcnJqT1F2Q0tiN0JJUzk1c1RMb1VvTXVzK1JDbTAiLCJtYWMiOiIwMGNkNGNiOTAxNWEwYzdiMGVhZjM5ODgyYThlMTVmNWZjODcyYzBlMDgwYzk0YWQyYTZjNjNjYzZmNjQ3YmJkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:38:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImEvdVZjRStMK0YvZ1lUY2p1WGJLQ0E9PSIsInZhbHVlIjoiMXR3S2x0UDhLTXNKSDhZUFRwN1NORVhyWXpsakVvYjBoWnd1S3lodTBnR2NvT3RMWkFiQWVHYmZXSXFaTHJJWjNVV0xZL2tuMGQxQkVmZWNkem54YmVjK1FWS3lsVUpPS0FLY2ZYR0hXbkFDdk92dVBuTU1teDlaaVB1VUp2Vkt3ZWNBayszMnEzbHYrc2RKMnJpVFo1ekJzQ2lEV0N6V0lHaCtJcDRKVWVGbHpjN3JJSVdGWnVyVkNoWDFkcTF2dXA0bWhXaWRCSE9GMURpRFVGc0grdTJCWGZnMmJIM0pZNFVUTjlSa21hNlhaL1BoMjhuNkpHa0c0MDR5bDl5bm1FaXZmd1MyOXVadURqZkdkcldYenVHVmE1T1lQNUg3aWp2dzZFeDFtTys4c3lVVHNOUmM5QldxajI0cGQrUzZ6ekwyQ2ZHYVpwK1c3Um9tSGVISXdiaExxYVhIZEowLzR4emRxQVhJTXMrSHhuU1JDT3dvemZvODAxRlY1dEg1aHBGNko3K3RPbHVNVDFBYlEvRFlrVjRmbjZiaEVJTXp5dWlheHo3WHBnR0F1cUNiaGNLNHV4ZWd3N3cwa3FsbWg0Qjh5V3IrZ3cvamFoT3pqNU4rZUlqR1pMbkV0YWNMN1h6aVpxc052U3YxeDJiRDNvZGl2RXc2WkVZcTFCVngiLCJtYWMiOiI5MDYzMmIzYTA3MjY4ZTNlMGJlMmJhZGNjNzljMjA1OWM4ZDc3MWE0YjE4NjhiOGVkYzViOGVhMGMxNjI0ZTEwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:38:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9KeXVpV3dPalc2ZURCci96WWllY2c9PSIsInZhbHVlIjoicVVwSDJkRnVWdDJvS29QSmFOL2hHcVN0Rjk5elNLV3licWRoUDh5RWw0cG5PRG1adVZ1YnI5SGNpYnRkV0M5WGpscVlwT2J4ZXpBalB2ckphOU5QampvMmRnZ2VYWUw0c2ZqVUxDc2liazBMc2kwL1J4clNFOFpNS2FycTdSTUpvZW9LVElDSERwRzMyd0tnclNGV0M3Z0lzMjRReWdOUzlJUU1ncGxDY2Z0aUdhbng5dkFBMG5wSmRDWEhHQmZQZU1zcWg4Tks0NFQ5WDF3SmY5dzR6UjJqNGRYa2pwMEZEOHpCYTdrbWVjN0czN0NRK21OQUF5YU1GanVRY25KWkc3Zis4L3dmQ0NSU1c3aFZsV2p4amRHSlhiMWIybWlJb3IxbkJ2TjNQQ01sRTlTaEJwNFoxaFJWSUF3N3hncVZyYjVEdEd6Rm40RVFEL3hvbm45ZmlmZVFxVjFqVHg5MkpkQUJNTThBV2pITlpvOHdTSDM5RnFIQm5nZ09Bc3NGWnpVcnhOUk9rZGVXL3NiWTVBUEp2b2QzWVNFdlY0Tm5tUHRpWVBxWXAzMDRZR0M5QnhlRG5IdHpoSmFPYmZQRHoyRm5td3RiRE9ZMUo5dkozTFo4WmJHcUlJcU1oMXlqeEZMcnJqT1F2Q0tiN0JJUzk1c1RMb1VvTXVzK1JDbTAiLCJtYWMiOiIwMGNkNGNiOTAxNWEwYzdiMGVhZjM5ODgyYThlMTVmNWZjODcyYzBlMDgwYzk0YWQyYTZjNjNjYzZmNjQ3YmJkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:38:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImEvdVZjRStMK0YvZ1lUY2p1WGJLQ0E9PSIsInZhbHVlIjoiMXR3S2x0UDhLTXNKSDhZUFRwN1NORVhyWXpsakVvYjBoWnd1S3lodTBnR2NvT3RMWkFiQWVHYmZXSXFaTHJJWjNVV0xZL2tuMGQxQkVmZWNkem54YmVjK1FWS3lsVUpPS0FLY2ZYR0hXbkFDdk92dVBuTU1teDlaaVB1VUp2Vkt3ZWNBayszMnEzbHYrc2RKMnJpVFo1ekJzQ2lEV0N6V0lHaCtJcDRKVWVGbHpjN3JJSVdGWnVyVkNoWDFkcTF2dXA0bWhXaWRCSE9GMURpRFVGc0grdTJCWGZnMmJIM0pZNFVUTjlSa21hNlhaL1BoMjhuNkpHa0c0MDR5bDl5bm1FaXZmd1MyOXVadURqZkdkcldYenVHVmE1T1lQNUg3aWp2dzZFeDFtTys4c3lVVHNOUmM5QldxajI0cGQrUzZ6ekwyQ2ZHYVpwK1c3Um9tSGVISXdiaExxYVhIZEowLzR4emRxQVhJTXMrSHhuU1JDT3dvemZvODAxRlY1dEg1aHBGNko3K3RPbHVNVDFBYlEvRFlrVjRmbjZiaEVJTXp5dWlheHo3WHBnR0F1cUNiaGNLNHV4ZWd3N3cwa3FsbWg0Qjh5V3IrZ3cvamFoT3pqNU4rZUlqR1pMbkV0YWNMN1h6aVpxc052U3YxeDJiRDNvZGl2RXc2WkVZcTFCVngiLCJtYWMiOiI5MDYzMmIzYTA3MjY4ZTNlMGJlMmJhZGNjNzljMjA1OWM4ZDc3MWE0YjE4NjhiOGVkYzViOGVhMGMxNjI0ZTEwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:38:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2106916004\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1529668684 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost:8000/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1529668684\", {\"maxDepth\":0})</script>\n"}}