{"__meta": {"id": "Xb734460f154df0dc478e0c404d32324b", "datetime": "2025-06-08 00:05:13", "utime": 1749341113.011238, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.420489, "end": 1749341113.011257, "duration": 0.5907678604125977, "duration_str": "591ms", "measures": [{"label": "Booting", "start": **********.420489, "relative_start": 0, "end": **********.893852, "relative_end": **********.893852, "duration": 0.47336292266845703, "duration_str": "473ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.893863, "relative_start": 0.4733738899230957, "end": 1749341113.011258, "relative_end": 9.5367431640625e-07, "duration": 0.11739492416381836, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45975800, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.958624, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.9685, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.998498, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": 1749341113.004917, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.019519999999999996, "accumulated_duration_str": "19.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.926077, "duration": 0.00985, "duration_str": "9.85ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 50.461}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9388309, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 50.461, "width_percent": 21.926}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9467518, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 72.387, "width_percent": 2.203}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.960844, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 74.59, "width_percent": 4.457}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9698808, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 79.047, "width_percent": 3.125}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.982265, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 82.172, "width_percent": 5.891}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.988632, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 88.064, "width_percent": 3.381}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9921572, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 91.445, "width_percent": 3.176}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": 1749341113.001352, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 94.621, "width_percent": 5.379}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VFf9Flz1bw194cGGSQu1AgRFkd5yaDWH2CB8uizW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-397352444 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-397352444\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-627989952 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-627989952\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1194113260 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1194113260\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-420936886 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1734 characters\">XSRF-TOKEN=eyJpdiI6IlJKVlRWbzNLYXFmOUNWajAwTXJJZUE9PSIsInZhbHVlIjoid0tjSHpCQUhqYk1iczFYVUdhS0MwZENRZUtFZDNydUIvSEVremQyVVp2M0pEMEIwY1hVSXM5ODZpdWM5bklNV1h2SVl5bTFuRUN2Y0cvZVpqeUlOb0hZZVhONUppbmNUdmFQcGVWaXcxUGxzNUMyWEhEYUJLTzV4UHI5aVNRNE5zbFlxN2hsMERaeFlOQmVmaDkreUJXaDN1RmFlbXdLelp5UHVnS2F6OFhRVGJFaGl3UjVGMDliVmFQZ2VNdnNPdlFodnhtR3BQZFdRWERTb0pLTkI2bFQ2c1VSMnFQL0xyQjVpaTAzNFN5aWJzNVFjc0V1ejFXcnFTb3RGUHgzTmpQSTlkeDRBRHRmVUpHY1lGMG9ybCtyaXFhclhHY21abFgwSG1NSDM2WEdkUi85Ukt4eVh4TWV4MjRxTmZXbEprcWRnWG5VVXh3d3ZQUW81eUR6YU0vSGR3a0RlK2tSWXh0YjVZbzNDa1RrL1dwS2JiZ2RleU5FdVBCcVVmNmpTODh4eXFITjhEbi9vV21YK3dJeTdIbXFHOThxNUZtS05Zc3VuMCtubDAxdFlGc0NaSWpSWElpek52VjZQV2VVV0hJekpHSzVKVzI5NWpvVCtEQ3lxdHlpaUV6SmFHaWN1enhUeER1QWNyenlCUjIvVTI5WjVEYitnenBsVWd5TmoiLCJtYWMiOiIzOGFkN2NhNTJjODYyZmQ5MjMyMjlhZDFlMTRiOWQ5MDE1MWRjZmIxYTM1NWJiNThjNDg3NGYwMzliMmRkZjNlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Iit3bXRuVWVGRVVWS01uSldOcXNhWGc9PSIsInZhbHVlIjoiZzhlNzgwN0YzSzFEOU1ldkk4enNJTkkyeFlmalEvQ0ppNEFnTFpIKytVbmNmcEdoYkhIblZhNmNtWEZzaFAvbCtpWjRpanI5VGZ1WlhkN2ZUa0F5MkZjOTR1eUxhT3F5WWxJZ3dDVlJCSHNEcWZoS3ZlMXdXV0FWaU1QcUlpRUhwZ09Xd3JwbEJUK1pMa21RNDRndmdFSVRoZnQ0ZFRpNDNOTENVN1JGK3hHdGJpTGZHbkd4TDRqbnNjaTVNQ2c1cGppb0JZb3U5MmJEc0RUWG5jOGJlNUlRS2dyMndoVjBIcVRwZE1uSGFidzJkQU5JR0RXVTZQc0QyQ3VncHZLcWtDVUxZV2Z4M2xsd2pwUUhobjFKbW5oQXRDTkpIaGJLUjFqK2dXQTFYMVJNR2lsUUpKQVZuSC96S2dDMjRQY0U3bXpHNUQzYlVUdk8vNzcyQmJ4eDVEUkJPSXcvcGxwUDBrQXhPakxabXVKb3JROVN1OGZEdy9lZFB1SzJOK0poanpnak9sVW1FWHhQWnRLN2tUK2swVmw2Sy90blkzeDhBdTZrWmFLcy9GWUhrWEQyZm52QTRtUmxOU1lOcXhMRFFDYUg2aCt2V2F0Z1NzcjhBSFcvZG9hZGNVbHNKWFhSUncxRGdyMzZubmkrWEx3MzU4VjBpcGVZK0pJSTdaeTMiLCJtYWMiOiJlZTVmYjE0Mjc2ZjRkYTBmNzE1YmFmNzNiYTQ4MzMzNDZkZTRkOWUyNTlmNzgyNjJlNTE4MmUyNmYyYTU3NmEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-420936886\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1015298661 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VFf9Flz1bw194cGGSQu1AgRFkd5yaDWH2CB8uizW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">F7TFL4CJG4Bnd7geNPHZR5Wc8xCe9JbGdL36Vjg4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1015298661\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-939137900 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:05:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdiNTV3bVFYVjRGUnBaSlR6UHNRVVE9PSIsInZhbHVlIjoiRWhYWm00QjBtL3ZrOGk2M0pGVm9vbWFDOU1kajBMVWFzTnZDUHRQcDJEYjNxUXpwNHN3R1YybmQwT0cza3drL0VGM1VLeUZvOEk5dlcvM2dpV05qQjJTZ2IwSkl6akVVWmt3VXFXdjlRNmxXT0VKeFMwWm16N3c5eFp3b28yaytmbElWbzRrSlpJV0FzOTY1ajVPbGdEMFB1NE1pQ3d5cWJmekhTaWtzRkZsaFR1SHZzdzZGaFJJdklPV3NDdCt5N2lmaTZuQXpnM3Y3MWMzK1NnY1VuaTlYcEJPU1R6aml6bXJESFFrN2lod2E0VnVTazR4SUZkelQ2Vkh1SUlIZlIvRUVxRFE4NU5CSWxNdkNFZG1BdmVCTzkvM3hUZzN0eTZmcW04Y1F2U1pWZkVqWVgrNk9Ia0l3RUFwNzAxTmFOMlpxNEpxUWtXaWwxOWg1NG54bTFaWlFDOHduRUxNUExDRkRsaUxmUG13VUdoU3ZvUW05VHhDL0N2SXRsUkduYXNXTWhKb2t3ZlI2Ni9nVFhPanh0U2hXT2RSRVN6eFU1VGVPNEpHaFdIRm1NZ3h6c2xRRThtUnBSdWVCcFYvcG1CUlR6ZFYyeXZvQkFLUDBiQmE4ZUVneE13d2hiVXFvQTh1ZGZpTHRGckI0WFc4SEhFT3Y5LzZZVHdnbkVkRlkiLCJtYWMiOiJhYTE5ZTQzNjdlMzFmNzVkMTAzYTI5MzU2ZDFlYjkxMzZiZmNjZmM4MGI2NWMyZmU3ZWEzNTc0ZmM5MDdmZDlmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:05:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRkRENmbkVScjRISmJHNzlJSlFUdHc9PSIsInZhbHVlIjoiZXlzL3VydW10RGxncmh3MWMxdDdaVTR0TmFjcERUOWo5UElOZy9xOFEwUFFUMGlBQkNNOUw2bDc4VE5CRW5id3laM3hGREF2MGZTYkF2d0gzU2NWT1hhek5ScEFvTmo0c0oxSkxma1FrcnpVME8vZFVMWHdDaklUdDEwVWsxaWFKTnZlVVVWQlBzOHArUW4yRGU1NVVoZTl3RGdKdlIxeTlhK1FxZ3lqUXBYYlpvWkhpanBmNUUwTU1tcC9oVjIzeTZSbFlZTU9uRThNaHNLMTJ0SkhLTDlMejZtcDNBRElxR0hZc201M3BVVXRDZFI0Zk41ZDQzczVPS1dQRFFOS2crWXhTQ1l2cnVJZEIydUM3THFadkdDT0FyS1hLWldaM1pHaFVPZGdMTEdIb1dTK1o2TndiUHFpdDQ5eHVWdm96VFRjMkFhb2dLRFV6dXF1ejNaTEJXVkV0Yjc1T2g5ekpjYnByZWxkVnZMTEs5ZVE4N24zcEhoenRqWllnTzlXcWphQ3RjaG5hMm9wMW5IT2FPbThENzgzcmVuME1GZlRCcVVZdksxaWlIUjFmdzFjbnMvTXduQm56V1A1VWEyWlQ2QlhpY0M4OXBGV2tXU1RwU29CT1EzcVhSaVBlSFhVWmYvcklLdW1DQU5LTmNYWkl5cm91dUQ0V1MzUERPeHUiLCJtYWMiOiI5MjM5OTIyYmI5ODU1YTRkNjM5NzI5MWJlYTJlNTUwOGU4MjBhYWE3ODYyOWQ0NmIzNjM4ZWMwOWM1YjkwYjI0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:05:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdiNTV3bVFYVjRGUnBaSlR6UHNRVVE9PSIsInZhbHVlIjoiRWhYWm00QjBtL3ZrOGk2M0pGVm9vbWFDOU1kajBMVWFzTnZDUHRQcDJEYjNxUXpwNHN3R1YybmQwT0cza3drL0VGM1VLeUZvOEk5dlcvM2dpV05qQjJTZ2IwSkl6akVVWmt3VXFXdjlRNmxXT0VKeFMwWm16N3c5eFp3b28yaytmbElWbzRrSlpJV0FzOTY1ajVPbGdEMFB1NE1pQ3d5cWJmekhTaWtzRkZsaFR1SHZzdzZGaFJJdklPV3NDdCt5N2lmaTZuQXpnM3Y3MWMzK1NnY1VuaTlYcEJPU1R6aml6bXJESFFrN2lod2E0VnVTazR4SUZkelQ2Vkh1SUlIZlIvRUVxRFE4NU5CSWxNdkNFZG1BdmVCTzkvM3hUZzN0eTZmcW04Y1F2U1pWZkVqWVgrNk9Ia0l3RUFwNzAxTmFOMlpxNEpxUWtXaWwxOWg1NG54bTFaWlFDOHduRUxNUExDRkRsaUxmUG13VUdoU3ZvUW05VHhDL0N2SXRsUkduYXNXTWhKb2t3ZlI2Ni9nVFhPanh0U2hXT2RSRVN6eFU1VGVPNEpHaFdIRm1NZ3h6c2xRRThtUnBSdWVCcFYvcG1CUlR6ZFYyeXZvQkFLUDBiQmE4ZUVneE13d2hiVXFvQTh1ZGZpTHRGckI0WFc4SEhFT3Y5LzZZVHdnbkVkRlkiLCJtYWMiOiJhYTE5ZTQzNjdlMzFmNzVkMTAzYTI5MzU2ZDFlYjkxMzZiZmNjZmM4MGI2NWMyZmU3ZWEzNTc0ZmM5MDdmZDlmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:05:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRkRENmbkVScjRISmJHNzlJSlFUdHc9PSIsInZhbHVlIjoiZXlzL3VydW10RGxncmh3MWMxdDdaVTR0TmFjcERUOWo5UElOZy9xOFEwUFFUMGlBQkNNOUw2bDc4VE5CRW5id3laM3hGREF2MGZTYkF2d0gzU2NWT1hhek5ScEFvTmo0c0oxSkxma1FrcnpVME8vZFVMWHdDaklUdDEwVWsxaWFKTnZlVVVWQlBzOHArUW4yRGU1NVVoZTl3RGdKdlIxeTlhK1FxZ3lqUXBYYlpvWkhpanBmNUUwTU1tcC9oVjIzeTZSbFlZTU9uRThNaHNLMTJ0SkhLTDlMejZtcDNBRElxR0hZc201M3BVVXRDZFI0Zk41ZDQzczVPS1dQRFFOS2crWXhTQ1l2cnVJZEIydUM3THFadkdDT0FyS1hLWldaM1pHaFVPZGdMTEdIb1dTK1o2TndiUHFpdDQ5eHVWdm96VFRjMkFhb2dLRFV6dXF1ejNaTEJXVkV0Yjc1T2g5ekpjYnByZWxkVnZMTEs5ZVE4N24zcEhoenRqWllnTzlXcWphQ3RjaG5hMm9wMW5IT2FPbThENzgzcmVuME1GZlRCcVVZdksxaWlIUjFmdzFjbnMvTXduQm56V1A1VWEyWlQ2QlhpY0M4OXBGV2tXU1RwU29CT1EzcVhSaVBlSFhVWmYvcklLdW1DQU5LTmNYWkl5cm91dUQ0V1MzUERPeHUiLCJtYWMiOiI5MjM5OTIyYmI5ODU1YTRkNjM5NzI5MWJlYTJlNTUwOGU4MjBhYWE3ODYyOWQ0NmIzNjM4ZWMwOWM1YjkwYjI0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:05:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-939137900\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1784739971 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VFf9Flz1bw194cGGSQu1AgRFkd5yaDWH2CB8uizW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1784739971\", {\"maxDepth\":0})</script>\n"}}